"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.authenticateForSSE = exports.singleDeviceLogin = exports.authenticateToken = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const User_1 = __importDefault(require("../models/User"));
const Session_1 = __importDefault(require("../models/Session"));
const sessionService = __importStar(require("../services/sessionService"));
const auth_config_1 = require("../config/auth-config");
// Middleware xác thực JWT
const authenticateToken = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        // Lấy token từ header hoặc cookies
        const token = req.cookies.jwt || ((_a = req.headers.authorization) === null || _a === void 0 ? void 0 : _a.split(" ")[1]);
        if (!token) {
            // Kiểm tra nếu là API request hay view request
            if (isApiRequest(req)) {
                return res.status(401).json({ message: "Không có token xác thực!" });
            }
            else {
                return res.redirect("/login");
            }
        }
        // Tìm phiên hợp lệ trong database
        const session = yield sessionService.findValidSession(token);
        if (!session) {
            // console.log(
            //   `Không tìm thấy phiên hợp lệ cho token: ${token.substring(0, 10)}...`
            // );
            // Xóa cookie nếu không có phiên hợp lệ
            res.clearCookie("jwt");
            // Trả về script để xóa localStorage khi phiên không hợp lệ
            if (!isApiRequest(req)) {
                const cleanupScript = `
          <script>
            // Xóa dữ liệu xác thực khỏi localStorage
            localStorage.removeItem('auth_status');
            localStorage.removeItem('user_id');
            localStorage.removeItem('session_id');
            console.log('Phiên đăng nhập không hợp lệ, đã xóa dữ liệu xác thực');
            
            // Chuyển hướng đến trang đăng nhập
            window.location.href = '/login?error=Phiên đăng nhập không hợp lệ hoặc đã hết hạn';
          </script>
        `;
                return res.send(`
          <!DOCTYPE html>
          <html>
            <head>
              <title>Đăng xuất...</title>
            </head>
            <body>
              <div style="text-align: center; margin-top: 100px;">
                <h2>Phiên đăng nhập đã hết hạn</h2>
                <p>Đang chuyển hướng về trang đăng nhập...</p>
              </div>
              ${cleanupScript}
            </body>
          </html>
        `);
            }
            if (isApiRequest(req)) {
                return res.status(401).json({
                    message: "Phiên đăng nhập không hợp lệ hoặc đã hết hạn!",
                    error: "invalid_session",
                });
            }
            else {
                return res.redirect("/login?error=Phiên đăng nhập không hợp lệ");
            }
        }
        // Xác thực token
        let decoded;
        try {
            decoded = jsonwebtoken_1.default.verify(token, process.env.JWT_SECRET);
        }
        catch (error) {
            // Token đã hết hạn hoặc không hợp lệ, nhưng phiên vẫn còn trong database
            // Tạo token mới nếu phiên vẫn hợp lệ
            if (error.name === "TokenExpiredError" && session) {
                // Tạo token mới
                const user = yield User_1.default.findById(session.userId);
                if (!user) {
                    throw error; // Nếu không tìm thấy user, trả về lỗi ban đầu
                }
                // Tạo JWT token mới
                const newToken = sessionService.generateToken(user);
                // Cập nhật token mới vào session
                yield Session_1.default.updateOne({ _id: session._id }, { token: newToken });
                // Cập nhật cookie với token mới
                res.cookie("jwt", newToken, auth_config_1.AuthUtils.getCookieOptions());
                // Giải mã token mới để sử dụng
                decoded = jsonwebtoken_1.default.verify(newToken, process.env.JWT_SECRET);
                console.log("Token đã được làm mới tự động");
            }
            else {
                throw error; // Các lỗi khác với TokenExpiredError
            }
        }
        // Kiểm tra user và token có khớp không
        const user = yield User_1.default.findById(decoded.id);
        if (!user) {
            if (isApiRequest(req)) {
                return res.status(404).json({ message: "Không tìm thấy người dùng!" });
            }
            else {
                return res.redirect("/login");
            }
        }
        // Kiểm tra phiên có thuộc về user này không
        if (session.userId.toString() !== user._id.toString()) {
            console.log(`Session userId mismatch: expected ${user._id}, got ${session.userId}`);
            if (isApiRequest(req)) {
                return res.status(401).json({
                    message: "Phiên đăng nhập không hợp lệ!",
                    error: "invalid_session",
                });
            }
            else {
                return res.redirect("/login?error=Phiên đăng nhập không hợp lệ");
            }
        }
        // Kiểm tra nếu token sắp hết hạn, tạo token mới
        if (decoded.exp) {
            const currentTime = Math.floor(Date.now() / 1000);
            const timeUntilExpiry = decoded.exp - currentTime;
            const refreshThresholdInSeconds = Math.floor(auth_config_1.AuthConfig.JWT.REFRESH_THRESHOLD / 1000);
            if (timeUntilExpiry < refreshThresholdInSeconds) {
                // Tạo token mới
                const newToken = sessionService.generateToken(user);
                // Cập nhật token mới vào session
                yield Session_1.default.updateOne({ _id: session._id }, { token: newToken });
                // Cập nhật cookie với token mới
                res.cookie("jwt", newToken, auth_config_1.AuthUtils.getCookieOptions());
                console.log("Token đã được làm mới chủ động (còn dưới 1 giờ)");
            }
        }
        // Cập nhật thời gian hoạt động cuối cùng
        sessionService.updateLastActiveTime(token).catch((err) => {
            console.error("Error updating last active time:", err);
        });
        // Gán thông tin user và session vào request
        req.user = user;
        req.session = session;
        next();
    }
    catch (error) {
        console.error("Token verification error:", error);
        if (isApiRequest(req)) {
            if (error.name === "JsonWebTokenError") {
                return res.status(401).json({ message: "Token không hợp lệ!" });
            }
            if (error.name === "TokenExpiredError") {
                return res.status(401).json({ message: "Token đã hết hạn!" });
            }
            return res
                .status(500)
                .json({ message: "Lỗi xác thực!", error: error.message });
        }
        else {
            // Chuyển hướng đến trang đăng nhập với thông báo lỗi
            return res.redirect("/login?error=Phiên đăng nhập không hợp lệ");
        }
    }
});
exports.authenticateToken = authenticateToken;
// Middleware kiểm tra đăng nhập đồng thời
const singleDeviceLogin = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = req.user;
        const newToken = req.newToken; // Token mới được tạo trong quá trình login
        // Kiểm tra xem người dùng đã có phiên đăng nhập nào chưa
        if (!user) {
            return res.status(400).json({
                message: "Thiếu thông tin người dùng!",
                error: "missing_user",
            });
        }
        const activeSessions = yield Session_1.default.find({
            userId: user._id,
            isActive: true,
        });
        if (activeSessions.length > 0) {
            return res.status(400).json({
                message: "Tài khoản này đang được đăng nhập ở thiết bị khác. Vui lòng đăng xuất trước khi đăng nhập lại.",
                isAlreadyLoggedIn: true,
                activeSessions: activeSessions.length,
            });
        }
        next();
    }
    catch (error) {
        return res
            .status(500)
            .json({ message: "Lỗi kiểm tra thiết bị!", error: error.message });
    }
});
exports.singleDeviceLogin = singleDeviceLogin;
// Middleware chỉ cho phép kết nối SSE từ client có token hợp lệ
const authenticateForSSE = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // Lấy token từ cookie
        const token = req.cookies.jwt;
        const clientId = req.query.clientId || "unknown";
        // console.log(`SSE authentication for clientId: ${clientId}`);
        if (!token) {
            // console.log(
            //   `SSE connection rejected for clientId ${clientId}: No JWT token found`
            // );
            return res.status(401).end();
        }
        // Tìm phiên hợp lệ
        const session = yield sessionService.findValidSession(token);
        // Cho phép kết nối SSE ngay cả khi phiên không hợp lệ để có thể gửi thông báo đăng xuất
        let user;
        try {
            // Xác thực token
            const decoded = jsonwebtoken_1.default.verify(token, process.env.JWT_SECRET);
            // Kiểm tra user tồn tại không
            user = yield User_1.default.findById(decoded.id);
            if (!user) {
                // console.log(
                //   `SSE connection allowing for clientId ${clientId} despite invalid user`
                // );
            }
        }
        catch (err) {
            // console.log(
            //   `SSE token verification error for clientId ${clientId}, but allowing connection`
            // );
        }
        // Gán thông tin user vào request nếu có
        if (user) {
            req.user = user;
            // console.log(
            //   `SSE connection authenticated for user ${user._id} (${user.email}), clientId: ${clientId}`
            // );
        }
        else {
            // console.log(
            //   `SSE connection partially authenticated (missing user) for clientId: ${clientId}`
            // );
        }
        next();
    }
    catch (error) {
        const clientId = req.query.clientId || "unknown";
        // console.error(`SSE authentication error for clientId ${clientId}:`, error);
        // Vẫn cho phép kết nối để có thể gửi thông báo đăng xuất
        next();
    }
});
exports.authenticateForSSE = authenticateForSSE;
// Hàm hỗ trợ để phân biệt giữa API request và view request
function isApiRequest(req) {
    // Kiểm tra Accept header hoặc đường dẫn để xác định là API request
    const acceptHeader = req.headers.accept || "";
    const path = req.path || "";
    return (acceptHeader.includes("application/json") ||
        path.startsWith("/api/") ||
        path.includes("/session-events"));
}
//# sourceMappingURL=authMiddleware.js.map