<!DOCTYPE html>
<html lang="vi">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><PERSON><PERSON><PERSON><PERSON> tập Quizizz - <%= product.name %></title>

    <!-- Meta tags cho page -->
    <meta name="productId" content="<%= product._id %>" />
    <meta name="practice-type" content="quizizz" />

    <!-- Favicon và web manifest -->
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
    <link rel="manifest" href="/site.webmanifest" />
    <link rel="shortcut icon" href="/favicon.ico" />

    <!-- Tailwind CSS từ CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              "quizizz-purple": "#8854d0",
              "quizizz-purple-dark": "#6c3fb5",
              "quizizz-purple-light": "#a374e8",
              "quizizz-bg": "#5a0773",
              "quizizz-bg-dark": "#3b0764",
              "quizizz-pink": "#ff4081",
              "quizizz-blue": "#2196f3",
              "quizizz-green": "#4caf50",
              "quizizz-yellow": "#ffeb3b",
              "quizizz-red": "#f44336",
              "quizizz-orange": "#ff9800",
              "quizizz-option-1": "#e21b3c",
              "quizizz-option-2": "#1368ce",
              "quizizz-option-3": "#d89e00",
              "quizizz-option-4": "#26890c",
            },
            animation: {
              "bounce-in": "bounce-in 0.6s ease-out",
              "slide-in": "slide-in 0.5s ease-out",
              "pulse-strong": "pulse-strong 1s ease-in-out infinite",
              "celebration": "celebration 1s ease-in-out",
              "shake": "shake 0.5s ease-in-out",
              "zoom-in": "zoom-in 0.3s ease-out",
              "fade-in": "fade-in 0.4s ease-out",
            },
            keyframes: {
              "bounce-in": {
                "0%": { transform: "scale(0.8)", opacity: "0" },
                "50%": { transform: "scale(1.1)", opacity: "1" },
                "100%": { transform: "scale(1)", opacity: "1" },
              },
              "slide-in": {
                "0%": { transform: "translateY(30px)", opacity: "0" },
                "100%": { transform: "translateY(0)", opacity: "1" },
              },
              "pulse-strong": {
                "0%, 100%": { transform: "scale(1)", opacity: "1" },
                "50%": { transform: "scale(1.05)", opacity: "0.9" },
              },
              "celebration": {
                "0%, 100%": { transform: "scale(1) rotate(0deg)" },
                "25%": { transform: "scale(1.1) rotate(-5deg)" },
                "50%": { transform: "scale(1.2) rotate(5deg)" },
                "75%": { transform: "scale(1.1) rotate(-2deg)" },
              },
              "shake": {
                "0%, 100%": { transform: "translateX(0)" },
                "25%": { transform: "translateX(-5px)" },
                "50%": { transform: "translateX(5px)" },
                "75%": { transform: "translateX(-3px)" },
              },
              "zoom-in": {
                "0%": { transform: "scale(0.5)", opacity: "0" },
                "100%": { transform: "scale(1)", opacity: "1" },
              },
              "fade-in": {
                "0%": { opacity: "0" },
                "100%": { opacity: "1" },
              },
            },
          },
        },
      };
    </script>

    <!-- Font Awesome -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />

    <!-- Google Fonts -->
    <link
      href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;500;600;700;800&display=swap"
      rel="stylesheet"
    />

    <!-- Quizizz Practice CSS -->
    <link rel="stylesheet" href="/css/quizizz-practice.css" />

    <style>
      body {
        font-family: "Nunito", sans-serif;
        margin: 0;
        padding: 0;
        overflow-x: hidden;
        background: linear-gradient(135deg, #5a0773 0%, #3b0764 100%);
        color: white;
        min-height: 100vh;
      }

      .quizizz-container {
        min-height: 100vh;
        display: flex;
        flex-direction: column;
      }

      .question-number {
        background: rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        padding: 8px 16px;
        font-weight: 600;
        font-size: 14px;
      }

      .timer-display {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        padding: 8px 16px;
        font-weight: 600;
        font-size: 14px;
        border: 2px solid rgba(255, 255, 255, 0.3);
      }

      .score-display {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        padding: 8px 16px;
        font-weight: 600;
        font-size: 14px;
      }

      .progress-bar-container {
        height: 8px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 4px;
        overflow: hidden;
      }

      .progress-bar {
        height: 100%;
        background: linear-gradient(90deg, #ff4081, #8854d0);
        border-radius: 4px;
        transition: width 0.5s ease;
        position: relative;
      }

      .progress-bar::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.3),
          transparent
        );
        animation: shimmer 2s infinite;
      }

      @keyframes shimmer {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
      }

      .option-card {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border: 2px solid rgba(255, 255, 255, 0.2);
        border-radius: 16px;
        padding: 20px;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        min-height: 80px;
        display: flex;
        align-items: center;
      }

      .option-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        border-color: rgba(255, 255, 255, 0.4);
      }

      .option-card.selected {
        background: rgba(255, 255, 255, 0.2);
        border-color: #fff;
        transform: translateY(-2px);
      }

      .option-letter {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 700;
        font-size: 18px;
        margin-right: 16px;
        flex-shrink: 0;
      }

      .option-card.selected .option-letter {
        background: #fff;
        color: #5a0773;
      }

      .question-text {
        font-size: 28px;
        font-weight: 700;
        line-height: 1.3;
        margin-bottom: 32px;
        text-align: center;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      }

      .loading-spinner {
        width: 60px;
        height: 60px;
        border: 4px solid rgba(255, 255, 255, 0.3);
        border-top: 4px solid #fff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      .streak-counter {
        background: linear-gradient(135deg, #ff4081, #ff6b9d);
        border-radius: 20px;
        padding: 8px 16px;
        font-weight: 700;
        font-size: 14px;
        display: flex;
        align-items: center;
        gap: 6px;
      }

      .celebration-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
        opacity: 0;
        pointer-events: none;
        transition: opacity 0.3s ease;
      }

      .celebration-overlay.show {
        opacity: 1;
        pointer-events: all;
      }

      .feedback-icon {
        font-size: 120px;
        margin-bottom: 20px;
      }

      .feedback-text {
        font-size: 36px;
        font-weight: 700;
        text-align: center;
      }
    </style>
  </head>

  <body>
    <div class="quizizz-container">
      <!-- Header -->
      <header class="p-4">
        <div class="flex items-center justify-between">
          <!-- Left side -->
          <div class="flex items-center gap-4">
            <button
              id="exitButton"
              class="text-white hover:text-quizizz-pink transition-colors p-2 rounded-full hover:bg-white hover:bg-opacity-10"
            >
              <i class="fas fa-arrow-left text-xl"></i>
            </button>
            <div class="question-number">
              <span id="questionCounter">Câu 1/<%= settings.count %></span>
            </div>
          </div>

          <!-- Right side -->
          <div class="flex items-center gap-4">
            <!-- Streak counter -->
            <div id="streakCounter" class="streak-counter hidden">
              <i class="fas fa-fire"></i>
              <span id="streakCount">0</span>
            </div>

            <!-- Score display -->
            <div class="score-display">
              <span id="scoreDisplay">0</span> pts
            </div>

            <!-- Timer -->
            <div class="timer-display">
              <i class="fas fa-clock mr-2"></i>
              <span id="timerDisplay"><%= settings.time %>:00</span>
            </div>
          </div>
        </div>

        <!-- Progress bar -->
        <div class="progress-bar-container mt-4">
          <div id="progressBar" class="progress-bar" style="width: 0%"></div>
        </div>
      </header>

      <!-- Main content -->
      <main class="flex-1 flex items-center justify-center p-4">
        <!-- Loading screen -->
        <div id="loadingContainer" class="text-center">
          <div class="loading-spinner mx-auto mb-6"></div>
          <h2 class="text-2xl font-bold mb-2">Đang chuẩn bị câu hỏi...</h2>
          <p class="text-lg opacity-80">Quizizz đang tải bài luyện tập của bạn</p>
        </div>

        <!-- Question container -->
        <div id="questionContainer" class="hidden w-full max-w-4xl mx-auto">
          <!-- Question text -->
          <div class="question-text" id="questionText"></div>

          <!-- Question image -->
          <div id="questionImageContainer" class="hidden text-center mb-8">
            <img
              id="questionImage"
              src=""
              alt="Question image"
              class="max-w-md max-h-60 mx-auto rounded-lg shadow-lg"
            />
          </div>

          <!-- Options grid -->
          <div id="optionsGrid" class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- Options will be inserted here by JavaScript -->
          </div>
        </div>

        <!-- Results container -->
        <div id="resultsContainer" class="hidden w-full max-w-4xl mx-auto text-center">
          <!-- Results content will be inserted by JavaScript -->
        </div>
      </main>
    </div>

    <!-- Answer feedback overlay -->
    <div id="answerFeedback" class="celebration-overlay">
      <div class="text-center">
        <div id="feedbackIcon" class="feedback-icon"></div>
        <div id="feedbackText" class="feedback-text"></div>
      </div>
    </div>

    <!-- Exit confirmation modal -->
    <div
      id="exitModal"
      class="fixed inset-0 bg-black bg-opacity-70 z-50 flex items-center justify-center hidden"
    >
      <div class="bg-white rounded-xl p-6 max-w-md w-full mx-4 text-center">
        <h3 class="text-2xl font-bold text-gray-800 mb-4">Bạn muốn thoát?</h3>
        <p class="text-gray-600 mb-6">
          Tiến độ của bạn sẽ không được lưu lại nếu thoát bây giờ.
        </p>
        <div class="flex justify-center gap-4">
          <button
            id="cancelExitBtn"
            class="px-6 py-3 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors font-medium"
          >
            Tiếp tục
          </button>
          <button
            id="confirmExitBtn"
            class="px-6 py-3 bg-quizizz-red text-white rounded-lg hover:bg-red-600 transition-colors font-medium"
          >
            Thoát
          </button>
        </div>
      </div>
    </div>

    <!-- Practice settings (hidden, for JavaScript access) -->
    <script type="application/json" id="practiceSettings">
      <%- JSON.stringify(settings) %>
    </script>

    <!-- Scripts -->
    <script src="/js/security-measures.js"></script>
    <script src="/js/quizizz-practice-core.js"></script>
    <script src="/js/quizizz-practice-ui.js"></script>

    <script>
      document.addEventListener("DOMContentLoaded", function () {
        // Apply security measures
        if (typeof GlobalSecurityMeasures === "function") {
          GlobalSecurityMeasures({
            contextMessage: "luyện tập Quizizz",
            enableDevToolsDetection: true,
            enableScreenshotBlocking: true,
            enableRightClickBlocking: true,
            enableCopyBlocking: true,
            enablePrintBlocking: true,
            enableViewSourceBlocking: true,
            enableSavePageBlocking: true,
            enableDragDropBlocking: true,
            devToolsThreshold: 160,
            redirectOnDevTools: false,
            redirectUrl: "/home",
          });
        }

        // Initialize Quizizz practice
        if (typeof window.QuizizzPractice !== "undefined") {
          window.QuizizzPractice.init();
        } else {
          console.error("Quizizz practice module not loaded!");
        }
      });
    </script>
  </body>
</html>
