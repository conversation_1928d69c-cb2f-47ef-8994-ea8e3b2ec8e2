"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const student_controller_1 = __importDefault(require("../controllers/student.controller"));
const asynHandler_1 = __importDefault(require("../util/asynHandler"));
const student_middleware_1 = require("../middlewares/student.middleware");
const router = express_1.default.Router();
router.get("/:productId", (0, asynHandler_1.default)(student_controller_1.default.getStudentsByCourse));
// router.post("/", asyncHandler(studentController.createStudent));
// router.delete("/:studentId", asyncHand<PERSON>(studentController.deleteStudent));
router.get("/search", (0, asynHandler_1.default)(student_controller_1.default.searchStudentByProductId));
router.get("/count/:productId", (0, asynHandler_1.default)(student_controller_1.default.getCountStudentByProductId));
// Thêm route mới để lấy danh sách khóa học của sinh viên
router.get("/:studentId/courses", (0, asynHandler_1.default)(student_middleware_1.validateStudent), (0, asynHandler_1.default)(student_middleware_1.getStudentCourses));
// Thêm route mới để lấy danh sách đề thi của sinh viên theo khóa học
router.get("/:studentId/courses/:productId/exams", (0, asynHandler_1.default)(student_middleware_1.validateStudent), (0, asynHandler_1.default)(student_middleware_1.validateStudentCourse), (0, asynHandler_1.default)(student_controller_1.default.getStudentExams));
// Thêm route mới để lấy danh sách bài kiểm tra của sinh viên theo khóa học
router.get("/:studentId/courses/:productId/tests", (0, asynHandler_1.default)(student_middleware_1.validateStudent), (0, asynHandler_1.default)(student_middleware_1.validateStudentCourse), (0, asynHandler_1.default)(student_controller_1.default.getStudentTests));
exports.default = router;
//# sourceMappingURL=student.route.js.map