{"version": 3, "file": "auth-config.js", "sourceRoot": "", "sources": ["../../src/config/auth-config.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAEH,iCAAiC;AACjC,MAAM,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC;AACzB,MAAM,IAAI,GAAG,EAAE,GAAG,MAAM,CAAC;AACzB,MAAM,GAAG,GAAG,EAAE,GAAG,IAAI,CAAC;AAEtB;;GAEG;AACU,QAAA,UAAU,GAAG;IACxB,2CAA2C;IAC3C,sBAAsB,EAAE,EAAE,GAAG,GAAG;IAEhC,2BAA2B;IAC3B,GAAG,EAAE;QACH,iCAAiC;QACjC,UAAU,EAAE,EAAE,GAAG,GAAG;QAEpB,iFAAiF;QACjF,iBAAiB,EAAE,CAAC,GAAG,IAAI;KAC5B;IAED,yBAAyB;IACzB,OAAO,EAAE;QACP,0DAA0D;QAC1D,UAAU,EAAE,EAAE,GAAG,GAAG;QAEpB,mCAAmC;QACnC,gBAAgB,EAAE,CAAC,GAAG,GAAG;KAC1B;IAED,wBAAwB;IACxB,MAAM,EAAE;QACN,sDAAsD;QACtD,OAAO,EAAE,EAAE,GAAG,GAAG;QAEjB,0BAA0B;QAC1B,SAAS,EAAE,IAAI;QACf,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY;QAC7C,SAAS,EAAE,KAAc;KAC1B;IAED,gCAAgC;IAChC,WAAW,EAAE;QACX,kCAAkC;QAClC,UAAU,EAAE,CAAC,GAAG,IAAI;KACrB;IAED,gCAAgC;IAChC,KAAK,EAAE;QACL,uBAAuB;QACvB,iBAAiB,EAAE,EAAE,GAAG,MAAM;QAE9B,4CAA4C;QAC5C,eAAe,EAAE,EAAE,GAAG,MAAM;KAC7B;CACO,CAAC;AAEX;;GAEG;AACU,QAAA,SAAS,GAAG;IACvB;;OAEG;IACH,yBAAyB;QACvB,OAAO,IAAI,CAAC,KAAK,CAAC,kBAAU,CAAC,GAAG,CAAC,UAAU,GAAG,IAAI,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACH,sBAAsB;QACpB,OAAO,GAAG,kBAAU,CAAC,GAAG,CAAC,UAAU,IAAI,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,cAAsB;QACvC,MAAM,mBAAmB,GAAG,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACxD,OAAO,mBAAmB,GAAG,kBAAU,CAAC,GAAG,CAAC,iBAAiB,CAAC;IAChE,CAAC;IAED;;OAEG;IACH,iBAAiB,CACf,WAAmB,kBAAU,CAAC,sBAAsB;QAEpD,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,QAAQ,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,OAAO;YACL,QAAQ,EAAE,kBAAU,CAAC,MAAM,CAAC,SAAS;YACrC,MAAM,EAAE,kBAAU,CAAC,MAAM,CAAC,MAAM;YAChC,QAAQ,EAAE,kBAAU,CAAC,MAAM,CAAC,SAAS;YACrC,MAAM,EAAE,kBAAU,CAAC,MAAM,CAAC,OAAO;SAClC,CAAC;IACJ,CAAC;CACF,CAAC;AAEF;;GAEG;AACU,QAAA,cAAc,GAAG;IAC5B,uCAAuC;IACvC,SAAS,EAAE;QACT,YAAY,EAAE,GAAG,kBAAU,CAAC,sBAAsB,GAAG,GAAG,OAAO;QAC/D,aAAa,EAAE,GAAG,kBAAU,CAAC,GAAG,CAAC,UAAU,GAAG,GAAG,OAAO;QACxD,iBAAiB,EAAE,GAAG,kBAAU,CAAC,OAAO,CAAC,UAAU,GAAG,GAAG,OAAO;QAChE,YAAY,EAAE,GAAG,kBAAU,CAAC,MAAM,CAAC,OAAO,GAAG,GAAG,OAAO;QACvD,oBAAoB,EAAE,GAAG,kBAAU,CAAC,WAAW,CAAC,UAAU,GAAG,IAAI,UAAU;KAC5E;IAED,oCAAoC;IACpC,OAAO,EAAE;QACP,aAAa,EAAE,kBAAU,CAAC,sBAAsB;QAChD,aAAa,EAAE,kBAAU,CAAC,GAAG,CAAC,UAAU;QACxC,iBAAiB,EAAE,kBAAU,CAAC,OAAO,CAAC,UAAU;QAChD,YAAY,EAAE,kBAAU,CAAC,MAAM,CAAC,OAAO;QACvC,sBAAsB,EACpB,kBAAU,CAAC,sBAAsB,KAAK,kBAAU,CAAC,GAAG,CAAC,UAAU;YAC/D,kBAAU,CAAC,GAAG,CAAC,UAAU,KAAK,kBAAU,CAAC,OAAO,CAAC,UAAU;YAC3D,kBAAU,CAAC,OAAO,CAAC,UAAU,KAAK,kBAAU,CAAC,MAAM,CAAC,OAAO;KAC9D;CACF,CAAC;AAEF,kBAAe,kBAAU,CAAC"}