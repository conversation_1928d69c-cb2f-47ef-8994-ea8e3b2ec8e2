import { Types } from "mongoose";
import UserProgress, { IUserProgress } from "../models/UserProgress";
import Product from "../models/products";
import Exam from "../models/exam";
import ExamHistory from "../models/ExamHistory";

/**
 * Cập nhật tiến độ học tập khi người dùng hoàn thành bài kiểm tra
 */
export async function updateUserProgress(
  examHistory: any
): Promise<IUserProgress | null> {
  try {
    // Lấy thông tin đề thi
    const exam = await Exam.findById(examHistory.examId);
    if (!exam) return null;

    // Lấy thông tin sản phẩm (môn học)
    const product = await Product.findById(exam.productId);
    if (!product) return null;

    // Tìm hoặc tạo mới tiến độ người dùng
    let userProgress = await UserProgress.findOne({
      user_id: examHistory.userId,
      product_id: exam.productId,
    });

    if (!userProgress) {
      userProgress = new UserProgress({
        user_id: examHistory.userId,
        product_id: exam.productId,
        best_exam_attempts: [],
        total_correct_answers: 0,
        total_questions: product.countQuestion,
        progress_percentage: 0,
        last_updated: new Date(),
      });
    }

    // Tính số câu đúng từ điểm số
    const correctAnswers = Math.round(
      (examHistory.score / 100) * examHistory.totalQuestions
    );

    // Tìm tất cả các bản ghi của đề thi này
    const existingAttempts = userProgress.best_exam_attempts.filter(
      (attempt) => attempt.exam_id.toString() === examHistory.examId.toString()
    );

    // Nếu có nhiều hơn 1 bản ghi, xóa tất cả trừ bản ghi có điểm cao nhất
    if (existingAttempts.length > 1) {
      console.log(
        `Phát hiện ${existingAttempts.length} bản ghi trùng lặp cho đề thi ${examHistory.examId}`
      );

      // Tìm bản ghi có điểm cao nhất
      const bestAttempt = existingAttempts.reduce(
        (best, current) => (current.score > best.score ? current : best),
        existingAttempts[0]
      );

      // Xóa tất cả bản ghi của đề thi này
      userProgress.best_exam_attempts = userProgress.best_exam_attempts.filter(
        (attempt) =>
          attempt.exam_id.toString() !== examHistory.examId.toString()
      );

      // Thêm lại bản ghi tốt nhất
      userProgress.best_exam_attempts.push(bestAttempt);

      // Tính lại tổng số câu đúng
      userProgress.total_correct_answers =
        userProgress.best_exam_attempts.reduce(
          (sum, attempt) => sum + attempt.correct_answers,
          0
        );

      console.log(
        `Đã xóa các bản ghi trùng lặp và giữ lại bản ghi có điểm cao nhất: ${bestAttempt.score}`
      );
    }

    // Tìm đề thi trong danh sách điểm (sau khi đã xử lý trùng lặp)
    const examIndex = userProgress.best_exam_attempts.findIndex(
      (attempt) => attempt.exam_id.toString() === examHistory.examId.toString()
    );

    // Biến để theo dõi sự thay đổi điểm số
    let scoreChanged = false;

    if (examIndex >= 0) {
      // Đã có điểm của đề thi này, kiểm tra nếu điểm mới cao hơn
      const currentAttempt = userProgress.best_exam_attempts[examIndex];
      if (examHistory.score > currentAttempt.score) {
        console.log(
          `Cập nhật điểm cao hơn: ${examHistory.score} > ${currentAttempt.score}`
        );

        // Cập nhật thông tin điểm tốt nhất
        userProgress.best_exam_attempts[examIndex] = {
          exam_id: examHistory.examId,
          score: examHistory.score,
          correct_answers: correctAnswers,
          total_questions: examHistory.totalQuestions,
          completed_at: examHistory.completedAt,
        };

        scoreChanged = true;
      } else {
      }
    } else {
      // Chưa có điểm của đề thi này, thêm mới
      userProgress.best_exam_attempts.push({
        exam_id: examHistory.examId,
        score: examHistory.score,
        correct_answers: correctAnswers,
        total_questions: examHistory.totalQuestions,
        completed_at: examHistory.completedAt,
      });

      scoreChanged = true;
    }

    // Chỉ cập nhật nếu có thay đổi điểm số
    if (scoreChanged) {
      // Tính lại tổng số câu đúng từ tất cả các bài thi tốt nhất
      userProgress.total_correct_answers =
        userProgress.best_exam_attempts.reduce(
          (sum, attempt) => sum + attempt.correct_answers,
          0
        );

      console.log(
        `Đã tính lại tổng số câu đúng: ${userProgress.total_correct_answers}/${userProgress.total_questions}`
      );

      // Tính lại phần trăm tiến độ
      userProgress.progress_percentage = Math.round(
        (userProgress.total_correct_answers / userProgress.total_questions) *
          100
      );
      userProgress.last_updated = new Date();

      // Lưu thay đổi
      await userProgress.save();
    }

    return userProgress;
  } catch (error) {
    console.error("Lỗi cập nhật tiến độ:", error);
    return null;
  }
}

/**
 * Lấy tiến độ của người dùng theo môn học
 */
export async function getUserProductProgress(
  userId: string,
  productId: string
) {
  try {
    // Sử dụng aggregation để tối ưu hiệu suất truy vấn
    const results = await UserProgress.aggregate([
      {
        $match: {
          user_id: new Types.ObjectId(userId),
          product_id: new Types.ObjectId(productId),
        },
      },
      {
        $lookup: {
          from: "products",
          localField: "product_id",
          foreignField: "_id",
          as: "productInfo",
        },
      },
      { $unwind: { path: "$productInfo", preserveNullAndEmptyArrays: true } },
      {
        $addFields: {
          exam_ids: {
            $map: {
              input: "$best_exam_attempts",
              as: "attempt",
              in: "$$attempt.exam_id",
            },
          },
        },
      },
      {
        $lookup: {
          from: "exams",
          let: { exam_ids: "$exam_ids" },
          pipeline: [
            { $match: { $expr: { $in: ["$_id", "$$exam_ids"] } } },
            { $project: { _id: 1, name: 1 } },
          ],
          as: "examInfo",
        },
      },
      {
        $project: {
          _id: 1,
          product_id: 1,
          product_name: "$productInfo.name",
          total_correct_answers: 1,
          total_questions: 1,
          progress_percentage: 1,
          last_updated: 1,
          best_exam_attempts: 1,
          examInfo: 1,
        },
      },
    ]);

    if (results.length === 0) {
      const product = await Product.findById(productId);
      return {
        product_id: productId,
        product_name: product?.name || "Unknown",
        total_correct_answers: 0,
        total_questions: product?.countQuestion || 0,
        progress_percentage: 0,
        best_exam_attempts: [],
      };
    }

    // Kết hợp thông tin đề thi với kết quả
    const result = results[0];
    const examMap: { [key: string]: string } = {};

    result.examInfo.forEach((exam: any) => {
      examMap[exam._id.toString()] = exam.name;
    });

    // Thêm tên đề thi vào kết quả
    result.best_exam_attempts = result.best_exam_attempts.map(
      (attempt: any) => ({
        ...attempt,
        exam_name: examMap[attempt.exam_id.toString()] || "Unknown",
      })
    );

    delete result.examInfo;
    delete result.exam_ids;

    return result;
  } catch (error) {
    console.error("Lỗi khi lấy tiến độ:", error);
    return null;
  }
}

/**
 * Lấy tiến độ tất cả môn học của người dùng
 */
export async function getAllUserProgress(userId: string) {
  try {
    // Sử dụng aggregation để tối ưu hiệu suất truy vấn
    return await UserProgress.aggregate([
      { $match: { user_id: new Types.ObjectId(userId) } },
      {
        $lookup: {
          from: "products",
          localField: "product_id",
          foreignField: "_id",
          as: "productInfo",
        },
      },
      { $unwind: "$productInfo" },
      {
        $project: {
          product_id: 1,
          product_name: "$productInfo.name",
          product_image: "$productInfo.image",
          total_correct_answers: 1,
          total_questions: 1,
          progress_percentage: 1,
          completed_exams: { $size: "$best_exam_attempts" },
          last_updated: 1,
        },
      },
      { $sort: { progress_percentage: -1 } },
    ]);
  } catch (error) {
    console.error("Lỗi khi lấy tất cả tiến độ:", error);
    return [];
  }
}

/**
 * Lấy tiến độ học tập của người dùng cho nhiều khóa học cùng lúc
 */
export default async function getUserProgressByProductIds(
  userId: string,
  productIds: string[]
) {
  try {
    // Chuyển đổi danh sách ID thành ObjectId
    const objectIds = productIds.map((id) => new Types.ObjectId(id));

    // Kiểm tra xem có bản ghi UserProgress nào cho người dùng này không
    const existingProgress = await UserProgress.find({
      user_id: new Types.ObjectId(userId),
    }).lean();

    if (existingProgress.length > 0) {
      return existingProgress;
    }

    // Sử dụng aggregation để tối ưu hiệu suất truy vấn
    const results = await UserProgress.aggregate([
      {
        $match: {
          user_id: new Types.ObjectId(userId),
          product_id: { $in: objectIds },
        },
      },
      {
        $lookup: {
          from: "products",
          localField: "product_id",
          foreignField: "_id",
          as: "productInfo",
        },
      },
      { $unwind: "$productInfo" },
      {
        $project: {
          product_id: 1,
          product_name: "$productInfo.name",
          product_image: "$productInfo.image",
          total_correct_answers: 1,
          total_questions: 1,
          progress_percentage: 1,
          completed_exams: { $size: "$best_exam_attempts" },
          last_updated: 1,
        },
      },
    ]);

    return results;
  } catch (error) {
    return [];
  }
}

/**
 * Cập nhật lại tiến độ cho một người dùng
 */
export async function recalculateUserProgress(userId: string) {
  try {
    // Lấy tất cả sản phẩm (môn học)
    const products = await Product.find({});

    for (const product of products) {
      // Lấy tất cả đề thi của môn học
      const exams = await Exam.find({ productId: product._id });

      if (exams.length === 0) continue;

      // Sử dụng aggregation để lấy điểm cao nhất cho mỗi đề thi trong một lần truy vấn
      const bestHistories = await ExamHistory.aggregate([
        {
          $match: {
            userId: new Types.ObjectId(userId),
            examId: { $in: exams.map((exam) => exam._id) },
          },
        },
        {
          $sort: { score: -1 },
        },
        {
          $group: {
            _id: "$examId",
            score: { $first: "$score" },
            totalQuestions: { $first: "$totalQuestions" },
            completedAt: { $first: "$completedAt" },
          },
        },
      ]);

      // Tính tổng số câu đúng và tạo danh sách điểm cao nhất
      let totalCorrectAnswers = 0;
      const bestAttempts = [];

      for (const history of bestHistories) {
        // Tính số câu đúng
        const correctAnswers = Math.round(
          (history.score / 100) * history.totalQuestions
        );
        totalCorrectAnswers += correctAnswers;

        bestAttempts.push({
          exam_id: history._id,
          score: history.score,
          correct_answers: correctAnswers,
          total_questions: history.totalQuestions,
          completed_at: history.completedAt,
        });
      }

      // Tính phần trăm tiến độ
      const progressPercentage = Math.round(
        (totalCorrectAnswers / product.countQuestion) * 100
      );

      // Cập nhật hoặc tạo mới tiến độ
      await UserProgress.findOneAndUpdate(
        { user_id: userId, product_id: product._id },
        {
          user_id: userId,
          product_id: product._id,
          best_exam_attempts: bestAttempts,
          total_correct_answers: totalCorrectAnswers,
          total_questions: product.countQuestion,
          progress_percentage: progressPercentage,
          last_updated: new Date(),
        },
        { upsert: true, new: true }
      );
    }

    return true;
  } catch (error) {
    console.error("Lỗi khi cập nhật lại tiến độ:", error);
    return false;
  }
}

/**
 * Cập nhật lại tất cả tiến độ (chạy định kỳ)
 */
export async function recalculateAllProgress() {
  try {
    // Import User model dynamically to avoid circular dependency
    const User = require("../models/User").default;
    const users = await User.find({});

    // Xử lý theo batch để tránh quá tải
    const batchSize = 10;
    for (let i = 0; i < users.length; i += batchSize) {
      const batch = users.slice(i, i + batchSize);
      await Promise.all(
        batch.map((user: any) => recalculateUserProgress(user._id))
      );
    }

    return true;
  } catch (error) {
    console.error("Lỗi khi cập nhật lại tất cả tiến độ:", error);
    return false;
  }
}
