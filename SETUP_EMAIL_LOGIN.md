# Tính năng Đăng nhập Email/Password

## Tóm tắt

Đã thêm thành công tính năng đăng nhập bằng email và mật khẩu vào hệ thống, bao gồm:

- Đăng ký tài khoản mới
- Đăng nhập bằng email/password
- Quên mật khẩu với gửi email
- Đặt lại mật khẩu qua link email

## Thiết lập

### 1. C<PERSON>u hình Email

Cập nhật file `.env` với thông tin email:

```env
# Email Configuration for Forgot Password
MAIL=<EMAIL>
PASSWORD=your_app_password
```

**Lưu ý:**

- <PERSON><PERSON>i với <PERSON>mail, bạn cần tạo "App Password" thay vì sử dụng mật khẩu thường
- Truy cập: [Google Account Settings](https://myaccount.google.com/apppasswords)
- B<PERSON><PERSON> x<PERSON><PERSON> thực 2 bước và tạo app password cho ứng dụng

### 2. Cài đặt Dependencies

```bash
npm install bcryptjs @types/bcryptjs nodemailer @types/nodemailer
```

## Các Routes mới

### API Routes (`/auth/*`)

- `POST /auth/register` - Đăng ký tài khoản mới
- `POST /auth/login` - Đăng nhập bằng email/password
- `POST /auth/forgot-password` - Gửi email quên mật khẩu
- `POST /auth/reset-password` - Đặt lại mật khẩu

### Page Routes

- `GET /register` - Trang đăng ký
- `GET /forgot-password` - Trang quên mật khẩu
- `GET /reset-password?token=xxx` - Trang đặt lại mật khẩu

## Cấu trúc Database

### User Model - Cập nhật

```typescript
export interface IUser extends Document {
  googleId?: string; // Không bắt buộc (cho phép đăng ký email)
  email: string; // Required, unique
  displayName: string; // Required
  firstName?: string;
  lastName?: string;
  profilePhoto?: string;
  password?: string; // Mới thêm, được hash tự động
  activeToken: string | null;
  lastActiveDevice?: string;
  createdAt: Date;
  comparePassword(candidatePassword: string): Promise<boolean>;
}
```

### ResetToken Model - Mới

```typescript
export interface IResetToken extends Document {
  email: string;
  token: string;
  expiresAt: Date;
  used: boolean;
  createdAt: Date;
}
```

## Tính năng

### 1. Đăng ký tài khoản

- Yêu cầu: email, password (≥6 ký tự), displayName
- Kiểm tra email đã tồn tại
- Tạo avatar tự động từ ui-avatars.com
- Gửi email chào mừng
- Mã hóa password với bcrypt (salt=12)

### 2. Đăng nhập Email/Password

- Xác thực email và password
- Áp dụng logic single device login giống Google OAuth
- Tạo JWT token và session
- Đăng xuất các thiết bị khác nếu cần

### 3. Quên mật khẩu

- Nhập email để yêu cầu reset
- Tạo token ngẫu nhiên 32 bytes
- Token có hiệu lực 1 giờ
- Gửi email với link reset password
- Không tiết lộ thông tin email có tồn tại hay không

### 4. Đặt lại mật khẩu

- Xác thực token hợp lệ và chưa hết hạn
- Đặt mật khẩu mới (≥6 ký tự)
- Đánh dấu token đã sử dụng
- Đăng xuất tất cả phiên hiện tại

## Bảo mật

### Password

- Mã hóa bằng bcrypt với salt round = 12
- Yêu cầu tối thiểu 6 ký tự
- So sánh an toàn với `bcrypt.compare()`

### Reset Token

- Sử dụng `crypto.randomBytes(32)` để tạo token ngẫu nhiên
- Token tự động hết hạn sau 1 giờ
- Tự động xóa khỏi database khi hết hạn
- Chỉ sử dụng 1 lần (marked as used)

### Session Management

- Giữ nguyên logic single device login
- Tích hợp với hệ thống SSE để thông báo logout
- Session tracking với device info

## UI/UX

### Trang đăng nhập cập nhật

- Form email/password
- Liên kết đến đăng ký và quên mật khẩu
- Vẫn giữ tùy chọn đăng nhập Google
- Loading states và error handling

### Trang đăng ký mới

- Form: tên hiển thị, email, password, confirm password
- Checkbox đồng ý điều khoản
- Tích hợp Google OAuth
- Avatar tự động từ email

### Trang quên mật khẩu

- Form nhập email đơn giản
- Thông báo rõ ràng về việc gửi email
- Links điều hướng về login/register

### Trang đặt lại mật khẩu

- Form mật khẩu mới và xác nhận
- Hiển thị yêu cầu password
- Xác thực token và redirect

## Testing

### Test đăng ký

1. Truy cập `/register`
2. Điền thông tin: tên, email, password
3. Kiểm tra email chào mừng được gửi
4. Verify tài khoản được tạo trong database

### Test đăng nhập

1. Truy cập `/login`
2. Nhập email/password đã đăng ký
3. Verify redirect về `/home`
4. Kiểm tra session được tạo

### Test quên mật khẩu

1. Truy cập `/forgot-password`
2. Nhập email đã đăng ký
3. Kiểm tra email reset được gửi
4. Click link trong email
5. Đặt mật khẩu mới
6. Verify đăng nhập với mật khẩu mới

## Lưu ý quan trọng

1. **Email Configuration**: Phải cấu hình đúng MAIL và PASSWORD trong .env
2. **HTTPS**: Trong production nên sử dụng HTTPS cho bảo mật
3. **Rate Limiting**: Nên thêm rate limiting cho forgot password
4. **Validation**: Frontend đã có validation, backend cũng validate
5. **Compatibility**: Tương thích hoàn toàn với Google OAuth hiện tại

## Troubleshooting

### Email không gửi được

- Kiểm tra MAIL và PASSWORD trong .env
- Đảm bảo đã bật App Password cho Gmail
- Kiểm tra network và firewall

### Password không hash

- Kiểm tra bcryptjs đã được cài đặt
- Verify middleware pre('save') trong User model

### Token hết hạn

- Token reset password có hiệu lực 1 giờ
- Yêu cầu reset mới nếu hết hạn

### Session conflicts

- Hệ thống áp dụng single device login
- Thiết bị mới sẽ đăng xuất thiết bị cũ
