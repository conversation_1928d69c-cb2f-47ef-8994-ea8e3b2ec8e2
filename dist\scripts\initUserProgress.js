"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importDefault(require("mongoose"));
const dotenv_1 = __importDefault(require("dotenv"));
const userProgress_service_1 = require("../services/userProgress.service");
// Load environment variables
dotenv_1.default.config();
// Connect to MongoDB
mongoose_1.default
    .connect(process.env.MONGODB_URI || "mongodb://localhost:27017/google-auth")
    .then(() => __awaiter(void 0, void 0, void 0, function* () {
    console.log("📚 Kết nối MongoDB thành công");
    console.log("🔄 Bắt đầu khởi tạo dữ liệu tiến độ học tập...");
    try {
        // Import các model cần thiết
        const User = require("../models/User").default;
        const Product = require("../models/products").default;
        const Exam = require("../models/exam").default;
        const ExamHistory = require("../models/ExamHistory").default;
        const UserProgress = require("../models/UserProgress").default;
        // Lấy danh sách người dùng
        const users = yield User.find({});
        console.log(`Tìm thấy ${users.length} người dùng`);
        // Lấy danh sách sản phẩm (môn học)
        const products = yield Product.find({});
        console.log(`Tìm thấy ${products.length} môn học`);
        // Xóa toàn bộ dữ liệu UserProgress hiện có
        yield UserProgress.deleteMany({});
        console.log("Đã xóa toàn bộ dữ liệu UserProgress hiện có");
        // Tính toán lại tiến độ học tập cho tất cả người dùng
        const result = yield (0, userProgress_service_1.recalculateAllProgress)();
        if (result) {
            console.log("✅ Khởi tạo dữ liệu tiến độ học tập thành công!");
        }
        else {
            console.error("❌ Khởi tạo dữ liệu tiến độ học tập thất bại!");
        }
        // Kiểm tra kết quả
        const progressCount = yield UserProgress.countDocuments();
        console.log(`Số bản ghi UserProgress sau khi khởi tạo: ${progressCount}`);
        // Hiển thị một số bản ghi mẫu
        const sampleProgress = yield UserProgress.find().limit(5).lean();
        console.log("Mẫu dữ liệu UserProgress:", JSON.stringify(sampleProgress, null, 2));
    }
    catch (error) {
        console.error("❌ Lỗi khi khởi tạo dữ liệu tiến độ học tập:", error);
    }
    // Đóng kết nối
    yield mongoose_1.default.connection.close();
    console.log("📚 Đã đóng kết nối MongoDB");
    process.exit(0);
}))
    .catch((err) => {
    console.error("❌ Lỗi kết nối MongoDB:", err);
    process.exit(1);
});
//# sourceMappingURL=initUserProgress.js.map