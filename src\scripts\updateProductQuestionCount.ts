import mongoose from "mongoose";
import dotenv from "dotenv";
import Product from "../models/products";
import Exam from "../models/exam";

// Load environment variables
dotenv.config();

/**
 * Kết nối đến cơ sở dữ liệu MongoDB
 */
async function connectToDatabase() {
  try {
    const mongoUri =
      process.env.MONGODB_URI || "mongodb://localhost:27017/google-auth";
    await mongoose.connect(mongoUri);
    console.log("Đã kết nối thành công đến MongoDB");
  } catch (error) {
    console.error("Lỗi kết nối đến MongoDB:", error);
    process.exit(1);
  }
}

/**
 * Cập nhật số lượng câu hỏi cho mỗi môn học
 */
async function updateProductQuestionCounts() {
  try {
    // Lấy tất cả các môn học
    const products = await Product.find({});
    console.log(`<PERSON><PERSON><PERSON> thấy ${products.length} môn học cần cập nhật`);

    // Xử lý từng môn học
    for (const product of products) {
      // Lấy tất cả các đề thi thuộc môn học này
      const exams = await Exam.find({ productId: product._id });

      // Tính tổng số câu hỏi từ tất cả các đề thi
      const totalQuestionCount = exams.reduce(
        (total, exam) => total + (exam.count || 0),
        0
      );

      // Cập nhật trường countQuestion của môn học
      await Product.updateOne(
        { _id: product._id },
        { $set: { countQuestion: totalQuestionCount } }
      );

      console.log(
        `Môn học "${product.name}" (ID: ${product._id}) - Tổng số câu hỏi: ${totalQuestionCount} (từ ${exams.length} đề thi)`
      );
    }

    console.log("Đã cập nhật tổng số câu hỏi cho tất cả môn học thành công!");
  } catch (error) {
    console.error("Lỗi khi cập nhật số lượng câu hỏi cho môn học:", error);
  } finally {
    // Ngắt kết nối cơ sở dữ liệu sau khi hoàn thành
    await mongoose.disconnect();
    console.log("Đã ngắt kết nối khỏi MongoDB");
  }
}

/**
 * Hàm chính để chạy script
 */
async function main() {
  await connectToDatabase();
  await updateProductQuestionCounts();
}

// Chạy script
main().catch((error) => {
  console.error("Lỗi không mong muốn:", error);
  process.exit(1);
});
