<div class="max-w-md mx-auto">
  <div class="bg-white rounded-lg shadow-md p-8">
    <div class="mb-6 text-center">
      <i class="fas fa-lock text-5xl text-indigo-500 mb-4"></i>
      <h1 class="text-2xl font-bold text-gray-800">Đ<PERSON>ng nhập</h1>
      <p class="text-gray-600 mt-2">
        Vui lòng đăng nhập để truy cập vào hệ thống
      </p>
    </div>

    <% if (error) { %>
    <div class="bg-red-50 text-red-600 p-4 rounded-md mb-6">
      <i class="fas fa-exclamation-circle mr-2"></i>
      <%= error %>
    </div>
    <% } %>

    <!-- Login Form -->
    <form id="login-form" class="space-y-4 hidden">
      <div>
        <label for="email" class="block text-sm font-medium text-gray-700 mb-2"
          >Email</label
        >
        <input
          type="email"
          id="email"
          name="email"
          required
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
          placeholder="Nhập email của bạn"
        />
      </div>

      <div>
        <label
          for="password"
          class="block text-sm font-medium text-gray-700 mb-2"
          >Mật khẩu</label
        >
        <input
          type="password"
          id="password"
          name="password"
          required
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
          placeholder="Nhập mật khẩu"
        />
      </div>

      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <input
            id="remember-me"
            name="remember-me"
            type="checkbox"
            class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
          />
          <label for="remember-me" class="ml-2 block text-sm text-gray-700">
            Ghi nhớ đăng nhập
          </label>
        </div>

        <div class="text-sm">
          <a
            href="#"
            id="forgot-password-link"
            class="font-medium text-indigo-600 hover:text-indigo-500"
          >
            Quên mật khẩu?
          </a>
        </div>
      </div>

      <button
        type="submit"
        class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors"
      >
        <span id="login-text">Đăng nhập</span>
        <span id="login-spinner" class="hidden">
          <i class="fas fa-spinner fa-spin mr-2"></i>Đang đăng nhập...
        </span>
      </button>
    </form>

    <div class="mt-6">
      <div class="relative hidden">
        <div class="absolute inset-0 flex items-center">
          <div class="w-full border-t border-gray-300"></div>
        </div>
        <div class="relative flex justify-center text-sm">
          <span class="px-2 bg-white text-gray-500">Hoặc</span>
        </div>
      </div>

      <div class="mt-6">
        <a
          href="#"
          id="google-login-btn"
          class="w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 transition-colors"
        >
          <img
            src="https://developers.google.com/identity/images/g-logo.png"
            alt="Google"
            class="w-5 h-5 mr-3"
          />
          Đăng nhập bằng Google (Ưu tiên)
        </a>
      </div>
    </div>

    <div class="mt-6 text-center hidden">
      <p class="text-sm text-gray-600">
        Chưa có tài khoản?
        <a
          href="#"
          id="register-link"
          class="font-medium text-indigo-600 hover:text-indigo-500"
        >
          Đăng ký ngay
        </a>
      </p>
    </div>

    <div class="mt-6 text-sm text-gray-500">
      <p>
        Bằng việc đăng nhập, bạn đồng ý với các
        <a href="#" class="text-indigo-600 hover:underline"
          >Điều khoản dịch vụ</a
        >
        và
        <a href="#" class="text-indigo-600 hover:underline"
          >Chính sách bảo mật</a
        >.
      </p>
    </div>
  </div>

  <div class="text-center mt-4">
    <a href="/" class="text-indigo-600 hover:underline">
      <i class="fas fa-arrow-left mr-1"></i> Quay lại trang chủ
    </a>
  </div>
</div>

<script>
  document.addEventListener("DOMContentLoaded", function () {
    const loginForm = document.getElementById("login-form");
    const googleLoginBtn = document.getElementById("google-login-btn");
    const registerLink = document.getElementById("register-link");
    const forgotPasswordLink = document.getElementById("forgot-password-link");
    const loginText = document.getElementById("login-text");
    const loginSpinner = document.getElementById("login-spinner");

    // Email/Password Login
    loginForm.addEventListener("submit", async function (e) {
      e.preventDefault();

      const email = document.getElementById("email").value;
      const password = document.getElementById("password").value;

      // Show loading
      loginText.classList.add("hidden");
      loginSpinner.classList.remove("hidden");

      try {
        const response = await fetch("/auth/login", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ email, password }),
        });

        const data = await response.json();

        if (data.success) {
          // Lưu thông tin vào localStorage
          localStorage.setItem("auth_status", "logged_in");
          localStorage.setItem("user_id", data.user.id);
          localStorage.setItem("clientId", data.clientId);
          localStorage.setItem("session_id", data.sessionId);

          // Chuyển hướng đến trang home
          window.location.href = `/home?clientId=${data.clientId}`;
        } else {
          // Hiển thị lỗi
          showAlert(data.message, "error");
        }
      } catch (error) {
        console.error("Login error:", error);
        showAlert("Lỗi kết nối. Vui lòng thử lại.", "error");
      } finally {
        // Hide loading
        loginText.classList.remove("hidden");
        loginSpinner.classList.add("hidden");
      }
    });

    // Google Login
    googleLoginBtn.addEventListener("click", function (e) {
      e.preventDefault();

      // Lấy clientId từ localStorage nếu có, hoặc tạo mới
      let clientId = localStorage.getItem("clientId");
      if (!clientId) {
        clientId =
          "client_" +
          Date.now() +
          "_" +
          Math.random().toString(36).substring(2, 10);
        localStorage.setItem("clientId", clientId);
        console.log("Generated new clientId:", clientId);
      } else {
        console.log("Using existing clientId:", clientId);
      }

      // Chuyển hướng đến URL đăng nhập Google với clientId
      window.location.href = `/auth/google?clientId=${clientId}`;
    });

    // Register Link
    registerLink.addEventListener("click", function (e) {
      e.preventDefault();
      window.location.href = "/register";
    });

    // Forgot Password Link
    forgotPasswordLink.addEventListener("click", function (e) {
      e.preventDefault();
      window.location.href = "/forgot-password";
    });

    // Utility function to show alerts
    function showAlert(message, type) {
      // Remove existing alerts
      const existingAlert = document.querySelector(".alert");
      if (existingAlert) {
        existingAlert.remove();
      }

      const alertClass =
        type === "error"
          ? "bg-red-50 text-red-600"
          : "bg-green-50 text-green-600";
      const iconClass =
        type === "error" ? "fa-exclamation-circle" : "fa-check-circle";

      const alert = document.createElement("div");
      alert.className = `alert ${alertClass} p-4 rounded-md mb-6`;
      alert.innerHTML = `
        <i class="fas ${iconClass} mr-2"></i>
        ${message}
      `;

      // Insert alert after the title
      const title = document.querySelector(".mb-6");
      title.parentNode.insertBefore(alert, title.nextSibling);

      // Auto remove after 5 seconds
      setTimeout(() => {
        if (alert.parentNode) {
          alert.remove();
        }
      }, 5000);
    }
  });
</script>
