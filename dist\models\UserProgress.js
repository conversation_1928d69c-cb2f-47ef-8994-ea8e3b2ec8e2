"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = require("mongoose");
const userProgressSchema = new mongoose_1.Schema({
    user_id: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: "User",
        required: true,
    },
    product_id: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: "Product",
        required: true,
    },
    best_exam_attempts: [
        {
            exam_id: {
                type: mongoose_1.Schema.Types.ObjectId,
                ref: "Exam",
            },
            score: {
                type: Number,
                default: 0,
            },
            correct_answers: {
                type: Number,
                default: 0,
            },
            total_questions: {
                type: Number,
                default: 0,
            },
            completed_at: {
                type: Date,
                default: Date.now,
            },
        },
    ],
    total_correct_answers: {
        type: Number,
        default: 0,
    },
    total_questions: {
        type: Number,
        default: 0,
    },
    progress_percentage: {
        type: Number,
        default: 0,
    },
    last_updated: {
        type: Date,
        default: Date.now,
    },
});
// Index cho truy vấn nhanh
userProgressSchema.index({ user_id: 1, product_id: 1 }, { unique: true });
const UserProgress = (0, mongoose_1.model)("UserProgress", userProgressSchema);
exports.default = UserProgress;
//# sourceMappingURL=UserProgress.js.map