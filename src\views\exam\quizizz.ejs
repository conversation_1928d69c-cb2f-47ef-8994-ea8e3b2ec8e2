<div class="mx-auto mb-10 px-2 sm:px-4 max-w-4xl" id="quizizzContainer">
  <div class="rounded-lg shadow-lg overflow-hidden">
    <!-- Header -->
    <div
      class="px-3 sm:px-6 py-3 sm:py-4 flex flex-col sm:flex-row justify-between sm:items-center"
    >
      <div class="flex items-center mb-2 sm:mb-0">
        <i class="fas fa-gamepad mr-2 text-white text-xl sm:text-2xl"></i>
        <h1 class="text-lg sm:text-xl font-bold text-white">
          Bài thi: <%= exam.name %>
        </h1>
      </div>
      <div
        class="text-white flex items-center justify-between sm:justify-end w-full sm:w-auto"
      >
        <div class="flex items-center mr-2">
          <label
            for="transitionTime"
            class="text-white text-xs sm:text-sm mr-1 hidden sm:inline"
            >Chuyển câu:</label
          >
          <select
            id="transitionTime"
            class="text-white text-xs sm:text-sm rounded px-1 py-0.5 border"
          >
            <option value="1000">1 giây</option>
            <option value="2000">2 giây</option>
            <option value="3000" selected>3 giây</option>
            <option value="4000">4 giây</option>
            <option value="5000">5 giây</option>
          </select>
        </div>

        <!-- Thêm tùy chọn bật/tắt âm thanh -->
        <div class="flex items-center mr-4">
          <button
            id="soundToggleBtn"
            class="text-white p-1 rounded focus:outline-none"
            title="Bật/tắt âm thanh"
          >
            <i id="soundIcon" class="fas fa-volume-up text-lg"></i>
          </button>
        </div>

        <!-- Thêm tùy chọn chuyển đổi giao diện -->
        <div class="flex items-center mr-4">
          <button
            id="themeToggleBtn"
            class="text-white p-1 rounded focus:outline-none"
            title="Đổi giao diện"
          >
            <i id="themeIcon" class="fas fa-palette text-lg"></i>
          </button>
        </div>

        <div
          id="examTimer"
          class="font-medium mr-4 px-3 py-1 rounded text-sm sm:text-base"
        >
          <i class="fas fa-clock mr-1"></i>
          <span id="timerDisplay">00:00:00</span>
        </div>
        <span class="font-medium text-sm sm:text-base" id="questionCounter"
          >Câu hỏi 1/<%= typeof questions !== 'undefined' ? questions.length :
          '?' %></span
        >
      </div>
    </div>

    <!-- Thông tin bài thi -->
    <div class="px-4 sm:px-6 py-2 sm:py-3 border-b">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <p class="text-sm sm:text-base">
            <span class="font-medium">Tổng số câu hỏi:</span> <%= typeof
            questions !== 'undefined' ? questions.length : '--' %>
          </p>
        </div>
        <div class="text-xs sm:text-sm mt-1 sm:mt-0">
          <p>Sinh viên: <%= student.email %></p>
        </div>
      </div>
    </div>

    <!-- Tiến độ -->
    <div class="h-2">
      <div
        id="progressBar"
        class="h-2 transition-all duration-300"
        style="width: 0%"
      ></div>
    </div>

    <!-- Loading indicator for encrypted questions -->
    <% if (typeof encryptedExamConfig !== 'undefined' && encryptedExamConfig) {
    %>
    <div id="loadingIndicator" class="p-6 text-center">
      <div class="flex items-center justify-center space-x-3 mb-4">
        <i class="fas fa-cog fa-spin text-purple-500 text-2xl"></i>
        <span class="text-gray-700 font-medium"
          >Đang tải cấu hình ứng dụng...</span
        >
      </div>
      <div class="bg-gray-200 rounded-full h-2 max-w-md mx-auto">
        <div
          id="loadingProgress"
          class="bg-purple-500 h-2 rounded-full transition-all duration-300"
          style="width: 0%"
        ></div>
      </div>
    </div>
    <% } %>

    <!-- Khu vực hiển thị câu hỏi -->
    <div
      id="questionContainer"
      class="quiz-question-container p-3 sm:p-6 quiz-transition <% if (typeof encryptedExamConfig !== 'undefined' && encryptedExamConfig) { %>hidden<% } %>"
    >
      <!-- Câu hỏi sẽ được hiển thị bằng JavaScript -->
    </div>

    <!-- Nút nộp bài -->
    <div class="text-center mb-4">
      <button
        id="submitExamButton"
        class="quiz-button py-2 px-6 rounded-full text-white font-medium focus:outline-none transition-all duration-300 text-sm sm:text-base quiz-transition hover:quiz-pulse"
      >
        Nộp bài
      </button>
    </div>
  </div>
</div>

<!-- Bao gồm các template từ partials -->
<%- include('../partials/quizizz/question-template') %> <%-
include('../partials/quizizz/result-template') %> <%-
include('../partials/quizizz/exit-popup') %>

<!-- Thời gian làm bài -->
<span id="exam-duration" class="hidden">
  <%= typeof exam.duration !== "undefined" ? exam.duration : 30 %>
</span>

<!-- Dữ liệu câu hỏi -->
<% if (typeof encryptedExamConfig !== 'undefined' && encryptedExamConfig) { %>
<script type="application/json" id="encrypted-exam-config">
  <%- JSON.stringify(encryptedExamConfig) %>
</script>
<% } else { %>
<script type="application/json" id="exam-data">
  <%- JSON.stringify({
    examId: exam._id,
    questions: questions
  }) %>
</script>
<% } %>

<!-- Thêm CSS -->
<link rel="stylesheet" href="/css/quizizz-styles.css" />

<!-- Load encryption utilities for Quizizz -->
<script src="/js/client-encryption.js"></script>

<!-- Quizizz localStorage handling -->
<script>
  // const pathPractice = window.location.pathname.includes(
  //   "/practice/quizizz-start"
  // );
  // if (pathPractice) {
  //   console.log("🚀 pathPractice", pathPractice);
  //   document.getElementById("retryWrongAnswers").style.display = "none";
  // }
  // Biến toàn cục cho Quizizz localStorage
  let quizizzLocalStorageChecked = false;

  // Sử dụng window.examData để tránh xung đột với quizizz-core.js
  window.examData = window.examData || null;

  // Extract exam ID từ các nguồn khác nhau
  function getQuizizzExamId() {
    // Thử lấy từ URL path
    const pathParts = window.location.pathname.split("/");
    const urlExamId = pathParts[2]; // /exam/{examId}/quizizz

    if (urlExamId) {
      return urlExamId;
    }

    console.error("❌ Không thể xác định examId cho quizizz!");
    return null;
  }

  const CURRENT_QUIZIZZ_EXAM_ID = getQuizizzExamId();
  const QUIZIZZ_STORAGE_KEY = `quizizzExam_${CURRENT_QUIZIZZ_EXAM_ID}`;

  // Validate exam ID trước khi sử dụng
  if (!CURRENT_QUIZIZZ_EXAM_ID) {
    console.error(
      "❌ CRITICAL: Không thể xác định examId cho quizizz - localStorage sẽ không hoạt động!"
    );
  }

  // LocalStorage functions cho Quizizz
  function saveQuizizzDataToStorage() {
    if (!CURRENT_QUIZIZZ_EXAM_ID) {
      console.error(
        "❌ Không thể lưu quizizz localStorage: examId không hợp lệ"
      );
      return;
    }

    try {
      // Lấy current state từ quizizz global variables
      const currentState = getCurrentQuizizzState();

      const quizizzData = {
        examId: CURRENT_QUIZIZZ_EXAM_ID,
        currentQuestionIndex: currentState.currentQuestionIndex || 0,
        userAnswers: currentState.userAnswers || [],
        score: currentState.score || 0,
        startTime: currentState.startTime || Date.now(),
        timeRemaining:
          currentState.timeRemaining ||
          (window.examData?.duration ? window.examData.duration * 60 : 30 * 60),
        timestamp: Date.now(),
        // Lưu toàn bộ dữ liệu câu hỏi để preserve order
        questionsData: window.examData?.questions || [],
        examDuration: window.examData?.duration || 30,
        totalQuestions: window.examData?.questions?.length || 0,
      };

      // Encrypt data nếu có encryption function
      if (typeof encryptStringOptimized === "function") {
        const encryptedData = encryptStringOptimized(
          JSON.stringify(quizizzData)
        );
        localStorage.setItem(
          QUIZIZZ_STORAGE_KEY,
          JSON.stringify(encryptedData)
        );
      } else {
        localStorage.setItem(QUIZIZZ_STORAGE_KEY, JSON.stringify(quizizzData));
      }
    } catch (error) {
      console.error("❌ Lỗi lưu dữ liệu Quizizz");
    }
  }

  function loadQuizizzDataFromStorage() {
    if (!CURRENT_QUIZIZZ_EXAM_ID) {
      console.error(
        "❌ Không thể load quizizz localStorage: examId không hợp lệ"
      );
      return null;
    }

    try {
      const storedData = localStorage.getItem(QUIZIZZ_STORAGE_KEY);

      if (!storedData) {
        console.log("📁 Không có dữ liệu");
        return null;
      }

      let quizizzData;

      // Thử decrypt nếu là encrypted data
      try {
        const encryptedObj = JSON.parse(storedData);
        if (
          encryptedObj.encryptedData &&
          typeof decryptStringOptimized === "function"
        ) {
          const decryptedText = decryptStringOptimized(
            encryptedObj.encryptedData,
            encryptedObj.token,
            encryptedObj.salt
          );
          quizizzData = JSON.parse(decryptedText);
        } else {
          // Fallback to plain text
          quizizzData = JSON.parse(storedData);
        }
      } catch (e) {
        // If decryption fails, try plain text
        quizizzData = JSON.parse(storedData);
      }

      // Validate data và check expiration
      if (quizizzData && quizizzData.timestamp) {
        const elapsed = Date.now() - quizizzData.timestamp;
        const maxAge = 60 * 60 * 1000; // 60 minutes

        if (
          elapsed < maxAge &&
          quizizzData.examId === CURRENT_QUIZIZZ_EXAM_ID &&
          quizizzData.timeRemaining > 0
        ) {
          return quizizzData;
        } else {
          clearQuizizzDataFromStorage();
          return null;
        }
      }

      return quizizzData;
    } catch (error) {
      clearQuizizzDataFromStorage();
      return null;
    }
  }

  function clearQuizizzDataFromStorage() {
    if (!CURRENT_QUIZIZZ_EXAM_ID) {
      console.warn(
        "⚠️ Không thể clear quizizz localStorage: examId không hợp lệ"
      );
      return;
    }

    try {
      localStorage.removeItem(QUIZIZZ_STORAGE_KEY);
    } catch (error) {
      console.error("❌ Lỗi xóa dữ liệu Quizizz");
    }
  }

  // Helper function để lấy current state từ quizizz global variables
  function getCurrentQuizizzState() {
    const state = {
      currentQuestionIndex:
        typeof window.currentQuestionIndex !== "undefined"
          ? window.currentQuestionIndex
          : 0,
      userAnswers:
        typeof window.userAnswers !== "undefined" ? window.userAnswers : [],
      score: typeof window.score !== "undefined" ? window.score : 0,
      startTime:
        typeof window.startTime !== "undefined" ? window.startTime : Date.now(),
      timeRemaining:
        typeof window.remainingTime !== "undefined"
          ? window.remainingTime
          : 30 * 60,
    };

    return state;
  }

  // Expose important functions to window scope for access from other JS files
  window.saveQuizizzDataToStorage = saveQuizizzDataToStorage;
  window.loadQuizizzDataFromStorage = loadQuizizzDataFromStorage;
  window.clearQuizizzDataFromStorage = clearQuizizzDataFromStorage;

  // Function để check localStorage trước khi render
  function checkForExistingQuizizzData() {
    if (quizizzLocalStorageChecked) {
      console.log("⚠️ Quizizz localStorage đã được kiểm tra rồi, bỏ qua");
      return false;
    }

    // *** CLEAR OLD EXAM DATA FIRST ***
    if (typeof clearAllExamDataFromStorage === "function") {
      const currentExamInfo = {
        type: "quizizz",
        examId: CURRENT_QUIZIZZ_EXAM_ID,
      };
      const cleanupResult = clearAllExamDataFromStorage(currentExamInfo);
    } else {
      console.warn("⚠️ Function clearAllExamDataFromStorage không tìm thấy");
    }

    const savedData = loadQuizizzDataFromStorage();

    if (savedData && savedData.timeRemaining > 0) {
      // Ẩn loading và prevent server rendering
      const loadingIndicator = document.getElementById("loadingIndicator");
      const questionContainer = document.getElementById("questionContainer");

      if (loadingIndicator) loadingIndicator.style.display = "none";
      if (questionContainer) questionContainer.classList.add("hidden");

      // Store saved data globally
      window.savedQuizizzDataForContinue = savedData;

      // Hiển thị modal cho user lựa chọn
      showContinueQuizizzModal(savedData);
      quizizzLocalStorageChecked = true;
      return true; // Found existing data, prevent server rendering
    } else {
      if (savedData) {
        console.log("⏰ Dữ liệu Quizizz đã hết hạn hoặc hết thời gian");
      } else {
        console.log("📭 Không tìm thấy dữ liệu Quizizz localStorage nào");
      }
      console.log("🚀 Cho phép render từ server và bắt đầu bài thi mới");
      quizizzLocalStorageChecked = true;
      return false; // No existing data, allow server rendering
    }
  }

  // Functions cho modal continue/new
  function showContinueQuizizzModal(savedData) {
    // Tạo modal HTML dynamically
    const modalHTML = `
      <div id="continueQuizizzModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 max-w-md mx-4 shadow-xl">
          <div class="text-center">
            <div class="mb-4">
              <i class="fas fa-gamepad text-purple-500 text-4xl mb-2"></i>
              <h3 class="text-xl font-bold text-gray-800">Tiếp tục bài thi Quizizz?</h3>
            </div>
            
            <div class="bg-gray-50 rounded-lg p-4 mb-6 text-left">
              <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                  <span class="text-gray-600">Tiến độ:</span>
                  <span class="font-medium">Câu ${
                    savedData.currentQuestionIndex + 1
                  }/${savedData.totalQuestions}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">Điểm hiện tại:</span>
                  <span class="font-medium">${savedData.score}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">Thời gian còn lại:</span>
                  <span class="font-medium">${formatTime(
                    savedData.timeRemaining
                  )}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">Lưu lúc:</span>
                  <span class="font-medium">${new Date(
                    savedData.timestamp
                  ).toLocaleTimeString()}</span>
                </div>
              </div>
            </div>

            <p class="text-gray-600 mb-6">
              Bạn có muốn tiếp tục bài thi đã lưu hay bắt đầu bài thi mới?
            </p>
            
            <div class="flex space-x-3">
              <button 
                onclick="continueQuizizzExam()" 
                class="flex-1 bg-purple-500 text-white px-4 py-2 rounded-lg hover:bg-purple-600 transition-colors font-medium">
                <i class="fas fa-play mr-2"></i>Tiếp tục
              </button>
              <button 
                onclick="startNewQuizizzExam()" 
                class="flex-1 bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors font-medium">
                <i class="fas fa-refresh mr-2"></i>Làm mới
              </button>
            </div>
          </div>
        </div>
      </div>
    `;

    // Append modal vào body
    document.body.insertAdjacentHTML("beforeend", modalHTML);
  }

  function closeContinueQuizizzModal() {
    const modal = document.getElementById("continueQuizizzModal");
    if (modal) {
      modal.remove();
    }
  }

  function continueQuizizzExam() {
    closeContinueQuizizzModal();

    const savedData = window.savedQuizizzDataForContinue;

    if (
      savedData &&
      savedData.questionsData &&
      savedData.questionsData.length > 0
    ) {
      // Tạo exam data từ localStorage
      window.examData = {
        examId: savedData.examId,
        questions: savedData.questionsData,
      };

      // Tạo exam-data script element
      const examDataScript = document.createElement("script");
      examDataScript.type = "application/json";
      examDataScript.id = "exam-data";
      examDataScript.textContent = JSON.stringify(window.examData);
      document.head.appendChild(examDataScript);

      // Load quizizz scripts và restore data
      loadQuizizzScriptsAndRestore(savedData);
    } else {
      console.error("❌ Không có question data trong localStorage");
      startNewQuizizzExam();
    }
  }

  function startNewQuizizzExam() {
    console.log("🆕 Chọn bắt đầu bài thi Quizizz mới");
    closeContinueQuizizzModal();

    // Clear old data
    clearQuizizzDataFromStorage();

    // *** CLEAR ANSWERED QUESTIONS MAP ***
    if (window.answeredQuestionsMap) {
      window.answeredQuestionsMap.clear();
    }

    // Tiếp tục với normal flow (render từ server)
    const loadingIndicator = document.getElementById("loadingIndicator");
    const questionContainer = document.getElementById("questionContainer");

    if (loadingIndicator) loadingIndicator.style.display = "block";
    if (questionContainer) questionContainer.classList.remove("hidden");

    // Load scripts normally
    console.log("🚀 Load quizizz scripts cho bài thi mới");

    // Check nếu có encrypted data
    const encryptedConfigElement = document.getElementById(
      "encrypted-exam-config"
    );
    if (encryptedConfigElement) {
      // Process encrypted data
      processEncryptedQuizizzData();
    } else {
      // Load scripts directly
      loadQuizizzScripts();
    }
  }

  function loadQuizizzScriptsAndRestore(savedData) {
    // Load quizizz-core.js first
    const coreScript = document.createElement("script");
    coreScript.src = "/js/quizizz-core.js";

    coreScript.onload = function () {
      // Load quizizz-quiz.js after core is loaded
      const quizScript = document.createElement("script");
      quizScript.src = "/js/quizizz-quiz.js";

      quizScript.onload = function () {
        // Wait for variables to be initialized
        setTimeout(() => {
          // *** RESTORE STATE TRƯỚC KHI KHỞI TẠO ***

          // Restore global variables trước
          window.currentQuestionIndex = savedData.currentQuestionIndex || 0;
          window.userAnswers = savedData.userAnswers || [];
          window.score = savedData.score || 0;
          window.startTime = savedData.startTime || Date.now();
          window.remainingTime = savedData.timeRemaining || 30 * 60;
          window.examDuration = (savedData.examDuration || 30) * 60;
          window.questionsProcessed = savedData.userAnswers
            ? savedData.userAnswers.length
            : 0;

          // Create answered questions map
          window.answeredQuestionsMap = new Map();
          if (window.userAnswers && window.userAnswers.length > 0) {
            window.userAnswers.forEach((answer) => {
              window.answeredQuestionsMap.set(answer.questionIndex, {
                selectedIndex: answer.selectedIndex,
                isCorrect: answer.isCorrect,
              });
            });
          }

          // Copy to local variables that initQuizizz uses
          if (typeof currentQuestionIndex !== "undefined") {
            currentQuestionIndex = window.currentQuestionIndex;
          }
          if (typeof userAnswers !== "undefined") {
            userAnswers = window.userAnswers;
          }
          if (typeof score !== "undefined") {
            score = window.score;
          }
          if (typeof startTime !== "undefined") {
            startTime = window.startTime;
          }
          if (typeof remainingTime !== "undefined") {
            remainingTime = window.remainingTime;
          }
          if (typeof examDuration !== "undefined") {
            examDuration = window.examDuration;
          }
          if (typeof questionsProcessed !== "undefined") {
            questionsProcessed = window.questionsProcessed;
          }

          // Set restore flag
          window.isRestoringFromLocalStorage = true;

          // Initialize Quizizz with restored state
          if (typeof window.initQuizizz === "function") {
            window.initQuizizz();

            // Hiển thị câu hỏi hiện tại sau khi init
            setTimeout(() => {
              // *** TÌM CÂU HỎI TIẾP THEO CHƯA ANSWERED (SAME LOGIC) ***
              let nextUnAnsweredIndex = window.currentQuestionIndex;

              // Tìm câu hỏi đầu tiên chưa được trả lời
              if (
                window.answeredQuestionsMap &&
                window.examData &&
                window.examData.questions
              ) {
                while (
                  nextUnAnsweredIndex < window.examData.questions.length &&
                  window.answeredQuestionsMap.has(nextUnAnsweredIndex)
                ) {
                  nextUnAnsweredIndex++;
                }

                // Nếu tìm thấy câu hỏi chưa answered
                if (nextUnAnsweredIndex < window.examData.questions.length) {
                  window.currentQuestionIndex = nextUnAnsweredIndex;

                  // Sync with local variable
                  if (typeof currentQuestionIndex !== "undefined") {
                    currentQuestionIndex = window.currentQuestionIndex;
                  }
                } else {
                  // Tất cả câu hỏi đã được trả lời - hiển thị kết quả
                  console.log("🏁 All questions answered, showing result");
                  if (typeof showResult === "function") {
                    showResult();
                    hideLoadingAndShowQuiz();
                    return;
                  }
                }
              }

              if (typeof showQuestion === "function") {
                showQuestion(window.currentQuestionIndex);
              }

              if (typeof updateTimer === "function") {
                if (window.timerInterval) {
                  clearInterval(window.timerInterval);
                }
                window.timerInterval = setInterval(updateTimer, 1000);
                updateTimer();
              }

              if (window.examData && window.examData.questions) {
                const totalQuestions = window.examData.questions.length;
                const currentProgress =
                  ((window.currentQuestionIndex + 1) / totalQuestions) * 100;

                const progressBar = document.getElementById("progressBar");
                const questionCounter =
                  document.getElementById("questionCounter");

                if (progressBar) {
                  progressBar.style.width = `${currentProgress}%`;
                }

                if (questionCounter) {
                  questionCounter.textContent = `Câu hỏi ${
                    window.currentQuestionIndex + 1
                  }/${totalQuestions}`;
                }
              }

              window.isRestoringFromLocalStorage = false;
              hideLoadingAndShowQuiz();
            }, 300);
          } else if (typeof initQuizizz === "function") {
            initQuizizz();

            setTimeout(() => {
              // Same logic as above
              if (typeof showQuestion === "function") {
                console.log(
                  `🎯 Hiển thị câu hỏi ${
                    window.currentQuestionIndex + 1
                  } sau khi init`
                );
                showQuestion(window.currentQuestionIndex);
              }

              if (typeof updateTimer === "function") {
                if (window.timerInterval) {
                  clearInterval(window.timerInterval);
                }
                window.timerInterval = setInterval(updateTimer, 1000);
                updateTimer();
              }

              if (window.examData && window.examData.questions) {
                const totalQuestions = window.examData.questions.length;
                const currentProgress =
                  ((window.currentQuestionIndex + 1) / totalQuestions) * 100;

                const progressBar = document.getElementById("progressBar");
                const questionCounter =
                  document.getElementById("questionCounter");

                if (progressBar) {
                  progressBar.style.width = `${currentProgress}%`;
                }

                if (questionCounter) {
                  questionCounter.textContent = `Câu hỏi ${
                    window.currentQuestionIndex + 1
                  }/${totalQuestions}`;
                }
              }

              window.isRestoringFromLocalStorage = false;
              hideLoadingAndShowQuiz();
            }, 300);
          } else {
            hideLoadingAndShowQuiz();
          }
        }, 500);
      };

      quizScript.onerror = function () {
        hideLoadingAndShowQuiz();
      };

      document.head.appendChild(quizScript);
    };

    coreScript.onerror = function () {
      hideLoadingAndShowQuiz();
    };

    document.head.appendChild(coreScript);
  }

  // Helper function format time
  function formatTime(seconds) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  }

  // Function process encrypted data
  async function processEncryptedQuizizzData() {
    const encryptedConfigElement = document.getElementById(
      "encrypted-exam-config"
    );

    if (encryptedConfigElement) {
      try {
        updateLoadingProgress("Đang xử lý cấu hình ứng dụng...", 20);

        const encryptedConfig = JSON.parse(encryptedConfigElement.textContent);

        updateLoadingProgress("Đang giải mã dữ liệu câu hỏi...", 50);

        // Process encrypted configuration
        window.examData = await processAppConfig(encryptedConfig);

        updateLoadingProgress("Đang chuẩn bị câu hỏi...", 80);

        // Create unencrypted exam-data script element for compatibility
        const examDataScript = document.createElement("script");
        examDataScript.type = "application/json";
        examDataScript.id = "exam-data";
        examDataScript.textContent = JSON.stringify(window.examData);

        // Insert before the Quizizz scripts load
        document.head.appendChild(examDataScript);

        // Update question counter after decryption
        updateQuestionCounter(window.examData.questions?.length || 0);

        updateLoadingProgress("Đang khởi tạo giao diện...", 95);

        // Load Quizizz scripts after decryption
        loadQuizizzScripts();
      } catch (error) {
        console.error("❌ Lỗi khi xử lý cấu hình bài thi:", error);
        // Show error
        showQuizizzError();
      }
    }
  }

  function showQuizizzError() {
    const quizContainer = document.getElementById("questionContainer");
    if (quizContainer) {
      quizContainer.innerHTML = `
        <div class="text-center py-8">
          <div class="flex items-center justify-center space-x-3 mb-4">
            <i class="fas fa-exclamation-triangle text-red-500 text-3xl"></i>
            <span class="text-red-700 font-bold text-xl">Không thể tải cấu hình ứng dụng</span>
          </div>
          <p class="text-gray-600 mb-6">Vui lòng thử tải lại trang hoặc liên hệ hỗ trợ.</p>
          <button onclick="window.location.reload()" class="bg-red-500 text-white px-6 py-3 rounded-lg hover:bg-red-600 font-medium">
            <i class="fas fa-refresh mr-2"></i>Tải lại trang
          </button>
        </div>
      `;
    }
  }
</script>

<!-- Script xử lý dữ liệu mã hóa cho Quizizz -->
<script>
  // Process encrypted exam data before loading Quizizz scripts
  document.addEventListener("DOMContentLoaded", async function () {
    // KIỂM TRA LOCALSTORAGE TRƯỚC KHI LÀM GÌ KHÁC

    const hasLocalStorageData = checkForExistingQuizizzData();

    if (hasLocalStorageData) {
      return; // Stop here, let user choose continue or new
    }

    console.log("✅ Không có localStorage data, tiếp tục render từ server");

    // Tiếp tục với normal flow
    const encryptedConfigElement = document.getElementById(
      "encrypted-exam-config"
    );

    if (encryptedConfigElement) {
      processEncryptedQuizizzData();
    } else {
      // No encryption, load scripts normally
      loadQuizizzScripts();
    }
  });

  // Function to update loading progress
  function updateLoadingProgress(message, percentage) {
    const loadingIndicator = document.getElementById("loadingIndicator");
    const loadingProgress = document.getElementById("loadingProgress");

    if (loadingIndicator && loadingProgress) {
      const messageElement = loadingIndicator.querySelector("span");
      if (messageElement) {
        messageElement.textContent = message;
      }
      loadingProgress.style.width = percentage + "%";
    }
  }

  // Function to hide loading indicator and show quiz
  function hideLoadingAndShowQuiz() {
    const loadingIndicator = document.getElementById("loadingIndicator");
    const questionContainer = document.getElementById("questionContainer");

    if (loadingIndicator) {
      updateLoadingProgress("Hoàn tất!", 100);

      setTimeout(() => {
        loadingIndicator.style.display = "none";
        if (questionContainer) {
          questionContainer.classList.remove("hidden");
        }
      }, 500);
    } else if (questionContainer) {
      // No loading indicator, just show container
      questionContainer.classList.remove("hidden");
    }
  }

  // Function to update question counter
  function updateQuestionCounter(totalQuestions) {
    const questionCounter = document.getElementById("questionCounter");

    if (questionCounter) {
      questionCounter.textContent = `Câu hỏi 1/${totalQuestions}`;
    }

    // Update total questions display - find by content
    const totalDisplays = document.querySelectorAll(
      "p.text-sm, p.sm\\:text-base"
    );
    for (const display of totalDisplays) {
      if (display.textContent.includes("Tổng số câu hỏi:")) {
        display.innerHTML = `<span class="font-medium">Tổng số câu hỏi:</span> ${totalQuestions}`;
        break;
      }
    }
  }

  // Function to dynamically load Quizizz scripts
  function loadQuizizzScripts() {
    console.log("📦 Bắt đầu load Quizizz scripts...");

    // Load quizizz-core.js first
    const coreScript = document.createElement("script");
    coreScript.src = "/js/quizizz-core.js";

    coreScript.onload = function () {
      console.log("✅ Loaded quizizz-core.js");

      // Load quizizz-quiz.js after core is loaded
      const quizScript = document.createElement("script");
      quizScript.src = "/js/quizizz-quiz.js";

      quizScript.onload = function () {
        console.log("✅ Loaded quizizz-quiz.js");

        // Wait a bit for variables to be initialized
        setTimeout(() => {
          // Initialize Quizizz after all scripts are loaded
          if (typeof window.initQuizizz === "function") {
            console.log("🎯 Khởi tạo Quizizz sau khi load scripts...");
            window.initQuizizz();

            // Hide loading indicator after Quizizz initialization
            setTimeout(() => {
              hideLoadingAndShowQuiz();
            }, 1000);
          } else if (typeof initQuizizz === "function") {
            console.log("🎯 Khởi tạo Quizizz với global function...");
            initQuizizz();

            setTimeout(() => {
              hideLoadingAndShowQuiz();
            }, 1000);
          } else {
            console.error("❌ Function initQuizizz không tồn tại");
            console.log(
              "🔍 Available functions:",
              Object.keys(window).filter((key) => key.includes("init"))
            );
            hideLoadingAndShowQuiz(); // Show quiz container even if init fails
          }
        }, 500);
      };

      quizScript.onerror = function () {
        console.error("❌ Lỗi load quizizz-quiz.js");
        hideLoadingAndShowQuiz();
      };

      document.head.appendChild(quizScript);
    };

    coreScript.onerror = function () {
      console.error("❌ Lỗi load quizizz-core.js");
      hideLoadingAndShowQuiz();
    };

    document.head.appendChild(coreScript);
  }
</script>

<!-- Thêm JavaScript -->
<script src="/js/security-measures.js"></script>
<script>
  // Thiết lập bảo mật ngay khi DOM sẵn sàng
  document.addEventListener("DOMContentLoaded", function () {
    // Tự động phát hiện ngữ cảnh và thiết lập bảo mật với chuyển hướng khi phát hiện DevTools
    GlobalSecurityMeasures({
      redirectOnDevTools: true, // Bật chuyển hướng khi phát hiện DevTools
      redirectUrl: "/home", // Chuyển về trang chủ
      devToolsThreshold: 200, // Tăng ngưỡng để giảm false positive
    });
  });
</script>
