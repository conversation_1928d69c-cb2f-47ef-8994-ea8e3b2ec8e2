import { Request, Response, NextFunction, RequestHandler } from "express";
import express from "express";
import mongoose from "mongoose";
import jwt from "jsonwebtoken";
import {
  getStudentByEmail,
  getStudentCourses,
} from "../services/student.service";
import { Product } from "../services/modelService";
import { getCourseExams } from "../controllers/course.controller";
import { checkStudentCourseAccess } from "../middlewares/student.middleware";
import asyncHandler from "../util/asynHandler";
import getUserProgressByProductIds from "../services/userProgress.service";
import Announcement from "../models/Announcement";

// Interface cho JWT payload
interface JwtPayload {
  id: string;
}

const router = express.Router();

// Middleware để kiểm tra user và thêm vào res.locals
export const userCheckMiddleware: RequestHandler = (req, res, next) => {
  // Lấy token từ cookie
  const token = req.cookies.jwt;
  if (!token) {
    res.locals.user = null;
    return next();
  }

  // Xác thực JWT và thiết lập user
  try {
    const { id } = jwt.verify(
      token,
      process.env.JWT_SECRET as string
    ) as JwtPayload;
    mongoose
      .model("User")
      .findById(id)
      .select("-password -createdAt -updatedAt -__v")
      .then((user) => {
        // Kiểm tra token với activeToken
        if (user && user.get("activeToken") === token) {
          res.locals.user = user;
        } else {
          res.locals.user = null;
        }
        next();
      })
      .catch(() => {
        res.locals.user = null;
        next();
      });
  } catch (error) {
    res.locals.user = null;
    next();
  }
};

// Handler cho trang chủ
const homeHandler: RequestHandler = async (req, res) => {
  // Nếu user đã đăng nhập, chuyển hướng đến /home
  if (res.locals.user) {
    return res.redirect("/home");
  }
  const announcement = await Announcement.findOne({
    location: "homepage_guest",
    isActive: true,
  }).sort({ priority: -1, createdAt: -1 }); // Sắp xếp theo mức độ ưu tiên và ngày tạo

  res.render("index", {
    user: res.locals.user,
    announcement: announcement || null,
  });
};

// Handler cho trang đăng nhập
const loginHandler: RequestHandler = (req, res) => {
  // Nếu user đã đăng nhập, chuyển hướng đến /home
  if (res.locals.user) {
    return res.redirect("/home");
  }

  res.render("login", {
    user: res.locals.user,
    error: req.query.error || null,
  });
};

// Handler cho trang home
const homePageHandler: RequestHandler = async (req, res) => {
  if (!res.locals.user) {
    return res.redirect("/login");
  }

  try {
    // Tìm sinh viên theo email của người dùng đã đăng nhập
    const student = await getStudentByEmail(res.locals.user.email);

    let courses: any[] = [];

    if (student) {
      // Lấy danh sách khóa học của sinh viên và chuyển đổi thành plain objects
      let rawCourses = await getStudentCourses(student._id.toString());
      courses = rawCourses.map((course) =>
        course.toObject ? course.toObject() : JSON.parse(JSON.stringify(course))
      );

      if (courses && courses.length > 0) {
        // Lấy danh sách ID khóa học
        const productIds = courses.map((course) => course._id.toString());

        try {
          // Lấy tiến độ học tập cho từng môn học
          const userProgressList = await getUserProgressByProductIds(
            res.locals.user._id.toString(),
            productIds
          );

          // Kết hợp thông tin tiến độ vào danh sách khóa học
          courses = courses.map((course) => {
            const progress = userProgressList.find(
              (p) => p.product_id.toString() === course._id.toString()
            );

            if (progress) {
              return {
                ...course,
                progress_percentage: progress.progress_percentage,
                total_correct_answers: progress.total_correct_answers,
                total_questions: progress.total_questions,
                completed_exams: progress.completed_exams,
              };
            }
            return {
              ...course,
              progress_percentage: 0,
              total_correct_answers: 0,
              total_questions: course.countQuestion || 0,
              completed_exams: 0,
            };
          });
        } catch (serviceError) {
          courses = [];
          return res.render("home", {
            user: res.locals.user,
            student,
            courses: [],
            error: "Có lỗi xảy ra khi lấy thông tin khóa học",
          });
        }
      }
    }
    const announcement = await Announcement.findOne({
      location: `homepage_authenticated`,
      isActive: true,
    }).sort({ priority: -1 }); // Lấy thông báo có priority cao nhất
    // Đảm bảo dữ liệu trả về là plain objects không có các thuộc tính Mongoose
    const cleanCourses = JSON.parse(JSON.stringify(courses));

    res.render("home", {
      user: res.locals.user,
      student,
      courses: cleanCourses,
      announcement: announcement || null,
    });
  } catch (error) {
    console.error("Lỗi khi lấy thông tin khóa học:", error);
    res.render("home", {
      user: res.locals.user,
      student: null,
      courses: [],
      error: "Có lỗi xảy ra khi lấy thông tin khóa học",
    });
  }
};

// Handler cho trang profile
const profileHandler: RequestHandler = (req, res) => {
  if (!res.locals.user) {
    return res.redirect("/login");
  }

  res.render("profile", { user: res.locals.user });
};

// Handler cho trang danh sách khóa học
const coursesHandler: RequestHandler = async (req, res) => {
  try {
    // Lấy danh sách khóa học từ API
    const products = await Product.find({ status: "active" }).lean();

    res.render("courses/index", {
      user: res.locals.user,
      products,
    });
  } catch (error) {
    console.error("Lỗi khi lấy danh sách khóa học:", error);
    res.render("error", {
      user: res.locals.user,
      message: "Có lỗi xảy ra khi tải danh sách khóa học",
    });
  }
};

// Đăng ký các routes
router.get("/", homeHandler);
router.get("/login", loginHandler);
router.get("/home", homePageHandler);
router.get("/profile", profileHandler);
router.get("/courses", coursesHandler);
router.get(
  "/course/:productId/exams",
  checkStudentCourseAccess,
  asyncHandler(getCourseExams) as RequestHandler
);

export default router;
