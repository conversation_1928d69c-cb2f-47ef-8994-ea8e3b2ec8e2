{"version": 3, "file": "updateExamQuestionCount.js", "sourceRoot": "", "sources": ["../../src/scripts/updateExamQuestionCount.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA,wDAAgC;AAChC,0DAAkC;AAClC,kEAA0C;AAC1C,oDAA4B;AAE5B,6BAA6B;AAC7B,gBAAM,CAAC,MAAM,EAAE,CAAC;AAEhB;;GAEG;AACH,SAAe,iBAAiB;;QAC9B,IAAI,CAAC;YACH,MAAM,QAAQ,GACZ,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,uCAAuC,CAAC;YACrE,MAAM,kBAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YACjC,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;QACnD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;YACjD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC;IACH,CAAC;CAAA;AAED;;GAEG;AACH,SAAe,wBAAwB;;QACrC,IAAI,CAAC;YACH,yBAAyB;YACzB,MAAM,KAAK,GAAG,MAAM,cAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAClC,OAAO,CAAC,GAAG,CAAC,YAAY,KAAK,CAAC,MAAM,uBAAuB,CAAC,CAAC;YAE7D,qBAAqB;YACrB,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gBACzB,uCAAuC;gBACvC,MAAM,aAAa,GAAG,MAAM,kBAAQ,CAAC,cAAc,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;gBAE1E,oCAAoC;gBACpC,MAAM,cAAI,CAAC,SAAS,CAClB,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,EACjB,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,aAAa,EAAE,EAAE,CACnC,CAAC;gBAEF,OAAO,CAAC,GAAG,CACT,YAAY,IAAI,CAAC,IAAI,UAAU,IAAI,CAAC,GAAG,mBAAmB,aAAa,EAAE,CAC1E,CAAC;YACJ,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,6DAA6D,CAAC,CAAC;QAC7E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;QAC7D,CAAC;gBAAS,CAAC;YACT,gDAAgD;YAChD,MAAM,kBAAQ,CAAC,UAAU,EAAE,CAAC;YAC5B,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;QAC9C,CAAC;IACH,CAAC;CAAA;AAED;;GAEG;AACH,SAAe,IAAI;;QACjB,MAAM,iBAAiB,EAAE,CAAC;QAC1B,MAAM,wBAAwB,EAAE,CAAC;IACnC,CAAC;CAAA;AAED,cAAc;AACd,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;IACrB,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;IAC7C,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC,CAAC"}