import { Request, Response, NextFunction } from "express";

// Cậ<PERSON> nhật kiểu hàm để không trả về gì
export const redirectLegacyExamRoute = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  // L<PERSON>y tham số từ query string
  const { examId, examType } = req.query;

  // Kiểm tra tham số bắt buộc
  if (!examId || !examType) {
    res.status(400).json({
      success: false,
      message: "Thiếu thông tin cần thiết: examId và examType là bắt buộc",
    });
    return;
  }

  // Chuyển hướng đến URL chính xác với cùng các tham số
  res.redirect(
    `/exam/retry-wrong-questions?examId=${examId}&examType=${examType}`
  );
};
