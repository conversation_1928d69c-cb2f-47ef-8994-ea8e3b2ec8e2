import mongoose, { Schema, Document } from "mongoose";
import { AuthConfig } from "../config/auth-config";

export interface ISession extends Document {
  userId: mongoose.Types.ObjectId;
  token: string;
  clientId: string;
  deviceInfo: {
    userAgent?: string;
    ip?: string;
    deviceName?: string;
    browser?: string;
    os?: string;
  };
  isActive: boolean;
  isCurrentDevice: boolean;
  lastActive: Date;
  expiresAt: Date;
  createdAt: Date;
}

const sessionSchema = new Schema<ISession>(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: true,
      index: true,
    },
    token: {
      type: String,
      required: true,
      unique: true,
    },
    clientId: {
      type: String,
      required: true,
      index: true,
    },
    deviceInfo: {
      userAgent: String,
      ip: String,
      deviceName: String,
      browser: String,
      os: String,
    },
    isActive: {
      type: Boolean,
      default: true,
      index: true,
    },
    isCurrentDevice: {
      type: <PERSON><PERSON><PERSON>,
      default: false,
    },
    lastActive: {
      type: Date,
      default: Date.now,
    },
    expiresAt: {
      type: Date,
      required: true,
      index: true,
    },
  },
  {
    timestamps: true,
  }
);

// Đánh chỉ mục phức hợp để truy vấn nhanh hơn
sessionSchema.index({ userId: 1, clientId: 1 });
sessionSchema.index({ userId: 1, isActive: 1 });

// Phương thức tĩnh để tạo phiên mới
sessionSchema.statics.createSession = async function (
  userId: mongoose.Types.ObjectId | string,
  token: string,
  clientId: string,
  deviceInfo: any,
  expiresIn: number = AuthConfig.SESSION.EXPIRATION // Sử dụng cấu hình mới (90 ngày)
) {
  // Tạo thời gian hết hạn
  const expiresAt = new Date(Date.now() + expiresIn);

  // Thiết lập thiết bị hiện tại là thiết bị mới nhất
  await this.updateMany(
    { userId, isCurrentDevice: true },
    { $set: { isCurrentDevice: false } }
  );

  // Tạo phiên mới
  return this.create({
    userId,
    token,
    clientId,
    deviceInfo,
    isActive: true,
    isCurrentDevice: true,
    expiresAt,
  });
};

// Phương thức tĩnh để tìm phiên hợp lệ theo token
sessionSchema.statics.findValidSessionByToken = function (token: string) {
  return this.findOne({
    token,
    isActive: true,
    expiresAt: { $gt: new Date() },
  });
};

// Phương thức tĩnh để đánh dấu tất cả các phiên khác là không hoạt động
sessionSchema.statics.deactivateOtherSessions = function (
  userId: mongoose.Types.ObjectId | string,
  currentClientId: string
) {
  return this.updateMany(
    {
      userId,
      clientId: { $ne: currentClientId },
      isActive: true,
    },
    {
      $set: { isActive: false },
    }
  );
};

// Phương thức tĩnh để đánh dấu phiên hiện tại là phiên mới nhất
sessionSchema.statics.setCurrentDevice = function (
  userId: mongoose.Types.ObjectId | string,
  clientId: string
) {
  return this.updateMany({ userId }, { $set: { isCurrentDevice: false } }).then(
    () => {
      return this.updateOne(
        { userId, clientId },
        { $set: { isCurrentDevice: true } }
      );
    }
  );
};

// Phương thức tĩnh để xóa tất cả các phiên hết hạn
sessionSchema.statics.removeExpiredSessions = function () {
  return this.deleteMany({
    $or: [{ expiresAt: { $lte: new Date() } }, { isActive: false }],
  });
};

// Tạo interface để TypeScript nhận biết các phương thức tĩnh
export interface SessionModel extends mongoose.Model<ISession> {
  createSession(
    userId: mongoose.Types.ObjectId | string,
    token: string,
    clientId: string,
    deviceInfo: any,
    expiresIn?: number
  ): Promise<ISession>;
  findValidSessionByToken(token: string): Promise<ISession | null>;
  deactivateOtherSessions(
    userId: mongoose.Types.ObjectId | string,
    currentClientId: string
  ): Promise<any>;
  setCurrentDevice(
    userId: mongoose.Types.ObjectId | string,
    clientId: string
  ): Promise<any>;
  removeExpiredSessions(): Promise<any>;
}

export default mongoose.model<ISession, SessionModel>("Session", sessionSchema);
