import { Request, Response } from "express";
import { Product, Exam } from "../services/modelService";
import Question from "../models/question";
import mongoose from "mongoose";
import { encryptObject } from "../util/encryption";
import Announcement from "../models/Announcement";
import MemoryQuestion from "../models/MemoryQuestion";

/**
 * Hiển thị trang danh sách đề thi của một môn học
 */
export const getCourseExams = async (req: Request, res: Response) => {
  try {
    const productId = req.params.productId;
    const tab = req.query.tab || "exams"; // Mặc định tab là 'exams'
    // Validate tab parameter
    const validTabs = ["exams", "practice", "outline", "memory"];
    const currentTab = validTabs.includes(tab as string) ? tab : "exams";
    // Lấy thông tin khóa học
    const product = await Product.findById(productId);
    if (!product) {
      return res.status(404).render("error", {
        message: "<PERSON>hông tìm thấy thông tin môn học",
        error: { status: 404 },
        user: res.locals.user,
      });
    }
    // Lấy danh sách đề thi của khóa học
    const exams = await Exam.find({
      productId: new mongoose.Types.ObjectId(productId),
    });
    let questionsByExam = [];

    if (currentTab === "outline" || currentTab === "practice") {
      // Bước 1: Lấy ID các đề thi
      const examIds = exams.map((exam) => exam._id);
      if (examIds.length > 0) {
        // Bước 2: Lấy danh sách câu hỏi của các đề thi
        const questions = await Question.find({
          examId: { $in: examIds },
        })
          .populate("examId", "name")
          .sort({ examId: 1 });
        // Bước 3: Nhóm câu hỏi theo đề thi ở backend
        const questionGroupMap = {};
        questions.forEach((question) => {
          const examId = question.examId._id.toString();
          const examName = (question.examId as any).name;
          if (!questionGroupMap[examId]) {
            questionGroupMap[examId] = {
              examId: examId,
              examName: examName,
              questions: [],
            };
          }
          questionGroupMap[examId].questions.push({
            _id: question._id,
            text: question.text,
            answers: question.answers,
            image: question.image,
            difficulty: question.difficulty,
          });
        });
        // Bước 4: Chuyển đổi object thành array
        questionsByExam = Object.values(questionGroupMap);
      }
    }

    // Nếu đang ở tab memory, lấy danh sách câu hỏi ghi nhớ của user
    let memoryQuestions = [];
    if (currentTab === "memory") {
      memoryQuestions = await MemoryQuestion.find({
        userId: res.locals.user._id,
        productId: productId,
        isActive: true,
      })
        .populate("questionId")
        .sort({ createdAt: -1 })
        .limit(100);
    }

    // Nếu đang ở tab outline, lấy danh sách IDs câu hỏi đã ghi nhớ
    let memorizedQuestionIds = [];
    if (currentTab === "outline") {
      const memorizedQuestions = await MemoryQuestion.find({
        userId: res.locals.user._id,
        productId: productId,
        isActive: true,
      }).select("questionId");

      memorizedQuestionIds = memorizedQuestions.map((mq) =>
        mq.questionId.toString()
      );
    }

    // Mã hóa dữ liệu câu hỏi nếu có
    let encryptedQuestions = null;
    let encryptionKey = null;
    let encryptionIV = null;
    if (questionsByExam && questionsByExam.length > 0) {
      try {
        const encryptionResult = encryptObject(questionsByExam);
        encryptedQuestions = encryptionResult.encryptedData;
        encryptionKey = encryptionResult.key;
        encryptionIV = encryptionResult.iv;
      } catch (error) {
        console.error("Lỗi khi mã hóa dữ liệu:", error);
        // Fallback: sử dụng dữ liệu không mã hóa
      }
    }
    // Render trang danh sách đề thi với thông tin tab
    const finalQuestionsByExam =
      currentTab === "outline"
        ? encryptedQuestions
          ? null
          : questionsByExam
        : questionsByExam;
    // Lấy thông báo
    const announcement = await Announcement.findOne({
      location: `course/${productId}`,
      isActive: true,
    }).sort({ priority: -1 }); // Lấy thông báo có priority cao nhất
    res.render("courses/exams", {
      user: res.locals.user,
      student: req.student,
      product,
      exams,
      questionsByExam: finalQuestionsByExam,
      encryptedQuestions,
      encryptionKey,
      encryptionIV,
      currentTab,
      announcement,
      memoryQuestions,
      memorizedQuestionIds,
    });
  } catch (error) {
    console.error("Lỗi khi lấy thông tin đề thi:", error);
    res.status(500).render("error", {
      message: "Có lỗi xảy ra khi tải thông tin đề thi",
      error: { status: 500 },
      user: res.locals.user,
    });
  }
};
