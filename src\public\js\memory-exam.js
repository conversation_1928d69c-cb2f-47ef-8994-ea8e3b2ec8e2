/**
 * memory-exam.js
 * <PERSON><PERSON>le ch<PERSON>h quản lý tính năng "Ghi nhớ"
 *
 * Pattern: IIFE với namespace isolation
 */
(function (window) {
  "use strict";

  // Private variables và state
  let _config = {
    productId: null,
    apiBase: "/exam/memory",
    domSelectors: {
      container: "#memoryQuestionsContainer",
      loadingIndicator: "#memoryLoadingIndicator",
      totalCount: "#totalMemoryQuestionsCount",
      searchInput: "#memorySearchInput",
      statsTotal: "#memoryStatsTotalQuestions",
      statsAccuracy: "#memoryStatsAccuracy",
      statsPracticed: "#memoryStatsPracticed",
      bulkDeleteBtn: "#bulkDeleteBtn",
      selectedCountSpan: "#selectedCount",
      practiceModal: "#memoryPracticeModal",
    },
  };

  let _state = {
    questions: [],
    filteredQuestions: [],
    selectedQuestions: [],
    isLoading: false,
    searchTerm: "",
    groupFilter: "all",
    currentPage: 1,
    itemsPerPage: 20,
    stats: {
      totalQuestions: 0,
      averageAccuracy: 0,
      practiceCount: 0,
    },
  };

  // DOM cache để lưu trữ tham chiếu đến DOM elements
  let _dom = {};

  // Khởi tạo DOM cache
  function _initDomCache() {
    Object.keys(_config.domSelectors).forEach((key) => {
      const selector = _config.domSelectors[key];
      _dom[key] = document.querySelector(selector);
    });
  }

  // Khởi tạo module
  function _init() {
    // Lấy productId từ URL
    const urlParams = new URLSearchParams(window.location.search);
    _config.productId =
      document
        .querySelector('meta[name="product-id"]')
        ?.getAttribute("content") || window.location.pathname.split("/")[2];

    // Khởi tạo DOM cache
    _initDomCache();

    // Gán các event listeners
    _setupEventListeners();

    // Tải dữ liệu
    _loadMemoryQuestions();
  }

  // Thiết lập các event listeners
  function _setupEventListeners() {
    // Search input với debounce
    if (_dom.searchInput) {
      _dom.searchInput.addEventListener(
        "input",
        _debounce(function () {
          _state.searchTerm = _dom.searchInput.value.toLowerCase().trim();
          _filterQuestions();
        }, 300)
      );
    }

    // Event delegation cho container chứa danh sách câu hỏi
    if (_dom.container) {
      _dom.container.addEventListener("click", function (e) {
        // Handle checkbox selection
        if (e.target.matches(".memory-question-checkbox")) {
          _handleCheckboxSelection(e.target);
        }

        // Handle delete button
        if (e.target.closest(".delete-memory-btn")) {
          const questionElement = e.target.closest(".memory-question-item");
          const questionId = questionElement.dataset.id;
          _deleteMemoryQuestion(questionId);
        }
      });
    }
  }

  // Hàm tải danh sách câu hỏi ghi nhớ
  function _loadMemoryQuestions() {
    _state.isLoading = true;
    _showLoadingIndicator();

    // Gọi API để lấy danh sách câu hỏi ghi nhớ
    fetch(
      `${_config.apiBase}/${_config.productId}?page=${_state.currentPage}&limit=${_state.itemsPerPage}`
    )
      .then((response) => response.json())
      .then((data) => {
        if (data.success) {
          _state.questions = data.data.questions || [];
          _state.stats = data.data.stats || {
            totalQuestions: _state.questions.length,
            averageAccuracy: 0,
            practiceCount: 0,
          };

          _updateUI();
        } else {
          console.error("Failed to load memory questions:", data.message);
          _showErrorMessage("Không thể tải danh sách câu hỏi ghi nhớ");
        }
      })
      .catch((error) => {
        console.error("Error loading memory questions:", error);
        _showErrorMessage("Đã có lỗi xảy ra khi tải dữ liệu");
      })
      .finally(() => {
        _state.isLoading = false;
        _hideLoadingIndicator();
      });
  }

  // Cập nhật giao diện người dùng
  function _updateUI() {
    _updateStats();
    _updateGroupFilter();
    _filterQuestions();
    _renderQuestions();
  }

  // Cập nhật thống kê
  function _updateStats() {
    if (_dom.statsTotal) {
      _dom.statsTotal.textContent = _state.stats.totalQuestions || 0;
    }

    if (_dom.totalCount) {
      _dom.totalCount.textContent = _state.stats.totalQuestions || 0;
    }

    if (_dom.statsAccuracy) {
      const accuracy = _state.stats.averageAccuracy || 0;
      _dom.statsAccuracy.textContent = `${Math.round(accuracy)}%`;
    }

    if (_dom.statsPracticed) {
      _dom.statsPracticed.textContent = _state.stats.practiceCount || 0;
    }
  }

  // Cập nhật group filter dropdown
  function _updateGroupFilter() {
    const groupFilter = document.getElementById("memoryGroupFilter");
    if (!groupFilter) return;

    // Get unique groups from questions
    const groups = new Set();
    _state.questions.forEach((question) => {
      if (question.group) {
        groups.add(question.group);
      }
    });

    // Clear existing options except default ones
    const defaultOptions = Array.from(
      groupFilter.querySelectorAll(
        'option[value="all"], option[value="ungrouped"]'
      )
    );
    groupFilter.innerHTML = "";
    defaultOptions.forEach((option) => groupFilter.appendChild(option));

    // Add group options
    Array.from(groups)
      .sort()
      .forEach((group) => {
        const option = document.createElement("option");
        option.value = group;
        option.textContent = group;
        groupFilter.appendChild(option);
      });
  }

  // Lọc câu hỏi dựa trên filter và search
  function _filterQuestions() {
    _state.filteredQuestions = _state.questions.filter((question) => {
      // Filter by search term
      const matchesSearch =
        _state.searchTerm === "" ||
        question.questionData?.text
          ?.toLowerCase()
          .includes(_state.searchTerm) ||
        question.questionData?.options?.some((option) =>
          option.text?.toLowerCase().includes(_state.searchTerm)
        );

      // Filter by group
      const matchesGroup =
        _state.groupFilter === "all" ||
        (_state.groupFilter === "ungrouped" && !question.group) ||
        question.group === _state.groupFilter;

      return matchesSearch && matchesGroup;
    });

    // Cập nhật số lượng hiển thị
    if (_dom.searchInput && _dom.searchInput.value.trim() !== "") {
      document.getElementById("memorySearchInfo").style.display = "block";
      document.getElementById("memorySearchResultCount").textContent =
        _state.filteredQuestions.length;
      document.getElementById("clearMemorySearchBtn").style.display = "block";
    } else {
      document.getElementById("memorySearchInfo").style.display = "none";
      document.getElementById("clearMemorySearchBtn").style.display = "none";
    }

    // Render lại câu hỏi
    _renderQuestions();
  }

  // Render danh sách câu hỏi
  function _renderQuestions() {
    if (!_dom.container) return;

    if (_state.filteredQuestions.length === 0) {
      _dom.container.innerHTML = `
        <div class="text-center py-8 bg-white rounded-lg">
          <div class="text-gray-400 mb-3">
            <i class="fas fa-search text-3xl"></i>
          </div>
          <h3 class="text-lg font-medium text-gray-700 mb-2">Không tìm thấy câu hỏi</h3>
          <p class="text-sm text-gray-500">Thử thay đổi bộ lọc hoặc từ khóa tìm kiếm</p>
        </div>
      `;
      return;
    }

    let html = "";
    _state.filteredQuestions.forEach((question, index) => {
      const groupLabel = question.group
        ? `<span class="bg-purple-100 text-purple-800 text-xs font-medium px-2 py-0.5 rounded-full">
             <i class="fas fa-layer-group mr-1"></i>${question.group}
           </span>`
        : '<span class="bg-gray-100 text-gray-600 text-xs font-medium px-2 py-0.5 rounded-full">Chưa phân nhóm</span>';

      const questionText =
        question.questionData?.text || "Không có nội dung câu hỏi";

      // Generate answer options HTML to match outline page format
      const answersHtml = (question.questionData?.answers || [])
        .map((answer, answerIndex) => {
          const optionLabel = String.fromCharCode(65 + answerIndex);
          const isCorrect = answer.isCorrect || false;
          let answerText = answer.text || answer || "Đáp án không có sẵn";

          // Remove prefix A., B., C., D. if present
          if (typeof answerText === "string") {
            const prefixRegex = /^\s*(?:[A-D]|[1-9])[\.\)][\s\u00A0]*/i;
            if (prefixRegex.test(answerText)) {
              answerText = answerText.replace(prefixRegex, "");
            }
          }

          // Set style for correct answer
          let borderColor = "border-gray-200";
          let bgColor = "";
          if (isCorrect) {
            borderColor = "border-green-200";
            bgColor = "bg-green-50";
          }

          return `
            <div class="answer-option flex items-center p-3 border ${borderColor} ${bgColor} rounded-lg text-sm answer-text" 
                 data-correct="${isCorrect}" data-answer-index="${answerIndex}">
              <span class="font-medium mr-3 text-xs bg-gray-100 px-2 py-1 rounded-full">${optionLabel}</span>
              <span class="flex-1 break-words">${answerText}</span>
              ${
                isCorrect
                  ? '<span class="correct-indicator ml-2"><i class="fas fa-check text-green-600"></i></span>'
                  : ""
              }
            </div>
          `;
        })
        .join("");

      html += `
        <div class="memory-question-item bg-white rounded-lg border border-gray-200 p-4 mb-4 hover:shadow-sm transition-all"
             data-id="${question._id}" data-index="${index}">
          <div class="flex items-start gap-3">
            
            <div class="flex-1">
              <div class="flex items-center justify-between mb-3">
                <div class="flex items-center gap-2">
                <div class="flex-shrink-0 pt-1">
              <input type="checkbox" class="memory-question-checkbox h-5 w-5 text-indigo-600 rounded" />
            </div>
                  <h4 class="font-medium text-gray-800 text-sm">
                    Câu ${index + 1}
                  </h4>
                  
                  ${groupLabel}
                  <span class="text-xs text-gray-500">${new Date(
                    question.createdAt
                  ).toLocaleDateString("vi-VN")}</span>
                </div>
                <button class="delete-memory-btn text-red-500 hover:text-red-700 p-1" title="Xóa khỏi ghi nhớ">
                  <i class="fas fa-trash-alt"></i>
                </button>
              </div>
              
              <div class="text-gray-700 mb-4 text-sm leading-relaxed break-words question-text lff">
                ${questionText}
              </div>
              
              <!-- Question Image Display -->
              ${
                question.questionData?.image
                  ? `
                <div class="question-image-container mb-4">
                  <img src="${question.questionData.image}" 
                       alt="Hình ảnh câu hỏi ${index + 1}" 
                       class="question-image max-w-full h-auto rounded-lg border border-gray-200 shadow-sm"
                       style="max-height: 400px; object-fit: contain;">
                
                </div>
              `
                  : ""
              }
              
              <div class="answers-container space-y-2 mb-4">
                ${answersHtml}
              </div>
              

            </div>
          </div>
        </div>
      `;
    });

    _dom.container.innerHTML = html;
  }

  // Xử lý checkbox selection
  function _handleCheckboxSelection(checkbox) {
    const questionItem = checkbox.closest(".memory-question-item");
    const questionId = questionItem.dataset.id;

    if (checkbox.checked) {
      _state.selectedQuestions.push(questionId);
      questionItem.classList.add("bg-indigo-50");
    } else {
      _state.selectedQuestions = _state.selectedQuestions.filter(
        (id) => id !== questionId
      );
      questionItem.classList.remove("bg-indigo-50");
    }

    // Update bulk delete button
    _updateBulkDeleteButton();
  }

  // Cập nhật trạng thái nút xóa hàng loạt
  function _updateBulkDeleteButton() {
    if (_dom.bulkDeleteBtn && _dom.selectedCountSpan) {
      _dom.bulkDeleteBtn.disabled = _state.selectedQuestions.length === 0;
      _dom.selectedCountSpan.textContent = _state.selectedQuestions.length;
    }
  }

  // Xóa một câu hỏi ghi nhớ
  function _deleteMemoryQuestion(questionId) {
    if (!confirm("Bạn có chắc muốn xóa câu hỏi này khỏi danh sách ghi nhớ?")) {
      return;
    }

    fetch(`${_config.apiBase}/${_config.productId}/${questionId}`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
      },
    })
      .then((response) => response.json())
      .then((data) => {
        if (data.success) {
          // Xóa câu hỏi khỏi state
          _state.questions = _state.questions.filter(
            (q) => q._id !== questionId
          );
          _state.filteredQuestions = _state.filteredQuestions.filter(
            (q) => q._id !== questionId
          );
          _state.selectedQuestions = _state.selectedQuestions.filter(
            (id) => id !== questionId
          );

          // Cập nhật stats
          _state.stats.totalQuestions = _state.questions.length;

          // Cập nhật UI
          _updateUI();

          // Hiển thị thông báo
          _showSuccessToast("Đã xóa câu hỏi khỏi danh sách ghi nhớ");
        } else {
          console.error("Failed to delete memory question:", data.message);
          _showErrorToast("Không thể xóa câu hỏi");
        }
      })
      .catch((error) => {
        console.error("Error deleting memory question:", error);
        _showErrorToast("Đã có lỗi xảy ra");
      });
  }

  // Xóa nhiều câu hỏi đã chọn
  function deleteBulkMemoryQuestions() {
    if (_state.selectedQuestions.length === 0) return;

    if (
      !confirm(
        `Bạn có chắc muốn xóa ${_state.selectedQuestions.length} câu hỏi đã chọn?`
      )
    ) {
      return;
    }

    fetch(`${_config.apiBase}/${_config.productId}/bulk-delete`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        questionIds: _state.selectedQuestions,
      }),
    })
      .then((response) => response.json())
      .then((data) => {
        if (data.success) {
          // Xóa các câu hỏi khỏi state
          _state.questions = _state.questions.filter(
            (q) => !_state.selectedQuestions.includes(q._id)
          );
          _state.filteredQuestions = _state.filteredQuestions.filter(
            (q) => !_state.selectedQuestions.includes(q._id)
          );

          // Reset selected questions
          _state.selectedQuestions = [];

          // Cập nhật stats
          _state.stats.totalQuestions = _state.questions.length;

          // Cập nhật UI
          _updateUI();
          _updateBulkDeleteButton();

          // Hiển thị thông báo
          _showSuccessToast(
            `Đã xóa ${data.deletedCount} câu hỏi khỏi danh sách ghi nhớ`
          );
        } else {
          console.error("Failed to delete memory questions:", data.message);
          _showErrorToast("Không thể xóa câu hỏi");
        }
      })
      .catch((error) => {
        console.error("Error deleting memory questions:", error);
        _showErrorToast("Đã có lỗi xảy ra");
      });
  }

  // State for practice modal
  let _practiceState = {
    selectedGroups: [],
    allGroups: [],
    groupQuestionCounts: {},
    isLoadingGroups: false,
  };

  // Hiển thị modal bắt đầu luyện tập
  function startMemoryPractice() {
    if (_state.questions.length === 0) {
      alert("Bạn chưa có câu hỏi nào trong danh sách ghi nhớ để luyện tập");
      return;
    }

    if (_dom.practiceModal) {
      _dom.practiceModal.classList.remove("hidden");
      // Load groups data when modal opens
      _loadGroupsForPractice();
    }
  }

  // Đóng modal luyện tập
  function closeMemoryPracticeModal() {
    if (_dom.practiceModal) {
      _dom.practiceModal.classList.add("hidden");
    }
  }

  // Load groups for practice selection
  async function _loadGroupsForPractice() {
    if (_practiceState.isLoadingGroups) return;

    _practiceState.isLoadingGroups = true;
    const select = document.getElementById("groupSelection");

    try {
      const response = await fetch(
        `${_config.apiBase}/${_config.productId}/groups`
      );
      const data = await response.json();

      if (data.success) {
        _practiceState.allGroups = data.data.groups;
        _practiceState.groupQuestionCounts = {
          ungrouped: data.data.ungrouped.count,
        };

        // Add count for each group
        data.data.groups.forEach((group) => {
          _practiceState.groupQuestionCounts[group.name] = group.count;
        });

        _renderGroupSelection(select);
      } else {
        _showGroupsError(select, "Không thể tải danh sách nhóm");
      }
    } catch (error) {
      console.error("Error loading groups:", error);
      _showGroupsError(select, "Đã có lỗi xảy ra khi tải danh sách nhóm");
    } finally {
      _practiceState.isLoadingGroups = false;
    }
  }

  // Render group selection UI
  function _renderGroupSelection(select) {
    // Clear existing options
    select.innerHTML = "";

    // Add default option
    const defaultOption = document.createElement("option");
    defaultOption.value = "";
    defaultOption.textContent = "Chọn nhóm câu hỏi để luyện tập";
    defaultOption.disabled = true;
    defaultOption.selected = true;
    select.appendChild(defaultOption);

    // Add "All questions" option
    const allQuestionsCount = Object.values(
      _practiceState.groupQuestionCounts
    ).reduce((sum, count) => sum + count, 0);

    const allOption = document.createElement("option");
    allOption.value = "all";
    allOption.textContent = `🌐 Tất cả câu hỏi (${allQuestionsCount} câu)`;
    select.appendChild(allOption);

    // Add ungrouped questions if any
    if (_practiceState.groupQuestionCounts.ungrouped > 0) {
      const ungroupedOption = document.createElement("option");
      ungroupedOption.value = "ungrouped";
      ungroupedOption.textContent = `❓ Chưa phân nhóm (${_practiceState.groupQuestionCounts.ungrouped} câu)`;
      select.appendChild(ungroupedOption);
    }

    // Add specific groups
    _practiceState.allGroups.forEach((group) => {
      const groupOption = document.createElement("option");
      groupOption.value = group.name;
      groupOption.textContent = `📁 ${group.name} (${group.count} câu)`;
      select.appendChild(groupOption);
    });

    // Add event listener for selection change
    select.onchange = function () {
      _handleGroupSelection(this.value);
    };

    // If no groups available, show appropriate message
    if (
      _practiceState.allGroups.length === 0 &&
      _practiceState.groupQuestionCounts.ungrouped === 0
    ) {
      select.innerHTML = '<option value="">Không có nhóm câu hỏi nào</option>';
      select.disabled = true;
    }

    _updatePracticeSummary();
  }

  // Handle group selection
  function _handleGroupSelection(selectedValue) {
    if (!selectedValue) {
      _practiceState.selectedGroups = [];
    } else {
      _practiceState.selectedGroups = [selectedValue];
    }

    _updatePracticeSummary();
  }

  // Update practice summary
  function _updatePracticeSummary() {
    const selectedGroupsInfo = document.getElementById("selectedGroupsInfo");
    const startBtn = document.getElementById("startPracticeBtn");

    let totalQuestions = 0;

    if (_practiceState.selectedGroups.length > 0) {
      const selectedGroup = _practiceState.selectedGroups[0];

      if (selectedGroup === "all") {
        totalQuestions = Object.values(
          _practiceState.groupQuestionCounts
        ).reduce((sum, count) => sum + count, 0);
        selectedGroupsInfo.textContent =
          "Sẽ luyện tập với tất cả câu hỏi ghi nhớ";
      } else if (selectedGroup === "ungrouped") {
        totalQuestions = _practiceState.groupQuestionCounts.ungrouped || 0;
        selectedGroupsInfo.textContent =
          "Sẽ luyện tập với câu hỏi chưa phân nhóm";
      } else {
        totalQuestions = _practiceState.groupQuestionCounts[selectedGroup] || 0;
        selectedGroupsInfo.textContent = `Sẽ luyện tập với nhóm "${selectedGroup}"`;
      }
    } else {
      selectedGroupsInfo.textContent = "Chọn một nhóm để bắt đầu luyện tập";
    }

    // Enable/disable start button
    if (startBtn) {
      startBtn.disabled = totalQuestions === 0;
    }
  }

  // Show groups loading error
  function _showGroupsError(select, message) {
    select.innerHTML = `<option value="">${message}</option>`;
    select.disabled = true;

    // Show error in selectedGroupsInfo
    const selectedGroupsInfo = document.getElementById("selectedGroupsInfo");
    if (selectedGroupsInfo) {
      selectedGroupsInfo.innerHTML = `
        <span class="text-red-500">
          <i class="fas fa-exclamation-triangle mr-1"></i>
          ${message}
          <button onclick="_loadGroupsForPractice()" class="ml-2 text-xs text-indigo-600 hover:text-indigo-800 underline">
            Thử lại
          </button>
        </span>
      `;
    }
  }

  // Bắt đầu bài luyện tập ghi nhớ
  function startMemoryExam() {
    // Clear localStorage for clean practice state
    try {
      localStorage.clear();
      console.log("🧹 Cleared localStorage for clean practice session");
    } catch (error) {
      console.warn("⚠️ Could not clear localStorage:", error);
    }

    // Validate selection
    if (_practiceState.selectedGroups.length === 0) {
      alert("Vui lòng chọn ít nhất một nhóm câu hỏi để luyện tập");
      return;
    }

    const timeLimit =
      parseInt(document.getElementById("timeLimit").value) || 15;
    const shuffleQuestions =
      document.getElementById("shuffleQuestions").checked;
    const shuffleAnswers = document.getElementById("shuffleAnswers").checked;

    // Prepare simplified practice data
    const practiceData = {
      selectedGroups: _practiceState.selectedGroups,
      questionCount: "all", // Always use all questions from selected group
      timeLimit: timeLimit,
      shuffleQuestions: shuffleQuestions,
      shuffleAnswers: shuffleAnswers,
      focusWeakQuestions: false, // Removed option
      reviewMode: false, // Removed option
    };

    // Show loading state
    const startBtn = document.getElementById("startPracticeBtn");
    const originalText = startBtn.innerHTML;
    startBtn.disabled = true;
    startBtn.innerHTML =
      '<i class="fas fa-spinner fa-spin mr-2"></i>Đang khởi tạo...';

    try {
      console.log("🚀 Submitting practice data:", practiceData);

      // Create a form to submit the practice data and navigate to Quizizz interface
      const form = document.createElement("form");
      form.method = "POST";
      form.action = `${_config.apiBase}/${_config.productId}/practice/quizizz-start`;

      // Add practice data as hidden inputs
      Object.keys(practiceData).forEach((key) => {
        const input = document.createElement("input");
        input.type = "hidden";
        input.name = key;

        if (Array.isArray(practiceData[key])) {
          input.value = JSON.stringify(practiceData[key]);
        } else {
          input.value = practiceData[key];
        }

        form.appendChild(input);
      });

      console.log("📤 Form action:", form.action);
      console.log("📤 Form data count:", form.elements.length);

      // Submit form to navigate to practice interface
      document.body.appendChild(form);
      form.submit();
    } catch (error) {
      console.error("❌ Error submitting practice form:", error);
      alert("Có lỗi xảy ra khi khởi tạo luyện tập. Vui lòng thử lại.");

      // Restore button state
      startBtn.disabled = false;
      startBtn.innerHTML = originalText;
    }
  }

  // Make group selection handler globally available
  window._handleGroupSelection = _handleGroupSelection;

  // Hiển thị thông báo lỗi
  function _showErrorMessage(message) {
    if (_dom.container) {
      _dom.container.innerHTML = `
        <div class="text-center py-8 bg-white rounded-lg">
          <div class="text-red-500 mb-3">
            <i class="fas fa-exclamation-circle text-3xl"></i>
          </div>
          <h3 class="text-lg font-medium text-gray-700 mb-2">Đã xảy ra lỗi</h3>
          <p class="text-sm text-gray-500 mb-4">${message}</p>
          <button onclick="window.MemoryExam.loadMemoryQuestions()" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
            <i class="fas fa-redo mr-2"></i>Thử lại
          </button>
        </div>
      `;
    }
  }

  // Hiển thị toast thành công
  function _showSuccessToast(message) {
    // Triển khai sau hoặc sử dụng thư viện toast
    console.log("Success:", message);
  }

  // Hiển thị toast lỗi
  function _showErrorToast(message) {
    // Triển khai sau hoặc sử dụng thư viện toast
    console.error("Error:", message);
  }

  // Hiển thị loading indicator
  function _showLoadingIndicator() {
    if (_dom.loadingIndicator) {
      _dom.loadingIndicator.style.display = "block";
    }
  }

  // Ẩn loading indicator
  function _hideLoadingIndicator() {
    if (_dom.loadingIndicator) {
      _dom.loadingIndicator.style.display = "none";
    }
  }

  // Utility: Debounce function
  function _debounce(func, wait) {
    let timeout;
    return function () {
      const context = this;
      const args = arguments;
      clearTimeout(timeout);
      timeout = setTimeout(() => func.apply(context, args), wait);
    };
  }

  // Utility: Throttle function
  function _throttle(func, wait) {
    let timeout = null;
    let previous = 0;
    return function () {
      const context = this;
      const args = arguments;
      const now = Date.now();

      if (!previous) previous = now;

      const remaining = wait - (now - previous);
      if (remaining <= 0 || remaining > wait) {
        if (timeout) {
          clearTimeout(timeout);
          timeout = null;
        }
        previous = now;
        func.apply(context, args);
      } else if (!timeout) {
        timeout = setTimeout(() => {
          previous = Date.now();
          timeout = null;
          func.apply(context, args);
        }, remaining);
      }
    };
  }

  // Private function để xóa câu hỏi khỏi memory
  function _removeFromMemory(questionId, examId) {
    const addBtn = document.querySelector(
      `[data-question-id="${questionId}"] .add-to-memory-btn`
    );

    if (!addBtn) {
      return Promise.reject(new Error("Button not found"));
    }

    // Prevent multiple clicks
    if (addBtn.disabled) {
      return Promise.resolve();
    }

    // Store original state
    const originalHTML = addBtn.innerHTML;
    const originalClasses = addBtn.className;
    const originalTitle = addBtn.title;

    // Show loading state
    addBtn.disabled = true;
    addBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    addBtn.className =
      "add-to-memory-btn text-gray-400 px-2 focus:outline-none transition-colors cursor-not-allowed";
    addBtn.title = "Đang xử lý...";

    // Function to restore original state
    const restoreOriginalState = () => {
      addBtn.disabled = false;
      addBtn.innerHTML = originalHTML;
      addBtn.className = originalClasses;
      addBtn.title = originalTitle;
    };

    return fetch(`${_config.apiBase}/${_config.productId}/${questionId}`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
      },
    })
      .then((response) => response.json())
      .then((data) => {
        if (data.success) {
          _showSuccessToast("Đã xóa câu hỏi khỏi danh sách ghi nhớ");
          // Cập nhật UI để hiển thị trạng thái chưa được thêm vào memory
          addBtn.disabled = false;
          addBtn.innerHTML = '<i class="far fa-bookmark"></i>';
          addBtn.className =
            "add-to-memory-btn text-gray-400 hover:text-indigo-500 px-2 focus:outline-none transition-colors";
          addBtn.title = "Thêm vào ghi nhớ";
        } else {
          restoreOriginalState();
          _showErrorToast(data.message || "Không thể xóa câu hỏi");
        }
        return data;
      })
      .catch((error) => {
        console.error("Error removing question from memory:", error);
        restoreOriginalState();
        _showErrorToast("Đã xảy ra lỗi khi xóa câu hỏi");
        throw error;
      });
  }

  // Public API
  const publicAPI = {
    init: _init,
    loadMemoryQuestions: _loadMemoryQuestions,
    startMemoryPractice: startMemoryPractice,
    closeMemoryPracticeModal: closeMemoryPracticeModal,
    startMemoryExam: startMemoryExam,
    deleteBulkMemoryQuestions: deleteBulkMemoryQuestions,

    // Helper cho tab Đề cương
    addQuestion: function (questionId, examId) {
      const addBtn = document.querySelector(
        `[data-question-id="${questionId}"] .add-to-memory-btn`
      );

      if (!addBtn) return;

      // Prevent multiple clicks
      if (addBtn.disabled) return;

      // Store original state
      const originalHTML = addBtn.innerHTML;
      const originalClasses = addBtn.className;
      const originalTitle = addBtn.title;

      // Show loading state
      addBtn.disabled = true;
      addBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
      addBtn.className =
        "add-to-memory-btn text-gray-400 px-2 focus:outline-none transition-colors cursor-not-allowed";
      addBtn.title = "Đang xử lý...";

      // Function to restore original state
      const restoreOriginalState = () => {
        addBtn.disabled = false;
        addBtn.innerHTML = originalHTML;
        addBtn.className = originalClasses;
        addBtn.title = originalTitle;
      };

      // Thêm câu hỏi vào danh sách ghi nhớ
      fetch(`${_config.apiBase}/${_config.productId}/add`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          questionId: questionId,
          examId: examId || null,
          source: "manual",
        }),
      })
        .then((response) => response.json())
        .then((data) => {
          if (data.success) {
            _showSuccessToast("Đã thêm câu hỏi vào danh sách ghi nhớ");
            // Cập nhật icon để hiển thị đã bookmark
            addBtn.disabled = false;
            addBtn.innerHTML = '<i class="fas fa-bookmark"></i>';
            addBtn.className =
              "add-to-memory-btn text-indigo-600 hover:text-indigo-700 px-2 focus:outline-none transition-colors added";
            addBtn.title = "Xóa khỏi ghi nhớ";
          } else if (data.duplicate) {
            // Khôi phục trạng thái trước khi hiển thị confirmation
            restoreOriginalState();
            // Hiển thị confirmation dialog
            if (confirm(data.message)) {
              // Người dùng chọn xóa câu hỏi khỏi memory
              _removeFromMemory(questionId, examId);
            }
          } else {
            restoreOriginalState();
            _showErrorToast(data.message || "Không thể thêm câu hỏi");
          }
        })
        .catch((error) => {
          console.error("Error adding question to memory:", error);
          restoreOriginalState();
          _showErrorToast("Đã có lỗi xảy ra");
        });
    },

    // Helper để xóa câu hỏi khỏi memory
    removeQuestion: function (questionId) {
      return _removeFromMemory(questionId);
    },

    // Filter helpers
    setGroupFilter: function (group) {
      _state.groupFilter = group;
      _filterQuestions();
    },

    clearSearch: function () {
      if (_dom.searchInput) {
        _dom.searchInput.value = "";
        _state.searchTerm = "";
        _filterQuestions();
      }
    },

    refresh: function () {
      _loadMemoryQuestions();
    },
  };

  // Expose public API to window
  window.MemoryExam = publicAPI;

  // Global functions for UI interaction
  window.filterMemoryByGroup = function () {
    const groupFilter = document.getElementById("memoryGroupFilter");
    if (groupFilter && window.MemoryExam) {
      window.MemoryExam.setGroupFilter(groupFilter.value);
    }
  };

  // Expose addToMemory helper globally for use from other modules
  window.addToMemory = function (questionId, examId) {
    publicAPI.addQuestion(questionId, examId);
  };

  // Auto-initialize on DOMContentLoaded if we're on memory tab
  document.addEventListener("DOMContentLoaded", function () {
    const currentTab = document
      .querySelector('meta[name="current-tab"]')
      ?.getAttribute("content");
    if (currentTab === "memory") {
      publicAPI.init();
    }
  });
})(window);
