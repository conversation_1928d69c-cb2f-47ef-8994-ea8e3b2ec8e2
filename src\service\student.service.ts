import mongoose from "mongoose";

// <PERSON><PERSON><PERSON> định có model Student
const Student = mongoose.model("Student");

export const getStudentByEmail = async (email: string) => {
  try {
    const student = await Student.findOne({ email }).lean();
    return student;
  } catch (error) {
    console.error("Error getting student by email:", error);
    throw error;
  }
};

export const getStudentCourses = async (studentId: string) => {
  try {
    const student = await Student.findById(studentId)
      .populate("courses.productId")
      .lean();

    if (!student) {
      return [];
    }

    return student.courses || [];
  } catch (error) {
    console.error("Error getting student courses:", error);
    throw error;
  }
};

export default {
  getStudentByEmail,
  getStudentCourses,
};
