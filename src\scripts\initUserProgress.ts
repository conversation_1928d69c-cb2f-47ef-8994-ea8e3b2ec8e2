import mongoose from "mongoose";
import dotenv from "dotenv";
import { recalculateAllProgress } from "../services/userProgress.service";

// Load environment variables
dotenv.config();

// Connect to MongoDB
mongoose
  .connect(process.env.MONGODB_URI || "mongodb://localhost:27017/google-auth")
  .then(async () => {
    console.log("📚 Kết nối MongoDB thành công");

    console.log("🔄 Bắt đầu khởi tạo dữ liệu tiến độ học tập...");

    try {
      // Import các model cần thiết
      const User = require("../models/User").default;
      const Product = require("../models/products").default;
      const Exam = require("../models/exam").default;
      const ExamHistory = require("../models/ExamHistory").default;
      const UserProgress = require("../models/UserProgress").default;

      // L<PERSON>y danh sách người dùng
      const users = await User.find({});
      console.log(`Tìm thấy ${users.length} người dùng`);

      // Lấy danh sách sản phẩm (môn học)
      const products = await Product.find({});
      console.log(`Tìm thấy ${products.length} môn học`);

      // Xóa toàn bộ dữ liệu UserProgress hiện có
      await UserProgress.deleteMany({});
      console.log("Đã xóa toàn bộ dữ liệu UserProgress hiện có");

      // Tính toán lại tiến độ học tập cho tất cả người dùng
      const result = await recalculateAllProgress();

      if (result) {
        console.log("✅ Khởi tạo dữ liệu tiến độ học tập thành công!");
      } else {
        console.error("❌ Khởi tạo dữ liệu tiến độ học tập thất bại!");
      }

      // Kiểm tra kết quả
      const progressCount = await UserProgress.countDocuments();
      console.log(`Số bản ghi UserProgress sau khi khởi tạo: ${progressCount}`);

      // Hiển thị một số bản ghi mẫu
      const sampleProgress = await UserProgress.find().limit(5).lean();
      console.log(
        "Mẫu dữ liệu UserProgress:",
        JSON.stringify(sampleProgress, null, 2)
      );
    } catch (error) {
      console.error("❌ Lỗi khi khởi tạo dữ liệu tiến độ học tập:", error);
    }

    // Đóng kết nối
    await mongoose.connection.close();
    console.log("📚 Đã đóng kết nối MongoDB");
    process.exit(0);
  })
  .catch((err) => {
    console.error("❌ Lỗi kết nối MongoDB:", err);
    process.exit(1);
  });
