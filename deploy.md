# Hướng dẫn triển khai ứng dụng lên Ubuntu 22.04

## Chuẩn bị Server

1. C<PERSON><PERSON> nh<PERSON><PERSON> hệ thống:

```bash
sudo apt update && sudo apt upgrade -y
```

2. Cài đặt Docker và Docker Compose:

```bash
# Cài đặt các packages cần thiết
sudo apt install apt-transport-https ca-certificates curl software-properties-common -y

# Thêm GPG key của Docker
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo apt-key add -

# Thêm repository Docker
sudo add-apt-repository "deb [arch=amd64] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable"

# Cập nhật package database
sudo apt update

# Cài đặt Docker
sudo apt install docker-ce -y

# Cài đặt Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.15.1/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose

# Cấp quyền thực thi cho Docker Compose
sudo chmod +x /usr/local/bin/docker-compose

# Thêm user hiện tại vào group docker
sudo usermod -aG docker ${USER}
```

## Triển khai ứng dụng

1. Clone repository về server:

```bash
git clone <repository-url> auth-app
cd auth-app
```

2. Tạo file .env chứa các biến môi trường:

```bash
cp exam.env .env
```

3. Chỉnh sửa file .env với thông tin thực tế:

```bash
nano .env
```

Đảm bảo các thông tin sau được cấu hình đúng:

- `MONGODB_URI`: URL kết nối MongoDB đã có sẵn (cấu trúc: ************************************:port/database)
- `GOOGLE_CLIENT_ID` và `GOOGLE_CLIENT_SECRET`: Thông tin từ Google Cloud Console
- `CALLBACK_URL`: URL callback thực tế (ví dụ: https://your-domain.com/auth/google/callback)
- `JWT_SECRET`: Mã bí mật để ký JWT token (nên tạo một chuỗi ngẫu nhiên dài)

4. Build và chạy Docker Compose:

```bash
docker-compose up -d
```

5. Kiểm tra logs để đảm bảo ứng dụng chạy đúng:

```bash
docker-compose logs -f app
```

## Cấu hình Nginx (Tùy chọn nếu muốn dùng domain)

1. Cài đặt Nginx:

```bash
sudo apt install nginx -y
```

2. Tạo cấu hình Nginx cho ứng dụng:

```bash
sudo nano /etc/nginx/sites-available/auth-app
```

3. Thêm nội dung sau:

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

4. Kích hoạt cấu hình và khởi động lại Nginx:

```bash
sudo ln -s /etc/nginx/sites-available/auth-app /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

5. Cấu hình SSL với Certbot (tùy chọn nhưng khuyến nghị):

```bash
sudo apt install certbot python3-certbot-nginx -y
sudo certbot --nginx -d your-domain.com
```

## Bảo trì

### Cập nhật ứng dụng

```bash
cd auth-app
git pull
docker-compose down
docker-compose up -d --build
```

### Khởi động lại ứng dụng

```bash
docker-compose restart
```

### Xem logs

```bash
docker-compose logs -f app
```
