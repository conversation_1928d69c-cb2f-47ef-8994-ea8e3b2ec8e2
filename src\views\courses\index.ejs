<!-- Hide default layout footer for courses page -->
<style>
  /* Ẩn footer mặc định của layout cho trang courses */
  body > footer {
    display: none !important;
  }

  /* Custom animations */
  .fade-in {
    animation: fadeIn 0.6s ease-in-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .card-hover {
    transition: all 0.3s ease;
  }

  .card-hover:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
      0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  .pulse-animation {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .shimmer {
    background: linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
  }

  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }

  .btn-primary {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  }

  .btn-primary:hover {
    background: linear-gradient(135deg, #1d4ed8, #1e40af);
  }

  .hidden {
    display: none !important;
  }
</style>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
  <!-- Header -->
  <div class="text-center mb-12 fade-in">
    <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
      Danh sách môn học
      <span
        class="bg-gradient-to-r from-blue-600 to-teal-600 bg-clip-text text-transparent"
        >HMC</span
      >
    </h1>
    <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
      Khám phá các môn học chất lượng cao được thiết kế riêng cho sinh viên
      <strong>Cao đẳng YTHN</strong> với tài liệu ôn tập, quizizz và form luyện
      tập cơ bản đến nâng cao.
    </p>

    <!-- Quick stats -->
    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-2xl mx-auto mt-8">
      <div class="text-center">
        <div class="text-2xl font-bold text-blue-600">50+</div>
        <div class="text-sm text-gray-600">Môn học</div>
      </div>
      <div class="text-center">
        <div class="text-2xl font-bold text-teal-600">2000+</div>
        <div class="text-sm text-gray-600">Sinh viên</div>
      </div>
      <div class="text-center">
        <div class="text-2xl font-bold text-purple-600">1000+</div>
        <div class="text-sm text-gray-600">Quizizz</div>
      </div>
      <div class="text-center">
        <div class="text-2xl font-bold text-orange-600">24/7</div>
        <div class="text-sm text-gray-600">Hỗ trợ</div>
      </div>
    </div>
  </div>

  <!-- Search and Filters -->
  <div class="bg-white rounded-2xl shadow-lg p-6 mb-8 fade-in">
    <div class="flex flex-col lg:flex-row gap-6">
      <!-- Search -->
      <div class="flex-1">
        <div class="relative">
          <i
            data-lucide="search"
            class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"
          ></i>
          <input
            type="text"
            placeholder="Tìm kiếm môn học: Giải phẫu, Sinh lý, Dược lý, Điều dưỡng..."
            id="search-input"
            class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
          />
        </div>
      </div>
    </div>
  </div>

  <!-- Results count -->
  <div class="mb-6">
    <p class="text-gray-600">
      Hiển thị <span id="results-count" class="font-semibold">tất cả</span> môn
      học
    </p>
  </div>

  <!-- Subjects Grid -->
  <div id="subjects-grid" class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
    <% if (products && products.length > 0) { %> <% products.forEach(product =>
    { %>
    <!-- Subject Card -->
    <div
      class="subject-card bg-white rounded-2xl shadow-lg overflow-hidden card-hover group fade-in"
      data-category="medical"
      data-rating="4.8"
      data-price="30000"
      data-students="1250"
    >
      <!-- Image & Badge -->
      <div class="relative">
        <img
          src="<%= product.image %>"
          alt="<%= product.name %> - Tài liệu ôn thi và quizizz"
          class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
        />
        <div
          class="absolute top-4 right-4 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-semibold"
        >
          -23%
        </div>
      </div>

      <!-- Content -->
      <div class="p-6">
        <!-- Title -->
        <div class="mb-3">
          <h3
            class="text-xl font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors duration-200"
          >
            <%= product.name %>
          </h3>
        </div>

        <!-- Stats -->
        <div
          class="flex items-center justify-between mb-4 text-sm text-gray-500"
        >
          <div class="flex items-center space-x-1">
            <i class="fas fa-star text-yellow-400"></i>
            <span class="font-medium">5</span>
          </div>
          <div class="flex items-center space-x-1">
            <i class="fas fa-users text-gray-400 w-4 h-4"></i>
            <span><%= Math.floor(Math.random() * 1001) + 1000 %></span>
          </div>
          <div class="flex items-center space-x-1">
            <i class="fas fa-clock text-gray-400 w-4 h-4"></i>
            <span>45 giờ</span>
          </div>
        </div>

        <!-- Highlights -->
        <div class="mb-4">
          <div class="flex flex-wrap gap-1">
            <span class="bg-blue-50 text-blue-700 px-2 py-1 rounded text-xs"
              >✓ Quizizz</span
            >
            <span class="bg-blue-50 text-blue-700 px-2 py-1 rounded text-xs"
              >✓ Google form</span
            >
            <span class="bg-blue-50 text-blue-700 px-2 py-1 rounded text-xs"
              >✓ Ghi nhớ</span
            >
            <span class="bg-blue-50 text-blue-700 px-2 py-1 rounded text-xs"
              >✓ Đáp án</span
            >
          </div>
        </div>

        <!-- CTA Buttons -->
        <div class="space-y-3">
          <a
            href="/course/<%= product._id %>/exams"
            class="w-full btn-primary text-white font-bold py-4 px-6 rounded-xl transition-all duration-200 transform hover:scale-105 hover:shadow-lg flex items-center justify-center space-x-2"
          >
            <i class="fas fa-play w-5 h-5"></i>
            <span>Học ngay</span>
          </a>

          <div class="grid grid-cols-2 gap-2">
            <a
              href="https://m.me/nvtai.kma"
              target="_blank"
              class="bg-green-50 hover:bg-green-100 text-green-700 font-semibold py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center space-x-2 border border-green-200 hover:border-green-300"
              id="try-course-btn"
              onclick="event.preventDefault(); alert('Tính năng này đang phát triển, hãy ấn vào tư vấn liên hệ admin để dùng thử');"
            >
              <i class="fas fa-play w-4 h-4"></i>
              <span class="text-xs">Học thử</span>
            </a>

            <a
              href="https://m.me/nvtai.kma"
              target="_blank"
              class="bg-gray-50 hover:bg-gray-100 text-gray-700 font-semibold py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center space-x-2 border border-gray-200 hover:border-gray-300"
            >
              <i class="fas fa-comment w-4 h-4"></i>
              <span class="text-xs">Tư vấn</span>
            </a>
          </div>
        </div>
      </div>
    </div>
    <% }); %> <% } else { %>
    <div class="col-span-full text-center py-10">
      <i class="fas fa-book-open text-5xl text-gray-300 mb-4"></i>
      <p class="text-gray-500">Chưa có khóa học nào được đăng tải.</p>
    </div>
    <% } %>
  </div>

  <!-- Contact CTA -->
  <div
    class="mb-16 mt-16 bg-gradient-to-r from-blue-600 to-teal-600 rounded-2xl p-8 text-white text-center fade-in"
  >
    <h3 class="text-2xl font-bold mb-4">Cần tư vấn chọn môn học phù hợp?</h3>
    <p class="text-blue-100 mb-6 max-w-2xl mx-auto">
      Đội ngũ tư vấn giáo dục của chúng tôi sẽ giúp bạn lựa chọn các môn học phù
      hợp với mục tiêu học tập và ngành nghề mong muốn
    </p>
    <div class="flex flex-col sm:flex-row gap-4 justify-center">
      <button
        class="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors duration-200 flex items-center justify-center space-x-2"
      >
        <i class="fas fa-phone w-5 h-5"></i>
        <span>Gọi tư vấn: ************</span>
      </button>
      <a
        href="https://m.me/nvtai.kma"
        target="_blank"
        class="bg-transparent border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-all duration-200 flex items-center justify-center space-x-2"
      >
        <i class="fas fa-comment w-5 h-5"></i>
        <span>Chat với tư vấn viên</span>
      </a>
    </div>
  </div>
</div>

<script>
  // Search and filter functionality
  const searchInput = document.getElementById("search-input");
  const subjectCards = document.querySelectorAll(".subject-card");
  const resultsCount = document.getElementById("results-count");

  function filterSearch() {
    const searchTerm = searchInput.value.toLowerCase();
    let visibleCards = 0;

    // Filter cards
    subjectCards.forEach((card) => {
      const title = card.querySelector("h3").textContent.toLowerCase();
      const matchesSearch = title.includes(searchTerm);

      if (matchesSearch) {
        card.style.display = "block";
        visibleCards++;
      } else {
        card.style.display = "none";
      }
    });

    // Update results count
    resultsCount.textContent = visibleCards;
  }

  // Event listeners for filters
  searchInput.addEventListener("input", filterSearch);
</script>

<!-- Footer -->
<footer class="bg-gray-900 text-white">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
      <!-- Logo & Description -->
      <div class="col-span-1 md:col-span-2 lg:col-span-1">
        <div class="flex items-center space-x-2 mb-4">
          <div class="bg-blue-600 text-white p-2 rounded-lg">
            <i class="fas fa-book-open"></i>
          </div>
          <span class="text-2xl font-bold">Test HMC</span>
        </div>
        <p class="text-gray-400 mb-6">
          Nền tảng học tập trực tuyến chuyên biệt cho
          <strong>sinh viên Cao đẳng YTHN</strong>, có đầy đủ tài liệu để ôn tập
          hiệu quả.
        </p>
        <div class="mb-4">
          <p class="text-gray-400 text-sm">
            🏥 Liên kết tới:
            <a
              href="https://yhn.edu.vn/"
              target="_blank"
              rel="noopener noreferrer"
              class="text-blue-400 hover:text-blue-300"
              >Cao đẳng YTHN</a
            >
          </p>
        </div>
        <div class="flex space-x-4">
          <i
            class="fab fa-facebook text-xl text-gray-400 hover:text-blue-400 cursor-pointer transition-colors duration-200"
          ></i>
          <i
            class="fab fa-twitter text-xl text-gray-400 hover:text-blue-400 cursor-pointer transition-colors duration-200"
          ></i>
          <i
            class="fab fa-instagram text-xl text-gray-400 hover:text-pink-400 cursor-pointer transition-colors duration-200"
          ></i>
        </div>
      </div>

      <!-- Tài liệu HMC -->
      <div>
        <h3 class="text-lg font-semibold mb-4">Tài liệu HMC</h3>
        <ul class="space-y-2">
          <li>
            <a
              href="/courses"
              class="text-gray-400 hover:text-white transition-colors duration-200"
              >Tài liệu ôn tập</a
            >
          </li>
          <li>
            <a
              href="/courses"
              class="text-gray-400 hover:text-white transition-colors duration-200"
              >Đề cương ôn tập</a
            >
          </li>
          <li>
            <a
              href="/courses"
              class="text-gray-400 hover:text-white transition-colors duration-200"
              >Giáo trình HMC</a
            >
          </li>
          <li>
            <a
              href="/courses"
              class="text-gray-400 hover:text-white transition-colors duration-200"
              >Luyện thi thử</a
            >
          </li>
        </ul>
      </div>

      <!-- Chuyên ngành -->
      <div>
        <h3 class="text-lg font-semibold mb-4">Chuyên ngành</h3>
        <ul class="space-y-2">
          <li>
            <a
              href="/courses"
              class="text-gray-400 hover:text-white transition-colors duration-200"
              >Điều dưỡng</a
            >
          </li>
          <li>
            <a
              href="/courses"
              class="text-gray-400 hover:text-white transition-colors duration-200"
              >Dược</a
            >
          </li>
          <li>
            <a
              href="/courses"
              class="text-gray-400 hover:text-white transition-colors duration-200"
              >Y sỹ đa khoa</a
            >
          </li>
          <li>
            <a
              href="/courses"
              class="text-gray-400 hover:text-white transition-colors duration-200"
              >Kỹ thuật xét nghiệm</a
            >
          </li>
          <li>
            <a
              href="/courses"
              class="text-gray-400 hover:text-white transition-colors duration-200"
              >Kỹ thuật hình ảnh</a
            >
          </li>
          <li>
            <a
              href="/courses"
              class="text-gray-400 hover:text-white transition-colors duration-200"
              >Phục hồi chức năng</a
            >
          </li>
          <li>
            <a
              href="/courses"
              class="text-gray-400 hover:text-white transition-colors duration-200"
              >Chăm sóc sắc đẹp</a
            >
          </li>
        </ul>
      </div>

      <!-- Liên hệ -->
      <div>
        <h3 class="text-lg font-semibold mb-4">Hỗ trợ</h3>
        <div class="space-y-3 mb-4">
          <div class="flex items-center space-x-3">
            <i class="fas fa-envelope text-gray-400"></i>
            <span class="text-gray-400"><EMAIL></span>
          </div>
          <div class="flex items-center space-x-3">
            <i class="fas fa-phone text-gray-400"></i>
            <span class="text-gray-400">************</span>
          </div>
          <div class="flex items-center space-x-3">
            <i class="fas fa-map-marker-alt text-gray-400"></i>
            <span class="text-gray-400">Hà Nội, Việt Nam</span>
          </div>
        </div>
        <ul class="space-y-2">
          <li>
            <a
              href="https://m.me/nvtai.kma"
              target="_blank"
              class="text-gray-400 hover:text-white transition-colors duration-200"
              >Trung tâm trợ giúp</a
            >
          </li>
          <li>
            <a
              href="https://m.me/nvtai.kma"
              target="_blank"
              class="text-gray-400 hover:text-white transition-colors duration-200"
              >Câu hỏi thường gặp</a
            >
          </li>
          <li>
            <a
              href="#"
              class="text-gray-400 hover:text-white transition-colors duration-200"
              >Chính sách bảo mật</a
            >
          </li>
        </ul>
      </div>
    </div>

    <div class="border-t border-gray-800 mt-12 pt-8 text-center">
      <p class="text-gray-400 mb-2">
        © 2024 Test HMC - Nền tảng học tập cho sinh viên Cao đẳng Y tế Hà Nội.
        Tất cả quyền được bảo lưu.
      </p>
      <p class="text-gray-900 text-sm">
        Keywords: test hmc, cao đẳng y tế hà nội, điều dưỡng, dược, y sỹ đa
        khoa, kỹ thuật xét nghiệm, kỹ thuật hình ảnh, phục hồi chức năng, chăm
        sóc sắc đẹp
      </p>
    </div>
  </div>
</footer>
