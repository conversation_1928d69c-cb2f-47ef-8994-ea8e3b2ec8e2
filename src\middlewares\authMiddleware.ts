import { Request, Response, NextFunction } from "express";
import jwt from "jsonwebtoken";
import User, { IUser } from "../models/User";
import Session from "../models/Session";
import * as sessionService from "../services/sessionService";

// Mở rộng interface Request để bổ sung thuộc tính user, session và newToken
declare module "express" {
  interface Request {
    user?: IUser;
    session?: any;
    newToken?: string;
  }
}

interface JwtPayload {
  id: string;
  email: string;
  exp?: number; // Thêm trường exp để truy cập thời gian hết hạn
}

// Middleware xác thực JWT
export const authenticateToken = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    // Lấy token từ header hoặc cookies
    const token = req.cookies.jwt || req.headers.authorization?.split(" ")[1];

    if (!token) {
      // Kiểm tra nếu là API request hay view request
      if (isApiRequest(req)) {
        return res.status(401).json({ message: "Không có token xác thực!" });
      } else {
        return res.redirect("/login");
      }
    }

    // Tìm phiên hợp lệ trong database
    const session = await sessionService.findValidSession(token);

    if (!session) {
      // console.log(
      //   `Không tìm thấy phiên hợp lệ cho token: ${token.substring(0, 10)}...`
      // );

      // Xóa cookie nếu không có phiên hợp lệ
      res.clearCookie("jwt");

      // Trả về script để xóa localStorage khi phiên không hợp lệ
      if (!isApiRequest(req)) {
        const cleanupScript = `
          <script>
            // Xóa dữ liệu xác thực khỏi localStorage
            localStorage.removeItem('auth_status');
            localStorage.removeItem('user_id');
            localStorage.removeItem('session_id');
            console.log('Phiên đăng nhập không hợp lệ, đã xóa dữ liệu xác thực');
            
            // Chuyển hướng đến trang đăng nhập
            window.location.href = '/login?error=Phiên đăng nhập không hợp lệ hoặc đã hết hạn';
          </script>
        `;

        return res.send(`
          <!DOCTYPE html>
          <html>
            <head>
              <title>Đăng xuất...</title>
            </head>
            <body>
              <div style="text-align: center; margin-top: 100px;">
                <h2>Phiên đăng nhập đã hết hạn</h2>
                <p>Đang chuyển hướng về trang đăng nhập...</p>
              </div>
              ${cleanupScript}
            </body>
          </html>
        `);
      }

      if (isApiRequest(req)) {
        return res.status(401).json({
          message: "Phiên đăng nhập không hợp lệ hoặc đã hết hạn!",
          error: "invalid_session",
        });
      } else {
        return res.redirect("/login?error=Phiên đăng nhập không hợp lệ");
      }
    }

    // Xác thực token
    let decoded: JwtPayload;
    try {
      decoded = jwt.verify(
        token,
        process.env.JWT_SECRET as string
      ) as JwtPayload;
    } catch (error: any) {
      // Token đã hết hạn hoặc không hợp lệ, nhưng phiên vẫn còn trong database
      // Tạo token mới nếu phiên vẫn hợp lệ
      if (error.name === "TokenExpiredError" && session) {
        // Tạo token mới
        const user = await User.findById(session.userId);
        if (!user) {
          throw error; // Nếu không tìm thấy user, trả về lỗi ban đầu
        }

        // Tạo JWT token mới
        const newToken = sessionService.generateToken(user);

        // Cập nhật token mới vào session
        await Session.updateOne({ _id: session._id }, { token: newToken });

        // Cập nhật cookie với token mới
        res.cookie("jwt", newToken, {
          httpOnly: true,
          secure: process.env.NODE_ENV === "production",
          maxAge: 365 * 24 * 60 * 60 * 1000, // 1 năm
        });

        // Giải mã token mới để sử dụng
        decoded = jwt.verify(
          newToken,
          process.env.JWT_SECRET as string
        ) as JwtPayload;

        console.log("Token đã được làm mới tự động");
      } else {
        throw error; // Các lỗi khác với TokenExpiredError
      }
    }

    // Kiểm tra user và token có khớp không
    const user = await User.findById(decoded.id);
    if (!user) {
      if (isApiRequest(req)) {
        return res.status(404).json({ message: "Không tìm thấy người dùng!" });
      } else {
        return res.redirect("/login");
      }
    }

    // Kiểm tra phiên có thuộc về user này không
    if (session.userId.toString() !== user._id.toString()) {
      console.log(
        `Session userId mismatch: expected ${user._id}, got ${session.userId}`
      );

      if (isApiRequest(req)) {
        return res.status(401).json({
          message: "Phiên đăng nhập không hợp lệ!",
          error: "invalid_session",
        });
      } else {
        return res.redirect("/login?error=Phiên đăng nhập không hợp lệ");
      }
    }

    // Kiểm tra nếu token sắp hết hạn (còn dưới 1 giờ), tạo token mới
    if (decoded.exp) {
      const currentTime = Math.floor(Date.now() / 1000);
      const timeUntilExpiry = decoded.exp - currentTime;
      const oneHour = 60 * 60; // 1 giờ tính bằng giây

      if (timeUntilExpiry < oneHour) {
        // Tạo token mới
        const newToken = sessionService.generateToken(user);

        // Cập nhật token mới vào session
        await Session.updateOne({ _id: session._id }, { token: newToken });

        // Cập nhật cookie với token mới
        res.cookie("jwt", newToken, {
          httpOnly: true,
          secure: process.env.NODE_ENV === "production",
          maxAge: 365 * 24 * 60 * 60 * 1000, // 1 năm
        });

        console.log("Token đã được làm mới chủ động (còn dưới 1 giờ)");
      }
    }

    // Cập nhật thời gian hoạt động cuối cùng
    sessionService.updateLastActiveTime(token).catch((err) => {
      console.error("Error updating last active time:", err);
    });

    // Gán thông tin user và session vào request
    req.user = user;
    req.session = session;
    next();
  } catch (error: any) {
    console.error("Token verification error:", error);

    if (isApiRequest(req)) {
      if (error.name === "JsonWebTokenError") {
        return res.status(401).json({ message: "Token không hợp lệ!" });
      }
      if (error.name === "TokenExpiredError") {
        return res.status(401).json({ message: "Token đã hết hạn!" });
      }
      return res
        .status(500)
        .json({ message: "Lỗi xác thực!", error: error.message });
    } else {
      // Chuyển hướng đến trang đăng nhập với thông báo lỗi
      return res.redirect("/login?error=Phiên đăng nhập không hợp lệ");
    }
  }
};

// Middleware kiểm tra đăng nhập đồng thời
export const singleDeviceLogin = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const user = req.user;
    const newToken = req.newToken; // Token mới được tạo trong quá trình login

    // Kiểm tra xem người dùng đã có phiên đăng nhập nào chưa
    if (!user) {
      return res.status(400).json({
        message: "Thiếu thông tin người dùng!",
        error: "missing_user",
      });
    }

    const activeSessions = await Session.find({
      userId: user._id,
      isActive: true,
    });

    if (activeSessions.length > 0) {
      return res.status(400).json({
        message:
          "Tài khoản này đang được đăng nhập ở thiết bị khác. Vui lòng đăng xuất trước khi đăng nhập lại.",
        isAlreadyLoggedIn: true,
        activeSessions: activeSessions.length,
      });
    }

    next();
  } catch (error: any) {
    return res
      .status(500)
      .json({ message: "Lỗi kiểm tra thiết bị!", error: error.message });
  }
};

// Middleware chỉ cho phép kết nối SSE từ client có token hợp lệ
export const authenticateForSSE = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    // Lấy token từ cookie
    const token = req.cookies.jwt;
    const clientId = (req.query.clientId as string) || "unknown";

    // console.log(`SSE authentication for clientId: ${clientId}`);

    if (!token) {
      // console.log(
      //   `SSE connection rejected for clientId ${clientId}: No JWT token found`
      // );
      return res.status(401).end();
    }

    // Tìm phiên hợp lệ
    const session = await sessionService.findValidSession(token);

    // Cho phép kết nối SSE ngay cả khi phiên không hợp lệ để có thể gửi thông báo đăng xuất
    let user;

    try {
      // Xác thực token
      const decoded = jwt.verify(
        token,
        process.env.JWT_SECRET as string
      ) as JwtPayload;

      // Kiểm tra user tồn tại không
      user = await User.findById(decoded.id);

      if (!user) {
        // console.log(
        //   `SSE connection allowing for clientId ${clientId} despite invalid user`
        // );
      }
    } catch (err) {
      // console.log(
      //   `SSE token verification error for clientId ${clientId}, but allowing connection`
      // );
    }

    // Gán thông tin user vào request nếu có
    if (user) {
      req.user = user;
      // console.log(
      //   `SSE connection authenticated for user ${user._id} (${user.email}), clientId: ${clientId}`
      // );
    } else {
      // console.log(
      //   `SSE connection partially authenticated (missing user) for clientId: ${clientId}`
      // );
    }

    next();
  } catch (error: any) {
    const clientId = (req.query.clientId as string) || "unknown";
    // console.error(`SSE authentication error for clientId ${clientId}:`, error);

    // Vẫn cho phép kết nối để có thể gửi thông báo đăng xuất
    next();
  }
};

// Hàm hỗ trợ để phân biệt giữa API request và view request
function isApiRequest(req: Request): boolean {
  // Kiểm tra Accept header hoặc đường dẫn để xác định là API request
  const acceptHeader = req.headers.accept || "";
  const path = req.path || "";

  return (
    acceptHeader.includes("application/json") ||
    path.startsWith("/api/") ||
    path.includes("/session-events")
  );
}
