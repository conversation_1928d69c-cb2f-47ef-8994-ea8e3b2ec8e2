<div class="flex flex-col h-screen bg-gray-100">
  <!-- Header -->
  <header class="bg-indigo-600 shadow">
    <div
      class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 flex justify-between items-center py-4"
    >
      <div class="flex items-center">
        <h1 class="text-xl font-bold text-white">
          Làm lại câu hỏi sai: <%= exam.name %>
        </h1>
      </div>
      <div class="flex items-center text-white space-x-4">
        <div class="bg-indigo-700 px-3 py-1 rounded-full flex items-center">
          <i class="fas fa-clock mr-1"></i>
          <span id="timer">00:00:00</span>
        </div>
      </div>
    </div>
  </header>

  <!-- Main content -->
  <div class="flex-grow flex">
    <!-- Sidebar -->
    <div
      class="hidden lg:block bg-white w-64 border-r border-gray-200 shadow-sm"
    >
      <div class="p-4 border-b border-gray-200">
        <h2 class="text-lg font-medium text-gray-800">Thông tin bài thi</h2>
        <div class="mt-2 text-sm text-gray-600 space-y-1">
          <p><span class="font-medium">Học viên:</span> <%= student.email %></p>
          <p>
            <span class="font-medium">Tổng câu hỏi:</span>
            <span id="total-questions">0</span>
          </p>
          <p>
            <span class="font-medium">Câu hỏi sai:</span>
            <span id="totalWrongQuestions">0</span>
          </p>
        </div>
      </div>
      <div class="p-4 border-b border-gray-200">
        <div class="flex justify-between items-center mb-2">
          <h3 class="font-medium text-gray-700">Tiến độ làm bài</h3>
          <span class="text-sm text-gray-600">
            <span id="answered-questions">0</span>/<span id="total-questions-2"
              >0</span
            >
          </span>
        </div>
        <div class="h-2 bg-gray-200 rounded-full overflow-hidden">
          <div
            id="progressBar"
            class="h-full bg-indigo-600"
            style="width: 0%"
          ></div>
        </div>
      </div>
      <div class="p-4">
        <h3 class="font-medium text-gray-700 mb-3">Danh sách câu hỏi</h3>
        <div class="grid grid-cols-5 gap-2" id="question-list">
          <!-- Các câu hỏi sẽ được thêm bằng JavaScript -->
        </div>
      </div>
    </div>

    <!-- Main question area -->
    <div class="flex-grow flex flex-col p-4 md:p-6 overflow-y-auto">
      <!-- Exam container - Sẽ ẩn khi hiển thị kết quả -->
      <div id="exam-container" class="flex flex-col h-full">
        <div
          class="bg-white rounded-lg shadow-sm p-4 md:p-6 mb-4 flex items-center justify-between"
        >
          <div>
            <h2 class="text-lg font-medium text-gray-800">
              Câu hỏi <span id="current-question">1</span>
            </h2>
          </div>
          <div class="hidden md:block">
            <span
              class="bg-indigo-100 text-indigo-800 text-xs font-medium px-2 py-1 rounded"
            >
              Làm lại câu hỏi sai
            </span>
          </div>
        </div>

        <form id="exam-form" onsubmit="return false;">
          <div
            id="questions-container"
            class="question-card mb-4 flex-grow flex flex-col"
          >
            <!-- Câu hỏi sẽ được thêm bằng JavaScript -->
          </div>
        </form>
      </div>

      <!-- Result container - Sẽ hiển thị sau khi nộp bài -->
      <div id="result-container" class="hidden">
        <div class="bg-white rounded-lg shadow-sm overflow-hidden">
          <!-- Header kết quả -->
          <div class="bg-indigo-600 px-6 py-4 text-white">
            <h2 class="text-xl font-bold">Kết quả làm lại câu hỏi sai</h2>
          </div>

          <!-- Thông tin kết quả -->
          <div class="p-6">
            <div class="text-center mb-8">
              <div
                class="inline-flex items-center justify-center w-24 h-24 rounded-full bg-indigo-100 text-indigo-600 mb-4"
              >
                <i class="fas fa-award text-5xl"></i>
              </div>
              <h3 class="text-2xl font-bold text-gray-800 mb-2">Hoàn thành!</h3>
              <div class="text-5xl font-bold text-indigo-600 mt-2">
                <span id="score-percentage">0</span>%
              </div>
              <p class="text-gray-600 mt-2">
                <span id="correct-count">0</span>/<span id="total-count"
                  >0</span
                >
                câu đúng
              </p>
            </div>

            <div class="bg-gray-50 rounded-lg p-4 mb-6">
              <div class="flex justify-between items-center mb-2">
                <span class="text-gray-700">Thời gian làm bài:</span>
                <span class="font-medium text-indigo-600" id="examTime"
                  >0:00</span
                >
              </div>
            </div>

            <div class="mb-6">
              <h4 class="font-medium text-gray-800 mb-3">Tóm tắt câu hỏi</h4>
              <div id="summaryContainer" class="grid grid-cols-6 gap-2">
                <!-- Summary items will be added here -->
              </div>
            </div>

            <div class="bg-gray-50 rounded-lg p-4 mb-6">
              <h4 class="font-medium text-gray-800 mb-4">
                Xem lại câu trả lời
              </h4>
              <div
                id="answerResults"
                class="max-h-96 overflow-y-auto pr-2 space-y-4"
              >
                <!-- Review items will be added here -->
              </div>
            </div>

            <div class="flex justify-center">
              <a
                href="/home"
                class="bg-indigo-600 text-white px-6 py-2 rounded-full font-medium hover:bg-indigo-700 transition-colors"
              >
                Về trang chủ
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Warning popup: Chưa trả lời hết câu hỏi -->
  <div
    id="warning-popup"
    class="fixed inset-0 flex items-center justify-center z-50 hidden"
  >
    <div class="fixed inset-0 bg-black opacity-50"></div>
    <div
      class="bg-white rounded-lg shadow-xl p-6 max-w-md w-full z-10 relative"
    >
      <div class="mb-4 text-center">
        <div
          class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-yellow-100 mb-4"
        >
          <i class="fas fa-exclamation-triangle text-yellow-600 text-2xl"></i>
        </div>
        <h3 class="text-lg font-medium text-gray-900">
          Bạn chưa trả lời hết câu hỏi
        </h3>
        <p class="text-gray-500 mt-2">
          Bạn vẫn còn một số câu hỏi chưa trả lời:
        </p>
        <div class="mt-3" id="unanswered-questions">
          <!-- Danh sách câu hỏi chưa trả lời -->
        </div>
        <p class="text-gray-500 mt-2">
          Bạn có thể nhấn vào số thứ tự để quay lại câu hỏi đó.
        </p>
      </div>
      <div class="flex justify-center space-x-3">
        <button
          id="continue-button"
          class="bg-gray-200 hover:bg-gray-300 text-gray-800 py-2 px-4 rounded-lg transition-colors"
        >
          Tiếp tục làm bài
        </button>
        <button
          id="submit-anyway"
          class="bg-indigo-600 hover:bg-indigo-700 text-white py-2 px-4 rounded-lg transition-colors"
        >
          Nộp bài luôn
        </button>
      </div>
    </div>
  </div>

  <!-- Submit confirm popup -->
  <div
    id="exitConfirmPopup"
    class="fixed inset-0 flex items-center justify-center z-50 hidden"
  >
    <div class="fixed inset-0 bg-black opacity-50"></div>
    <div
      class="bg-white rounded-lg shadow-xl p-6 max-w-md w-full z-10 relative"
    >
      <div class="mb-4 text-center">
        <div
          class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-indigo-100 mb-4"
        >
          <i class="fas fa-question-circle text-indigo-600 text-2xl"></i>
        </div>
        <h3 class="text-lg font-medium text-gray-900" id="exitConfirmTitle">
          Xác nhận nộp bài
        </h3>
        <p class="text-gray-500 mt-2" id="exitConfirmMessage">
          Bạn có chắc chắn muốn nộp bài thi? Thao tác này không thể hoàn tác.
        </p>
      </div>
      <div class="flex justify-center space-x-3">
        <button
          id="exitConfirmStayBtn"
          class="bg-gray-200 hover:bg-gray-300 text-gray-800 py-2 px-4 rounded-lg transition-colors"
        >
          Hủy
        </button>
        <button
          id="exitConfirmLeaveBtn"
          class="bg-indigo-600 hover:bg-indigo-700 text-white py-2 px-4 rounded-lg transition-colors"
        >
          Nộp bài
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Dữ liệu bài thi -->
<script type="application/json" id="exam-data">
  <%- JSON.stringify({
    id: exam._id,
    examId: exam._id,
    duration: exam.duration || 30
  }) %>
</script>

<!-- CSS cho danh sách câu hỏi -->
<style>
  footer {
    display: none;
  }
  .question-item {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: #f3f4f6;
    color: #6b7280;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    border: 2px solid transparent;
  }

  .question-item.current {
    border-color: #4f46e5;
    color: #4f46e5;
  }

  .question-item.answered {
    background-color: #e0e7ff;
    color: #4f46e5;
  }

  .question-item.answered.current {
    background-color: #4f46e5;
    color: white;
    border-color: #4338ca;
  }

  .option-red.selected {
    border-color: #ef4444 !important;
    background-color: #fee2e2 !important;
  }

  .option-blue.selected {
    border-color: #3b82f6 !important;
    background-color: #dbeafe !important;
  }

  .option-green.selected {
    border-color: #10b981 !important;
    background-color: #d1fae5 !important;
  }

  .option-yellow.selected {
    border-color: #f59e0b !important;
    background-color: #fef3c7 !important;
  }

  .highlight {
    animation: highlight-animation 1.5s;
  }

  @keyframes highlight-animation {
    0% {
      background-color: #e0e7ff;
    }
    50% {
      background-color: #c7d2fe;
    }
    100% {
      background-color: transparent;
    }
  }
</style>

<!-- JavaScript để xử lý mã hóa -->
<script src="/js/client-encryption.js"></script>

<!-- JavaScript để xử lý trang làm lại câu hỏi sai -->
<script src="/js/retry-wrong-questions-google-form.js"></script>

<!-- Script bảo mật toàn cục -->
<script src="/js/security-measures.js"></script>
<script>
  // Thiết lập bảo mật ngay khi DOM sẵn sàng
  document.addEventListener("DOMContentLoaded", function () {
    // Tự động phát hiện ngữ cảnh và thiết lập bảo mật với chuyển hướng khi phát hiện DevTools
    GlobalSecurityMeasures({
      contextMessage: "làm lại câu sai Google Form",
      redirectOnDevTools: true, // Bật chuyển hướng khi phát hiện DevTools
      redirectUrl: "/home", // Chuyển về trang chủ
      devToolsThreshold: 200, // Tăng ngưỡng để giảm false positive
    });
  });
</script>
