{"version": 3, "file": "quizizz-practice-ui.js", "sourceRoot": "", "sources": ["../../../src/public/js/quizizz-practice-ui.js"], "names": [], "mappings": "AAAA;;;GAGG;AACH,CAAC,UAAU,MAAM;IACf,YAAY,CAAC;IAEb,sBAAsB;IACtB,MAAM,MAAM,GAAG;QACb,OAAO,EAAE,2BAA2B;QACpC,SAAS,EAAE,yBAAyB;QACpC,IAAI,EAAE,iBAAiB;QACvB,MAAM,EAAE,mBAAmB;QAC3B,QAAQ,EAAE,qBAAqB;KAChC,CAAC;IAEF,0BAA0B;IAC1B,MAAM,UAAU,GAAG;QACjB,kBAAkB,EAAE,GAAG;QACvB,WAAW,EAAE,GAAG;QAChB,gBAAgB,EAAE,IAAI;QACtB,mBAAmB,EAAE,IAAI;KAC1B,CAAC;IAEF,IAAI,YAAY,GAAG,IAAI,CAAC;IACxB,IAAI,UAAU,GAAG,EAAE,CAAC;IAEpB;;OAEG;IACH,SAAS,MAAM;QACb,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;QAE1D,iBAAiB;QACjB,aAAa,EAAE,CAAC;QAEhB,8BAA8B;QAC9B,yBAAyB,EAAE,CAAC;QAE5B,2BAA2B;QAC3B,sBAAsB,EAAE,CAAC;QAEzB,qBAAqB;QACrB,gBAAgB,EAAE,CAAC;QAEnB,wBAAwB;QACxB,aAAa,EAAE,CAAC;IAClB,CAAC;IAED;;OAEG;IACH,SAAS,aAAa;QACpB,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,EAAE;YAC5C,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;YAC7B,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;YACvB,KAAK,CAAC,MAAM,GAAG,GAAG,CAAC;YACnB,UAAU,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QAC1B,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,SAAS,SAAS,CAAC,QAAQ;QACzB,IAAI,CAAC,YAAY,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC;YAAE,OAAO;QAEnD,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,UAAU,CAAC,QAAQ,CAAC,CAAC;YACnC,KAAK,CAAC,WAAW,GAAG,CAAC,CAAC;YACtB,KAAK,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC,CAAC,CAAC,CAAC;QACpE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;QAC/C,CAAC;IACH,CAAC;IAED;;OAEG;IACH,SAAS,yBAAyB;QAChC,oCAAoC;QACpC,QAAQ,CAAC,gBAAgB,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE,EAAE;YAC3C,IAAI,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE,CAAC;gBACrC,MAAM,IAAI,GAAG,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;gBAC9C,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;oBACzC,cAAc,CAAC,IAAI,CAAC,CAAC;gBACvB,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,gBAAgB,CAAC,UAAU,EAAE,CAAC,CAAC,EAAE,EAAE;YAC1C,IAAI,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE,CAAC;gBACrC,MAAM,IAAI,GAAG,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;gBAC9C,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAC1B,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,0BAA0B;QAC1B,QAAQ,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;YACvC,IAAI,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,+BAA+B,CAAC,EAAE,CAAC;gBACtD,eAAe,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,+BAA+B,CAAC,EAAE,CAAC,CAAC,CAAC;YACxE,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,SAAS,cAAc,CAAC,IAAI;QAC1B,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,8BAA8B,CAAC;QACtD,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,gCAAgC,CAAC;IAC1D,CAAC;IAED;;OAEG;IACH,SAAS,iBAAiB,CAAC,IAAI;QAC7B,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YACzC,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,EAAE,CAAC;YAC1B,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,EAAE,CAAC;QAC5B,CAAC;IACH,CAAC;IAED;;OAEG;IACH,SAAS,eAAe,CAAC,OAAO,EAAE,KAAK;QACrC,MAAM,IAAI,GAAG,OAAO,CAAC,qBAAqB,EAAE,CAAC;QAC7C,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAC/C,MAAM,CAAC,GAAG,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC;QAC/C,MAAM,CAAC,GAAG,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,GAAG,CAAC,CAAC;QAE9C,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC7C,MAAM,CAAC,KAAK,CAAC,OAAO,GAAG;;eAEZ,IAAI;gBACH,IAAI;cACN,CAAC;aACF,CAAC;;;;;;;KAOT,CAAC;QAEF,OAAO,CAAC,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC;QACpC,OAAO,CAAC,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAClC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QAE5B,UAAU,CAAC,GAAG,EAAE;YACd,MAAM,CAAC,MAAM,EAAE,CAAC;QAClB,CAAC,EAAE,GAAG,CAAC,CAAC;IACV,CAAC;IAED;;OAEG;IACH,SAAS,sBAAsB;QAC7B,QAAQ,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC,CAAC,EAAE,EAAE;;YACzC,8BAA8B;YAC9B,IAAI,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,GAAG,EAAE,CAAC;gBACjC,CAAC,CAAC,cAAc,EAAE,CAAC;gBACnB,MAAM,WAAW,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBACxC,mBAAmB,CAAC,WAAW,CAAC,CAAC;YACnC,CAAC;YAED,yCAAyC;YACzC,IAAI,CAAC,CAAC,GAAG,KAAK,GAAG,EAAE,CAAC;gBAClB,CAAC,CAAC,cAAc,EAAE,CAAC;gBACnB,MAAM,WAAW,GAAG,QAAQ,CAAC,aAAa,CAAC,oBAAoB,CAAC,CAAC;gBACjE,IAAI,WAAW,EAAE,CAAC;oBAChB,WAAW,CAAC,KAAK,EAAE,CAAC;gBACtB,CAAC;YACH,CAAC;YAED,4BAA4B;YAC5B,IAAI,CAAC,CAAC,GAAG,KAAK,QAAQ,EAAE,CAAC;gBACvB,CAAC,CAAC,cAAc,EAAE,CAAC;gBACnB,IAAI,MAAM,CAAC,eAAe,EAAE,CAAC;oBAC3B,MAAA,QAAQ,CAAC,cAAc,CAAC,YAAY,CAAC,0CAAE,KAAK,EAAE,CAAC;gBACjD,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,SAAS,mBAAmB,CAAC,KAAK;QAChC,MAAM,OAAO,GAAG,QAAQ,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC;QAC1D,IAAI,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YACrE,OAAO,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,CAAC;QACzB,CAAC;IACH,CAAC;IAED;;OAEG;IACH,SAAS,gBAAgB;QACvB,6BAA6B;QAC7B,MAAM,WAAW,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QACrD,WAAW,CAAC,SAAS,GAAG,cAAc,CAAC;QACvC,WAAW,CAAC,SAAS,GAAG,kCAAkC,CAAC;QAC3D,WAAW,CAAC,KAAK,GAAG,kBAAkB,CAAC;QAEvC,WAAW,CAAC,gBAAgB,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;QAEnD,gBAAgB;QAChB,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;QACtD,IAAI,MAAM,EAAE,CAAC;YACX,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;QAClC,CAAC;IACH,CAAC;IAED;;OAEG;IACH,SAAS,WAAW;QAClB,YAAY,GAAG,CAAC,YAAY,CAAC;QAC7B,MAAM,IAAI,GAAG,QAAQ,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC;QACvD,IAAI,IAAI,EAAE,CAAC;YACT,IAAI,CAAC,SAAS,GAAG,YAAY,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,oBAAoB,CAAC;QAC5E,CAAC;QAED,8BAA8B;QAC9B,IAAI,YAAY,EAAE,CAAC;YACjB,SAAS,CAAC,MAAM,CAAC,CAAC;QACpB,CAAC;IACH,CAAC;IAED;;OAEG;IACH,SAAS,aAAa;QACpB,4BAA4B;QAC5B,MAAM,iBAAiB,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QACxD,iBAAiB,CAAC,EAAE,GAAG,mBAAmB,CAAC;QAC3C,iBAAiB,CAAC,KAAK,CAAC,OAAO,GAAG;;;;;;;;KAQjC,CAAC;QACF,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,SAAS,cAAc;QACrB,MAAM,SAAS,GAAG,QAAQ,CAAC,cAAc,CAAC,mBAAmB,CAAC,CAAC;QAC/D,IAAI,CAAC,SAAS;YAAE,OAAO;QAEvB,MAAM,MAAM,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;QACvE,MAAM,aAAa,GAAG,EAAE,CAAC;QAEzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,EAAE,CAAC,EAAE,EAAE,CAAC;YACvC,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YAC/C,QAAQ,CAAC,KAAK,CAAC,OAAO,GAAG;;;;sBAIT,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;gBACvD,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG;;yBAEV,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG;mCACvB,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC;4BAC5B,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG;OACxC,CAAC;YAEF,SAAS,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;YAEhC,yBAAyB;YACzB,UAAU,CAAC,GAAG,EAAE;gBACd,QAAQ,CAAC,MAAM,EAAE,CAAC;YACpB,CAAC,EAAE,IAAI,CAAC,CAAC;QACX,CAAC;IACH,CAAC;IAED;;OAEG;IACH,SAAS,oBAAoB,CAAC,SAAS,EAAE,MAAM,GAAG,CAAC;QACjD,yBAAyB;QACzB,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;QAE/C,oCAAoC;QACpC,IAAI,SAAS,EAAE,CAAC;YACd,cAAc,EAAE,CAAC;YAEjB,gCAAgC;YAChC,IAAI,MAAM,GAAG,CAAC,EAAE,CAAC;gBACf,UAAU,CAAC,GAAG,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,GAAG,CAAC,CAAC;YAC7C,CAAC;QACH,CAAC;QAED,yCAAyC;QACzC,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,wBAAwB,CAAC;YACzD,UAAU,CAAC,GAAG,EAAE;gBACd,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,EAAE,CAAC;YACrC,CAAC,EAAE,GAAG,CAAC,CAAC;QACV,CAAC;IACH,CAAC;IAED;;OAEG;IACH,SAAS,oBAAoB,CAAC,QAAQ,EAAE,QAAQ;QAC9C,MAAM,YAAY,GAAG,QAAQ,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;QAC7D,IAAI,CAAC,YAAY;YAAE,OAAO;QAE1B,MAAM,UAAU,GAAG,QAAQ,GAAG,QAAQ,CAAC;QACvC,IAAI,UAAU,IAAI,CAAC;YAAE,OAAO;QAE5B,kCAAkC;QAClC,MAAM,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QACpD,aAAa,CAAC,WAAW,GAAG,IAAI,UAAU,EAAE,CAAC;QAC7C,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG;;;;;;;;KAQ7B,CAAC;QAEF,MAAM,IAAI,GAAG,YAAY,CAAC,qBAAqB,EAAE,CAAC;QAClD,aAAa,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAC7C,aAAa,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC;QAE1C,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;QAEzC,wBAAwB;QACxB,cAAc,CAAC,YAAY,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;QAEvD,wBAAwB;QACxB,UAAU,CAAC,GAAG,EAAE;YACd,aAAa,CAAC,MAAM,EAAE,CAAC;QACzB,CAAC,EAAE,IAAI,CAAC,CAAC;IACX,CAAC;IAED;;OAEG;IACH,SAAS,cAAc,CAAC,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE,QAAQ;QACnD,MAAM,SAAS,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;QAEpC,SAAS,aAAa,CAAC,WAAW;YAChC,MAAM,OAAO,GAAG,WAAW,GAAG,SAAS,CAAC;YACxC,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,GAAG,QAAQ,EAAE,CAAC,CAAC,CAAC;YAEjD,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,QAAQ,CAAC,CAAC;YAC7D,OAAO,CAAC,WAAW,GAAG,OAAO,CAAC;YAE9B,IAAI,QAAQ,GAAG,CAAC,EAAE,CAAC;gBACjB,qBAAqB,CAAC,aAAa,CAAC,CAAC;YACvC,CAAC;QACH,CAAC;QAED,qBAAqB,CAAC,aAAa,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,SAAS,aAAa,CAAC,WAAW;QAChC,MAAM,aAAa,GAAG,QAAQ,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;QAC/D,IAAI,CAAC,aAAa;YAAE,OAAO;QAE3B,IAAI,WAAW,GAAG,CAAC,EAAE,CAAC;YACpB,aAAa,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YACzC,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG,0BAA0B,CAAC;YAE3D,UAAU,CAAC,GAAG,EAAE;gBACd,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG,EAAE,CAAC;YACrC,CAAC,EAAE,GAAG,CAAC,CAAC;QACV,CAAC;aAAM,CAAC;YACN,aAAa,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACxC,CAAC;IACH,CAAC;IAED;;OAEG;IACH,SAAS,oBAAoB,CAAC,OAAO;QACnC,MAAM,IAAI,GAAG,OAAO,CAAC,qBAAqB,EAAE,CAAC;QAC7C,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;QAC3C,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;QAE3C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;YAC5B,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YAC/C,QAAQ,CAAC,KAAK,CAAC,OAAO,GAAG;;;;;;gBAMf,OAAO;eACR,OAAO;;;;4BAIM,CAAC,GAAG,EAAE;OAC3B,CAAC;YAEF,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;YAEpC,UAAU,CAAC,GAAG,EAAE;gBACd,QAAQ,CAAC,MAAM,EAAE,CAAC;YACpB,CAAC,EAAE,GAAG,CAAC,CAAC;QACV,CAAC;IACH,CAAC;IAED;;OAEG;IACH,SAAS,kBAAkB;QACzB,MAAM,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QAC9C,KAAK,CAAC,WAAW,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA8DnB,CAAC;QACF,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;IACnC,CAAC;IAED,+BAA+B;IAC/B,IAAI,QAAQ,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;QACtC,QAAQ,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,GAAG,EAAE;YACjD,kBAAkB,EAAE,CAAC;YACrB,MAAM,EAAE,CAAC;QACX,CAAC,CAAC,CAAC;IACL,CAAC;SAAM,CAAC;QACN,kBAAkB,EAAE,CAAC;QACrB,MAAM,EAAE,CAAC;IACX,CAAC;IAED,oBAAoB;IACpB,MAAM,CAAC,SAAS,GAAG;QACjB,SAAS;QACT,oBAAoB;QACpB,oBAAoB;QACpB,aAAa;QACb,oBAAoB;QACpB,cAAc;QACd,WAAW;KACZ,CAAC;AAEJ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC"}