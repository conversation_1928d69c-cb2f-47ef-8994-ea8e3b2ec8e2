<div class="max-w-4xl mx-auto">
  <div class="mb-8">
    <h1 class="text-2xl font-bold text-gray-800 mb-6">Thông tin tài khoản</h1>
    <div class="bg-white rounded-lg shadow-md p-6">
      <div class="flex items-center mb-6">
        <img
          src="<%= user.profilePhoto || 'https://via.placeholder.com/80' %>"
          alt="<%= user.displayName %>"
          class="w-20 h-20 rounded-full object-cover mr-6"
        />
        <div>
          <h2 class="text-xl font-semibold text-gray-800">
            <%= user.displayName %>
          </h2>
          <p class="text-gray-600"><%= user.email %></p>
        </div>
      </div>
    </div>
  </div>

  <div class="mb-8">
    <h2 class="text-xl font-bold text-gray-800 mb-4">Phiên đăng nhập</h2>

    <div class="bg-white rounded-lg shadow-md p-6">
      <p class="mb-4 text-gray-600">
        Dưới đây là danh sách các thiết bị bạn đã đăng nhập. Bạn có thể đăng
        xuất khỏi các thiết bị không sử dụng.
      </p>

      <div id="sessions-container" class="space-y-4">
        <div class="text-center py-8">
          <div
            class="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-500 mb-2"
          ></div>
          <p>Đang tải dữ liệu...</p>
        </div>
      </div>
    </div>
  </div>

  <div class="mb-8">
    <h2 class="text-xl font-bold text-gray-800 mb-4">Bảo mật</h2>

    <div class="bg-white rounded-lg shadow-md p-6">
      <button
        id="logout-all-devices-btn"
        class="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50 transition-colors"
      >
        <i class="fas fa-sign-out-alt mr-2"></i>
        Đăng xuất khỏi tất cả thiết bị
      </button>
    </div>
  </div>

  <!-- Thông báo kết quả -->
  <div
    id="notification"
    class="fixed top-4 right-4 max-w-md bg-white rounded-lg shadow-lg border-l-4 border-green-500 overflow-hidden transition-all duration-300 transform translate-x-full opacity-0"
  >
    <div class="p-4 flex items-start">
      <div class="flex-shrink-0">
        <i
          id="notification-icon"
          class="fas fa-check-circle text-green-500 text-xl"
        ></i>
      </div>
      <div class="ml-3">
        <p id="notification-message" class="text-sm font-medium text-gray-900">
          Thành công!
        </p>
      </div>
      <div class="ml-auto pl-3">
        <div class="-mx-1.5 -my-1.5">
          <button
            id="notification-close"
            class="inline-flex bg-white rounded-md p-1.5 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <span class="sr-only">Đóng</span>
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener("DOMContentLoaded", function () {
    // Lấy danh sách phiên đăng nhập
    fetchSessions();

    // Xử lý nút đăng xuất tất cả thiết bị
    document
      .getElementById("logout-all-devices-btn")
      .addEventListener("click", function () {
        if (confirm("Bạn có chắc chắn muốn đăng xuất khỏi tất cả thiết bị?")) {
          window.location.href = "/auth/logout";
        }
      });

    // Xử lý đóng thông báo
    document
      .getElementById("notification-close")
      .addEventListener("click", function () {
        hideNotification();
      });
  });

  // Lấy danh sách phiên đăng nhập
  async function fetchSessions() {
    try {
      const response = await fetch("/auth/sessions");
      const data = await response.json();

      if (data.success) {
        renderSessions(data.sessions);
      } else {
        showError("Không thể tải danh sách phiên đăng nhập");
      }
    } catch (error) {
      console.error("Error fetching sessions:", error);
      showError("Đã xảy ra lỗi khi tải dữ liệu");
    }
  }

  // Hiển thị danh sách phiên
  function renderSessions(sessions) {
    const container = document.getElementById("sessions-container");

    if (sessions.length === 0) {
      container.innerHTML = `
        <div class="text-center py-4">
          <p class="text-gray-500">Không có phiên đăng nhập nào</p>
        </div>
      `;
      return;
    }

    const sessionElements = sessions.map((session) => {
      const isCurrentDevice = session.isCurrentDevice;
      const lastActive = new Date(session.lastActive);
      const createdAt = new Date(session.createdAt);

      const formattedLastActive = formatDate(lastActive);
      const formattedCreatedAt = formatDate(createdAt);

      return `
        <div class="border rounded-lg p-4 ${
          isCurrentDevice
            ? "bg-blue-50 border-blue-200"
            : "bg-white border-gray-200"
        }">
          <div class="flex justify-between items-center mb-2">
            <div class="flex items-center">
              <i class="fas fa-${getDeviceIcon(
                session.deviceInfo
              )} text-xl text-gray-500 mr-3"></i>
              <div>
                <div class="font-medium">${session.deviceInfo.browser}</div>
                <div class="text-sm text-gray-500">${session.deviceInfo.os} - ${
        session.deviceInfo.deviceName
      }</div>
              </div>
            </div>
            ${
              isCurrentDevice
                ? `<span class="px-3 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">Thiết bị hiện tại</span>`
                : `<button data-session-id="${session.id}" class="logout-session-btn text-red-600 hover:text-red-800 focus:outline-none transition-colors">
                <i class="fas fa-sign-out-alt mr-1"></i> Đăng xuất
              </button>`
            }
          </div>
          <div class="text-xs text-gray-500 mt-2">
            <div>Hoạt động lần cuối: ${formattedLastActive}</div>
            <div>Đăng nhập lần đầu: ${formattedCreatedAt}</div>
          </div>
        </div>
      `;
    });

    container.innerHTML = sessionElements.join("");

    // Thêm sự kiện cho nút đăng xuất
    document.querySelectorAll(".logout-session-btn").forEach((button) => {
      button.addEventListener("click", function () {
        const sessionId = this.getAttribute("data-session-id");
        logoutSession(sessionId);
      });
    });
  }

  // Đăng xuất một phiên cụ thể
  async function logoutSession(sessionId) {
    try {
      const response = await fetch(`/auth/logout-session/${sessionId}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      const data = await response.json();

      if (data.success) {
        showSuccess("Đăng xuất thiết bị thành công");

        // Làm mới danh sách
        setTimeout(() => {
          fetchSessions();
        }, 1000);
      } else {
        showError(data.message || "Đăng xuất thiết bị thất bại");
      }
    } catch (error) {
      console.error("Error logging out session:", error);
      showError("Đã xảy ra lỗi khi đăng xuất thiết bị");
    }
  }

  // Hiển thị thông báo thành công
  function showSuccess(message) {
    const notification = document.getElementById("notification");
    const icon = document.getElementById("notification-icon");
    const messageElement = document.getElementById("notification-message");

    // Cập nhật nội dung
    icon.className = "fas fa-check-circle text-green-500 text-xl";
    notification.className = notification.className.replace(
      "border-red-500",
      "border-green-500"
    );
    messageElement.textContent = message;

    // Hiển thị
    notification.classList.remove("translate-x-full", "opacity-0");

    // Tự động ẩn sau 3 giây
    setTimeout(hideNotification, 3000);
  }

  // Hiển thị thông báo lỗi
  function showError(message) {
    const notification = document.getElementById("notification");
    const icon = document.getElementById("notification-icon");
    const messageElement = document.getElementById("notification-message");

    // Cập nhật nội dung
    icon.className = "fas fa-exclamation-circle text-red-500 text-xl";
    notification.className = notification.className.replace(
      "border-green-500",
      "border-red-500"
    );
    messageElement.textContent = message;

    // Hiển thị
    notification.classList.remove("translate-x-full", "opacity-0");

    // Tự động ẩn sau 3 giây
    setTimeout(hideNotification, 3000);
  }

  // Ẩn thông báo
  function hideNotification() {
    const notification = document.getElementById("notification");
    notification.classList.add("translate-x-full", "opacity-0");
  }

  // Định dạng ngày
  function formatDate(date) {
    const now = new Date();
    const diff = now.getTime() - date.getTime();

    // Nếu dưới 24 giờ, hiển thị "x giờ trước"
    if (diff < 24 * 60 * 60 * 1000) {
      const hours = Math.floor(diff / (60 * 60 * 1000));
      if (hours === 0) {
        const minutes = Math.floor(diff / (60 * 1000));
        return minutes === 0 ? "Vừa xong" : `${minutes} phút trước`;
      }
      return `${hours} giờ trước`;
    }

    // Nếu dưới 7 ngày, hiển thị "x ngày trước"
    if (diff < 7 * 24 * 60 * 60 * 1000) {
      const days = Math.floor(diff / (24 * 60 * 60 * 1000));
      return `${days} ngày trước`;
    }

    // Nếu trên 7 ngày, hiển thị ngày tháng đầy đủ
    return date.toLocaleDateString("vi-VN", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  }

  // Lấy icon phù hợp với thiết bị
  function getDeviceIcon(deviceInfo) {
    const os = deviceInfo.os.toLowerCase();

    if (os.includes("android")) return "mobile-alt";
    if (os.includes("ios") || os.includes("iphone") || os.includes("ipad"))
      return "mobile-alt";
    if (os.includes("windows")) return "desktop";
    if (os.includes("mac")) return "laptop";
    if (os.includes("linux")) return "desktop";

    return "desktop";
  }
</script>
