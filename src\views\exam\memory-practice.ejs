<!DOCTYPE html>
<html lang="vi">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><PERSON><PERSON><PERSON><PERSON> tập ghi nhớ - Test HMC</title>

    <!-- Meta tags cho page -->
    <meta name="practiceId" content="<%= practiceId %>" />
    <meta name="productId" content="<%= product._id %>" />
    <meta name="practice-type" content="memory" />

    <!-- Favicon và web manifest -->
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
    <link rel="manifest" href="/site.webmanifest" />
    <link rel="shortcut icon" href="/favicon.ico" />

    <!-- Tailwind CSS từ CDN -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Font Awesome -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />

    <style>
      body {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        display: flex;
        flex-direction: column;
        min-height: 100vh;
        margin: 0;
        padding: 0;
      }
      main {
        flex: 1;
      }
      /* Prevent text overflow */
      .break-words {
        overflow-wrap: break-word !important;
        word-wrap: break-word !important;
        word-break: break-word !important;
        hyphens: auto;
      }
      /* Question container responsive */
      .question-container {
        min-height: 0;
        display: flex;
        flex-direction: column;
      }
      .options-container {
        flex: 1;
        overflow-y: auto;
      }
      /* Ngăn chặn in ấn */
      @media print {
        body * {
          display: none !important;
          visibility: hidden !important;
        }
      }
    </style>
  </head>

  <body class="bg-gray-50">
    <!-- Container chính cho trang luyện tập -->
    <div class="flex flex-col min-h-screen bg-gray-50">
      <!-- Header -->
      <header class="bg-white border-b border-gray-200 py-2">
        <div class="container mx-auto px-4 flex justify-between items-center">
          <div class="flex items-center">
            <h1 class="text-lg font-semibold text-gray-800">
              <i class="fas fa-bookmark text-indigo-500 mr-2"></i>
              Luyện tập ghi nhớ
            </h1>
            <span class="ml-3 text-sm text-gray-600"><%= product.name %></span>
          </div>
          <div class="flex items-center space-x-4">
            <div class="bg-blue-50 px-3 py-1 rounded-full">
              <span id="timerDisplay" class="text-blue-700 font-medium">
                <%= settings.time %>:00
              </span>
            </div>
            <button id="exitButton" class="text-gray-600 hover:text-red-600">
              <i class="fas fa-times-circle"></i> Thoát
            </button>
          </div>
        </div>
      </header>

      <!-- Main content -->
      <main class="flex-grow container mx-auto px-4 py-6">
        <!-- Phần cố định hiển thị trước khi tải câu hỏi -->
        <div
          id="loadingContainer"
          class="flex items-center justify-center h-64"
        >
          <div class="text-center">
            <div
              class="animate-spin rounded-full h-16 w-16 border-b-2 border-indigo-500 mb-4"
            ></div>
            <p class="text-gray-600">Đang tải câu hỏi...</p>
          </div>
        </div>

        <!-- Container cho phần bài thi -->
        <div id="practiceContainer" class="hidden">
          <!-- Progress bar -->
          <div class="mb-6">
            <div class="flex items-center justify-between mb-1">
              <span
                id="currentQuestionIndicator"
                class="text-sm font-medium text-gray-600"
                >Câu 1/<%= settings.count %></span
              >
              <span
                id="progressPercentage"
                class="text-sm font-medium text-gray-600"
                >0%</span
              >
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2.5">
              <div
                id="progressBar"
                class="bg-indigo-600 h-2.5 rounded-full"
                style="width: 0%"
              ></div>
            </div>
          </div>

          <!-- Câu hỏi hiện tại -->
          <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div
              id="questionContainer"
              class="text-lg font-medium text-gray-800 mb-4"
            ></div>
            <div id="optionsContainer" class="space-y-3"></div>
          </div>

          <!-- Nút điều hướng -->
          <div class="flex justify-between">
            <button
              id="prevButton"
              class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <i class="fas fa-chevron-left mr-2"></i> Câu trước
            </button>
            <button
              id="nextButton"
              class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Câu tiếp theo <i class="fas fa-chevron-right ml-2"></i>
            </button>
          </div>
        </div>

        <!-- Container cho kết quả -->
        <div id="resultContainer" class="hidden"></div>

        <!-- Container cho kết quả chi tiết -->
        <div id="detailedResultsContainer" class="hidden"></div>
      </main>

      <!-- Footer -->
      <footer class="bg-gray-100 py-4">
        <div class="container mx-auto px-4 text-center text-gray-500 text-sm">
          <p>
            &copy; <%= new Date().getFullYear() %> Test HMC. Nơi học tập của
            bạn.
          </p>
        </div>
      </footer>

      <!-- Exit confirmation modal -->
      <div
        id="exitConfirmModal"
        class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden"
      >
        <div class="bg-white rounded-lg shadow-xl p-6 max-w-md w-full mx-4">
          <h3 class="text-lg font-semibold text-gray-800 mb-4">
            Xác nhận thoát
          </h3>
          <p class="text-gray-600 mb-6">
            Bạn có chắc muốn thoát khỏi bài luyện tập? Tiến độ của bạn sẽ không
            được lưu lại.
          </p>
          <div class="flex justify-end space-x-4">
            <button
              id="cancelExitBtn"
              class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300"
            >
              Ở lại
            </button>
            <button
              id="confirmExitBtn"
              class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
            >
              Thoát
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Sounds -->
    <audio
      id="correctSound"
      src="/media/correct_answer.mp3"
      preload="auto"
    ></audio>
    <audio id="wrongSound" src="/media/wrong_answer.mp3" preload="auto"></audio>

    <!-- Scripts -->
    <script src="/js/security-measures.js"></script>
    <script src="/js/memory-exam-practice.js"></script>

    <!-- Áp dụng security measures -->
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        // Áp dụng các biện pháp bảo mật
        if (typeof GlobalSecurityMeasures === "function") {
          GlobalSecurityMeasures({
            contextMessage: "luyện tập ghi nhớ",
            enableDevToolsDetection: true,
            enableScreenshotBlocking: true,
            enableRightClickBlocking: true,
            enableCopyBlocking: true,
            enablePrintBlocking: true,
            enableViewSourceBlocking: true,
            enableSavePageBlocking: true,
            enableDragDropBlocking: true,
            devToolsThreshold: 160,
            redirectOnDevTools: false,
            redirectUrl: "/home",
          });
        }

        // Khởi tạo module luyện tập
        if (
          typeof window.MemoryExamPractice !== "undefined" &&
          typeof window.MemoryExamPractice.init === "function"
        ) {
          window.MemoryExamPractice.init();
        } else {
          console.error("Memory exam practice module not loaded!");
        }

        // Xử lý nút thoát
        document
          .getElementById("exitButton")
          .addEventListener("click", function () {
            document
              .getElementById("exitConfirmModal")
              .classList.remove("hidden");
          });

        document
          .getElementById("cancelExitBtn")
          .addEventListener("click", function () {
            document.getElementById("exitConfirmModal").classList.add("hidden");
          });

        document
          .getElementById("confirmExitBtn")
          .addEventListener("click", function () {
            window.location.href =
              "/course/<%= product._id %>/exams?tab=memory";
          });
      });
    </script>
  </body>
</html>
