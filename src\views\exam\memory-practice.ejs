<!DOCTYPE html>
<html lang="vi">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><PERSON><PERSON><PERSON><PERSON> tập ghi nhớ - Test HMC</title>

    <!-- Meta tags cho page -->
    <meta name="practiceId" content="<%= practiceId %>" />
    <meta name="productId" content="<%= product._id %>" />
    <meta name="practice-type" content="memory" />

    <!-- Favicon và web manifest -->
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
    <link rel="manifest" href="/site.webmanifest" />
    <link rel="shortcut icon" href="/favicon.ico" />

    <!-- Tailwind CSS từ CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              "quizizz-purple": "#6200ea",
              "quizizz-purple-dark": "#5100c2",
              "quizizz-pink": "#ff4081",
              "quizizz-blue": "#40c4ff",
              "quizizz-yellow": "#ffea00",
              "quizizz-green": "#00e676",
              "quizizz-red": "#ff5252",
              "quizizz-orange": "#ff6e40",
              "quizizz-bg": "#2e044e",
              "quizizz-bg-dark": "#1a0046",
              "quizizz-card-1": "#673ab7",
              "quizizz-card-2": "#3f51b5",
              "quizizz-card-3": "#2196f3",
              "quizizz-card-4": "#009688",
            },
            animation: {
              "bounce-in": "bounce-in 0.5s ease-out",
              "bounce-out": "bounce-out 0.5s ease-in",
              "scale-in": "scale-in 0.3s ease-out",
              "pulse-strong":
                "pulse-strong 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite",
              float: "float 3s ease-in-out infinite",
              "streak-pop": "streak-pop 0.5s ease-out",
            },
            keyframes: {
              "bounce-in": {
                "0%": { transform: "scale(0.8)", opacity: "0" },
                "80%": { transform: "scale(1.05)", opacity: "1" },
                "100%": { transform: "scale(1)", opacity: "1" },
              },
              "bounce-out": {
                "0%": { transform: "scale(1)", opacity: "1" },
                "20%": { transform: "scale(1.05)", opacity: "1" },
                "100%": { transform: "scale(0.8)", opacity: "0" },
              },
              "scale-in": {
                "0%": { transform: "scale(0.5)", opacity: "0" },
                "100%": { transform: "scale(1)", opacity: "1" },
              },
              "pulse-strong": {
                "0%, 100%": { opacity: "1", transform: "scale(1)" },
                "50%": { opacity: "0.8", transform: "scale(1.05)" },
              },
              float: {
                "0%, 100%": { transform: "translateY(0)" },
                "50%": { transform: "translateY(-10px)" },
              },
              "streak-pop": {
                "0%": { transform: "scale(0)", opacity: "0" },
                "50%": { transform: "scale(1.2)", opacity: "1" },
                "100%": { transform: "scale(1)", opacity: "1" },
              },
            },
            boxShadow: {
              quizizz: "0 4px 10px 0 rgba(0, 0, 0, 0.25)",
              "quizizz-hover": "0 8px 16px 0 rgba(0, 0, 0, 0.3)",
            },
          },
        },
      };
    </script>

    <!-- Font Awesome -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />

    <!-- Google Fonts -->
    <link
      href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />

    <style>
      body {
        font-family: "Poppins", sans-serif;
        margin: 0;
        padding: 0;
        overflow-x: hidden;
        background-color: theme("colors.quizizz-bg");
        color: white;
      }

      .question-container {
        max-width: 800px;
        margin: 0 auto;
      }

      .option-card {
        transition: all 0.2s ease;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        height: 120px;
      }

      .option-card:hover {
        transform: translateY(-5px);
        box-shadow: theme("boxShadow.quizizz-hover");
      }

      .option-card::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background-color: rgba(255, 255, 255, 0.3);
      }

      .option-card.selected::before {
        background-color: white;
      }

      .timer-container {
        width: 100%;
        height: 8px;
        background-color: rgba(255, 255, 255, 0.2);
        overflow: hidden;
      }

      .timer-bar {
        height: 100%;
        width: 100%;
        background: linear-gradient(90deg, #ff4081, #7c4dff);
        transition: width 1s linear;
      }

      .confetti {
        position: absolute;
        width: 10px;
        height: 10px;
        opacity: 0;
      }

      .streak-counter {
        transition: all 0.3s ease;
      }

      .streak-badge {
        animation: streak-pop 0.5s ease-out;
      }

      /* Quizizz-specific components */
      .quizizz-progress-dots {
        display: flex;
        justify-content: center;
        margin: 10px 0;
      }

      .quizizz-progress-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.3);
        margin: 0 4px;
        transition: all 0.3s ease;
      }

      .quizizz-progress-dot.active {
        background-color: white;
        transform: scale(1.2);
      }

      .quizizz-progress-dot.correct {
        background-color: theme("colors.quizizz-green");
      }

      .quizizz-progress-dot.incorrect {
        background-color: theme("colors.quizizz-red");
      }

      /* Prevent text selection for better UX */
      .no-select {
        user-select: none;
        -webkit-user-select: none;
      }

      /* Quizizz Animations */
      .correct-answer {
        animation: correct-answer-animation 0.5s ease-out;
      }

      @keyframes correct-answer-animation {
        0% {
          transform: scale(1);
        }
        50% {
          transform: scale(1.05);
        }
        100% {
          transform: scale(1);
        }
      }

      .incorrect-answer {
        animation: incorrect-answer-animation 0.5s ease-out;
      }

      @keyframes incorrect-answer-animation {
        0% {
          transform: scale(1);
        }
        25% {
          transform: translateX(-5px);
        }
        50% {
          transform: translateX(5px);
        }
        75% {
          transform: translateX(-5px);
        }
        100% {
          transform: scale(1);
        }
      }

      /* Power-up animations */
      .power-up-icon {
        transition: all 0.3s ease;
      }

      .power-up-icon:hover {
        transform: scale(1.1) rotate(5deg);
      }

      .power-up-active {
        animation: power-up-pulse 1s infinite alternate;
      }

      @keyframes power-up-pulse {
        0% {
          filter: brightness(1);
        }
        100% {
          filter: brightness(1.5);
        }
      }

      /* Full screen mode */
      .quizizz-fullscreen {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 9999;
        background-color: theme("colors.quizizz-bg");
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }
    </style>
  </head>

  <body class="bg-quizizz-bg">
    <div id="quizizzApp" class="quizizz-fullscreen flex flex-col h-full">
      <!-- Header with timer and progress -->
      <header class="px-4 py-3 bg-quizizz-bg-dark">
        <div class="container mx-auto">
          <div class="flex items-center justify-between mb-2">
            <!-- Left side with logo and question counter -->
            <div class="flex items-center">
              <button
                id="exitButton"
                class="text-white hover:text-quizizz-pink mr-3 focus:outline-none"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M15 19l-7-7 7-7"
                  />
                </svg>
              </button>
              <div
                class="text-xs sm:text-sm bg-black bg-opacity-30 px-3 py-1 rounded-full"
              >
                Câu <span id="currentQuestionNumber">1</span>/<span
                  id="totalQuestions"
                  ><%= settings.count %></span
                >
              </div>
            </div>

            <!-- Right side with timer and streak -->
            <div class="flex items-center space-x-3">
              <!-- Streak counter -->
              <div
                id="streakCounter"
                class="hidden items-center bg-black bg-opacity-30 px-3 py-1 rounded-full streak-counter"
              >
                <span class="text-quizizz-yellow mr-1">
                  <i class="fas fa-fire"></i>
                </span>
                <span id="streakCount" class="text-xs sm:text-sm font-bold"
                  >0</span
                >
              </div>

              <!-- Score display -->
              <div class="bg-black bg-opacity-30 px-3 py-1 rounded-full">
                <span id="scoreDisplay" class="text-xs sm:text-sm font-bold"
                  >0</span
                >
                <span class="text-xs sm:text-sm">pts</span>
              </div>

              <!-- Timer display -->
              <div
                class="bg-black bg-opacity-30 px-3 py-1 rounded-full flex items-center"
              >
                <span class="text-quizizz-pink mr-1">
                  <i class="fas fa-clock"></i>
                </span>
                <span id="timerDisplay" class="text-xs sm:text-sm font-mono">
                  <%= settings.time %>:00
                </span>
              </div>
            </div>
          </div>

          <!-- Timer bar -->
          <div class="timer-container rounded-full overflow-hidden">
            <div id="timerBar" class="timer-bar" style="width: 100%"></div>
          </div>
        </div>
      </header>

      <!-- Progress dots -->
      <div
        class="quizizz-progress-dots py-2 px-4 overflow-x-auto hide-scrollbar"
        id="progressDots"
      ></div>

      <!-- Main content area -->
      <main class="flex-grow flex items-center justify-center p-4">
        <!-- Loading screen -->
        <div
          id="loadingContainer"
          class="flex flex-col items-center justify-center text-center"
        >
          <div class="animate-bounce text-6xl mb-6 text-quizizz-pink">
            <i class="fas fa-brain"></i>
          </div>
          <div class="animate-pulse text-2xl font-bold mb-2">
            Đang chuẩn bị bài luyện tập...
          </div>
          <p class="text-gray-300">Quizizz đang tải câu hỏi của bạn</p>
        </div>

        <!-- Question screen -->
        <div id="questionContainer" class="hidden w-full max-w-4xl mx-auto">
          <!-- Question text -->
          <div class="mb-6 text-center">
            <h2
              id="questionText"
              class="text-xl sm:text-2xl md:text-3xl font-bold mb-2 animate-bounce-in"
            ></h2>
            <div id="questionImage" class="mx-auto max-w-md mb-4 hidden">
              <img
                src=""
                alt="Question image"
                class="rounded-lg shadow-lg max-h-60 mx-auto"
              />
            </div>
          </div>

          <!-- Options grid -->
          <div
            id="optionsGrid"
            class="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6"
          >
            <!-- Options will be inserted here by JavaScript -->
          </div>
        </div>

        <!-- Result screen -->
        <div
          id="resultContainer"
          class="hidden w-full max-w-4xl mx-auto text-center animate-bounce-in"
        >
          <!-- Content will be inserted by JavaScript -->
        </div>
      </main>

      <!-- Power-up bar (Quizizz game feature) -->
      <div class="p-4 bg-quizizz-bg-dark hidden md:block">
        <div class="container mx-auto flex justify-center space-x-6">
          <div
            class="power-up-icon opacity-50 cursor-not-allowed"
            title="Xóa 50:50 (Không khả dụng)"
          >
            <div class="bg-black bg-opacity-30 p-2 rounded-full">
              <i class="fas fa-columns text-quizizz-blue"></i>
            </div>
          </div>
          <div
            class="power-up-icon opacity-50 cursor-not-allowed"
            title="Đóng băng thời gian (Không khả dụng)"
          >
            <div class="bg-black bg-opacity-30 p-2 rounded-full">
              <i class="fas fa-snowflake text-quizizz-blue"></i>
            </div>
          </div>
          <div
            class="power-up-icon opacity-50 cursor-not-allowed"
            title="Nhân đôi điểm (Không khả dụng)"
          >
            <div class="bg-black bg-opacity-30 p-2 rounded-full">
              <i class="fas fa-calculator text-quizizz-yellow"></i>
            </div>
          </div>
        </div>
      </div>

      <!-- Exit confirmation modal -->
      <div
        id="exitConfirmModal"
        class="fixed inset-0 bg-black bg-opacity-70 z-50 flex items-center justify-center hidden animate-scale-in"
      >
        <div
          class="bg-quizizz-bg-dark rounded-xl shadow-quizizz p-6 max-w-md w-full mx-4 border-2 border-quizizz-purple"
        >
          <h3 class="text-2xl font-bold text-white mb-4">Bạn muốn thoát?</h3>
          <p class="text-gray-300 mb-6">
            Tiến độ của bạn sẽ không được lưu lại nếu thoát bây giờ.
          </p>
          <div class="flex justify-center space-x-4">
            <button
              id="cancelExitBtn"
              class="px-6 py-3 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors font-medium focus:outline-none"
            >
              Tiếp tục học
            </button>
            <button
              id="confirmExitBtn"
              class="px-6 py-3 bg-quizizz-pink text-white rounded-lg hover:bg-pink-600 transition-colors font-medium focus:outline-none"
            >
              Thoát
            </button>
          </div>
        </div>
      </div>

      <!-- Answer feedback overlay -->
      <div
        id="answerFeedback"
        class="fixed inset-0 z-40 pointer-events-none hidden"
      >
        <div class="h-full w-full flex items-center justify-center">
          <div id="correctFeedback" class="hidden animate-scale-in">
            <div class="text-8xl text-quizizz-green mb-2 text-center">
              <i class="fas fa-check-circle"></i>
            </div>
            <div class="text-3xl font-bold text-white text-center">
              Đúng rồi!
            </div>
          </div>
          <div id="incorrectFeedback" class="hidden animate-scale-in">
            <div class="text-8xl text-quizizz-red mb-2 text-center">
              <i class="fas fa-times-circle"></i>
            </div>
            <div class="text-3xl font-bold text-white text-center">
              Sai rồi!
            </div>
          </div>
        </div>
      </div>

      <!-- Confetti container for celebrations -->
      <div
        id="confettiContainer"
        class="fixed inset-0 pointer-events-none z-30"
      ></div>
    </div>

    <!-- Sounds -->
    <audio
      id="correctSound"
      src="/media/correct_answer.mp3"
      preload="auto"
    ></audio>
    <audio id="wrongSound" src="/media/wrong_answer.mp3" preload="auto"></audio>
    <audio id="timerSound" preload="auto">
      <source
        src="https://assets.quizizz.com/sounds/countdown-clock.mp3"
        type="audio/mpeg"
      />
    </audio>
    <audio id="streakSound" preload="auto">
      <source
        src="https://assets.quizizz.com/sounds/streak-increment.mp3"
        type="audio/mpeg"
      />
    </audio>

    <!-- Scripts -->
    <script src="/js/security-measures.js"></script>
    <script src="/js/memory-exam-practice.js"></script>

    <script>
      document.addEventListener("DOMContentLoaded", function () {
        // Áp dụng các biện pháp bảo mật
        if (typeof GlobalSecurityMeasures === "function") {
          GlobalSecurityMeasures({
            contextMessage: "luyện tập ghi nhớ",
            enableDevToolsDetection: true,
            enableScreenshotBlocking: true,
            enableRightClickBlocking: true,
            enableCopyBlocking: true,
            enablePrintBlocking: true,
            enableViewSourceBlocking: true,
            enableSavePageBlocking: true,
            enableDragDropBlocking: true,
            devToolsThreshold: 160,
            redirectOnDevTools: false,
            redirectUrl: "/home",
          });
        }

        // Tạo progress dots
        const totalQuestions = parseInt(
          document.getElementById("totalQuestions").innerText
        );
        const progressDotsContainer = document.getElementById("progressDots");

        for (let i = 0; i < totalQuestions; i++) {
          const dot = document.createElement("div");
          dot.className = "quizizz-progress-dot";
          if (i === 0) dot.classList.add("active");
          progressDotsContainer.appendChild(dot);
        }

        // Khởi tạo module luyện tập
        if (
          typeof window.MemoryExamPractice !== "undefined" &&
          typeof window.MemoryExamPractice.init === "function"
        ) {
          window.MemoryExamPractice.init();
        } else {
          console.error("Memory exam practice module not loaded!");
          document.getElementById("loadingContainer").innerHTML = `
          <div class="text-center">
            <div class="text-6xl text-quizizz-red mb-6">
              <i class="fas fa-exclamation-triangle"></i>
            </div>
            <h3 class="text-2xl font-bold text-white mb-2">Không thể tải module luyện tập</h3>
            <p class="text-gray-300 mb-6">Vui lòng làm mới trang và thử lại</p>
            <button onclick="window.location.reload()" class="px-6 py-3 bg-quizizz-purple text-white rounded-lg hover:bg-quizizz-purple-dark">
              Tải lại trang
            </button>
          </div>
        `;
        }

        // Xử lý nút thoát
        document
          .getElementById("exitButton")
          .addEventListener("click", function () {
            document
              .getElementById("exitConfirmModal")
              .classList.remove("hidden");
          });

        document
          .getElementById("cancelExitBtn")
          .addEventListener("click", function () {
            document.getElementById("exitConfirmModal").classList.add("hidden");
          });

        document
          .getElementById("confirmExitBtn")
          .addEventListener("click", function () {
            window.location.href =
              "/course/<%= product._id %>/exams?tab=memory";
          });

        // Cho mục đích demo, có thể hiển thị giao diện câu hỏi sau 2 giây nếu chưa được load
        setTimeout(function () {
          const isQuestionVisible = !document
            .getElementById("questionContainer")
            .classList.contains("hidden");
          if (!isQuestionVisible && window.MemoryExamPractice) {
            console.log("Triggering question display manually");
            // Các hàm này sẽ được triển khai trong memory-exam-practice.js
          }
        }, 2000);
      });
    </script>
  </body>
</html>
