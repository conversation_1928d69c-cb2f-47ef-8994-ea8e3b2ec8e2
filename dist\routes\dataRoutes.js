"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const modelService_1 = require("../services/modelService");
const authMiddleware_1 = require("../middlewares/authMiddleware");
const asynHandler_1 = __importDefault(require("../util/asynHandler"));
const router = express_1.default.Router();
// === API cho Student ===
router.get("/students", (0, asynHandler_1.default)(authMiddleware_1.authenticateToken), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const students = yield modelService_1.Student.find();
        res.json(students);
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
}));
router.get("/students/:id", (0, asynHandler_1.default)(authMiddleware_1.authenticateToken), (0, asynHandler_1.default)((req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const student = yield modelService_1.Student.findById(req.params.id);
        if (!student) {
            return res.status(404).json({ message: "Không tìm thấy sinh viên" });
        }
        res.json(student);
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
})));
// === API cho Product ===
router.get("/products", (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const products = yield modelService_1.Product.find({ status: "active" }).lean();
        res.json({ success: true, products });
    }
    catch (error) {
        console.error("Lỗi khi lấy danh sách sản phẩm:", error);
        res.status(500).json({ success: false, message: "Lỗi server" });
    }
}));
router.get("/products/:id", (0, asynHandler_1.default)(authMiddleware_1.authenticateToken), (0, asynHandler_1.default)((req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const product = yield modelService_1.Product.findById(req.params.id);
        if (!product) {
            return res.status(404).json({ message: "Không tìm thấy sản phẩm" });
        }
        res.json(product);
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
})));
// === API cho Exam ===
router.get("/exams", (0, asynHandler_1.default)(authMiddleware_1.authenticateToken), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const exams = yield modelService_1.Exam.find();
        res.json(exams);
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
}));
router.get("/exams/:id", (0, asynHandler_1.default)(authMiddleware_1.authenticateToken), (0, asynHandler_1.default)((req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const exam = yield modelService_1.Exam.findById(req.params.id);
        if (!exam) {
            return res.status(404).json({ message: "Không tìm thấy bài kiểm tra" });
        }
        res.json(exam);
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
})));
// === API cho Question ===
router.get("/questions", (0, asynHandler_1.default)(authMiddleware_1.authenticateToken), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const questions = yield modelService_1.Question.find();
        res.json(questions);
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
}));
router.get("/questions/:id", (0, asynHandler_1.default)(authMiddleware_1.authenticateToken), (0, asynHandler_1.default)((req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const question = yield modelService_1.Question.findById(req.params.id);
        if (!question) {
            return res.status(404).json({ message: "Không tìm thấy câu hỏi" });
        }
        res.json(question);
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
})));
// === API cho Question theo Exam ===
router.get("/exams/:examId/questions", (0, asynHandler_1.default)(authMiddleware_1.authenticateToken), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const questions = yield modelService_1.Question.find({ examId: req.params.examId });
        res.json(questions);
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
}));
exports.default = router;
//# sourceMappingURL=dataRoutes.js.map