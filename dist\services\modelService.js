"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Question = exports.Exam = exports.Product = exports.Student = void 0;
const mongoose_1 = __importDefault(require("mongoose"));
// Import schemas từ các file model hiện có
require("../models/student");
require("../models/products");
require("../models/exam");
require("../models/question");
// Các model đã được tự động đăng ký với kết nối mặc định của mongoose
// khi import các file, không cần đăng ký lại
// Export các model để sử dụng trong toàn bộ ứng dụng
const Student = mongoose_1.default.model("Student");
exports.Student = Student;
const Product = mongoose_1.default.model("Product");
exports.Product = Product;
const Exam = mongoose_1.default.model("Exam");
exports.Exam = Exam;
const Question = mongoose_1.default.model("Question");
exports.Question = Question;
//# sourceMappingURL=modelService.js.map