{"version": 3, "file": "errorhandler.js", "sourceRoot": "", "sources": ["../../src/util/errorhandler.ts"], "names": [], "mappings": ";;;AAcA,MAAM,YAAa,SAAQ,KAAK;IAG9B,YAAY,OAAe,EAAE,aAAqB,GAAG;QACnD,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;IAClD,CAAC;CACF;AAkDQ,oCAAY;AAhDrB,MAAM,eAAe,GAAG,CACtB,GAAa,EACb,OAAe,EACf,IAAQ,EACR,aAAqB,GAAG,EAClB,EAAE;IACR,MAAM,QAAQ,GAAuB;QACnC,OAAO,EAAE,IAAI;QACb,OAAO;QACP,IAAI;KACL,CAAC;IACF,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACxC,CAAC,CAAC;AAoCsC,0CAAe;AAlCvD,MAAM,aAAa,GAAG,CACpB,GAAa,EACb,OAAe,EACf,aAAqB,GAAG,EAClB,EAAE;IACR,MAAM,QAAQ,GAA0B;QACtC,OAAO,EAAE,KAAK;QACd,OAAO;KACR,CAAC;IACF,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACxC,CAAC,CAAC;AAwBuD,sCAAa;AAtBtE,MAAM,eAAe,GAAG,CACtB,GAAiB,EACjB,GAAY,EACZ,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,MAAM,KAAK,GAAkB;QAC3B,OAAO,EAAE,GAAG,CAAC,OAAO,IAAI,oBAAoB;QAC5C,UAAU,EAAE,GAAG,CAAC,UAAU,IAAI,GAAG;KAClC,CAAC;IAEF,gDAAgD;IAChD,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;QAC3C,KAAK,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC;IAC1B,CAAC;IAED,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;QACvC,OAAO,EAAE,KAAK;QACd,KAAK,EAAE,KAAK;KACb,CAAC,CAAC;AACL,CAAC,CAAC;AAEqB,0CAAe"}