"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const uuid_1 = require("uuid");
const authMiddleware_1 = require("../middlewares/authMiddleware");
const sseService_1 = require("../services/sseService");
const router = express_1.default.Router();
// Route cho kết nối SSE
router.get("/events", authMiddleware_1.authenticateForSSE, (req, res) => {
    try {
        const user = req.user;
        if (!user || !user._id) {
            // console.error("SSE connection rejected: Invalid user object");
            res.status(401).end();
            return;
        }
        const userId = user._id.toString();
        // Sử dụng clientId từ query hoặc tạo mới nếu không có
        const clientId = req.query.clientId || (0, uuid_1.v4)();
        // console.log(
        //   `SSE connection request from user ${userId}, clientId: ${clientId}`
        // );
        // Thiết lập headers ngay lập tức để tránh timeout
        res.setHeader("Content-Type", "text/event-stream");
        res.setHeader("Cache-Control", "no-cache");
        res.setHeader("Connection", "keep-alive");
        res.setHeader("X-Accel-Buffering", "no");
        // Gửi một event ban đầu để kiểm tra kết nối
        res.write(`:ok\n\n`);
        // Thêm kết nối mới vào service
        (0, sseService_1.addConnection)(userId, clientId, res);
        // Bắt lỗi khi client ngắt kết nối
        req.on("close", () => {
            // console.log(
            //   `SSE connection closed for user ${userId}, clientId: ${clientId}`
            // );
        });
    }
    catch (error) {
        // console.error("Error establishing SSE connection:", error);
        res.status(500).end();
    }
});
exports.default = router;
//# sourceMappingURL=sseRoutes.js.map