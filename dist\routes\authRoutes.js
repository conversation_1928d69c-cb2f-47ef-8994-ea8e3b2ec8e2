"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const passport_1 = __importDefault(require("passport"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const crypto_1 = __importDefault(require("crypto"));
const User_1 = __importDefault(require("../models/User"));
const ResetToken_1 = __importDefault(require("../models/ResetToken"));
const auth_config_1 = require("../config/auth-config");
const authMiddleware_1 = require("../middlewares/authMiddleware");
const rateLimitMiddleware_1 = require("../middlewares/rateLimitMiddleware");
const sseService = __importStar(require("../services/sseService"));
const sessionService = __importStar(require("../services/sessionService"));
const emailService = __importStar(require("../services/emailService"));
const Session_1 = __importDefault(require("../models/Session"));
const asynHandler_1 = __importDefault(require("../util/asynHandler"));
const router = express_1.default.Router();
// Tạo JWT token
const generateToken = (user) => {
    const secret = process.env.JWT_SECRET || "default_secret";
    const expiration = "90d";
    // Sử dụng 'as any' để bỏ qua lỗi TypeScript
    return jsonwebtoken_1.default.sign({ id: user._id, email: user.email }, secret, {
        expiresIn: expiration,
    });
};
// Kiểm tra trạng thái xác thực (cho client gọi mỗi 3 phút)
router.get("/check", authMiddleware_1.authenticateToken, (0, asynHandler_1.default)((req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // Nếu đã đi qua middleware authenticateToken, người dùng đã được xác thực
        // Token mới (nếu cần) đã được cập nhật trong middleware
        return res.status(200).json({
            isAuthenticated: true,
            message: "Phiên đăng nhập hợp lệ",
            user: {
                id: req.user._id,
                email: req.user.email,
            },
        });
    }
    catch (error) {
        console.error("Auth check error:", error);
        return res.status(500).json({
            isAuthenticated: false,
            message: "Lỗi server khi kiểm tra xác thực",
            error: error.message,
        });
    }
})));
// Khởi tạo đăng nhập Google
router.get("/google", passport_1.default.authenticate("google", { scope: ["profile", "email"] }));
// Callback URL sau khi Google xác thực
router.get("/google/callback", passport_1.default.authenticate("google", {
    session: false,
    failureRedirect: "/login?error=Đăng nhập thất bại",
}), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = req.user;
        // console.log(`Google auth callback for user: ${user.email}`);
        // Lấy clientId từ query hoặc tạo mới với định dạng rõ ràng
        const clientId = req.query.clientId ||
            `device_${Math.random().toString(36).substring(2, 10)}_${Date.now()}`;
        // console.log(`Using clientId: ${clientId} for login callback`);
        // Kiểm tra các phiên đăng nhập hiện tại
        const activeSessions = yield Session_1.default.find({
            userId: user._id,
            isActive: true,
        });
        if (activeSessions.length > 0) {
            // console.log(
            //   `User ${user._id} (${user.email}) logged in from a new device. Found ${activeSessions.length} active sessions.`
            // );
            // Đánh dấu thiết bị hiện tại là thiết bị hoạt động mới nhất
            yield sseService.setCurrentDeviceAsActive(user._id.toString(), clientId);
            // console.log(
            //   `Set clientId ${clientId} as active device for user ${user._id}`
            // );
            // QUAN TRỌNG: Đăng xuất tất cả thiết bị khác TRƯỚC KHI CẬP NHẬT TOKEN
            yield sseService.logoutOtherDevices(user._id.toString(), clientId);
            // Đợi một chút để đảm bảo thông báo được gửi
            yield new Promise((resolve) => setTimeout(resolve, 500));
        }
        else {
            // console.log(
            //   `First login or no active sessions for user ${user._id} (${user.email})`
            // );
            // Vẫn cần đánh dấu thiết bị này là thiết bị hoạt động mới nhất cho lần đăng nhập đầu tiên
            yield sseService.setCurrentDeviceAsActive(user._id.toString(), clientId);
            // console.log(
            //   `Set clientId ${clientId} as active device for user ${user._id} (first login)`
            // );
        }
        // Tạo phiên đăng nhập mới
        const { token, session } = yield sessionService.createSession(user, clientId, req);
        console.log(`Created new session with ID: ${session._id} for user ${user._id}`);
        // Lưu token vào cookie
        res.cookie("jwt", token, auth_config_1.AuthUtils.getCookieOptions());
        // console.log(`JWT token set in cookie for user ${user._id}`);
        // Thêm script để lưu JWT vào localStorage (không lưu token thực, chỉ lưu trạng thái có token)
        const authScript = `
        <script>
          // Lưu trạng thái đăng nhập và thông tin người dùng vào localStorage
          localStorage.setItem('auth_status', 'logged_in');
          localStorage.setItem('user_id', '${user._id}');
          localStorage.setItem('clientId', '${clientId}');
          localStorage.setItem('session_id', '${session._id}');
          console.log('Auth data stored in localStorage');
          
          // Chuyển hướng đến trang home
          window.location.href = '/home?clientId=${clientId}';
        </script>
      `;
        // Trả về trang HTML với script thay vì chuyển hướng trực tiếp
        res.send(`
        <!DOCTYPE html>
        <html>
          <head>
            <title>Đang chuyển hướng...</title>
          </head>
          <body>
            <div style="text-align: center; margin-top: 100px;">
              <h2>Đăng nhập thành công</h2>
              <p>Đang chuyển hướng đến trang chính...</p>
            </div>
            ${authScript}
          </body>
        </html>
      `);
    }
    catch (error) {
        // console.error("Google auth callback error:", error);
        res.redirect("/login?error=Lỗi đăng nhập: " + error.message);
    }
}));
// Đăng xuất
router.get("/logout", (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // Lấy token từ cookie
        const token = req.cookies.jwt;
        // Lấy clientId từ query hoặc localStorage (thông qua script)
        const clientId = req.query.clientId;
        if (token) {
            // Đăng xuất phiên hiện tại
            yield sessionService.logoutSession(token);
            // console.log(
            //   `Logged out session with token: ${token.substring(0, 10)}...`
            // );
        }
        // Xóa cookie
        res.clearCookie("jwt");
        // Trả về script để xóa localStorage trước khi chuyển hướng
        const logoutScript = `
      <script>
        // Xóa dữ liệu xác thực khỏi localStorage
        localStorage.removeItem('auth_status');
        localStorage.removeItem('user_id');
        localStorage.removeItem('session_id');
        
        // Không xóa clientId để có thể sử dụng lại
        console.log('Auth data cleared from localStorage');
        
        // Chuyển hướng về trang đăng nhập
        window.location.href = '/login';
      </script>
    `;
        // Trả về trang HTML với script để đảm bảo localStorage được xóa
        res.send(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Đăng xuất...</title>
        </head>
        <body>
          <div style="text-align: center; margin-top: 100px;">
            <h2>Đăng xuất thành công</h2>
            <p>Đang chuyển hướng về trang đăng nhập...</p>
          </div>
          ${logoutScript}
        </body>
      </html>
    `);
    }
    catch (error) {
        res.redirect("/login?error=Lỗi đăng xuất: " + error.message);
    }
}));
// Kiểm tra trạng thái đăng nhập - API
router.get("/status", authMiddleware_1.authenticateToken, (0, asynHandler_1.default)((req, res) => __awaiter(void 0, void 0, void 0, function* () {
    console.log("Kiểm tra trạng thái đăng nhập");
    const user = req.user;
    const session = req.session;
    res.status(200).json({
        success: true,
        // user: {
        //   id: user._id,
        //   email: user.email,
        //   displayName: user.displayName,
        //   profilePhoto: user.profilePhoto,
        // },
        // session: {
        //   id: session._id,
        //   clientId: session.clientId,
        //   deviceInfo: session.deviceInfo,
        //   lastActive: session.lastActive,
        //   createdAt: session.createdAt,
        // },
    });
})));
// Lấy danh sách các phiên đăng nhập
router.get("/sessions", authMiddleware_1.authenticateToken, (0, asynHandler_1.default)((req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = req.user;
        // Lấy danh sách phiên
        const sessions = yield sessionService.getUserSessions(user._id.toString());
        // Định dạng lại dữ liệu để hiển thị
        const formattedSessions = sessions.map((session) => ({
            id: session._id,
            deviceInfo: {
                browser: session.deviceInfo.browser || "Unknown",
                os: session.deviceInfo.os || "Unknown",
                deviceName: session.deviceInfo.deviceName || "Unknown device",
            },
            isCurrentDevice: session.isCurrentDevice,
            lastActive: session.lastActive,
            createdAt: session.createdAt,
        }));
        return res.status(200).json({
            success: true,
            sessions: formattedSessions,
        });
    }
    catch (error) {
        // console.error("Error fetching sessions:", error);
        return res.status(500).json({
            success: false,
            message: "Lỗi khi lấy danh sách phiên đăng nhập",
            error: error.message,
        });
    }
})));
// Đăng xuất một phiên cụ thể
router.post("/logout-session/:sessionId", (0, asynHandler_1.default)(authMiddleware_1.authenticateToken), (0, asynHandler_1.default)((req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = req.user;
        const { sessionId } = req.params;
        // Tìm phiên
        const session = yield Session_1.default.findById(sessionId);
        if (!session) {
            return res.status(404).json({
                success: false,
                message: "Không tìm thấy phiên đăng nhập",
            });
        }
        // Kiểm tra phiên có thuộc về user này không
        if (session.userId.toString() !== user._id.toString()) {
            return res.status(403).json({
                success: false,
                message: "Bạn không có quyền đăng xuất phiên này",
            });
        }
        // Kiểm tra xem có phải phiên hiện tại không
        if (session.token === req.cookies.jwt) {
            return res.status(400).json({
                success: false,
                message: "Không thể đăng xuất phiên hiện tại qua API này. Vui lòng sử dụng /auth/logout",
            });
        }
        // Xóa hoàn toàn phiên thay vì chỉ vô hiệu hóa
        yield Session_1.default.deleteOne({ _id: sessionId });
        // Nếu cần, gửi thông báo đến thiết bị đó qua SSE
        yield sseService.logoutOtherDevices(user._id.toString(), req.session.clientId);
        return res.status(200).json({
            success: true,
            message: "Đã xóa phiên đăng nhập thành công",
        });
    }
    catch (error) {
        // console.error("Error logging out session:", error);
        return res.status(500).json({
            success: false,
            message: "Lỗi khi đăng xuất phiên",
            error: error.message,
        });
    }
})));
// ==== ROUTES CHO EMAIL/PASSWORD LOGIN ====
// Đăng ký tài khoản mới với email/password
router.post("/register", (0, asynHandler_1.default)((req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { email, password, displayName } = req.body;
        // Validate input
        if (!email || !password || !displayName) {
            return res.status(400).json({
                success: false,
                message: "Email, mật khẩu và tên hiển thị là bắt buộc",
            });
        }
        if (password.length < 6) {
            return res.status(400).json({
                success: false,
                message: "Mật khẩu phải có ít nhất 6 ký tự",
            });
        }
        // Kiểm tra email đã tồn tại
        const existingUser = yield User_1.default.findOne({ email });
        if (existingUser) {
            return res.status(409).json({
                success: false,
                message: "Tài khoản đã tồn tại",
            });
        }
        // Tạo avatar từ ui-avatars
        const profilePhoto = `https://ui-avatars.com/api/?name=${encodeURIComponent(email)}&background=random`;
        // Tạo user mới
        const newUser = new User_1.default({
            email,
            password,
            displayName,
            profilePhoto,
        });
        yield newUser.save();
        // Gửi email chào mừng (không chặn nếu lỗi)
        try {
            yield emailService.sendWelcomeEmail(email, displayName);
        }
        catch (emailError) {
            console.error("Failed to send welcome email:", emailError);
        }
        res.status(201).json({
            success: true,
            message: "Đăng ký thành công! Vui lòng đăng nhập.",
        });
    }
    catch (error) {
        console.error("Register error:", error);
        res.status(500).json({
            success: false,
            message: "Lỗi server khi đăng ký",
            error: error.message,
        });
    }
})));
// Đăng nhập với email/password
router.post("/login", (0, asynHandler_1.default)((req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { email, password } = req.body;
        // Validate input
        if (!email || !password) {
            return res.status(400).json({
                success: false,
                message: "Email và mật khẩu là bắt buộc",
            });
        }
        // Tìm user
        const user = yield User_1.default.findOne({ email });
        if (!user || !user.password) {
            return res.status(401).json({
                success: false,
                message: "Email hoặc mật khẩu không đúng",
            });
        }
        // Kiểm tra password
        const isPasswordValid = yield user.comparePassword(password);
        if (!isPasswordValid) {
            return res.status(401).json({
                success: false,
                message: "Email hoặc mật khẩu không đúng",
            });
        }
        // Tạo clientId
        const clientId = `device_${Math.random()
            .toString(36)
            .substring(2, 10)}_${Date.now()}`;
        // Kiểm tra các phiên đăng nhập hiện tại (giống logic Google OAuth)
        const activeSessions = yield Session_1.default.find({
            userId: user._id,
            isActive: true,
        });
        if (activeSessions.length > 0) {
            // Đánh dấu thiết bị hiện tại là thiết bị hoạt động mới nhất
            yield sseService.setCurrentDeviceAsActive(user._id.toString(), clientId);
            // Đăng xuất tất cả thiết bị khác
            yield sseService.logoutOtherDevices(user._id.toString(), clientId);
            // Đợi một chút để đảm bảo thông báo được gửi
            yield new Promise((resolve) => setTimeout(resolve, 500));
        }
        else {
            // Đánh dấu thiết bị này là thiết bị hoạt động mới nhất cho lần đăng nhập đầu tiên
            yield sseService.setCurrentDeviceAsActive(user._id.toString(), clientId);
        }
        // Tạo phiên đăng nhập mới
        const { token, session } = yield sessionService.createSession(user, clientId, req);
        // Lưu token vào cookie
        res.cookie("jwt", token, auth_config_1.AuthUtils.getCookieOptions());
        res.status(200).json({
            success: true,
            message: "Đăng nhập thành công",
            user: {
                id: user._id,
                email: user.email,
                displayName: user.displayName,
                profilePhoto: user.profilePhoto,
            },
            clientId,
            sessionId: session._id,
        });
    }
    catch (error) {
        console.error("Login error:", error);
        res.status(500).json({
            success: false,
            message: "Lỗi server khi đăng nhập",
            error: error.message,
        });
    }
})));
// Quên mật khẩu
router.post("/forgot-password", (0, asynHandler_1.default)(rateLimitMiddleware_1.forgotPasswordRateLimit), (0, asynHandler_1.default)((req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { email } = req.body;
        if (!email) {
            return res.status(400).json({
                success: false,
                message: "Email là bắt buộc",
            });
        }
        // Kiểm tra email có tồn tại không
        const user = yield User_1.default.findOne({ email });
        if (!user) {
            // Không tiết lộ thông tin về việc email có tồn tại hay không
            return res.status(200).json({
                success: true,
                message: "Nếu email tồn tại, liên kết đặt lại mật khẩu đã được gửi.",
            });
        }
        // Tạo reset token
        const resetToken = crypto_1.default.randomBytes(32).toString("hex");
        const expiresAt = new Date(Date.now() + auth_config_1.AuthConfig.RESET_TOKEN.EXPIRATION);
        // Xóa token cũ nếu có
        yield ResetToken_1.default.deleteMany({ email });
        // Lưu token mới
        const newResetToken = new ResetToken_1.default({
            email,
            token: resetToken,
            expiresAt,
        });
        yield newResetToken.save();
        // Gửi email
        yield emailService.sendForgotPasswordEmail(email, resetToken);
        res.status(200).json({
            success: true,
            message: "Liên kết đặt lại mật khẩu đã được gửi đến email của bạn. Nếu bạn không nhận được email, vui lòng kiểm tra thư rác.",
        });
    }
    catch (error) {
        console.error("Forgot password error:", error);
        res.status(500).json({
            success: false,
            message: "Không thể gửi email đặt lại mật khẩu. Vui lòng thử lại sau.",
            error: error.message,
        });
    }
})));
// Đặt lại mật khẩu
router.post("/reset-password", (0, asynHandler_1.default)((req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { token, newPassword } = req.body;
        if (!token || !newPassword) {
            return res.status(400).json({
                success: false,
                message: "Token và mật khẩu mới là bắt buộc",
            });
        }
        if (newPassword.length < 6) {
            return res.status(400).json({
                success: false,
                message: "Mật khẩu phải có ít nhất 6 ký tự",
            });
        }
        // Tìm và kiểm tra token
        const resetToken = yield ResetToken_1.default.findOne({
            token,
            used: false,
            expiresAt: { $gt: new Date() },
        });
        if (!resetToken) {
            return res.status(400).json({
                success: false,
                message: "Token không hợp lệ hoặc đã hết hạn",
            });
        }
        // Tìm user
        const user = yield User_1.default.findOne({ email: resetToken.email });
        if (!user) {
            return res.status(404).json({
                success: false,
                message: "Không tìm thấy người dùng",
            });
        }
        // Cập nhật mật khẩu
        user.password = newPassword;
        yield user.save();
        // Đánh dấu token đã sử dụng
        resetToken.used = true;
        yield resetToken.save();
        // Đăng xuất tất cả phiên hiện tại của user này
        yield Session_1.default.updateMany({ userId: user._id }, { isActive: false });
        res.status(200).json({
            success: true,
            message: "Đặt lại mật khẩu thành công. Vui lòng đăng nhập lại.",
        });
    }
    catch (error) {
        console.error("Reset password error:", error);
        res.status(500).json({
            success: false,
            message: "Lỗi server khi đặt lại mật khẩu",
            error: error.message,
        });
    }
})));
exports.default = router;
//# sourceMappingURL=authRoutes.js.map