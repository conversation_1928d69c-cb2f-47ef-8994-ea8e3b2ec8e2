"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RedisActiveDeviceService = void 0;
const ioredis_1 = __importDefault(require("ioredis"));
class RedisActiveDeviceService {
    constructor() {
        this.redis = null;
        this.isConnected = false;
        this.fallbackMap = new Map(); // Fallback cho khi Redis fail
        this.ACTIVE_DEVICES_PREFIX = "active_device:";
        this.DEFAULT_TTL = 7 * 24 * 60 * 60; // 7 ngày (seconds)
        this.initializeRedis();
    }
    initializeRedis() {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const config = {
                    host: process.env.REDIS_HOST || "localhost",
                    port: parseInt(process.env.REDIS_PORT || "6379"),
                    password: process.env.REDIS_PASSWORD || undefined,
                    retryDelayOnFailover: 100,
                    maxRetriesPerRequest: 3,
                    lazyConnect: true,
                    connectTimeout: 10000,
                    commandTimeout: 5000,
                };
                this.redis = new ioredis_1.default(config);
                // Event handlers
                this.redis.on("connect", () => {
                    console.log("✅ Redis connected successfully");
                    this.isConnected = true;
                });
                this.redis.on("error", (error) => {
                    console.error("❌ Redis connection error:", error.message);
                    this.isConnected = false;
                });
                this.redis.on("close", () => {
                    console.log("⚠️  Redis connection closed");
                    this.isConnected = false;
                });
                this.redis.on("reconnecting", () => {
                    console.log("🔄 Redis reconnecting...");
                });
                // Thử kết nối
                yield this.redis.connect();
            }
            catch (error) {
                console.error("❌ Failed to initialize Redis:", error);
                this.isConnected = false;
            }
        });
    }
    getKey(userId) {
        return `${this.ACTIVE_DEVICES_PREFIX}${userId}`;
    }
    /**
     * Set active device cho user với TTL
     */
    setActiveDevice(userId, clientId) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                if (this.isConnected && this.redis) {
                    const key = this.getKey(userId);
                    yield this.redis.setex(key, this.DEFAULT_TTL, clientId);
                    // Đồng bộ với fallback map
                    this.fallbackMap.set(userId, clientId);
                }
                else {
                    // Fallback khi Redis không khả dụng
                    console.warn("⚠️  Redis not available, using fallback memory storage");
                    this.fallbackMap.set(userId, clientId);
                }
            }
            catch (error) {
                console.error("❌ Error setting active device in Redis:", error);
                // Fallback nếu Redis operation fail
                this.fallbackMap.set(userId, clientId);
            }
        });
    }
    /**
     * Get active device cho user
     */
    getActiveDevice(userId) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                if (this.isConnected && this.redis) {
                    const key = this.getKey(userId);
                    const clientId = yield this.redis.get(key);
                    if (clientId) {
                        // Đồng bộ với fallback map
                        this.fallbackMap.set(userId, clientId);
                        return clientId;
                    }
                    // Không tìm thấy trong Redis, check fallback
                    return this.fallbackMap.get(userId) || null;
                }
                else {
                    // Redis không khả dụng, dùng fallback
                    return this.fallbackMap.get(userId) || null;
                }
            }
            catch (error) {
                console.error("❌ Error getting active device from Redis:", error);
                // Fallback nếu Redis operation fail
                return this.fallbackMap.get(userId) || null;
            }
        });
    }
    /**
     * Check có active device hay không
     */
    hasActiveDevice(userId) {
        return __awaiter(this, void 0, void 0, function* () {
            const device = yield this.getActiveDevice(userId);
            return device !== null;
        });
    }
    /**
     * Check active device có phải là clientId cụ thể hay không
     */
    isActiveDevice(userId, clientId) {
        return __awaiter(this, void 0, void 0, function* () {
            const activeDevice = yield this.getActiveDevice(userId);
            return activeDevice === clientId;
        });
    }
    /**
     * Check nếu user có active device NHƯNG không phải clientId này
     */
    hasActiveDeviceButNotThis(userId, clientId) {
        return __awaiter(this, void 0, void 0, function* () {
            const activeDevice = yield this.getActiveDevice(userId);
            return activeDevice !== null && activeDevice !== clientId;
        });
    }
    /**
     * Remove active device cho user
     */
    removeActiveDevice(userId) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                if (this.isConnected && this.redis) {
                    const key = this.getKey(userId);
                    yield this.redis.del(key);
                }
                // Remove từ fallback map cũng
                this.fallbackMap.delete(userId);
            }
            catch (error) {
                console.error("❌ Error removing active device from Redis:", error);
                // Vẫn remove từ fallback map
                this.fallbackMap.delete(userId);
            }
        });
    }
    /**
     * Refresh TTL cho active device
     */
    refreshActiveDeviceTTL(userId) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                if (this.isConnected && this.redis) {
                    const key = this.getKey(userId);
                    yield this.redis.expire(key, this.DEFAULT_TTL);
                }
            }
            catch (error) {
                console.error("❌ Error refreshing TTL for active device:", error);
            }
        });
    }
    /**
     * Khôi phục từ database (tương tự restoreActiveDevices cũ)
     */
    restoreFromDatabase(users) {
        return __awaiter(this, void 0, void 0, function* () {
            let count = 0;
            for (const user of users) {
                if (user.lastActiveDevice) {
                    yield this.setActiveDevice(user._id.toString(), user.lastActiveDevice);
                    count++;
                }
            }
            console.log(`🔄 Restored ${count} active devices to Redis`);
            return count;
        });
    }
    /**
     * Get tất cả active devices (cho debug hoặc monitoring)
     */
    getAllActiveDevices() {
        return __awaiter(this, void 0, void 0, function* () {
            const result = {};
            try {
                if (this.isConnected && this.redis) {
                    const pattern = `${this.ACTIVE_DEVICES_PREFIX}*`;
                    const keys = yield this.redis.keys(pattern);
                    if (keys.length > 0) {
                        const values = yield this.redis.mget(...keys);
                        keys.forEach((key, index) => {
                            if (values[index]) {
                                const userId = key.replace(this.ACTIVE_DEVICES_PREFIX, "");
                                result[userId] = values[index];
                            }
                        });
                    }
                }
                // Merge với fallback map
                this.fallbackMap.forEach((clientId, userId) => {
                    if (!result[userId]) {
                        result[userId] = clientId;
                    }
                });
            }
            catch (error) {
                console.error("❌ Error getting all active devices:", error);
                // Return fallback map nếu Redis fail
                this.fallbackMap.forEach((clientId, userId) => {
                    result[userId] = clientId;
                });
            }
            return result;
        });
    }
    /**
     * Health check
     */
    healthCheck() {
        return __awaiter(this, void 0, void 0, function* () {
            let redisHealth = false;
            try {
                if (this.redis && this.isConnected) {
                    yield this.redis.ping();
                    redisHealth = true;
                }
            }
            catch (error) {
                redisHealth = false;
            }
            return {
                redis: redisHealth,
                fallback: this.fallbackMap.size >= 0, // Fallback luôn available
            };
        });
    }
    /**
     * Cleanup - đóng Redis connection
     */
    cleanup() {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                if (this.redis) {
                    yield this.redis.quit();
                    this.redis = null;
                    this.isConnected = false;
                    console.log("✅ Redis connection closed cleanly");
                }
            }
            catch (error) {
                console.error("❌ Error closing Redis connection:", error);
            }
        });
    }
    /**
     * Get connection status
     */
    getConnectionStatus() {
        return {
            isConnected: this.isConnected,
            fallbackSize: this.fallbackMap.size,
        };
    }
}
exports.RedisActiveDeviceService = RedisActiveDeviceService;
// Singleton instance
const redisActiveDeviceService = new RedisActiveDeviceService();
exports.default = redisActiveDeviceService;
//# sourceMappingURL=redisService.js.map