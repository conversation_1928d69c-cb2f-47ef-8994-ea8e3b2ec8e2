<!-- Hidden data for JavaScript -->
<input
  type="hidden"
  id="serverQuestionCount"
  value="<% if (questionsByExam && questionsByExam.length > 0) { %><%= Math.min(100, questionsByExam.reduce((total, exam) => total + exam.questions.length, 0)) %><% } else { %>0<% } %>"
/>

<!-- Load external scripts -->
<% if (currentTab === 'outline') { %>
<script src="/js/client-encryption.js"></script>
<% } %> <% if (currentTab === 'memory') { %>
<script src="/js/memory-exam.js"></script>
<script src="/js/memory-exam-management.js"></script>
<script src="/js/memory-exam-practice.js"></script>
<% } %>

<script src="/js/security-measures.js"></script>

<script>
  // Global variables
  let selectedExamType = null;
  let currentExamId = null;

  // Global function to add questions to memory
  function addToMemory(questionId, examId) {
    // Nếu đã load MemoryExam module, sử dụng phương thức của nó
    if (
      window.MemoryExam &&
      typeof window.MemoryExam.addQuestion === "function"
    ) {
      window.MemoryExam.addQuestion(questionId, examId);
      return;
    }

    // Fallback nếu chưa load module - gọi API trực tiếp
    const productId = "<%= product._id %>";

    const addButton = document.querySelector(
      `[data-question-id="${questionId}"] .add-to-memory-btn`
    );

    if (!addButton) return;

    // Prevent multiple clicks
    if (addButton.disabled) return;

    // Store original state
    const originalHTML = addButton.innerHTML;
    const originalClasses = addButton.className;
    const originalTitle = addButton.title;

    // Show loading state
    addButton.disabled = true;
    addButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    addButton.className =
      "add-to-memory-btn text-gray-400 px-2 focus:outline-none transition-colors cursor-not-allowed";
    addButton.title = "Đang xử lý...";

    // Function to restore original state
    const restoreOriginalState = () => {
      addButton.disabled = false;
      addButton.innerHTML = originalHTML;
      addButton.className = originalClasses;
      addButton.title = originalTitle;
    };

    // Gọi API để lưu
    fetch(`/exam/memory/${productId}/add`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        questionId: questionId,
        examId: examId || null,
        source: "manual",
      }),
    })
      .then((response) => response.json())
      .then((data) => {
        if (data.success) {
          // Hiển thị thông báo thành công
          showToast("Đã thêm câu hỏi vào danh sách ghi nhớ", "success");
          // Update to bookmarked state
          addButton.disabled = false;
          addButton.innerHTML = '<i class="fas fa-bookmark"></i>';
          addButton.className =
            "add-to-memory-btn text-indigo-600 hover:text-indigo-700 px-2 focus:outline-none transition-colors";
          addButton.title = "Xóa khỏi ghi nhớ";
        } else if (data.duplicate) {
          // Khôi phục UI trước khi hiển thị confirmation
          restoreOriginalState();

          // Hiển thị confirmation dialog
          if (confirm(data.message)) {
            // Người dùng chọn xóa câu hỏi khỏi memory
            removeFromMemory(questionId, productId, addButton);
          }
        } else {
          // Khôi phục UI nếu có lỗi khác
          restoreOriginalState();
          showToast(data.message || "Không thể thêm câu hỏi", "error");
        }
      })
      .catch((error) => {
        console.error("Error adding question to memory:", error);
        // Khôi phục UI nếu có lỗi
        restoreOriginalState();
        showToast("Đã xảy ra lỗi khi thêm câu hỏi", "error");
      });
  }

  // Function để xóa câu hỏi khỏi memory
  function removeFromMemory(questionId, productId, addButton) {
    if (!addButton) return;

    // Prevent multiple clicks
    if (addButton.disabled) return;

    // Store original state
    const originalHTML = addButton.innerHTML;
    const originalClasses = addButton.className;
    const originalTitle = addButton.title;

    // Show loading state
    addButton.disabled = true;
    addButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    addButton.className =
      "add-to-memory-btn text-gray-400 px-2 focus:outline-none transition-colors cursor-not-allowed";
    addButton.title = "Đang xử lý...";

    // Function to restore original state
    const restoreOriginalState = () => {
      addButton.disabled = false;
      addButton.innerHTML = originalHTML;
      addButton.className = originalClasses;
      addButton.title = originalTitle;
    };

    fetch(`/exam/memory/${productId}/${questionId}`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
      },
    })
      .then((response) => response.json())
      .then((data) => {
        if (data.success) {
          showToast("Đã xóa câu hỏi khỏi danh sách ghi nhớ", "success");
          // Cập nhật UI để hiển thị trạng thái chưa được thêm vào memory
          addButton.disabled = false;
          addButton.innerHTML = '<i class="far fa-bookmark"></i>';
          addButton.className =
            "add-to-memory-btn text-gray-400 hover:text-indigo-500 px-2 focus:outline-none transition-colors";
          addButton.title = "Thêm vào ghi nhớ";
        } else {
          restoreOriginalState();
          showToast(data.message || "Không thể xóa câu hỏi", "error");
        }
      })
      .catch((error) => {
        console.error("Error removing question from memory:", error);
        restoreOriginalState();
        showToast("Đã xảy ra lỗi khi xóa câu hỏi", "error");
      });
  }

  // Function để toggle memory question (one-click toggle cho outline page)
  function toggleMemoryQuestion(questionId, examId) {
    const productId = "<%= product._id %>";
    const addButton = document.querySelector(
      `[data-question-id="${questionId}"] .add-to-memory-btn`
    );

    if (!addButton) return;

    // Prevent multiple clicks by checking if button is disabled
    if (addButton.disabled) return;

    // Store original state
    const originalHTML = addButton.innerHTML;
    const originalClasses = addButton.className;
    const originalTitle = addButton.title;

    // Check current state based on icon
    const isCurrentlyMemorized =
      addButton.innerHTML.includes("fas fa-bookmark");

    // Disable button and show loading state
    addButton.disabled = true;
    addButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    addButton.className =
      "add-to-memory-btn text-gray-400 px-2 focus:outline-none transition-colors cursor-not-allowed";
    addButton.title = "Đang xử lý...";

    // Function to restore original state on error
    const restoreOriginalState = () => {
      addButton.disabled = false;
      addButton.innerHTML = originalHTML;
      addButton.className = originalClasses;
      addButton.title = originalTitle;
    };

    if (isCurrentlyMemorized) {
      // Remove from memory without confirmation
      fetch(`/exam/memory/${productId}/${questionId}`, {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
      })
        .then((response) => response.json())
        .then((data) => {
          if (data.success) {
            showToast("Đã xóa câu hỏi khỏi danh sách ghi nhớ", "success");
            // Update UI to show not memorized state
            addButton.disabled = false;
            addButton.innerHTML = '<i class="far fa-bookmark"></i>';
            addButton.className =
              "add-to-memory-btn text-gray-400 hover:text-indigo-500 px-2 focus:outline-none transition-colors";
            addButton.title = "Thêm vào ghi nhớ";

            // Update global memorizedQuestionIds array
            if (window.memorizedQuestionIds) {
              const index = window.memorizedQuestionIds.indexOf(questionId);
              if (index > -1) {
                window.memorizedQuestionIds.splice(index, 1);
              }
            }
          } else {
            restoreOriginalState();
            showToast(data.message || "Không thể xóa câu hỏi", "error");
          }
        })
        .catch((error) => {
          console.error("Error removing question from memory:", error);
          restoreOriginalState();
          showToast("Đã xảy ra lỗi khi xóa câu hỏi", "error");
        });
    } else {
      // Add to memory
      fetch(`/exam/memory/${productId}/add`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          questionId: questionId,
          examId: examId || null,
          source: "manual",
        }),
      })
        .then((response) => response.json())
        .then((data) => {
          if (data.success) {
            showToast("Đã thêm câu hỏi vào danh sách ghi nhớ", "success");
            // Update UI to show memorized state
            addButton.disabled = false;
            addButton.innerHTML = '<i class="fas fa-bookmark"></i>';
            addButton.className =
              "add-to-memory-btn text-indigo-600 hover:text-indigo-700 px-2 focus:outline-none transition-colors";
            addButton.title = "Xóa khỏi ghi nhớ";

            // Update global memorizedQuestionIds array
            if (
              window.memorizedQuestionIds &&
              !window.memorizedQuestionIds.includes(questionId)
            ) {
              window.memorizedQuestionIds.push(questionId);
            }
          } else if (data.duplicate) {
            // This shouldn't happen with the new toggle logic, but handle just in case
            restoreOriginalState();
            showToast("Câu hỏi đã có trong danh sách ghi nhớ", "info");
          } else {
            restoreOriginalState();
            showToast(data.message || "Không thể thêm câu hỏi", "error");
          }
        })
        .catch((error) => {
          console.error("Error adding question to memory:", error);
          restoreOriginalState();
          showToast("Đã xảy ra lỗi khi thêm câu hỏi", "error");
        });
    }
  }

  // Hàm hiển thị thông báo toast đơn giản
  function showToast(message, type = "info") {
    // Kiểm tra nếu có toast container, nếu không tạo mới
    let toastContainer = document.getElementById("toast-container");

    if (!toastContainer) {
      toastContainer = document.createElement("div");
      toastContainer.id = "toast-container";
      toastContainer.style.position = "fixed";
      toastContainer.style.top = "20px";
      toastContainer.style.right = "20px";
      toastContainer.style.zIndex = "9999";
      document.body.appendChild(toastContainer);
    }

    // Tạo toast element
    const toast = document.createElement("div");

    // Xác định màu sắc dựa trên loại thông báo
    let bgColor, textColor, icon;

    switch (type) {
      case "success":
        bgColor = "bg-green-100";
        textColor = "text-green-700";
        icon = '<i class="fas fa-check-circle mr-2"></i>';
        break;
      case "error":
        bgColor = "bg-red-100";
        textColor = "text-red-700";
        icon = '<i class="fas fa-exclamation-circle mr-2"></i>';
        break;
      case "warning":
        bgColor = "bg-yellow-100";
        textColor = "text-yellow-700";
        icon = '<i class="fas fa-exclamation-triangle mr-2"></i>';
        break;
      default:
        bgColor = "bg-blue-100";
        textColor = "text-blue-700";
        icon = '<i class="fas fa-info-circle mr-2"></i>';
    }

    // Thiết lập style cho toast
    toast.className = `${bgColor} ${textColor} px-4 py-3 rounded-lg shadow-md mb-2 flex items-center`;
    toast.innerHTML = `${icon}${message}`;

    // Thêm toast vào container
    toastContainer.appendChild(toast);

    // Sau 3 giây, xóa toast
    setTimeout(() => {
      toast.style.opacity = "0";
      toast.style.transition = "opacity 0.5s ease";
      setTimeout(() => {
        toastContainer.removeChild(toast);
      }, 500);
    }, 3000);
  }

  // Exam modal functions
  function openExamModal(examId, examName) {
    currentExamId = examId;
    document.getElementById("examId").value = examId;
    document.getElementById(
      "modalTitle"
    ).textContent = `Chọn hình thức làm bài: ${examName}`;
    document.getElementById("examModal").classList.remove("hidden");
    selectedExamType = null;
    document.getElementById("startExamBtn").disabled = true;

    // Reset selection
    document.querySelectorAll(".option-card").forEach((card) => {
      card.classList.remove("border-indigo-500", "bg-indigo-50");
    });
  }

  function closeExamModal() {
    document.getElementById("examModal").classList.add("hidden");
  }

  function selectExamType(type) {
    selectedExamType = type;
    document.getElementById("startExamBtn").disabled = false;

    // Reset all cards
    document.querySelectorAll(".option-card").forEach((card) => {
      card.classList.remove("border-indigo-500", "bg-indigo-50");
    });

    // Highlight selected card
    const selectedCard = document.querySelector(
      `.option-card:nth-child(${type === "google-form" ? "1" : "2"})`
    );
    selectedCard.classList.add("border-indigo-500", "bg-indigo-50");
  }

  function startExam() {
    if (!selectedExamType || !currentExamId) return;

    // Get checkbox states
    const shuffleQuestions =
      document.getElementById("shuffleQuestions").checked;
    const shuffleAnswers = document.getElementById("shuffleAnswers").checked;

    // Create query parameters
    const queryParams = new URLSearchParams();
    if (shuffleQuestions) queryParams.set("shuffleQuestions", "true");
    if (shuffleAnswers) queryParams.set("shuffleAnswers", "true");

    const queryString = queryParams.toString();
    const urlSuffix = queryString ? `?${queryString}` : "";

    // Redirect based on selected exam type
    if (selectedExamType === "google-form") {
      window.location.href = `/exam/${currentExamId}/google-form${urlSuffix}`;
    } else if (selectedExamType === "quizizz") {
      window.location.href = `/exam/${currentExamId}/quizizz${urlSuffix}`;
    }

    closeExamModal();
  }

  // Practice exam functions
  async function loadPracticeHistory() {
    const container = document.getElementById("practiceHistoryContainer");
    const loadingEl = document.getElementById("historyLoading");

    if (!container || !loadingEl) return;

    loadingEl.style.display = "block";

    try {
      const productId = "<%= product._id %>";
      const response = await fetch(`/exam/practice/${productId}/history`);
      const data = await response.json();

      loadingEl.style.display = "none";

      if (data.success && data.data && data.data.length > 0) {
        const historyHtml = data.data
          .map((item, index) => {
            const completedAt = new Date(item.completedAt);
            const duration = Math.round(item.duration / 60);
            const scoreColor =
              item.score >= 70
                ? "text-green-600"
                : item.score >= 50
                ? "text-yellow-600"
                : "text-red-600";
            const bgColor =
              item.score >= 70
                ? "bg-green-50"
                : item.score >= 50
                ? "bg-yellow-50"
                : "bg-red-50";
            const scoreDisplay = (item.correctAnswers * 0.1).toFixed(1);
            const statusDisplay = getStatusDisplay(item.status);

            return `
              <div class="flex items-center justify-between p-3 ${bgColor} rounded-lg mb-2 cursor-pointer hover:bg-opacity-80 transition-colors"
                   onclick="showPracticeHistoryDetail('${item._id}')">
                <div class="flex-1">
                  <div class="flex items-center space-x-2 mb-1">
                    <span class="text-xs sm:text-sm font-medium text-gray-700">Lần ${
                      data.data.length - index
                    }</span>
                    <span class="text-xs text-gray-500">${completedAt.toLocaleDateString(
                      "vi-VN",
                      {
                        day: "2-digit",
                        month: "2-digit",
                        year: "numeric",
                        hour: "2-digit",
                        minute: "2-digit",
                      }
                    )}</span>
                    ${statusDisplay}
                  </div>
                  <div class="flex items-center space-x-3 text-xs sm:text-sm">
                    <span class="${scoreColor} font-semibold">${
              item.score
            }%</span>
                    <span class="text-gray-600">Điểm: ${scoreDisplay}</span>
                    <span class="text-gray-600">${duration} phút</span>
                  </div>
                </div>
                <div class="flex items-center space-x-2">
                  ${
                    item.score >= 70
                      ? '<i class="fas fa-trophy text-yellow-500"></i>'
                      : item.score >= 50
                      ? '<i class="fas fa-thumbs-up text-blue-500"></i>'
                      : '<i class="fas fa-redo text-gray-400"></i>'
                  }
                  <i class="fas fa-chevron-right text-gray-400 text-sm"></i>
                </div>
              </div>
            `;
          })
          .join("");

        container.innerHTML = `
            <div class="space-y-2">
              ${historyHtml}
            </div>
            <div class="text-center mt-3 pt-3 border-t border-gray-200">
              <p class="text-xs text-gray-500">
                Hiển thị ${Math.min(data.data.length, 10)} lần thi gần nhất
              </p>
            </div>
          `;
      } else {
        container.innerHTML = `
            <div class="text-center py-4">
              <i class="fas fa-history text-gray-300 text-2xl mb-2"></i>
              <p class="text-sm text-gray-500">Chưa có lịch sử thi thử nào</p>
              <p class="text-xs text-gray-400 mt-1">Hãy bắt đầu bài thi thử đầu tiên!</p>
            </div>
          `;
      }
    } catch (error) {
      console.error("Error loading practice history:", error);
      loadingEl.style.display = "none";
      container.innerHTML = `
          <div class="text-center py-4">
            <i class="fas fa-exclamation-triangle text-red-400 text-xl mb-2"></i>
            <p class="text-sm text-red-600">Không thể tải lịch sử thi thử</p>
            <button onclick="loadPracticeHistory()" class="text-xs text-indigo-600 hover:text-indigo-800 mt-1">
              <i class="fas fa-redo mr-1"></i>Thử lại
            </button>
          </div>
        `;
    }
  }

  function getStatusDisplay(status) {
    const statusMap = {
      completed:
        '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">🟢 Hoàn thành</span>',
      in_progress:
        '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">🔵 Đang làm</span>',
      time_up:
        '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">🟠 Hết giờ</span>',
      abandoned:
        '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">⚫ Bỏ dở</span>',
    };
    return (
      statusMap[status] ||
      '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">⚫ Không xác định</span>'
    );
  }

  // Practice exam detail modal functions
  async function showPracticeHistoryDetail(practiceId) {
    const modal = document.getElementById("practiceDetailModal");
    const content = document.getElementById("practiceDetailContent");
    const loading = document.getElementById("detailLoading");

    // Xóa dữ liệu cũ trước khi tải dữ liệu mới
    content.innerHTML = "";

    // Tạo context mới để lưu trữ dữ liệu
    const context = {
      currentPracticeId: practiceId,
      fullQuestions: null,
    };

    modal.classList.remove("hidden");
    loading.style.display = "block";

    try {
      const response = await fetch(`/exam/practice/detail/${practiceId}`);
      const data = await response.json();

      loading.style.display = "none";

      if (data.success) {
        const practice = data.data;

        // Kiểm tra dữ liệu fullQuestions
        if (practice.fullQuestions && practice.fullQuestions.length > 0) {
          console.log("✅ Đã nhận được dữ liệu câu hỏi đầy đủ");
        } else {
          console.warn("⚠️ Không nhận được dữ liệu");
        }

        // Gọi renderPracticeDetail với context
        renderPracticeDetail.call(context, practice);
      } else {
        content.innerHTML = `
            <div class="text-center py-8">
              <i class="fas fa-exclamation-triangle text-red-400 text-2xl mb-3"></i>
              <p class="text-red-600 font-medium">Không thể tải chi tiết</p>
              <p class="text-gray-500 text-sm mt-1">${
                data.message || "Đã có lỗi xảy ra"
              }</p>
            </div>
          `;
      }
    } catch (error) {
      console.error("Error loading practice detail:", error);
      loading.style.display = "none";
      content.innerHTML = `
          <div class="text-center py-8">
            <i class="fas fa-wifi text-red-400 text-2xl mb-3"></i>
            <p class="text-red-600 font-medium">Lỗi kết nối</p>
            <p class="text-gray-500 text-sm mt-1">Vui lòng thử lại sau</p>
          </div>
        `;
    }
  }

  function renderPracticeDetail(practice) {
    const content = document.getElementById("practiceDetailContent");
    const completedAt = new Date(practice.completedAt);
    const duration = Math.round(practice.duration / 60);
    const scoreDisplay = (practice.correctAnswers * 0.1).toFixed(1);
    const statusDisplay = getStatusDisplay(practice.status);
    const wrongAnswers = practice.totalQuestions - practice.correctAnswers;

    // Lưu dữ liệu fullQuestions vào this để sử dụng trong các hàm khác
    this.fullQuestions = practice.fullQuestions;
    this.currentPracticeId = practice._id;

    // Chuyển đổi dữ liệu từ selectedQuestions và userAnswers thành format mong đợi
    const processedAnswers = processDetailedResults.call(
      this,
      practice.selectedQuestions,
      practice.userAnswers
    );

    content.innerHTML = `
        <!-- Summary header -->
        <div class="bg-white rounded-lg border border-gray-200 p-6 mb-6">
          <div class="grid grid-cols-1 md:grid-cols-4 gap-4 text-center">
            <div class="bg-blue-50 rounded-lg p-4">
              <div class="text-2xl font-bold text-blue-600">${
                practice.score
              }%</div>
              <div class="text-sm text-gray-600">Điểm số</div>
            </div>
            <div class="bg-green-50 rounded-lg p-4">
              <div class="text-2xl font-bold text-green-600">${
                practice.correctAnswers
              }</div>
              <div class="text-sm text-gray-600">Câu đúng</div>
            </div>
            <div class="bg-red-50 rounded-lg p-4">
              <div class="text-2xl font-bold text-red-600">${wrongAnswers}</div>
              <div class="text-sm text-gray-600">Câu sai</div>
            </div>
            <div class="bg-purple-50 rounded-lg p-4">
              <div class="text-2xl font-bold text-purple-600">${duration} phút</div>
              <div class="text-sm text-gray-600">Thời gian</div>
            </div>
          </div>

          <div class="mt-4 pt-4 border-t border-gray-200 flex justify-between items-center">
            <div>
              <div class="text-sm text-gray-500">Hoàn thành lúc: ${completedAt.toLocaleString(
                "vi-VN"
              )}</div>
              <div class="text-sm text-gray-500">Điểm theo thang 10: ${scoreDisplay}</div>
              <div class="text-sm text-gray-500">ID: ${practice._id}</div>
            </div>
            <div>${statusDisplay}</div>
          </div>
        </div>

        <!-- Question summary grid -->
        <div class="bg-white rounded-lg border border-gray-200 p-6 mb-6">
          <h4 class="font-semibold text-gray-800 mb-4 flex items-center">
            <i class="fas fa-th-large mr-2 text-indigo-500"></i>
            Tổng quan câu hỏi (${processedAnswers.length} câu)
          </h4>
          <div id="questionSummaryGrid-${
            practice._id
          }" class="grid grid-cols-10 gap-2">
            <!-- Will be populated by JavaScript -->
          </div>
        </div>

        <!-- Detailed review -->
        <div class="bg-white rounded-lg border border-gray-200 p-6">
          <h4 class="font-semibold text-gray-800 mb-4 flex items-center">
            <i class="fas fa-list-alt mr-2 text-indigo-500"></i>
            Chi tiết từng câu hỏi
          </h4>
          <div id="detailReviewContainer-${practice._id}">
            <!-- Will be populated by JavaScript -->
          </div>
        </div>
      `;

    // Populate question summary and details
    populateQuestionSummary.call(this, processedAnswers, practice._id);
    renderReviewQuestions.call(this, processedAnswers, practice._id);
  }

  function processDetailedResults(selectedQuestions, userAnswers) {
    // Kiểm tra xem có dữ liệu fullQuestions không (dữ liệu đã được nâng cao)
    if (this.fullQuestions && Array.isArray(this.fullQuestions)) {
      return this.fullQuestions.map((question, index) => {
        // Lấy thông tin đúng/sai trực tiếp từ dữ liệu câu hỏi đã được nâng cao
        const isCorrect =
          question.isCorrect !== undefined ? question.isCorrect : false;
        const selectedAnswerId = question.selectedAnswerId;

        // Tìm index của đáp án đã chọn trong options
        let userAnswerIndex = -1;
        if (selectedAnswerId) {
          const options = question.options || [];
          userAnswerIndex = options.findIndex(
            (opt) =>
              opt._id && opt._id.toString() === selectedAnswerId.toString()
          );
        }

        // Tìm đáp án đúng cho hiển thị
        let correctAnswerIndex = -1;
        let answersArray = question.options || [];

        if (Array.isArray(answersArray)) {
          correctAnswerIndex = answersArray.findIndex(
            (answer) => answer.isCorrect
          );
        }

        // Đã trả lời nếu có selectedAnswerId, không phụ thuộc vào userAnswerIndex
        const isAnswered =
          selectedAnswerId !== null && selectedAnswerId !== undefined;

        return {
          question: {
            text: question.text || "Không có nội dung câu hỏi",
            options: answersArray,
            correctAnswer: correctAnswerIndex,
          },
          userAnswerIndex: userAnswerIndex,
          isCorrect: isCorrect,
          isAnswered: isAnswered,
        };
      });
    }

    // Fallback cho dữ liệu cũ
    console.warn("⚠️ Không tìm thấy dữ liệu fullQuestions, sử dụng fallback");

    if (!selectedQuestions || !Array.isArray(selectedQuestions)) {
      console.error("selectedQuestions is not available or not an array");
      return [];
    }

    // Tạo map từ questionId -> userAnswer object theo model PracticeExamHistory
    const userAnswersByQuestionId = {};
    if (userAnswers && Array.isArray(userAnswers)) {
      userAnswers.forEach((answerObj) => {
        if (answerObj && answerObj.questionId) {
          userAnswersByQuestionId[answerObj.questionId.toString()] = answerObj;
        }
      });
    }

    return selectedQuestions.map((question, index) => {
      // Lấy questionId từ selectedQuestion
      const questionId = question.questionId
        ? question.questionId.toString()
        : question._id
        ? question._id.toString()
        : null;
      const userAnswerObj = questionId
        ? userAnswersByQuestionId[questionId]
        : null;

      let userAnswerIndex = -1;
      let isCorrect = false;
      let selectedAnswerId = null;

      // Xử lý userAnswer nếu có
      if (userAnswerObj) {
        // Sử dụng isCorrect từ database (đã được tính toán khi lưu)
        isCorrect = userAnswerObj.isCorrect || false;
        selectedAnswerId = userAnswerObj.selectedAnswerId;

        // Tìm index của đáp án đã chọn dựa vào selectedAnswerId
        if (userAnswerObj.selectedAnswerId) {
          const options = question.options || question.answers || [];
          userAnswerIndex = options.findIndex(
            (opt) =>
              opt._id &&
              opt._id.toString() === userAnswerObj.selectedAnswerId.toString()
          );
        }
      }

      // Tìm đáp án đúng cho hiển thị
      let correctAnswerIndex = -1;
      let answersArray = question.answers || question.options || [];

      if (Array.isArray(answersArray)) {
        correctAnswerIndex = answersArray.findIndex(
          (answer) => answer.isCorrect
        );
      }

      // Đã trả lời nếu có selectedAnswerId, không phụ thuộc vào userAnswerIndex
      const isAnswered =
        selectedAnswerId !== null && selectedAnswerId !== undefined;

      return {
        question: {
          text: question.text || "Không có nội dung câu hỏi",
          options: answersArray,
          correctAnswer: correctAnswerIndex,
        },
        userAnswerIndex: userAnswerIndex,
        isCorrect: isCorrect, // Sử dụng isCorrect từ database
        isAnswered: isAnswered,
      };
    });
  }

  function populateQuestionSummary(answers, practiceId) {
    const grid = document.getElementById(`questionSummaryGrid-${practiceId}`);
    if (!grid || !answers) return;

    grid.innerHTML = answers
      .map((answer, index) => {
        const isCorrect = answer.isCorrect;
        const isAnswered = answer.isAnswered;

        let bgColor, icon, textColor, title;

        if (!isAnswered) {
          bgColor = "bg-gray-100 hover:bg-gray-200";
          textColor = "text-gray-600";
          icon = "-";
          title = `Câu ${index + 1}: Chưa trả lời`;
        } else if (isCorrect) {
          bgColor = "bg-green-100 hover:bg-green-200";
          textColor = "text-green-800";
          icon = "✓";
          title = `Câu ${index + 1}: Đúng`;
        } else {
          bgColor = "bg-red-100 hover:bg-red-200";
          textColor = "text-red-800";
          icon = "✗";
          title = `Câu ${index + 1}: Sai`;
        }

        return `
          <div class="w-10 h-10 ${bgColor} rounded-lg flex items-center justify-center cursor-pointer transition-colors ${textColor} font-semibold text-sm"
               onclick="scrollToQuestion('${practiceId}', ${index})" title="${title}">
            ${index + 1}
          </div>
        `;
      })
      .join("");

    // Thống kê tổng quan
    const correctCount = answers.filter(
      (a) => a.isAnswered && a.isCorrect
    ).length;
    const wrongCount = answers.filter(
      (a) => a.isAnswered && !a.isCorrect
    ).length;
    const unansweredCount = answers.filter((a) => !a.isAnswered).length;
  }

  function renderReviewQuestions(answers, practiceId) {
    const container = document.getElementById(
      `detailReviewContainer-${practiceId}`
    );
    if (!container || !answers) {
      if (container) {
        container.innerHTML =
          '<div class="text-center py-8 text-gray-500">Không có dữ liệu chi tiết</div>';
      }
      return;
    }

    container.innerHTML = answers
      .map((answer, index) => {
        const question = answer.question;
        const isCorrect = answer.isCorrect;
        const isAnswered = answer.isAnswered;
        const userAnswerIndex = answer.userAnswerIndex;

        let statusText = "";
        let statusClass = "";

        if (!isAnswered) {
          statusText = "Chưa trả lời";
          statusClass = "bg-gray-100 text-gray-600";
        } else if (isCorrect) {
          statusText = "Đúng";
          statusClass = "bg-green-100 text-green-800";
        } else {
          statusText = "Sai";
          statusClass = "bg-red-100 text-red-800";
        }

        return `
          <div id="question-${practiceId}-${index}" class="border border-gray-200 rounded-lg p-4 mb-4 hover:shadow-sm transition-shadow">
            <div class="flex items-start justify-between mb-3">
              <h5 class="font-medium text-gray-800 text-sm">Câu ${
                index + 1
              }</h5>
              <span class="px-2 py-1 rounded text-xs font-medium ${statusClass}">
                ${statusText}
              </span>
            </div>

            <div class="text-gray-700 mb-4 text-sm leading-relaxed">
              ${question.text}
            </div>

            <div class="space-y-2">
              ${question.options
                .map((option, optionIndex) => {
                  const isUserAnswer =
                    isAnswered && userAnswerIndex === optionIndex;
                  const isCorrectAnswer =
                    optionIndex === question.correctAnswer;

                  // Loại bỏ tiền tố A., B., C., D. nếu có
                  let cleanOptionText = option.text || option;
                  if (typeof cleanOptionText === "string") {
                    const prefixRegex = /^\s*(?:[A-D]|[1-9])[\.\)][\s\u00A0]*/i;
                    if (prefixRegex.test(cleanOptionText)) {
                      cleanOptionText = cleanOptionText.replace(
                        prefixRegex,
                        ""
                      );
                    }
                  }

                  let bgColor = "";
                  let textColor = "text-gray-700";
                  let borderColor = "border-gray-200";
                  let icons = "";

                  // Đáp án đúng luôn được highlight xanh
                  if (isCorrectAnswer) {
                    bgColor = "bg-green-50";
                    textColor = "text-green-800";
                    borderColor = "border-green-200";
                    icons +=
                      '<i class="fas fa-check text-green-600 ml-2" title="Đáp án đúng"></i>';
                  }

                  // Nếu user chọn sai, highlight đỏ và thêm icon X
                  if (isUserAnswer && !isCorrectAnswer) {
                    bgColor = "bg-red-50";
                    textColor = "text-red-800";
                    borderColor = "border-red-200";
                    icons +=
                      '<i class="fas fa-times text-red-600 ml-2" title="Bạn đã chọn"></i>';
                  }

                  // Nếu user chọn đúng, chỉ cần icon check (đã có ở trên)
                  if (isUserAnswer && isCorrectAnswer) {
                    icons +=
                      '<i class="fas fa-user-check text-blue-600 ml-1" title="Bạn đã chọn đúng"></i>';
                  }

                  return `
                  <div class="flex items-center p-3 border ${borderColor} rounded-lg ${bgColor} ${textColor} text-sm">
                    <span class="font-medium mr-3 text-xs">${String.fromCharCode(
                      65 + optionIndex
                    )}.</span>
                    <span class="flex-1">${cleanOptionText}</span>
                    ${icons}
                  </div>
                `;
                })
                .join("")}
            </div>
          </div>
        `;
      })
      .join("");
  }

  function scrollToQuestion(practiceId, index) {
    const element = document.getElementById(`question-${practiceId}-${index}`);
    if (element) {
      element.scrollIntoView({ behavior: "smooth", block: "center" });
      element.classList.add("ring-2", "ring-indigo-500");
      setTimeout(() => {
        element.classList.remove("ring-2", "ring-indigo-500");
      }, 2000);
    }
  }

  function closePracticeDetailModal() {
    const modal = document.getElementById("practiceDetailModal");
    const content = document.getElementById("practiceDetailContent");

    // Xóa dữ liệu khi đóng modal
    content.innerHTML = `
        <div class="text-center py-8" id="detailLoading">
          <i class="fas fa-spinner fa-spin text-gray-400 mb-2 text-2xl"></i>
          <p class="text-gray-500">Đang tải chi tiết...</p>
        </div>
      `;

    modal.classList.add("hidden");
  }

  // Practice exam flow functions
  async function startPracticeExam() {
    const serverQuestionCount = parseInt(
      document.getElementById("serverQuestionCount")?.value || "0"
    );

    if (serverQuestionCount === 0) {
      alert("Không có câu hỏi để tạo bài thi thử!");
      return;
    }

    try {
      const productId = "<%= product._id %>";

      // Bước 1: Kiểm tra xem có bài thi đang làm dở không
      const checkResponse = await fetch(`/exam/practice/${productId}/check`, {
        method: "GET",
        headers: { "Content-Type": "application/json" },
      });

      const checkData = await checkResponse.json();

      if (
        checkData.success &&
        (checkData.hasExistingPractice || checkData.hasIncompletePractice)
      ) {
        // Có bài thi đang làm dở - hiển thị modal cho người dùng chọn
        const timeRemaining = document.getElementById("timeRemaining");
        if (timeRemaining && checkData.timeRemaining) {
          const minutes = Math.floor(checkData.timeRemaining / 60);
          const seconds = checkData.timeRemaining % 60;
          timeRemaining.textContent = `${minutes}:${seconds
            .toString()
            .padStart(2, "0")}`;
        }

        document.getElementById("continueModal").classList.remove("hidden");
      } else {
        // Không có bài thi đang làm dở - tạo bài mới luôn
        await createNewPracticeExam();
      }
    } catch (error) {
      console.error("Error checking practice status:", error);
      alert("Có lỗi xảy ra khi kiểm tra trạng thái thi thử. Vui lòng thử lại.");
    }
  }

  async function createNewPracticeExam() {
    try {
      const productId = "<%= product._id %>";

      const response = await fetch(`/exam/practice/${productId}/start`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ action: "new" }),
      });

      const data = await response.json();

      if (data.success && data.practiceId) {
        // Chuyển đến trang làm bài với practiceId
        window.location.href = `/course/${productId}/${data.practiceId}/practice-exam`;
      } else {
        alert(
          "Có lỗi xảy ra khi tạo bài thi thử: " +
            (data.message || "Không rõ nguyên nhân")
        );
      }
    } catch (error) {
      console.error("Error creating practice exam:", error);
      alert("Có lỗi xảy ra khi tạo bài thi thử. Vui lòng thử lại.");
    }
  }

  function startNewPractice() {
    closeContinueModal();
    createNewPracticeExam();
  }

  async function continueExistingPractice() {
    try {
      const productId = "<%= product._id %>";

      // Gọi API để lấy thông tin practice exam hiện tại
      const checkResponse = await fetch(`/exam/practice/${productId}/check`, {
        method: "GET",
        headers: { "Content-Type": "application/json" },
      });

      const checkData = await checkResponse.json();

      if (checkData.success && checkData.practiceId) {
        // Chuyển đến trang làm bài với practiceId của bài đang làm dở
        window.location.href = `/course/${productId}/${checkData.practiceId}/practice-exam`;
      } else {
        alert("Không tìm thấy bài thi đang làm dở. Sẽ tạo bài mới.");
        createNewPracticeExam();
      }

      closeContinueModal();
    } catch (error) {
      console.error("Error continuing existing practice:", error);
      alert("Có lỗi xảy ra. Sẽ tạo bài thi mới.");
      closeContinueModal();
      createNewPracticeExam();
    }
  }

  function closeContinueModal() {
    document.getElementById("continueModal").classList.add("hidden");
  }

  // Outline questions loading functions
  async function loadOutlineQuestions() {
    const container = document.getElementById("questionsContainer");
    const loading = document.getElementById("decryptionLoading");

    if (!container) {
      return;
    }

    try {
      let questionsData = null;

      // Check if encrypted data exists
      const appConfigElement = document.getElementById("appConfigData");

      if (appConfigElement) {
        const configData = JSON.parse(appConfigElement.textContent);

        // Use the global decryptAndLoadQuestions function

        if (typeof window.decryptAndLoadQuestions === "function") {
          questionsData = await window.decryptAndLoadQuestions(
            configData.payload,
            configData.token,
            configData.salt
          );
        } else {
          throw new Error("Decryption function not available");
        }
      } else {
        // Check for non-encrypted data
        const allQuestionsElement = document.getElementById("allQuestionsData");

        if (allQuestionsElement) {
          const rawData = allQuestionsElement.textContent;

          questionsData = JSON.parse(rawData);
        } else {
          // Debug: List all script elements
          const allScripts = document.querySelectorAll(
            'script[type="application/json"]'
          );

          allScripts.forEach((script, index) => {
            console.log(`Script ${index}:`, {
              id: script.id,
              hasContent: !!script.textContent,
              contentLength: script.textContent?.length || 0,
              contentSample: script.textContent?.substring(0, 100) || "empty",
            });
          });

          // Check all elements with possible data
          const serverQuestionCount = document.getElementById(
            "serverQuestionCount"
          );
          console.log(
            "serverQuestionCount element:",
            !!serverQuestionCount,
            serverQuestionCount?.value
          );
        }
      }

      if (questionsData && questionsData.length > 0) {
        displayOutlineQuestions(questionsData);
        setupOutlineControls(questionsData);
      } else {
        console.warn("No questions data found, showing empty state");
        showEmptyState();
      }

      // Hide loading indicator
      if (loading) {
        loading.style.display = "none";
      }
    } catch (error) {
      console.error("Error loading outline questions:", error);

      // Show error message
      container.innerHTML = `
          <div class="text-center py-8">
            <div class="text-red-600 mb-2">
              <i class="fas fa-exclamation-triangle text-2xl"></i>
            </div>
            <p class="text-gray-600 mb-2">Không thể tải dữ liệu câu hỏi</p>
            <p class="text-sm text-gray-500 mb-4">Chi tiết lỗi: ${error.message}</p>
            <button onclick="location.reload()" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
              Tải lại trang
            </button>
          </div>
        `;

      // Hide loading indicator
      if (loading) {
        loading.style.display = "none";
      }
    }
  }

  function displayOutlineQuestions(questionsByExam) {
    const container = document.getElementById("questionsContainer");
    if (!container || !questionsByExam) return;

    // Đảm bảo CSS styles được thêm vào
    addCustomStyles();

    // Debug: Log cấu trúc dữ liệu để hiểu rõ hơn
    console.log(
      "Questions data structure:",
      JSON.parse(JSON.stringify(questionsByExam))
    );

    let html = "";
    let allQuestions = [];
    let examCounter = 1;
    let examNamesMap = new Map(); // Map để theo dõi tên đề thi

    // Đầu tiên, trích xuất tất cả câu hỏi từ tất cả các đề thi vào một mảng duy nhất
    questionsByExam.forEach((examGroup, index) => {
      // Xác định tên đề thi
      let examName;

      // Kiểm tra các thuộc tính khác nhau có thể chứa tên đề thi
      if (
        examGroup.name &&
        typeof examGroup.name === "string" &&
        examGroup.name.trim() !== ""
      ) {
        examName = examGroup.name;
      } else if (examGroup._id) {
        // Sử dụng ID nếu không có tên
        examName = `Đề thi ${examCounter}`;
        examCounter++;
      } else {
        // Fallback nếu không có thuộc tính nào phù hợp
        examName = `Đề thi ${index + 1}`;
      }

      // Lưu tên đề thi vào map để sử dụng sau này
      examNamesMap.set(examName, examName);

      console.log(`Processing exam group ${index}:`, {
        name: examName,
        originalName: examGroup.name,
        id: examGroup._id,
        questionCount: examGroup.questions ? examGroup.questions.length : 0,
      });

      const examQuestions = examGroup.questions || [];

      // Thêm tất cả câu hỏi của đề thi này vào mảng tổng hợp
      allQuestions = allQuestions.concat(
        examQuestions.map((q) => ({
          ...q,
          examName: examName, // Sử dụng tên đề thi đã được xác định
          examId: examGroup._id || `exam-${index}`, // Lưu ID đề thi để tham chiếu
        }))
      );
    });

    // Nếu không có câu hỏi nào, hiển thị thông báo trống
    if (allQuestions.length === 0) {
      showEmptyState();
      return;
    }

    // In ra thông tin debug
    console.log(`Total questions: ${allQuestions.length}`);
    console.log(`Exam names:`, Array.from(examNamesMap.values()));
    console.log(
      "Sample questions with exam names:",
      allQuestions.slice(0, 3).map((q) => ({
        examName: q.examName,
        text: q.text ? q.text.substring(0, 30) : "No text",
      }))
    );

    // Get memorized question IDs from server data
    const memorizedQuestionIds = window.memorizedQuestionIds || [];

    // Hiển thị tất cả câu hỏi trong một danh sách liên tục
    allQuestions.forEach((question, index) => {
      const questionId = question._id || "";
      const isMemorized = memorizedQuestionIds.includes(questionId);

      // Set bookmark icon state based on memory status
      const bookmarkIcon = isMemorized
        ? '<i class="fas fa-bookmark"></i>'
        : '<i class="far fa-bookmark"></i>';
      const bookmarkClasses = isMemorized
        ? "add-to-memory-btn text-indigo-600 hover:text-indigo-700 px-2 focus:outline-none transition-colors"
        : "add-to-memory-btn text-gray-400 hover:text-indigo-500 px-2 focus:outline-none transition-colors";
      const bookmarkTitle = isMemorized
        ? "Xóa khỏi ghi nhớ"
        : "Thêm vào ghi nhớ";

      html += `
            <div class="question-item bg-white rounded-lg border border-gray-200 p-4 mb-4 hover:shadow-sm transition-all"
                 data-question-index="${index}"
                 data-question-id="${questionId}"
                 id="question-${index}"
                 data-exam-name="${question.examName}">
              <div class="flex items-start justify-between mb-3">
                <h4 class="font-medium text-gray-800 text-sm">
                  Câu ${index + 1}
                  <span class="text-xs text-gray-500">(${
                    question.examName
                  })</span>
                </h4>
                <button class="${bookmarkClasses}"
                        onclick="toggleMemoryQuestion('${questionId}', '${
        question.examId || ""
      }')" title="${bookmarkTitle}">
                  ${bookmarkIcon}
                </button>
              </div>

              <div class="text-gray-700 mb-4 text-sm leading-relaxed break-words question-text">
                ${question.text || "Nội dung câu hỏi không có sẵn"}
              </div>

              <div class="answers-container space-y-2" data-question="${index}">
                ${(question.answers || question.options || [])
                  .map((answer, answerIndex) => {
                    const optionLabel = String.fromCharCode(65 + answerIndex);
                    const isCorrect = answer.isCorrect || false;
                    let answerText =
                      answer.text || answer || "Đáp án không có sẵn";

                    // Loại bỏ tiền tố A., B., C., D. nếu có
                    if (typeof answerText === "string") {
                      const prefixRegex =
                        /^\s*(?:[A-D]|[1-9])[\.\)][\s\u00A0]*/i;
                      if (prefixRegex.test(answerText)) {
                        answerText = answerText.replace(prefixRegex, "");
                      }
                    }

                    // Thiết lập style cho đáp án đúng
                    let borderColor = "border-gray-200";
                    let bgColor = "";
                    if (isCorrect) {
                      borderColor = "border-green-200";
                      bgColor = "bg-green-50";
                    }

                    return `
                    <div class="answer-option flex items-center p-3 border ${borderColor} ${bgColor} rounded-lg text-sm answer-text"
                         data-correct="${isCorrect}" data-answer-index="${answerIndex}">
                      <span class="font-medium mr-3 text-xs bg-gray-100 px-2 py-1 rounded-full">${optionLabel}</span>
                      <span class="flex-1 break-words">${answerText}</span>
                      ${
                        isCorrect
                          ? '<span class="correct-indicator ml-2"><i class="fas fa-check text-green-600"></i></span>'
                          : ""
                      }
                    </div>
                  `;
                  })
                  .join("")}
              </div>
            </div>
          `;
    });

    container.innerHTML = html;

    // Update total questions count
    const totalCountElement = document.getElementById("totalQuestionsCount");
    if (totalCountElement) {
      totalCountElement.textContent = allQuestions.length;
    }

    // Cập nhật dropdown lọc đề thi với tên đề thi đã được xác định
    updateExamFilterDropdown(Array.from(examNamesMap.values()));
  }

  // Hàm mới để cập nhật dropdown lọc đề thi
  function updateExamFilterDropdown(examNames) {
    const examFilter = document.getElementById("examFilter");
    if (!examFilter) return;

    // Clear existing options except "Hiển thị tất cả"
    examFilter.innerHTML = '<option value="all">Hiển thị tất cả</option>';

    // Sắp xếp tên đề thi
    examNames.sort();

    // Thêm các option cho mỗi đề thi
    examNames.forEach((examName) => {
      if (examName) {
        // Đảm bảo không thêm tên đề thi rỗng
        const option = document.createElement("option");
        option.value = examName;
        option.textContent = examName;
        examFilter.appendChild(option);
      }
    });

    console.log("Updated exam filter with options:", examNames);
  }

  // Cập nhật lại function setupOutlineControls để sử dụng hàm mới
  function setupOutlineControls(questionsData) {
    // Các hoạt động khác nếu cần
    console.log("Setting up outline controls");
  }

  function showEmptyState() {
    const container = document.getElementById("questionsContainer");
    const loading = document.getElementById("decryptionLoading");

    if (loading) {
      loading.style.display = "none";
    }

    if (container) {
      container.innerHTML = `
          <div class="text-center py-12">
            <div class="text-gray-400 mb-4">
              <i class="fas fa-file-text text-4xl"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-700 mb-2">Chưa có dữ liệu câu hỏi</h3>
            <p class="text-gray-500 mb-4">Hiện tại chưa có câu hỏi nào trong đề cương này.</p>
            <button onclick="location.reload()" class="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors">
              <i class="fas fa-refresh mr-2"></i>Tải lại trang
            </button>
          </div>
        `;
    }
  }

  // Global functions for outline tab - toggle functions removed as answers are always shown

  // Biến toàn cục để quản lý trạng thái tìm kiếm
  let searchState = {
    currentMatchIndex: -1,
    matchedQuestions: [],
    searchTerm: "",
    debounceTimer: null,
  };

  // Hàm debounce để trì hoãn thực thi hàm
  function debounce(func, delay) {
    return function () {
      const context = this;
      const args = arguments;
      clearTimeout(searchState.debounceTimer);
      searchState.debounceTimer = setTimeout(
        () => func.apply(context, args),
        delay
      );
    };
  }

  // Hàm tìm kiếm với debounce
  const debouncedSearch = debounce(function () {
    searchQuestions(true); // true: thực hiện tìm kiếm mới
  }, 500); // 500ms debounce delay

  // Hàm chuyển đến kết quả tìm kiếm tiếp theo
  function nextSearchResult() {
    if (!searchState.matchedQuestions.length) return;

    searchState.currentMatchIndex++;
    if (searchState.currentMatchIndex >= searchState.matchedQuestions.length) {
      searchState.currentMatchIndex = 0; // Quay lại kết quả đầu tiên
    }

    navigateToResult(searchState.currentMatchIndex);
    updateNavigationUI();
  }

  // Hàm chuyển đến kết quả tìm kiếm trước đó
  function prevSearchResult() {
    if (!searchState.matchedQuestions.length) return;

    searchState.currentMatchIndex--;
    if (searchState.currentMatchIndex < 0) {
      searchState.currentMatchIndex = searchState.matchedQuestions.length - 1; // Chuyển đến kết quả cuối cùng
    }

    navigateToResult(searchState.currentMatchIndex);
    updateNavigationUI();
  }

  // Hàm di chuyển đến một kết quả cụ thể
  function navigateToResult(index) {
    if (!searchState.matchedQuestions[index]) return;

    // Loại bỏ highlight active trước đó
    document
      .querySelectorAll(".question-item.active-result")
      .forEach((item) => {
        item.classList.remove("active-result");
      });

    // Thêm class active-result vào kết quả hiện tại
    const currentResult = searchState.matchedQuestions[index];
    currentResult.classList.add("active-result");

    // Cuộn đến kết quả
    currentResult.scrollIntoView({
      behavior: "smooth",
      block: "center",
    });
  }

  // Cập nhật giao diện nút điều hướng
  function updateNavigationUI() {
    const navInfoEl = document.getElementById("searchNavigationInfo");
    if (!navInfoEl) return;

    if (searchState.matchedQuestions.length === 0) {
      navInfoEl.innerHTML = "Không có kết quả";
      return;
    }

    navInfoEl.innerHTML = `${searchState.currentMatchIndex + 1} / ${
      searchState.matchedQuestions.length
    } kết quả`;

    // Cập nhật trạng thái nút
    const prevBtn = document.getElementById("prevResultBtn");
    const nextBtn = document.getElementById("nextResultBtn");

    if (prevBtn) {
      prevBtn.disabled = searchState.matchedQuestions.length <= 1;
    }

    if (nextBtn) {
      nextBtn.disabled = searchState.matchedQuestions.length <= 1;
    }
  }

  // Tạo và thêm các nút điều hướng vào khu vực thông tin tìm kiếm
  function createNavigationButtons() {
    const searchInfo = document.getElementById("searchInfo");
    if (!searchInfo) return;

    // Kiểm tra xem đã có nút điều hướng chưa
    if (document.getElementById("searchNavigation")) return;

    // Tạo phần tử chứa nút điều hướng
    const navigationDiv = document.createElement("div");
    navigationDiv.id = "searchNavigation";
    navigationDiv.className = "flex items-center justify-between";

    navigationDiv.innerHTML = `
      <div id="searchNavigationInfo" class="text-xs font-medium text-gray-600 ml-2">
      Không có kết quả
  </div>
  <div class="flex items-center space-x-1">
      <button id="prevResultBtn" style="padding-top:4px!important; padding-bottom:4px!important; min-height:auto!important;" class="p-1 px-2 sm:py-3 py-0 bg-gray-200 text-gray-700 rounded-l hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-indigo-500" onclick="prevSearchResult()">
          <i class="fas fa-chevron-left"></i>
      </button>
      <button id="nextResultBtn" style="padding-top:4px!important; padding-bottom:4px!important; min-height:auto!important;" class="p-1 px-2 sm:py-3 py-0 bg-gray-200 text-gray-700 rounded-r hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-indigo-500" onclick="nextSearchResult()">
          <i class="fas fa-chevron-right"></i>
      </button>
  </div>


        `;

    // Thêm vào searchInfo
    searchInfo.appendChild(navigationDiv);
  }

  // Cập nhật hàm addCustomStyles để thêm CSS cho kết quả active
  function addCustomStyles() {
    // Nếu đã tồn tại style với ID "outline-custom-styles", không thêm lại
    if (document.getElementById("outline-custom-styles")) {
      return;
    }

    const style = document.createElement("style");
    style.id = "outline-custom-styles";
    style.textContent = `
            .highlight-match {
                border-left: 3px solid #4f46e5 !important;
                box-shadow: 0 4px 6px -1px rgba(79, 70, 229, 0.1), 0 2px 4px -1px rgba(79, 70, 229, 0.06) !important;
            }
            .question-item {
                transition: all 0.3s ease;
            }
            mark {
                background-color: rgba(250, 204, 21, 0.4);
                border-radius: 0.25rem;
                padding: 0 0.25rem;
                transition: background-color 0.3s ease;
            }
            mark:hover {
                background-color: rgba(250, 204, 21, 0.6);
            }
            .active-result {
                border: 2px solid #4f46e5 !important;
                background-color: rgba(79, 70, 229, 0.05) !important;
                box-shadow: 0 4px 6px -1px rgba(79, 70, 229, 0.2), 0 2px 4px -1px rgba(79, 70, 229, 0.1) !important;
            }
        `;
    document.head.appendChild(style);
  }

  // Cập nhật hàm searchQuestions để hỗ trợ điều hướng
  window.searchQuestions = function (isNewSearch = false) {
    const searchInput = document.getElementById("searchInput");
    const clearBtn = document.getElementById("clearSearchBtn");
    const searchInfo = document.getElementById("searchInfo");
    const searchResultCount = document.getElementById("searchResultCount");

    if (!searchInput) return;

    // Đảm bảo CSS styles được thêm vào
    addCustomStyles();

    const searchTerm = searchInput.value.toLowerCase().trim();

    // Nếu là tìm kiếm mới hoặc từ khóa tìm kiếm thay đổi, cập nhật trạng thái
    if (isNewSearch || searchTerm !== searchState.searchTerm) {
      searchState.searchTerm = searchTerm;
      searchState.currentMatchIndex = -1;
      searchState.matchedQuestions = [];
    }

    const allQuestions = document.querySelectorAll(".question-item");
    let visibleCount = 0;

    // Loại bỏ highlighting trước đó
    allQuestions.forEach((question) => {
      question.classList.remove("highlight-match");
      question.classList.remove("active-result");

      // Khôi phục nội dung ban đầu từ chuỗi đã lưu trữ (nếu có)
      const questionText = question.querySelector(".question-text");
      if (questionText && questionText.dataset.originalText) {
        questionText.innerHTML = questionText.dataset.originalText;
        delete questionText.dataset.originalText;
      }

      // Khôi phục nội dung đáp án
      const answerTexts = question.querySelectorAll(".answer-text");
      answerTexts.forEach((answerEl) => {
        if (answerEl.dataset.originalText) {
          const textSpan = answerEl.querySelector(".flex-1");
          if (textSpan) textSpan.innerHTML = answerEl.dataset.originalText;
          delete answerEl.dataset.originalText;
        }
      });
    });

    if (searchTerm) {
      clearBtn.style.display = "block";
      searchInfo.style.display = "block";

      // Thêm nút điều hướng nếu chưa có
      createNavigationButtons();

      allQuestions.forEach((question, idx) => {
        // Hiển thị tất cả câu hỏi
        question.style.display = "block";

        const questionTextEl = question.querySelector(".question-text");
        const questionText = questionTextEl
          ? questionTextEl.textContent.toLowerCase()
          : "";

        const answerTexts = Array.from(
          question.querySelectorAll(".answer-text")
        ).map((el) => {
          const textSpan = el.querySelector(".flex-1");
          return textSpan ? textSpan.textContent.toLowerCase() : "";
        });

        const allText = questionText + " " + answerTexts.join(" ");

        // Kiểm tra nếu có match
        if (allText.includes(searchTerm)) {
          visibleCount++;
          searchState.matchedQuestions.push(question);

          // Highlight kết quả
          question.classList.add("highlight-match");

          // Highlight trong text câu hỏi
          if (questionText.includes(searchTerm) && questionTextEl) {
            // Lưu text gốc để khôi phục sau
            if (!questionTextEl.dataset.originalText) {
              questionTextEl.dataset.originalText = questionTextEl.innerHTML;
            }

            // Highlight từ khóa trong text
            questionTextEl.innerHTML = highlightSearchTerm(
              questionTextEl.innerHTML,
              searchTerm
            );
          }

          // Highlight trong các đáp án
          answerTexts.forEach((text, i) => {
            if (text.includes(searchTerm)) {
              const answerEl = question.querySelectorAll(".answer-text")[i];
              const textSpan = answerEl.querySelector(".flex-1");

              if (textSpan) {
                // Lưu text gốc để khôi phục sau
                if (!answerEl.dataset.originalText) {
                  answerEl.dataset.originalText = textSpan.innerHTML;
                }

                // Highlight từ khóa trong đáp án
                textSpan.innerHTML = highlightSearchTerm(
                  textSpan.innerHTML,
                  searchTerm
                );
              }
            }
          });
        }
      });

      // Cập nhật số lượng kết quả
      searchResultCount.textContent = visibleCount;

      // Nếu là tìm kiếm mới và có kết quả
      if (isNewSearch && searchState.matchedQuestions.length > 0) {
        // Đặt index hiện tại là 0 và di chuyển đến kết quả đầu tiên
        searchState.currentMatchIndex = 0;
        navigateToResult(0);
      }

      // Cập nhật UI
      updateNavigationUI();
    } else {
      // Xóa kết quả tìm kiếm
      clearBtn.style.display = "none";
      searchInfo.style.display = "none";

      searchState.matchedQuestions = [];
      searchState.currentMatchIndex = -1;

      allQuestions.forEach((question) => {
        question.style.display = "block";
      });
    }
  };

  // Hàm mới để highlight từ khóa tìm kiếm
  function highlightSearchTerm(text, term) {
    if (!term || typeof text !== "string") return text;

    // Escape ký tự đặc biệt trong term để sử dụng an toàn trong regex
    const escapedTerm = term.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");

    // Tạo regex với flag 'gi' để case-insensitive và global matching
    const regex = new RegExp(`(${escapedTerm})`, "gi");

    // Replace với highlight
    return text.replace(
      regex,
      '<mark class="bg-yellow-200 rounded px-1">$1</mark>'
    );
  }

  // Cập nhật hàm clearSearch để xóa kết quả và trạng thái tìm kiếm
  window.clearSearch = function () {
    const searchInput = document.getElementById("searchInput");
    if (searchInput) {
      searchInput.value = "";

      // Reset trạng thái tìm kiếm
      searchState.matchedQuestions = [];
      searchState.currentMatchIndex = -1;
      searchState.searchTerm = "";

      // Gọi searchQuestions để cập nhật giao diện
      searchQuestions(true);
    }
  };

  // Hàm lọc theo đề thi (sửa lại)
  window.filterByExam = function () {
    const examFilter = document.getElementById("examFilter");
    if (!examFilter) return;

    const selectedExam = examFilter.value;
    console.log("Selected exam:", selectedExam); // Debug

    const allQuestions = document.querySelectorAll(".question-item");

    let visibleCount = 0;

    if (selectedExam === "all") {
      allQuestions.forEach((question) => {
        question.style.display = "block";
        visibleCount++;
      });
    } else {
      // Log ra một số phần tử đầu tiên để kiểm tra
      console.log(
        "First few questions data-exam-name:",
        Array.from(allQuestions)
          .slice(0, 3)
          .map((q) => q.dataset.examName)
      );

      allQuestions.forEach((question) => {
        // Thử dùng includes thay vì so sánh chính xác
        if (
          question.dataset.examName &&
          question.dataset.examName === selectedExam
        ) {
          question.style.display = "block";
          visibleCount++;
          console.log(
            "Matched question:",
            question.dataset.examName,
            question
              .querySelector(".question-text")
              .textContent.substring(0, 30)
          );
        } else {
          question.style.display = "none";
        }
      });

      // Nếu không tìm thấy câu hỏi nào phù hợp, log ra thông tin chi tiết
      if (visibleCount === 0) {
        console.log("No questions found for:", selectedExam);
        console.log(
          "All available exam names:",
          Array.from(allQuestions).map((q) => q.dataset.examName)
        );
        // Thử mở rộng tìm kiếm
        allQuestions.forEach((question) => {
          const examName = question.dataset.examName || "";
          if (
            examName.toLowerCase().includes(selectedExam.toLowerCase()) ||
            selectedExam.toLowerCase().includes(examName.toLowerCase())
          ) {
            question.style.display = "block";
            visibleCount++;
            console.log("Fuzzy match found:", examName);
          }
        });
      }
    }

    // Cập nhật thông tin số câu hỏi hiển thị
    const searchInfo = document.getElementById("searchInfo");
    const searchResultCount = document.getElementById("searchResultCount");

    if (searchInfo && searchResultCount) {
      searchInfo.style.display = "block";
      searchResultCount.textContent = visibleCount;
    }

    // Nếu không có kết quả, hiển thị thông báo trống
    if (visibleCount === 0) {
      const container = document.getElementById("questionsContainer");
      const originalContent = container.innerHTML;

      // Lưu nội dung gốc trong data attribute nếu chưa có
      if (!container.dataset.originalContent) {
        container.dataset.originalContent = originalContent;
      }

      // Hiển thị thông báo không tìm thấy
      container.innerHTML = `
              <div class="text-center py-8 bg-white rounded-lg border border-gray-200 p-6">
                  <div class="text-indigo-400 mb-4">
                      <i class="fas fa-search text-4xl"></i>
                  </div>
                  <h3 class="text-lg font-medium text-gray-700 mb-2">Không tìm thấy câu hỏi</h3>
                  <p class="text-gray-500 mb-4">Không có câu hỏi nào thuộc "${selectedExam}" được tìm thấy.</p>
                  <button onclick="resetFilter()" class="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors">
                      <i class="fas fa-undo mr-2"></i>Quay lại tất cả câu hỏi
                  </button>
              </div>
          `;
    } else if (container.dataset.originalContent && selectedExam === "all") {
      // Khôi phục nội dung gốc nếu chọn "Hiển thị tất cả"
      container.innerHTML = container.dataset.originalContent;
      delete container.dataset.originalContent;
    }

    // Nếu đang tìm kiếm, thực hiện tìm kiếm lại để cập nhật kết quả
    if (searchState.searchTerm) {
      searchQuestions(true);
    }
  };

  // Thêm hàm để reset bộ lọc
  window.resetFilter = function () {
    const examFilter = document.getElementById("examFilter");
    if (examFilter) {
      examFilter.value = "all";
      filterByExam();
    }
  };

  // Initialize page
  document.addEventListener("DOMContentLoaded", function () {
    // Get current tab first
    const currentTab = "<%= currentTab %>";

    // Load practice history if on practice tab
    if (currentTab === "practice") {
      setTimeout(loadPracticeHistory, 100);
    }

    // Load questions if on outline tab
    if (currentTab === "outline") {
      setTimeout(loadOutlineQuestions, 100);
    }

    // Khởi tạo memory module nếu đang ở tab memory
    if (currentTab === "memory" && window.MemoryExam) {
      setTimeout(function () {
        if (typeof window.MemoryExam.init === "function") {
          window.MemoryExam.init();
        }
      }, 100);
    }

    // Apply security measures based on current tab

    if (currentTab === "outline") {
      GlobalSecurityMeasures({
        contextMessage: "xem đề cương",
        enableDevToolsDetection: true,
        enableScreenshotBlocking: true,
        enableRightClickBlocking: true,
        enableCopyBlocking: true,
        enablePrintBlocking: true,
        enableViewSourceBlocking: true,
        enableSavePageBlocking: true,
        enableDragDropBlocking: true,
        devToolsThreshold: 160,
        redirectOnDevTools: false,
        redirectUrl: "/home",
      });
    } else if (currentTab === "practice") {
      GlobalSecurityMeasures({
        contextMessage: "thi thử",
        enableDevToolsDetection: true,
        enableScreenshotBlocking: true,
        enableRightClickBlocking: true,
        enableCopyBlocking: true,
        enablePrintBlocking: true,
        enableViewSourceBlocking: true,
        enableSavePageBlocking: true,
        enableDragDropBlocking: true,
        devToolsThreshold: 160,
        redirectOnDevTools: false,
        redirectUrl: "/home",
      });
    } else if (currentTab === "memory") {
      GlobalSecurityMeasures({
        contextMessage: "luyện tập ghi nhớ",
        enableDevToolsDetection: true,
        enableScreenshotBlocking: true,
        enableRightClickBlocking: true,
        enableCopyBlocking: true,
        enablePrintBlocking: true,
        enableViewSourceBlocking: true,
        enableSavePageBlocking: true,
        enableDragDropBlocking: true,
        devToolsThreshold: 160,
        redirectOnDevTools: false,
        redirectUrl: "/home",
      });
    } else {
      GlobalSecurityMeasures({
        contextMessage: "duyệt đề thi",
        enableDevToolsDetection: false,
        enableScreenshotBlocking: false,
        enableRightClickBlocking: false,
        enableCopyBlocking: false,
        enablePrintBlocking: false,
        enableViewSourceBlocking: false,
        enableSavePageBlocking: false,
        enableDragDropBlocking: false,
      });
    }

    const searchInput = document.getElementById("searchInput");
    if (searchInput) {
      // Xóa onkeyup event trực tiếp
      searchInput.removeAttribute("onkeyup");

      // Thêm sự kiện keyup với debounce
      searchInput.addEventListener("keyup", function () {
        debouncedSearch();
      });
    }
  });

  // ========================
  // GROUP MANAGEMENT FUNCTIONS
  // ========================

  // State for group management
  let groupState = {
    selectedQuestions: [],
    allQuestions: [],
    groups: [],
    isLoading: false,
  };

  // Open group management modal
  function openGroupModal() {
    const modal = document.getElementById("groupModal");
    if (modal) {
      modal.classList.remove("hidden");
      loadGroupData();
    }
  }

  // Close group management modal
  function closeGroupModal() {
    const modal = document.getElementById("groupModal");
    if (modal) {
      modal.classList.add("hidden");
      // Reset state
      groupState.selectedQuestions = [];
      updateSelectedCount();
    }
  }

  // Load group data (groups and questions)
  async function loadGroupData() {
    if (groupState.isLoading) return;

    groupState.isLoading = true;
    const productId = "<%= product._id %>";

    try {
      // Load groups and questions in parallel
      const [groupsResponse, questionsResponse] = await Promise.all([
        fetch(`/exam/memory/${productId}/groups`),
        fetch(`/exam/memory/${productId}`),
      ]);

      const groupsData = await groupsResponse.json();
      const questionsData = await questionsResponse.json();

      if (groupsData.success) {
        groupState.groups = groupsData.data.groups;
        displayCurrentGroups(groupsData.data);
      }

      if (questionsData.success) {
        groupState.allQuestions = questionsData.data.questions;
        displayQuestionsForGrouping(questionsData.data.questions);
      }
    } catch (error) {
      console.error("Error loading group data:", error);
      showToast("Có lỗi xảy ra khi tải dữ liệu nhóm", "error");
    } finally {
      groupState.isLoading = false;
    }
  }

  // Display current groups
  function displayCurrentGroups(data) {
    const container = document.getElementById("currentGroupsList");
    if (!container) return;

    const { groups, ungrouped } = data;
    let html = "";

    // Show ungrouped questions first
    if (ungrouped.count > 0) {
      html += `
          <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg border border-gray-200">
            <div class="flex items-center">
              <i class="fas fa-question-circle text-gray-400 mr-2"></i>
              <span class="font-medium text-gray-700">Chưa phân nhóm</span>
              <span class="ml-2 px-2 py-1 bg-gray-200 text-gray-600 rounded-full text-xs">${ungrouped.count} câu</span>
            </div>
                         <div class="flex gap-2">
               <button onclick="viewGroupQuestions('ungrouped')"
                       class="text-sm text-blue-600 hover:text-blue-800">
                 <i class="fas fa-list-alt mr-1"></i>Chi tiết
               </button>
             </div>
          </div>
        `;
    }

    // Show existing groups
    groups.forEach((group) => {
      html += `
          <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg border border-blue-200">
            <div class="flex items-center">
              <i class="fas fa-layer-group text-blue-500 mr-2"></i>
              <span class="font-medium text-gray-700">${group.name}</span>
              <span class="ml-2 px-2 py-1 bg-blue-200 text-blue-700 rounded-full text-xs">${group.count} câu</span>
            </div>
                         <div class="flex gap-2">
               <button onclick="viewGroupQuestions('${group.name}')"
                       class="text-sm text-blue-600 hover:text-blue-800">
                 <i class="fas fa-list-alt mr-1"></i>Chi tiết
               </button>
              <button onclick="renameGroup('${group.name}')"
                      class="text-sm text-yellow-600 hover:text-yellow-800">
                <i class="fas fa-edit mr-1"></i>Sửa
              </button>
              <button onclick="deleteGroup('${group.name}')"
                      class="text-sm text-red-600 hover:text-red-800">
                <i class="fas fa-trash mr-1"></i>Xóa
              </button>
            </div>
          </div>
        `;
    });

    if (html === "") {
      html = `
          <div class="text-center py-4 text-gray-500">
            <i class="fas fa-folder-open text-2xl mb-2"></i>
            <p>Chưa có nhóm nào được tạo</p>
          </div>
        `;
    }

    container.innerHTML = html;
  }

  // Display questions for grouping (only ungrouped questions)
  function displayQuestionsForGrouping(questions) {
    const container = document.getElementById("questionsGroupList");
    if (!container || !questions) return;

    // Filter to only show ungrouped questions (unique assignment rule)
    const ungroupedQuestions = questions.filter((mq) => !mq.group);

    let html = "";

    if (ungroupedQuestions.length === 0) {
      html = `
        <div class="text-center py-8 text-gray-500">
          <i class="fas fa-check-circle text-green-400 text-3xl mb-3"></i>
          <h4 class="font-medium text-gray-700 mb-2">Tất cả câu hỏi đã được phân nhóm</h4>
          <p class="text-sm">Không có câu hỏi nào khả dụng để thêm vào nhóm mới.</p>
          <p class="text-xs text-gray-400 mt-1">Mỗi câu hỏi chỉ có thể thuộc một nhóm duy nhất.</p>
        </div>
      `;
    } else {
      ungroupedQuestions.forEach((mq, index) => {
        const questionData = mq.questionData;
        const questionText = questionData?.text || "Không có nội dung câu hỏi";
        const isChecked = groupState.selectedQuestions.includes(mq.questionId);

        html += `
          <div class="flex items-start gap-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
            <input type="checkbox"
                   class="mt-1 question-checkbox"
                   data-question-id="${mq.questionId}"
                   ${isChecked ? "checked" : ""}>
            <div class="flex-1 min-w-0">
              <div class="flex items-center gap-2 mb-1">
                <span class="font-medium text-sm text-gray-700">Câu ${
                  index + 1
                }</span>
                <span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-700">
                  <i class="fas fa-plus-circle mr-1"></i>Khả dụng
                </span>
              </div>
              <p class="text-sm text-gray-600 line-clamp-2">${questionText}</p>
            </div>
          </div>
        `;
      });
    }

    container.innerHTML = html;

    // Add event listeners for checkboxes
    container.querySelectorAll(".question-checkbox").forEach((checkbox) => {
      checkbox.addEventListener("change", function () {
        const questionId = this.getAttribute("data-question-id");
        if (this.checked) {
          if (!groupState.selectedQuestions.includes(questionId)) {
            groupState.selectedQuestions.push(questionId);
          }
        } else {
          const index = groupState.selectedQuestions.indexOf(questionId);
          if (index > -1) {
            groupState.selectedQuestions.splice(index, 1);
          }
        }
        updateSelectedCount();
      });
    });

    updateSelectedCount();
  }

  // Update selected questions count
  function updateSelectedCount() {
    const countEl = document.getElementById("selectedQuestionsCount");
    if (countEl) {
      countEl.textContent = groupState.selectedQuestions.length;
    }
  }

  // Select all questions
  function selectAllQuestions() {
    const checkboxes = document.querySelectorAll(".question-checkbox");
    groupState.selectedQuestions = [];

    checkboxes.forEach((checkbox) => {
      checkbox.checked = true;
      const questionId = checkbox.getAttribute("data-question-id");
      groupState.selectedQuestions.push(questionId);
    });

    updateSelectedCount();
  }

  // Deselect all questions
  function deselectAllQuestions() {
    const checkboxes = document.querySelectorAll(".question-checkbox");
    groupState.selectedQuestions = [];

    checkboxes.forEach((checkbox) => {
      checkbox.checked = false;
    });

    updateSelectedCount();
  }

  // Assign selected questions to group
  async function assignSelectedToGroup() {
    const groupName = document.getElementById("groupNameInput").value.trim();

    if (!groupName) {
      showToast("Vui lòng nhập tên nhóm", "error");
      return;
    }

    if (groupState.selectedQuestions.length === 0) {
      showToast("Vui lòng chọn ít nhất một câu hỏi", "error");
      return;
    }

    // Validate that selected questions are still ungrouped
    const ungroupedQuestions = groupState.allQuestions.filter(
      (mq) => !mq.group
    );
    const ungroupedQuestionIds = ungroupedQuestions.map((mq) => mq.questionId);

    const invalidSelections = groupState.selectedQuestions.filter(
      (questionId) => !ungroupedQuestionIds.includes(questionId)
    );

    if (invalidSelections.length > 0) {
      showToast(
        `${invalidSelections.length} câu hỏi đã được phân nhóm. Vui lòng tải lại danh sách.`,
        "warning"
      );
      loadGroupData(); // Refresh data
      return;
    }

    try {
      const productId = "<%= product._id %>";
      const response = await fetch(`/exam/memory/${productId}/groups`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          groupName: groupName,
          questionIds: groupState.selectedQuestions,
          action: "assign",
        }),
      });

      const data = await response.json();

      if (data.success) {
        showToast(
          `✅ Đã thêm ${
            data.modifiedCount || groupState.selectedQuestions.length
          } câu hỏi vào nhóm "${groupName}"`,
          "success"
        );
        document.getElementById("groupNameInput").value = "";
        groupState.selectedQuestions = [];
        loadGroupData(); // Reload data

        // Refresh the main memory list to show updated group information
        if (
          window.MemoryExam &&
          typeof window.MemoryExam.refresh === "function"
        ) {
          window.MemoryExam.refresh();
        }
      } else {
        showToast(data.message || "Có lỗi xảy ra", "error");
      }
    } catch (error) {
      console.error("Error assigning questions to group:", error);
      showToast("Có lỗi xảy ra khi phân nhóm câu hỏi", "error");
    }
  }

  // Remove selected questions from their groups
  async function ungroupSelected() {
    if (groupState.selectedQuestions.length === 0) {
      showToast("Vui lòng chọn ít nhất một câu hỏi", "error");
      return;
    }

    if (
      !confirm(
        `Bạn có chắc chắn muốn bỏ nhóm ${groupState.selectedQuestions.length} câu hỏi đã chọn?`
      )
    ) {
      return;
    }

    try {
      const productId = "<%= product._id %>";
      const response = await fetch(`/exam/memory/${productId}/groups`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          questionIds: groupState.selectedQuestions,
          action: "ungroup",
        }),
      });

      const data = await response.json();

      if (data.success) {
        showToast(data.message, "success");
        groupState.selectedQuestions = [];
        loadGroupData(); // Reload data

        // Refresh the main memory list to show updated group information
        if (
          window.MemoryExam &&
          typeof window.MemoryExam.refresh === "function"
        ) {
          window.MemoryExam.refresh();
        }
      } else {
        showToast(data.message || "Có lỗi xảy ra", "error");
      }
    } catch (error) {
      console.error("Error ungrouping questions:", error);
      showToast("Có lỗi xảy ra khi bỏ nhóm câu hỏi", "error");
    }
  }

  // View questions in a specific group
  async function viewGroupQuestions(groupName) {
    try {
      const productId = "<%= product._id %>";
      const response = await fetch(
        `/exam/memory/${productId}/groups/${encodeURIComponent(groupName)}`
      );
      const data = await response.json();

      if (data.success) {
        openGroupDetailModal(data.data);
      } else {
        showToast(data.message || "Không thể tải chi tiết nhóm", "error");
      }
    } catch (error) {
      console.error("Error loading group details:", error);
      showToast("Có lỗi xảy ra khi tải chi tiết nhóm", "error");
    }
  }

  // Rename a group
  async function renameGroup(oldName) {
    const newName = prompt(`Nhập tên mới cho nhóm "${oldName}":`, oldName);

    if (!newName || newName.trim() === "" || newName.trim() === oldName) {
      return;
    }

    try {
      const productId = "<%= product._id %>";
      const response = await fetch(
        `/exam/memory/${productId}/groups/${encodeURIComponent(oldName)}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            newGroupName: newName.trim(),
          }),
        }
      );

      const data = await response.json();

      if (data.success) {
        showToast(
          `✅ Đã đổi tên nhóm "${oldName}" thành "${newName.trim()}"`,
          "success"
        );

        // Update group detail modal if it's showing the renamed group
        if (
          groupDetailState.currentGroup &&
          groupDetailState.currentGroup.groupName === oldName
        ) {
          groupDetailState.currentGroup.groupName = newName.trim();
          const title = document.getElementById("groupDetailTitle");
          if (title) {
            title.textContent = `Chi tiết nhóm: ${newName.trim()}`;
          }
        }

        loadGroupData(); // Reload data

        // Refresh the main memory list to show updated group information
        if (
          window.MemoryExam &&
          typeof window.MemoryExam.refresh === "function"
        ) {
          window.MemoryExam.refresh();
        }
      } else {
        showToast(data.message || "Có lỗi xảy ra", "error");
      }
    } catch (error) {
      console.error("Error renaming group:", error);
      showToast("Có lỗi xảy ra khi đổi tên nhóm", "error");
    }
  }

  // Delete a group
  async function deleteGroup(groupName) {
    if (
      !confirm(
        `Bạn có chắc chắn muốn xóa nhóm "${groupName}"? Các câu hỏi sẽ được chuyển về trạng thái chưa phân nhóm.`
      )
    ) {
      return;
    }

    try {
      const productId = "<%= product._id %>";
      const response = await fetch(
        `/exam/memory/${productId}/groups/${encodeURIComponent(groupName)}`,
        {
          method: "DELETE",
        }
      );

      const data = await response.json();

      if (data.success) {
        showToast(
          `✅ Đã xóa nhóm "${groupName}" và chuyển ${
            data.modifiedCount || 0
          } câu hỏi về trạng thái chưa phân nhóm`,
          "success"
        );

        // Close group detail modal if it's showing the deleted group
        if (
          groupDetailState.currentGroup &&
          groupDetailState.currentGroup.groupName === groupName
        ) {
          closeGroupDetailModal();
        }

        loadGroupData(); // Reload data

        // Refresh the main memory list to show updated group information
        if (
          window.MemoryExam &&
          typeof window.MemoryExam.refresh === "function"
        ) {
          window.MemoryExam.refresh();
        }
      } else {
        showToast(data.message || "Có lỗi xảy ra", "error");
      }
    } catch (error) {
      console.error("Error deleting group:", error);
      showToast("Có lỗi xảy ra khi xóa nhóm", "error");
    }
  }

  // Group Detail Modal State
  let groupDetailState = {
    currentGroup: null,
    selectedQuestions: [],
    questions: [],
  };

  // Open group detail modal
  function openGroupDetailModal(groupData) {
    const modal = document.getElementById("groupDetailModal");
    const title = document.getElementById("groupDetailTitle");
    const subtitle = document.getElementById("groupDetailSubtitle");

    if (!modal || !groupData) return;

    groupDetailState.currentGroup = groupData;
    groupDetailState.questions = groupData.questions || [];
    groupDetailState.selectedQuestions = [];

    const groupName = groupData.groupName || "Chưa phân nhóm";
    const questionCount = groupDetailState.questions.length;

    title.textContent = `Chi tiết nhóm: ${groupName}`;
    subtitle.textContent = `${questionCount} câu hỏi`;

    displayGroupDetailQuestions();
    updateSelectedInDetailCount();

    modal.classList.remove("hidden");
  }

  // Close group detail modal
  function closeGroupDetailModal() {
    const modal = document.getElementById("groupDetailModal");
    if (modal) {
      modal.classList.add("hidden");
      groupDetailState = {
        currentGroup: null,
        selectedQuestions: [],
        questions: [],
      };
    }
  }

  // Display questions in group detail modal
  function displayGroupDetailQuestions() {
    const container = document.getElementById("groupDetailQuestions");
    if (!container || !groupDetailState.questions) return;

    if (groupDetailState.questions.length === 0) {
      container.innerHTML = `
        <div class="text-center py-12 text-gray-500">
          <i class="fas fa-folder-open text-4xl mb-4"></i>
          <h4 class="text-lg font-medium text-gray-700 mb-2">Nhóm này chưa có câu hỏi</h4>
          <p class="text-sm">Thêm câu hỏi vào nhóm từ giao diện quản lý nhóm.</p>
        </div>
      `;
      return;
    }

    let html = "";
    groupDetailState.questions.forEach((question, index) => {
      const questionData = question.questionData;
      const questionText = questionData?.text || "Không có nội dung câu hỏi";
      const isChecked = groupDetailState.selectedQuestions.includes(
        question.questionId
      );

      // Generate answer options
      const answersHtml = (questionData?.answers || [])
        .map((answer, answerIndex) => {
          const optionLabel = String.fromCharCode(65 + answerIndex);
          const isCorrect = answer.isCorrect || false;
          let answerText = answer.text || answer || "Đáp án không có sẵn";

          // Remove prefix A., B., C., D. if present
          if (typeof answerText === "string") {
            const prefixRegex = /^\s*(?:[A-D]|[1-9])[\.\)][\s\u00A0]*/i;
            if (prefixRegex.test(answerText)) {
              answerText = answerText.replace(prefixRegex, "");
            }
          }

          let borderColor = "border-gray-200";
          let bgColor = "";
          if (isCorrect) {
            borderColor = "border-green-200";
            bgColor = "bg-green-50";
          }

          return `
            <div class="answer-option flex items-center p-3 border ${borderColor} ${bgColor} rounded-lg text-sm">
              <span class="font-medium mr-3 text-xs bg-gray-100 px-2 py-1 rounded-full">${optionLabel}</span>
              <span class="flex-1 break-words">${answerText}</span>
              ${
                isCorrect
                  ? '<span class="correct-indicator ml-2"><i class="fas fa-check text-green-600"></i></span>'
                  : ""
              }
            </div>
          `;
        })
        .join("");

      html += `
        <div class="group-detail-question bg-white rounded-lg border border-gray-200 p-6 mb-4 hover:shadow-sm transition-all">
          <div class="flex items-start gap-4">
            <input type="checkbox" 
                   class="mt-1 group-detail-checkbox h-5 w-5 text-indigo-600 rounded" 
                   data-question-id="${question.questionId}"
                   ${isChecked ? "checked" : ""}>
            <div class="flex-1">
              <div class="flex items-center gap-3 mb-3">
                <h4 class="font-semibold text-gray-800">Câu ${index + 1}</h4>
                <span class="px-2 py-1 text-xs rounded-full bg-purple-100 text-purple-700">
                  <i class="fas fa-layer-group mr-1"></i>${
                    groupDetailState.currentGroup?.groupName || "N/A"
                  }
                </span>
                <span class="text-xs text-gray-500">
                  <i class="fas fa-calendar mr-1"></i>
                  ${new Date(question.createdAt).toLocaleDateString("vi-VN")}
                </span>
              </div>
              
              <div class="text-gray-700 mb-4 leading-relaxed">
                ${questionText}
              </div>
              
              <div class="space-y-2 mb-4">
                ${answersHtml}
              </div>
              
              <div class="flex items-center gap-4 text-sm text-gray-500">
                <span><i class="fas fa-chart-line mr-1"></i>Luyện tập: ${
                  question.practiceCount || 0
                } lần</span>
                <span><i class="fas fa-check-circle mr-1"></i>Đúng: ${
                  question.correctCount || 0
                } lần</span>
                ${
                  question.practiceCount > 0
                    ? `<span><i class="fas fa-percentage mr-1"></i>Tỷ lệ: ${Math.round(
                        ((question.correctCount || 0) /
                          question.practiceCount) *
                          100
                      )}%</span>`
                    : ""
                }
              </div>
            </div>
          </div>
        </div>
      `;
    });

    container.innerHTML = html;

    // Add event listeners for checkboxes
    container.querySelectorAll(".group-detail-checkbox").forEach((checkbox) => {
      checkbox.addEventListener("change", function () {
        const questionId = this.getAttribute("data-question-id");
        if (this.checked) {
          if (!groupDetailState.selectedQuestions.includes(questionId)) {
            groupDetailState.selectedQuestions.push(questionId);
          }
        } else {
          const index = groupDetailState.selectedQuestions.indexOf(questionId);
          if (index > -1) {
            groupDetailState.selectedQuestions.splice(index, 1);
          }
        }
        updateSelectedInDetailCount();
      });
    });
  }

  // Update selected count in detail modal
  function updateSelectedInDetailCount() {
    const countEl = document.getElementById("selectedInDetailCount");
    if (countEl) {
      countEl.textContent = groupDetailState.selectedQuestions.length;
    }
  }

  // Select all questions in detail modal
  function selectAllInDetail() {
    const checkboxes = document.querySelectorAll(".group-detail-checkbox");
    groupDetailState.selectedQuestions = [];

    checkboxes.forEach((checkbox) => {
      checkbox.checked = true;
      const questionId = checkbox.getAttribute("data-question-id");
      groupDetailState.selectedQuestions.push(questionId);
    });

    updateSelectedInDetailCount();
  }

  // Deselect all questions in detail modal
  function deselectAllInDetail() {
    const checkboxes = document.querySelectorAll(".group-detail-checkbox");
    groupDetailState.selectedQuestions = [];

    checkboxes.forEach((checkbox) => {
      checkbox.checked = false;
    });

    updateSelectedInDetailCount();
  }

  // Edit group name from detail modal
  function editGroupName() {
    if (!groupDetailState.currentGroup) return;

    const currentName = groupDetailState.currentGroup.groupName;
    renameGroup(currentName);
  }

  // Remove selected questions from group
  async function removeQuestionsFromGroup() {
    if (groupDetailState.selectedQuestions.length === 0) {
      showToast("Vui lòng chọn ít nhất một câu hỏi để bỏ khỏi nhóm", "error");
      return;
    }

    if (
      !confirm(
        `Bạn có chắc chắn muốn bỏ ${groupDetailState.selectedQuestions.length} câu hỏi khỏi nhóm này?`
      )
    ) {
      return;
    }

    try {
      const productId = "<%= product._id %>";
      const response = await fetch(`/exam/memory/${productId}/groups`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          questionIds: groupDetailState.selectedQuestions,
          action: "ungroup",
        }),
      });

      const data = await response.json();

      if (data.success) {
        showToast(data.message, "success");

        // Refresh the group detail view
        const groupName = groupDetailState.currentGroup.groupName;
        await viewGroupQuestions(groupName);

        // Refresh the main group list if the group modal is open
        if (groupState.isLoading === false) {
          loadGroupData();
        }

        // Refresh the main memory list to show updated group information
        if (
          window.MemoryExam &&
          typeof window.MemoryExam.refresh === "function"
        ) {
          window.MemoryExam.refresh();
        }
      } else {
        showToast(data.message || "Có lỗi xảy ra", "error");
      }
    } catch (error) {
      console.error("Error removing questions from group:", error);
      showToast("Có lỗi xảy ra khi bỏ câu hỏi khỏi nhóm", "error");
    }
  }

  // Delete group from detail modal
  function deleteGroupFromDetail() {
    if (!groupDetailState.currentGroup) return;

    const groupName = groupDetailState.currentGroup.groupName;

    if (
      confirm(
        `Bạn có chắc chắn muốn xóa nhóm "${groupName}"? Tất cả câu hỏi sẽ được chuyển về trạng thái chưa phân nhóm.`
      )
    ) {
      deleteGroup(groupName).then(() => {
        closeGroupDetailModal();
      });
    }
  }

  // Make functions available globally
  window.openGroupModal = openGroupModal;
  window.closeGroupModal = closeGroupModal;
  window.selectAllQuestions = selectAllQuestions;
  window.deselectAllQuestions = deselectAllQuestions;
  window.assignSelectedToGroup = assignSelectedToGroup;
  window.ungroupSelected = ungroupSelected;
  window.viewGroupQuestions = viewGroupQuestions;
  window.renameGroup = renameGroup;
  window.deleteGroup = deleteGroup;

  // Group detail modal functions
  window.openGroupDetailModal = openGroupDetailModal;
  window.closeGroupDetailModal = closeGroupDetailModal;
  window.selectAllInDetail = selectAllInDetail;
  window.deselectAllInDetail = deselectAllInDetail;
  window.editGroupName = editGroupName;
  window.removeQuestionsFromGroup = removeQuestionsFromGroup;
  window.deleteGroupFromDetail = deleteGroupFromDetail;
</script>
