// Phiên bản sửa lỗi của Google Form
// Được chỉnh sửa từ mã nguồn trong google-form.ejs
// Sửa 2 lỗi chính:
// 1. Thời gian làm bài trôi nhanh gấp 2 lần thực tế
// 2. Nộp bài liên tục không ngừng khi hết thời gian
// Biến lưu trữ trạng thái
let timeRemaining = 0; // Sẽ được khởi tạo từ examData.duration * 60
let timerInterval;
let isSubmitting = false; // Trạng thái đang nộp bài
let isExitPopupVisible = false; // Trạng thái hiển thị popup
let startTime = Date.now(); // Thời gian bắt đầu làm bài
let wrongQuestions = []; // Mảng lưu các câu hỏi sai
let isTimeUp = false; // Flag để kiểm tra đã hết thời gian chưa
let hasAutoSubmitted = false; // Flag để tránh nộp bài nhiều lần
let forceSubmitEnabled = false; // Flag để cho phép nộp bài bất kể trạng thái
// Biến theo dõi câu hỏi hiện tại
let currentQuestion = 1;
let totalQuestions = 0; // Sẽ được gán giá trị từ examData.questions.length
// Bắt đầu đếm thời gian - ĐÃ SỬA
function startTimer() {
    // Đảm bảo xóa bất kỳ interval cũ nào để tránh nhiều timer chạy cùng lúc
    if (timerInterval) {
        clearInterval(timerInterval);
    }
    // Tạo mới interval với chính xác 1000ms (1 giây)
    timerInterval = setInterval(updateTimer, 1000);
    // Cập nhật ngay lần đầu
    updateTimer();
}
// Cập nhật hiển thị thời gian - ĐÃ SỬA
function updateTimer() {
    if (timeRemaining <= 0 && !isTimeUp) {
        // Hết thời gian, tự động nộp bài
        clearInterval(timerInterval);
        // Đánh dấu đã hết giờ để tránh gọi hàm nhiều lần
        isTimeUp = true;
        // Cho phép nộp bài thủ công (mới thêm)
        forceSubmitEnabled = true;
        // Hiển thị popup thông báo hết giờ
        showExitConfirmPopup("Đã hết thời gian làm bài!", "Thời gian làm bài đã kết thúc. Bài thi của bạn sẽ được nộp tự động.");
        // Tự động nhấn nút nộp bài sau 5 giây nếu người dùng không phản hồi
        // và chỉ thực hiện nếu chưa tự động nộp
        if (!hasAutoSubmitted) {
            setTimeout(function () {
                if (isExitPopupVisible && !hasAutoSubmitted) {
                    // Đánh dấu đã tự động nộp bài
                    hasAutoSubmitted = true;
                    // Click nút nộp bài
                    document.getElementById("exitConfirmLeaveBtn").click();
                }
            }, 5000);
        }
        return;
    }
    const hours = Math.floor(timeRemaining / 3600);
    const minutes = Math.floor((timeRemaining % 3600) / 60);
    const seconds = timeRemaining % 60;
    document.getElementById("timer").textContent = `${hours
        .toString()
        .padStart(2, "0")}:${minutes.toString().padStart(2, "0")}:${seconds
        .toString()
        .padStart(2, "0")}`;
    // Thay đổi màu khi gần hết thời gian
    if (timeRemaining <= 300) {
        // 5 phút cuối
        document.getElementById("examTimer").classList.add("bg-red-600");
        document.getElementById("examTimer").classList.remove("bg-red-700");
        // Nhấp nháy khi còn dưới 1 phút
        if (timeRemaining <= 60) {
            document.getElementById("examTimer").classList.toggle("bg-red-800");
        }
    }
    // Giảm thời gian còn lại đúng 1 giây
    timeRemaining--;
}
// Hiển thị popup xác nhận - ĐÃ SỬA
function showExitConfirmPopup(title, message) {
    const popup = document.getElementById("exitConfirmPopup");
    const titleElement = document.getElementById("exitConfirmTitle");
    const messageElement = document.getElementById("exitConfirmMessage");
    const stayButton = document.getElementById("exitConfirmStayBtn");
    const leaveButton = document.getElementById("exitConfirmLeaveBtn");
    // Cập nhật nội dung
    titleElement.textContent = title || "Bạn có chắc chắn muốn rời khỏi?";
    messageElement.textContent =
        message ||
            "Rời khỏi trang này sẽ kết thúc bài thi của bạn và không thể hoàn tác.";
    // Hiển thị popup
    popup.classList.remove("hidden");
    isExitPopupVisible = true;
    // Xử lý sự kiện nút ở lại
    stayButton.onclick = function () {
        popup.classList.add("hidden");
        isExitPopupVisible = false;
        // Nếu đã hết thời gian thì không thể ở lại
        if (isTimeUp && !hasAutoSubmitted) {
            // Đánh dấu đã tự động nộp bài
            hasAutoSubmitted = true;
            forceSubmitEnabled = true; // Mới thêm - cho phép nộp bài bất kể trạng thái
            // Nộp bài ngay lập tức
            submitExam();
        }
        else {
            // Nếu đang sử dụng nút Back, thêm một mục vào lịch sử để vô hiệu hóa nút Back
            history.pushState(null, null, window.location.pathname);
        }
    };
    // Xử lý sự kiện nút nộp bài/rời khỏi
    leaveButton.onclick = function () {
        // Đánh dấu đang nộp bài
        isSubmitting = true;
        // Ẩn popup
        popup.classList.add("hidden");
        isExitPopupVisible = false;
        // Cho phép nộp bài khi hết giờ (mới thêm)
        if (isTimeUp) {
            forceSubmitEnabled = true;
        }
        // Nếu đã tự động nộp bài rồi và không phải là force submit thì không nộp nữa
        if (hasAutoSubmitted && !forceSubmitEnabled) {
            return;
        }
        // Đánh dấu đã nộp bài để tránh nộp nhiều lần
        hasAutoSubmitted = true;
        // Hiển thị kết quả bài thi
        if (document.getElementById("exam-container").style.display !== "none") {
            submitExam();
        }
    };
}
// Nộp bài thi - ĐÃ SỬA
function submitExam() {
    // Kiểm tra nếu đã nộp bài thì không làm gì, TRỪ KHI được force submit
    if (isSubmitting && hasAutoSubmitted && !forceSubmitEnabled) {
        return false;
    }
    // Đánh dấu là đang nộp bài để không hiển thị cảnh báo
    isSubmitting = true;
    // Đánh dấu đã nộp bài để tránh nộp nhiều lần
    hasAutoSubmitted = true;
    // Reset trạng thái force submit
    forceSubmitEnabled = false;
    // Clear localStorage when submitting exam
    clearGoogleFormDataFromStorage();
    // Dừng bộ đếm thời gian
    clearInterval(timerInterval);
    // Tính thời gian làm bài
    const endTime = Date.now();
    const totalTimeMs = endTime - startTime;
    const totalTimeSeconds = Math.floor(totalTimeMs / 1000);
    const minutes = Math.floor(totalTimeSeconds / 60);
    const seconds = totalTimeSeconds % 60;
    const formattedTime = `${minutes}:${seconds.toString().padStart(2, "0")}`;
    document.getElementById("examTime").textContent = formattedTime;
    // Thu thập câu trả lời
    const formData = new FormData(document.getElementById("exam-form"));
    const answers = {};
    // Xử lý và chấm điểm
    let correctCount = 0;
    let questionResults = []; // Mảng lưu kết quả từng câu
    // Lấy đối tượng examData từ phạm vi toàn cục
    const examDataElement = document.getElementById("exam-data");
    const examData = JSON.parse(examDataElement.textContent);
    examData.questions.forEach((question, qIndex) => {
        const answerId = `answer_${question._id}`;
        const userAnswer = formData.get(answerId);
        answers[question._id] = userAnswer;
        let isCorrect = false;
        let userSelectedOption = null;
        let correctOption = null;
        // Kiểm tra đáp án đúng (giả định là option có isCorrect = true là đáp án đúng)
        if ((!question.type || question.type === "multiple-choice") &&
            userAnswer !== null) {
            const selectedOptionIndex = parseInt(userAnswer);
            const answerOptions = question.answers || question.options || [];
            if (selectedOptionIndex >= 0 &&
                selectedOptionIndex < answerOptions.length) {
                userSelectedOption = answerOptions[selectedOptionIndex];
                if (userSelectedOption && userSelectedOption.isCorrect) {
                    isCorrect = true;
                    correctCount++;
                }
                // Tìm đáp án đúng
                correctOption = answerOptions.find((opt) => opt.isCorrect);
            }
        }
        // Lưu kết quả của câu hỏi
        questionResults.push({
            questionIndex: qIndex,
            question: question,
            userAnswer: userAnswer,
            selectedOptionIndex: userAnswer !== null ? parseInt(userAnswer) : null,
            isCorrect: isCorrect,
            userSelectedOption: userSelectedOption,
            correctOption: correctOption,
        });
    });
    // Tính điểm
    const totalQuestions = examData.questions.length;
    const score = Math.round((correctCount / totalQuestions) * 10 * 100) / 100;
    // Hiển thị kết quả
    document.getElementById("correctAnswers").textContent = `${correctCount}/${totalQuestions}`;
    document.getElementById("examScore").textContent = `${score}/10`;
    // Đếm số câu chưa trả lời
    const skippedQuestions = questionResults.filter((result) => result.selectedOptionIndex === null).length;
    // Tạo phần tử hiển thị số câu chưa trả lời nếu có
    if (skippedQuestions > 0) {
        const skippedElement = document.createElement("div");
        skippedElement.className = "flex items-center justify-between mb-2";
        skippedElement.innerHTML = `
      <span class="text-gray-700">Số câu chưa trả lời:</span>
      <span class="font-medium text-gray-600">${skippedQuestions}/${totalQuestions}</span>
    `;
        // Chèn vào trước thời gian làm bài
        const summaryContainer = document.querySelector("#result-container .bg-gray-50.rounded-lg");
        const timeElement = summaryContainer.querySelector(".flex.items-center.justify-between:last-child");
        summaryContainer.insertBefore(skippedElement, timeElement);
    }
    // Cập nhật tiến độ để hiển thị hoàn thành
    currentQuestion = totalQuestions;
    updateProgressBar();
    // Lưu kết quả bài thi vào lịch sử
    saveExamHistory(correctCount, totalQuestions, totalTimeSeconds);
    // Cập nhật danh sách câu hỏi sai
    updateWrongQuestions(questionResults);
    // Tạo biểu đồ tóm tắt dạng dots
    const summaryContainer = document.getElementById("summaryContainer");
    summaryContainer.innerHTML = "";
    // Hiển thị dots và xem chi tiết kết quả
    renderResultDetails(questionResults);
    // Ẩn phần bài thi
    document.getElementById("exam-container").classList.add("hidden");
    // Hiển thị phần kết quả
    document.getElementById("result-container").classList.remove("hidden");
    return false; // Ngăn form submit
}
// Hàm cập nhật thanh tiến độ
function updateProgressBar() {
    const progressPercent = (currentQuestion / totalQuestions) * 100;
    document.getElementById("progressBar").style.width = `${progressPercent}%`;
    document.getElementById("questionCounter").textContent = `Câu hỏi ${currentQuestion}/${totalQuestions}`;
}
// Hàm render kết quả chi tiết
function renderResultDetails(questionResults) {
    // Tạo dots
    const summaryContainer = document.getElementById("summaryContainer");
    questionResults.forEach((result, index) => {
        const questionDot = document.createElement("div");
        // Xác định lớp CSS dựa trên trạng thái trả lời
        let dotClass = "";
        let tooltipText = "";
        if (result.selectedOptionIndex === null) {
            // Chưa trả lời
            dotClass = "bg-gray-100 text-gray-800 border border-gray-300";
            tooltipText = `Câu ${index + 1}: Chưa trả lời`;
        }
        else if (result.isCorrect) {
            // Trả lời đúng
            dotClass = "bg-green-100 text-green-800 border border-green-300";
            tooltipText = `Câu ${index + 1}: Đúng`;
        }
        else {
            // Trả lời sai
            dotClass = "bg-red-100 text-red-800 border border-red-300";
            tooltipText = `Câu ${index + 1}: Sai`;
        }
        questionDot.className = `h-8 w-8 rounded-full flex items-center justify-center text-xs font-medium ${dotClass}`;
        questionDot.textContent = index + 1;
        questionDot.title = tooltipText;
        // Click vào số câu hỏi để cuộn đến câu đó trong phần xem lại
        questionDot.style.cursor = "pointer";
        questionDot.addEventListener("click", () => {
            const reviewContainer = document.getElementById("answerResults");
            const questionReview = document.getElementById(`question-review-${index}`);
            if (reviewContainer && questionReview) {
                // Cuộn đến vị trí câu hỏi trong container scroll
                questionReview.scrollIntoView({
                    behavior: "smooth",
                    block: "center",
                    inline: "nearest",
                });
                questionReview.classList.add("bg-red-50");
                setTimeout(() => {
                    questionReview.classList.remove("bg-red-50");
                }, 1500);
            }
        });
        summaryContainer.appendChild(questionDot);
    });
    // Hiển thị chi tiết từng câu
    const resultsContainer = document.getElementById("answerResults");
    resultsContainer.innerHTML = "";
    // Render chi tiết từng câu hỏi
    questionResults.forEach((result, index) => {
        const reviewItem = document.createElement("div");
        reviewItem.id = `question-review-${index}`;
        reviewItem.className =
            "border-b border-gray-200 pb-4 mb-4 transition-colors duration-300";
        // Thêm nội dung chi tiết câu hỏi và đáp án
        reviewItem.innerHTML = createQuestionReviewHTML(result, index);
        resultsContainer.appendChild(reviewItem);
    });
}
// Hàm tạo HTML cho xem lại câu hỏi
function createQuestionReviewHTML(result, index) {
    const question = result.question;
    const answerOptions = question.answers || question.options || [];
    // Tìm đáp án đúng
    const correctOption = answerOptions.find((opt) => typeof opt === "object" && opt.isCorrect);
    let correctText = correctOption
        ? correctOption.text
        : "Không tìm thấy đáp án đúng";
    // Loại bỏ tiền tố cho đáp án đúng
    const prefixRegex = /^\s*(?:[A-D]|[1-9])[\.\)][\s\u00A0]*/i;
    if (prefixRegex.test(correctText)) {
        correctText = correctText.replace(prefixRegex, "");
    }
    // Tạo HTML cho các đáp án
    let answerOptionsHtml = "";
    if (answerOptions && answerOptions.length > 0) {
        answerOptionsHtml = `
      <div class="grid grid-cols-1 gap-2 text-left">
        ${answerOptions
            .map((opt, i) => {
            const optText = typeof opt === "object" ? opt.text || "" : opt;
            // Loại bỏ tiền tố "A.", "B.", "C.", "D." nếu có
            let cleanOptText = optText;
            if (prefixRegex.test(cleanOptText)) {
                cleanOptText = cleanOptText.replace(prefixRegex, "");
            }
            let className = "p-2 rounded border";
            // Xác định class cho từng loại đáp án
            if (result.selectedOptionIndex === null) {
                // Chưa có đáp án được chọn
                if (typeof opt === "object" && opt.isCorrect) {
                    className += " bg-blue-50 border-blue-300 text-blue-800";
                }
                else {
                    className += " border-gray-200 text-gray-600";
                }
            }
            else {
                // Đã có đáp án được chọn
                if (i === result.selectedOptionIndex) {
                    className += result.isCorrect
                        ? " bg-green-100 border-green-500 text-green-800"
                        : " bg-red-100 border-red-500 text-red-800";
                }
                else if (!result.isCorrect &&
                    typeof opt === "object" &&
                    opt.isCorrect) {
                    className += " bg-green-100 border-green-500 text-green-800";
                }
                else {
                    className += " border-gray-200 text-gray-600";
                }
            }
            return `<div class="${className}">
              <div class="flex items-center">
                <div class="flex-shrink-0 h-6 w-6 rounded-full bg-red-100 text-red-600 flex items-center justify-center mr-3">
                  ${String.fromCharCode(65 + i)}
                </div>
                <div>${cleanOptText}</div>
                ${result.selectedOptionIndex !== null &&
                i === result.selectedOptionIndex
                ? `<div class="ml-auto ${result.isCorrect ? "text-green-600" : "text-red-600"}">
                    <i class="fas ${result.isCorrect ? "fa-check" : "fa-times"}"></i>
                  </div>`
                : ""}
                ${(result.selectedOptionIndex === null || !result.isCorrect) &&
                typeof opt === "object" &&
                opt.isCorrect
                ? `<div class="ml-auto ${result.selectedOptionIndex === null
                    ? "text-blue-600"
                    : "text-green-600"}">
                    <i class="fas fa-${result.selectedOptionIndex === null
                    ? "info-circle"
                    : "check"}"></i>
                  </div>`
                : ""}
              </div>
            </div>`;
        })
            .join("")}
      </div>
    `;
    }
    // Tạo HTML cho phần xem lại câu hỏi
    return `
    <div class="text-left mb-2">
      <h4 class="font-medium text-gray-800">Câu ${index + 1}: ${question.text}</h4>
      ${question.image
        ? `<img src="${question.image}" alt="Hình ảnh câu hỏi" class="max-w-full h-auto rounded my-2">`
        : ""}
    </div>
    ${answerOptionsHtml}
    <div class="mt-2 text-sm ${result.isCorrect ? "text-green-600" : "text-red-600"}">
      ${result.isCorrect
        ? '<i class="fas fa-check mr-1"></i>Đáp án đúng'
        : result.selectedOptionIndex === null
            ? '<i class="fas fa-exclamation-circle mr-1"></i>Chưa có câu trả lời. Đáp án đúng: ' +
                correctText
            : '<i class="fas fa-times mr-1"></i>Đáp án sai. Đáp án đúng: ' +
                correctText}
    </div>
  `;
}
// Hàm để cập nhật danh sách câu hỏi sai
function updateWrongQuestions(questionResults) {
    wrongQuestions = [];
    // Lọc ra các câu hỏi sai (không bao gồm câu chưa trả lời)
    questionResults.forEach((result) => {
        // Chỉ tính câu đã trả lời nhưng trả lời sai
        if (result.selectedOptionIndex !== null && !result.isCorrect) {
            // Thêm câu hỏi vào danh sách sai
            wrongQuestions.push(Object.assign(Object.assign({}, result.question), { userSelectedIndex: result.selectedOptionIndex }));
        }
    });
    // Ẩn nút làm lại nếu không có câu hỏi sai nào
    if (wrongQuestions.length === 0) {
        document.getElementById("retryWrongAnswers").style.display = "none";
    }
    else {
        // Hiển thị nút làm lại nếu có câu hỏi sai
        document.getElementById("retryWrongAnswers").style.display = "inline-flex";
    }
}
// Hàm để lưu kết quả bài thi vào lịch sử
function saveExamHistory(score, totalQuestions, duration) {
    // Kiểm tra nếu người dùng đã đăng nhập
    if (!document.querySelector("#profile-button")) {
        return;
    }
    // Lấy examData
    const examDataElement = document.getElementById("exam-data");
    const examData = JSON.parse(examDataElement.textContent);
    // Chuẩn bị dữ liệu để gửi API
    const historyData = {
        examId: examData.id,
        examName: document.querySelector(".bg-red-500 h1").textContent.trim(),
        score: score,
        totalQuestions: totalQuestions,
        duration: duration, // Thời gian làm bài (giây)
        examType: "google-form",
    };
    // Gửi request API để lưu lịch sử
    fetch("/exam/save-history", {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify(historyData),
    })
        .then((response) => response.json())
        .then((data) => {
        if (data.success) {
            console.log("Đã lưu lịch sử bài thi");
        }
        else {
            console.error("Lỗi khi lưu lịch sử");
        }
    })
        .catch((error) => {
        console.error("Lỗi khi gửi request lưu lịch sử:", error);
    });
}
// Khởi tạo khi trang được tải
document.addEventListener("DOMContentLoaded", function () {
    // Đọc dữ liệu bài thi từ DOM
    const examDataElement = document.getElementById("exam-data");
    if (examDataElement) {
        const examData = JSON.parse(examDataElement.textContent);
        timeRemaining = examData.duration * 60;
        totalQuestions = examData.questions ? examData.questions.length : 0;
    }
    // Khởi động timer
    startTimer();
    // Cập nhật tiến độ ban đầu
    updateProgressBar();
    // Xử lý sự kiện khi người dùng nhấn nút nộp bài
    const examForm = document.getElementById("exam-form");
    if (examForm) {
        examForm.addEventListener("submit", function (e) {
            e.preventDefault();
            // Cho phép nộp bài khi hết giờ (mới thêm)
            if (isTimeUp) {
                forceSubmitEnabled = true;
            }
            if (!isSubmitting && !isExitPopupVisible) {
                // Hiển thị popup xác nhận
                showExitConfirmPopup("Xác nhận nộp bài", "Bạn có chắc chắn muốn nộp bài? Thời gian làm bài còn lại sẽ bị hủy.");
            }
            else if (isTimeUp) {
                // Nếu đã hết giờ và đang hiển thị popup, buộc nộp bài
                forceSubmitEnabled = true;
                submitExam();
            }
            return false;
        });
    }
    // Xử lý sự kiện nút thoát
    const exitButton = document.getElementById("exitButton");
    if (exitButton) {
        exitButton.addEventListener("click", function () {
            if (!isSubmitting && !isExitPopupVisible) {
                // Hiển thị popup xác nhận thoát
                showExitConfirmPopup("Xác nhận thoát bài thi", "Bạn có chắc chắn muốn thoát? Mọi câu trả lời sẽ bị hủy và không thể hoàn tác.");
            }
        });
    }
    // Xử lý sự kiện nút làm lại câu hỏi sai
    const retryButton = document.getElementById("retryWrongAnswers");
    if (retryButton) {
        retryButton.addEventListener("click", function () {
            // Kiểm tra xem có câu hỏi sai nào không
            if (!wrongQuestions || wrongQuestions.length === 0) {
                alert("Bạn đã làm đúng tất cả các câu hỏi!");
                return;
            }
            try {
                // Lưu thông tin câu hỏi sai vào localStorage
                const wrongQuestionsData = {
                    examId: examData.id,
                    questions: wrongQuestions,
                };
                // Sử dụng localStorage thường (không dùng SecureStorage)
                localStorage.removeItem("wrongQuestions");
                localStorage.setItem("wrongQuestions", JSON.stringify(wrongQuestionsData));
                // Chuyển hướng đến trang làm lại
                window.location.href = `/exam/retry-wrong-questions?examId=${examData.id}&examType=google-form`;
            }
            catch (error) {
                console.error("Lỗi khi chuẩn bị làm lại câu hỏi sai:", error);
                alert("Đã xảy ra lỗi khi chuẩn bị làm lại câu hỏi sai");
            }
        });
    }
});
// Hàm hỗ trợ xóa dữ liệu trong LocalStorage
function clearGoogleFormDataFromStorage() {
    try {
        const CURRENT_EXAM_ID = getExamId();
        if (CURRENT_EXAM_ID) {
            const GOOGLE_FORM_STORAGE_KEY = `googleFormExam_${CURRENT_EXAM_ID}`;
            localStorage.removeItem(GOOGLE_FORM_STORAGE_KEY);
        }
    }
    catch (error) {
        console.error("❌ Lỗi xóa dữ liệu Google Form localStorage:", error);
    }
}
// Lấy examId từ nhiều nguồn
function getExamId() {
    // Lấy từ examData
    const examDataElement = document.getElementById("exam-data");
    if (examDataElement) {
        try {
            const examData = JSON.parse(examDataElement.textContent);
            if (examData && examData.id) {
                return examData.id;
            }
        }
        catch (e) { }
    }
    // Lấy từ URL
    const pathParts = window.location.pathname.split("/");
    const urlExamId = pathParts[2]; // /exam/{examId}/google-form
    if (urlExamId) {
        return urlExamId;
    }
    return null;
}
//# sourceMappingURL=google-form-fixed.js.map