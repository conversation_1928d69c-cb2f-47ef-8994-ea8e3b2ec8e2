import express from "express";
import studentController from "../controllers/student.controller";
import asyncHandler from "../util/asynHandler";
import {
  getStudentCourses,
  validateStudent,
  validateStudentCourse,
} from "../middlewares/student.middleware";

const router = express.Router();

router.get("/:productId", asyncHandler(studentController.getStudentsByCourse));
// router.post("/", async<PERSON>and<PERSON>(studentController.createStudent));
// router.delete("/:studentId", asyncHandler(studentController.deleteStudent));
router.get("/search", asyncHandler(studentController.searchStudentByProductId));
router.get(
  "/count/:productId",
  asyncHandler(studentController.getCountStudentByProductId)
);

// Thêm route mới để lấy danh sách khóa học của sinh viên
router.get(
  "/:studentId/courses",
  as<PERSON><PERSON><PERSON><PERSON>(validateStudent),
  async<PERSON>and<PERSON>(getStudentCourses)
);

// Thêm route mới để lấy danh sách đề thi của sinh viên theo khóa học
router.get(
  "/:studentId/courses/:productId/exams",
  asyncHandler(validateStudent),
  asyncHandler(validateStudentCourse),
  asyncHandler(studentController.getStudentExams)
);

// Thêm route mới để lấy danh sách bài kiểm tra của sinh viên theo khóa học
router.get(
  "/:studentId/courses/:productId/tests",
  asyncHandler(validateStudent),
  asyncHandler(validateStudentCourse),
  asyncHandler(studentController.getStudentTests)
);

export default router;
