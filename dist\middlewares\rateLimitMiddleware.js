"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.clearRateLimitData = exports.generalRateLimit = exports.forgotPasswordRateLimit = void 0;
// Map để lưu trữ thời gian gửi email cuối cùng của mỗi email
const forgotPasswordAttempts = new Map();
// Rate limit cho forgot password: 1 lần/phút
const forgotPasswordRateLimit = (req, res, next) => {
    const { email } = req.body;
    if (!email) {
        return next(); // Để validation khác xử lý lỗi thiếu email
    }
    const now = Date.now();
    const lastAttempt = forgotPasswordAttempts.get(email.toLowerCase());
    const cooldownPeriod = 60 * 1000; // 1 phút = 60,000ms
    if (lastAttempt && now - lastAttempt < cooldownPeriod) {
        const remainingTime = Math.ceil((cooldownPeriod - (now - lastAttempt)) / 1000);
        return res.status(429).json({
            success: false,
            message: `Vui lòng đợi ${remainingTime} gi<PERSON>y trước khi gửi lại yêu cầu đặt lại mật khẩu.`,
            remainingTime,
        });
    }
    // Lưu thời gian hiện tại
    forgotPasswordAttempts.set(email.toLowerCase(), now);
    // Tự động xóa entry sau 5 phút để tiết kiệm memory
    setTimeout(() => {
        forgotPasswordAttempts.delete(email.toLowerCase());
    }, 5 * 60 * 1000);
    next();
};
exports.forgotPasswordRateLimit = forgotPasswordRateLimit;
// Middleware rate limiting chung cho API
const generalRateLimit = (maxRequests, windowMs) => {
    const requestCounts = new Map();
    return (req, res, next) => {
        const clientIp = req.ip || req.connection.remoteAddress || "unknown";
        const now = Date.now();
        let clientData = requestCounts.get(clientIp);
        if (!clientData || now > clientData.resetTime) {
            // Reset hoặc tạo mới counter
            clientData = {
                count: 1,
                resetTime: now + windowMs,
            };
            requestCounts.set(clientIp, clientData);
            return next();
        }
        if (clientData.count >= maxRequests) {
            const remainingTime = Math.ceil((clientData.resetTime - now) / 1000);
            return res.status(429).json({
                success: false,
                message: `Quá nhiều yêu cầu. Vui lòng đợi ${remainingTime} giây.`,
                remainingTime,
            });
        }
        clientData.count++;
        next();
    };
};
exports.generalRateLimit = generalRateLimit;
// Hàm để xóa tất cả rate limit data (dùng cho testing hoặc maintenance)
const clearRateLimitData = () => {
    forgotPasswordAttempts.clear();
    console.log("Đã xóa tất cả dữ liệu rate limiting");
};
exports.clearRateLimitData = clearRateLimitData;
//# sourceMappingURL=rateLimitMiddleware.js.map