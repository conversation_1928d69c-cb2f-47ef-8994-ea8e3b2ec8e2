import express, {
  Request,
  Response,
  NextFunction,
  RequestHandler,
} from "express";
import mongoose from "mongoose";
import passport from "passport";
import cookieParser from "cookie-parser";
import cors from "cors";
import path from "path";
import expressLayouts from "express-ejs-layouts";
import dotenv from "dotenv";

// Import routes
import authRoutes from "./routes/authRoutes";
import userRoutes from "./routes/userRoutes";
import dataRoutes from "./routes/dataRoutes";
import examRoutes from "./routes/exam.route";
import sseRoutes from "./routes/sseRoutes";
import pageRoutes, { userCheckMiddleware } from "./routes/pageRoutes";
import practiceRoutes from "./routes/practiceRoutes";
import redirectRoutes from "./routes/redirectRoutes";
import memoryRoutes from "./routes/memory.route";

// Import controllers
import { redirectLegacyExamRoute } from "./controllers/redirectController";

// Import passport config
import configurePassport from "./config/passport";

// Import middleware xử lý lỗi
import {
  notFoundMiddleware,
  uncaughtErrorMiddleware,
  globalErrorMiddleware,
} from "./middlewares/errorMiddleware";

dotenv.config();

// Tạo ứng dụng Express
const app = express();

// Cấu hình EJS và layouts
app.set("view engine", "ejs");
app.set("views", path.join(__dirname, "views"));
app.use(expressLayouts);
app.set("layout", "layouts/layout");
app.set("layout extractScripts", true);

// Static files
app.use(express.static(path.join(__dirname, "public")));

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(cookieParser());
app.use(
  cors({
    origin: "http://localhost:3000", // Frontend URL
    credentials: true, // Cho phép chia sẻ cookie giữa domains
  })
);

// Khởi tạo Passport
app.use(passport.initialize());
configurePassport();

// Middleware để kiểm tra user và thêm vào res.locals
app.use("/", userCheckMiddleware);

// Đăng ký các routes
app.use("/auth", authRoutes);
app.use("/", pageRoutes);
app.use("/", practiceRoutes);
app.use("/exam", examRoutes);
app.use("/exam/memory", memoryRoutes);
app.use("/api", userRoutes);
app.use("/api/data", dataRoutes);
app.use("/api/sse", sseRoutes);
app.use("/", redirectRoutes);

// Chuyển hướng URL cũ
app.get(
  "/retry-wrong-questions",
  (req: Request, res: Response, next: NextFunction) => {
    redirectLegacyExamRoute(req, res, next);
  }
);

// Đăng ký middleware xử lý lỗi 404 cho tất cả các routes không khớp
app.use("/", (req: Request, res: Response, next: NextFunction) => {
  notFoundMiddleware(req, res, next);
});

// Middleware xử lý lỗi không lường trước
app.use((err: Error, req: Request, res: Response, next: NextFunction) => {
  uncaughtErrorMiddleware(err, req, res, next);
});

// Middleware xử lý lỗi chung
app.use((err: Error, req: Request, res: Response, next: NextFunction) => {
  globalErrorMiddleware(err as any, req, res, next);
});

// Khởi động server
const PORT = process.env.PORT || 5000;
mongoose
  .connect(process.env.MONGODB_URI_DB as string)
  .then(() => {
    console.log("Đã kết nối với MongoDB");
    app.listen(PORT, () => {
      console.log(`Server đang chạy trên cổng ${PORT}`);
    });
  })
  .catch((err) => {
    console.error("Lỗi kết nối MongoDB:", err);
    process.exit(1);
  });

export default app;
