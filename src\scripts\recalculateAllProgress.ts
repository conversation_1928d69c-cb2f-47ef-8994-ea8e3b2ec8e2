import mongoose from "mongoose";
import dotenv from "dotenv";
import { recalculateAllProgress } from "../services/userProgress.service";

// Load environment variables
dotenv.config();

// Connect to MongoDB
mongoose
  .connect(process.env.MONGODB_URI || "mongodb://localhost:27017/google-auth")
  .then(async () => {
    console.log("📚 Kết nối MongoDB thành công");

    console.log(
      "🔄 Bắt đầu cập nhật lại tiến độ học tập của tất cả người dùng..."
    );

    try {
      const result = await recalculateAllProgress();

      if (result) {
        console.log("✅ Cập nhật tiến độ học tập thành công!");
      } else {
        console.error("❌ Cập nhật tiến độ học tập thất bại!");
      }
    } catch (error) {
      console.error("❌ Lỗi khi cập nhật tiến độ học tập:", error);
    }

    // Đóng kết nối
    await mongoose.connection.close();
    console.log("📚 Đã đóng kết nối MongoDB");
    process.exit(0);
  })
  .catch((err) => {
    console.error("❌ Lỗi kết nối MongoDB:", err);
    process.exit(1);
  });
