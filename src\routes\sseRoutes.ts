import express, { Request, Response } from "express";
import { v4 as uuidv4 } from "uuid";
import { authenticateForSSE } from "../middlewares/authMiddleware";
import { addConnection } from "../services/sseService";
import { IUser } from "../models/User";

const router = express.Router();

// Route cho kết nối SSE
router.get(
  "/events",
  authenticateForSSE as any,
  (req: Request, res: Response) => {
    try {
      const user = req.user as IUser;

      if (!user || !user._id) {
        // console.error("SSE connection rejected: Invalid user object");
        res.status(401).end();
        return;
      }

      const userId = user._id.toString();

      // Sử dụng clientId từ query hoặc tạo mới nếu không có
      const clientId = (req.query.clientId as string) || uuidv4();

      // console.log(
      //   `SSE connection request from user ${userId}, clientId: ${clientId}`
      // );

      // Thiết lập headers ngay lập tức để tránh timeout
      res.setHeader("Content-Type", "text/event-stream");
      res.setHeader("Cache-Control", "no-cache");
      res.setHeader("Connection", "keep-alive");
      res.setHeader("X-Accel-Buffering", "no");

      // Gửi một event ban đầu để kiểm tra kết nối
      res.write(`:ok\n\n`);

      // Thêm kết nối mới vào service
      addConnection(userId, clientId, res);

      // Bắt lỗi khi client ngắt kết nối
      req.on("close", () => {
        // console.log(
        //   `SSE connection closed for user ${userId}, clientId: ${clientId}`
        // );
      });
    } catch (error) {
      // console.error("Error establishing SSE connection:", error);
      res.status(500).end();
    }
  }
);

export default router;
