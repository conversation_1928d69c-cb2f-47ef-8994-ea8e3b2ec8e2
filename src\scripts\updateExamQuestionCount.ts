import mongoose from "mongoose";
import Exam from "../models/exam";
import Question from "../models/question";
import dotenv from "dotenv";

// Load environment variables
dotenv.config();

/**
 * Kết nối đến cơ sở dữ liệu MongoDB
 */
async function connectToDatabase() {
  try {
    const mongoUri =
      process.env.MONGODB_URI || "mongodb://localhost:27017/google-auth";
    await mongoose.connect(mongoUri);
    console.log("Đã kết nối thành công đến MongoDB");
  } catch (error) {
    console.error("Lỗi kết nối đến MongoDB:", error);
    process.exit(1);
  }
}

/**
 * Cập nhật số lượng câu hỏi cho mỗi bài thi
 */
async function updateExamQuestionCounts() {
  try {
    // Lấy tất cả các bài thi
    const exams = await Exam.find({});
    console.log(`<PERSON><PERSON><PERSON> thấy ${exams.length} bài thi cần cập nhật`);

    // Xử lý từng bài thi
    for (const exam of exams) {
      // Đếm số lượng câu hỏi cho bài thi này
      const questionCount = await Question.countDocuments({ examId: exam._id });

      // Cập nhật trường count của bài thi
      await Exam.updateOne(
        { _id: exam._id },
        { $set: { count: questionCount } }
      );

      console.log(
        `Bài thi "${exam.name}" (ID: ${exam._id}) - Số câu hỏi: ${questionCount}`
      );
    }

    console.log("Đã cập nhật số lượng câu hỏi cho tất cả bài thi thành công!");
  } catch (error) {
    console.error("Lỗi khi cập nhật số lượng câu hỏi:", error);
  } finally {
    // Ngắt kết nối cơ sở dữ liệu sau khi hoàn thành
    await mongoose.disconnect();
    console.log("Đã ngắt kết nối khỏi MongoDB");
  }
}

/**
 * Hàm chính để chạy script
 */
async function main() {
  await connectToDatabase();
  await updateExamQuestionCounts();
}

// Chạy script
main().catch((error) => {
  console.error("Lỗi không mong muốn:", error);
  process.exit(1);
});
