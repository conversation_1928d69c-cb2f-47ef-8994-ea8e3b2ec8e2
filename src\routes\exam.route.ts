import express from "express";
import examController from "../controllers/exam.controller";
import questionController from "../controllers/question.controller";
import {
  saveExamHistory,
  getUserExamHistory,
} from "../controllers/examHistory.controller";
import asyncHandler from "../util/asynHandler";
import {
  createExam,
  getExamById,
  getExamsByProductId,
  takeExamGoogleForm,
  takeExamQuizizz,
  retryWrongQuestions,
  getWrongQuestions,
  startPracticeExam,
  takePracticeExam,
  savePracticeExamResult,
  checkExistingPractice,
} from "../controllers/exam.controller";
import { checkStudentCourseAccess } from "../middlewares/student.middleware";

const router = express.Router();

// Route để xem lịch sử làm bài thi
router.get("/history", asyncHandler(getUserExamHistory));

// Route để lưu kết quả bài thi (API)
router.post("/save-history", asyncHandler(saveExamHistory));

// Route cho trang làm lại câu hỏi sai - đặt trước các route có ":examId" để tránh xung đột
router.get(
  "/retry-wrong-questions",
  checkStudentCourseAccess,
  asyncHandler(retryWrongQuestions)
);

// Route lấy tất cả đề thi của một khóa học
router.get("/product/:productId", asyncHandler(getExamsByProductId));

// Lấy bài kiểm tra theo ID
router.get("/:examId", asyncHandler(examController.getExamById));

// API route để lấy danh sách câu hỏi sai của một bài thi
router.get("/:examId/wrong-questions", asyncHandler(getWrongQuestions));

// Cập nhật bài kiểm tra
// router.patch("/:examId", asyncHandler(examController.updateExam));

// Xóa bài kiểm tra
// router.delete("/:examId", asyncHandler(examController.deleteExam));

// Lấy bài kiểm tra kèm tất cả câu hỏi
router.get("/:examId/full", asyncHandler(examController.getExamWithQuestions));

// Routes cho questions liên quan đến exam
router.get(
  "/:examId/questions",
  asyncHandler(questionController.getQuestionsByExam)
);
// router.post(
//   "/:examId/questions",
//   asyncHandler(questionController.createQuestion)
// );

// Route để tạo nhiều câu hỏi cùng lúc
// router.post(
//   "/:examId/questions/batch",
//   asyncHandler(questionController.createMultipleQuestions)
// );

// Routes cho các hình thức làm bài thi
router.get(
  "/:examId/google-form",
  checkStudentCourseAccess,
  takeExamGoogleForm
);
router.get("/:examId/quizizz", checkStudentCourseAccess, takeExamQuizizz);

// Routes cho thi thử
router.get("/practice/:productId/check", asyncHandler(checkExistingPractice));
router.get(
  "/practice/:productId/history",
  asyncHandler(async (req, res) => {
    try {
      const { productId } = req.params;
      const userId = res.locals.user?._id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: "Người dùng chưa đăng nhập",
        });
      }

      // Import PracticeExamHistory model
      const PracticeExamHistory =
        require("../models/PracticeExamHistory").default;

      // Lấy lịch sử practice exam của user cho course cụ thể
      const history = await PracticeExamHistory.find({
        userId: userId,
        courseId: productId,
        status: "completed",
      })
        .sort({ completedAt: -1 })
        .limit(10)
        .select(
          "score totalQuestions correctAnswers duration completedAt createdAt status"
        );

      return res.json({
        success: true,
        data: history,
      });
    } catch (error: any) {
      console.error("❌ Lỗi lấy lịch sử practice:", error);
      return res.status(500).json({
        success: false,
        message: "Lỗi server khi lấy lịch sử",
        error: error.message,
      });
    }
  })
);

// Route để lấy chi tiết một lần thi cụ thể
router.get(
  "/practice/detail/:practiceId",
  asyncHandler(async (req, res) => {
    try {
      const { practiceId } = req.params;
      const userId = res.locals.user?._id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: "Người dùng chưa đăng nhập",
        });
      }

      // Import PracticeExamHistory model
      const PracticeExamHistory =
        require("../models/PracticeExamHistory").default;
      const Question = require("../models/question").default;

      // Lấy chi tiết practice exam của user
      const detail = await PracticeExamHistory.findOne({
        _id: practiceId,
        userId: userId,
      }).select(
        "score totalQuestions correctAnswers duration completedAt createdAt status courseName userAnswers selectedQuestions"
      );

      if (!detail) {
        return res.status(404).json({
          success: false,
          message: "Không tìm thấy lịch sử thi",
        });
      }

      // Lấy thông tin đầy đủ của câu hỏi từ database
      const enhancedDetail = { ...detail.toObject() };

      // Lấy danh sách questionIds từ selectedQuestions
      const questionIds = detail.selectedQuestions.map((q) => q.questionId);

      // Tạo map từ questionId -> userAnswer object để truy xuất nhanh
      const userAnswerMap = {};
      if (detail.userAnswers && Array.isArray(detail.userAnswers)) {
        detail.userAnswers.forEach((answer) => {
          if (answer && answer.questionId) {
            userAnswerMap[answer.questionId.toString()] = answer;
          }
        });
      }

      // Lấy thông tin đầy đủ của câu hỏi từ database
      const fullQuestions = await Question.find({
        _id: { $in: questionIds },
      });

      // Map câu hỏi đã tối ưu với dữ liệu đầy đủ và sắp xếp theo thứ tự ban đầu
      enhancedDetail.fullQuestions = detail.selectedQuestions
        .map((savedQuestion, index) => {
          // Tìm câu hỏi đầy đủ tương ứng với questionId
          const fullQuestion = fullQuestions.find(
            (q) => q._id.toString() === savedQuestion.questionId.toString()
          );

          if (!fullQuestion) {
            console.warn(
              `⚠️ Không tìm thấy câu hỏi với ID: ${savedQuestion.questionId}`
            );
            return null;
          }

          // Lấy thông tin userAnswer cho câu hỏi này
          const userAnswer = userAnswerMap[savedQuestion.questionId.toString()];
          const isCorrect = userAnswer ? userAnswer.isCorrect : false;
          const selectedAnswerId = userAnswer
            ? userAnswer.selectedAnswerId
            : null;

          // Chuyển từ document sang plain object
          const questionObj = fullQuestion.toObject
            ? fullQuestion.toObject()
            : { ...fullQuestion };

          // Xử lý options theo answerOrder đã lưu
          if (questionObj.answers && Array.isArray(questionObj.answers)) {
            // Sắp xếp lại options theo answerOrder đã lưu
            const originalOptions = [...questionObj.answers];
            const orderedOptions = [];

            // Đảm bảo answerOrder hợp lệ
            const answerOrder =
              Array.isArray(savedQuestion.answerOrder) &&
              savedQuestion.answerOrder.length > 0
                ? savedQuestion.answerOrder
                : [1, 2, 3, 4];

            // Sắp xếp theo answerOrder
            for (let i = 0; i < answerOrder.length; i++) {
              const originalIndex = answerOrder[i] - 1;
              if (
                originalIndex >= 0 &&
                originalIndex < originalOptions.length
              ) {
                orderedOptions.push(originalOptions[originalIndex]);
              }
            }

            questionObj.options =
              orderedOptions.length > 0 ? orderedOptions : originalOptions;
          }

          return {
            _id: questionObj._id,
            text: questionObj.text,
            options: questionObj.options || questionObj.answers || [],
            image: questionObj.image || null,
            questionNumber: index + 1,
            isCorrect: isCorrect, // Thêm thông tin đúng/sai
            selectedAnswerId: selectedAnswerId, // Thêm ID đáp án đã chọn
          };
        })
        .filter(Boolean); // Lọc bỏ các null values

      return res.json({
        success: true,
        data: enhancedDetail,
      });
    } catch (error: any) {
      console.error("❌ Lỗi lấy chi tiết practice:", error);
      return res.status(500).json({
        success: false,
        message: "Lỗi server khi lấy chi tiết",
        error: error.message,
      });
    }
  })
);

router.post("/practice/:productId/start", asyncHandler(startPracticeExam)); // API để tạo practice exam mới
router.post("/practice/save-result", asyncHandler(savePracticeExamResult));

// API route để sync answers cho cross-device support
router.post(
  "/practice/sync-answers",
  asyncHandler(async (req, res) => {
    try {
      const { practiceId, userAnswers, currentQuestionIndex } = req.body;
      const userId = res.locals.user?._id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: "Người dùng chưa đăng nhập",
        });
      }

      if (!practiceId || !userAnswers) {
        return res.status(400).json({
          success: false,
          message: "Thiếu practiceId hoặc userAnswers",
        });
      }

      // Import PracticeExamHistory model
      const PracticeExamHistory =
        require("../models/PracticeExamHistory").default;

      // Update practice exam with latest answers
      const updated = await PracticeExamHistory.findOneAndUpdate(
        {
          _id: practiceId,
          userId: userId,
          status: "in_progress",
        },
        {
          userAnswers: userAnswers,
          currentQuestionIndex: currentQuestionIndex || 0,
          lastSyncAt: new Date(),
        },
        { new: true }
      );

      if (!updated) {
        return res.status(404).json({
          success: false,
          message: "Không tìm thấy bài thi hoặc bài thi đã hoàn thành",
        });
      }

      return res.json({
        success: true,
        message: "Đã sync answers thành công",
        syncedAt: updated.lastSyncAt,
      });
    } catch (error: any) {
      console.error("❌ Lỗi sync answers:", error);
      return res.status(500).json({
        success: false,
        message: "Lỗi server khi sync answers",
        error: error.message,
      });
    }
  })
);

export default router;
