<div class="mx-auto mb-10 px-2 sm:px-4 max-w-4xl">
  <div class="bg-white rounded-lg shadow-lg overflow-hidden">
    <!-- Header -->
    <div
      class="bg-red-500 px-3 sm:px-6 py-3 sm:py-4 flex flex-col sm:flex-row justify-between sm:items-center"
    >
      <div class="flex items-center mb-2 sm:mb-0">
        <i class="fab fa-google mr-2 text-white text-xl sm:text-2xl"></i>
        <h1 class="text-lg sm:text-xl font-bold text-white">
          Bài thi: <%= exam.name %>
        </h1>
      </div>
      <div
        class="text-white flex items-center justify-between sm:justify-end w-full sm:w-auto"
      >
        <div
          id="examTimer"
          class="font-medium mr-4 bg-red-700 px-3 py-1 rounded text-sm sm:text-base"
        >
          <i class="fas fa-clock mr-1"></i>
          <span id="timer">00:00:00</span>
        </div>
        <span class="font-medium text-sm sm:text-base" id="questionCounter"
          >Câu hỏi 1/<%= questions.length %></span
        >
      </div>
    </div>

    <!-- Thông tin bài thi -->
    <div class="bg-red-50 px-4 sm:px-6 py-2 sm:py-3 border-b border-red-100">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div class="mb-1 sm:mb-0">
          <p class="text-red-800 text-sm sm:text-base">
            <span class="font-medium">Time:</span> <%= exam.duration %> phút
          </p>
          <p class="text-red-800 text-sm sm:text-base">
            <span class="font-medium">Tổng câu hỏi:</span> <%= questions.length
            %>
          </p>
        </div>
        <div class="text-red-800 text-xs sm:text-sm">
          <p>Sinh viên: <%= student.email %></p>
        </div>
      </div>
    </div>

    <!-- Tiến độ -->
    <div class="bg-gray-100 h-2">
      <div
        id="progressBar"
        class="bg-red-500 h-2 transition-all duration-300"
        style="width: 0%"
      ></div>
    </div>

    <!-- Form bài thi - Sẽ ẩn khi hiển thị kết quả -->
    <div id="exam-container">
      <!-- Loading indicator for encrypted questions -->
      <% if (typeof encryptedQuestionsConfig !== 'undefined' &&
      encryptedQuestionsConfig) { %>
      <div id="loadingIndicator" class="p-6 text-center">
        <div class="flex items-center justify-center space-x-3 mb-4">
          <i class="fas fa-cog fa-spin text-red-500 text-2xl"></i>
          <span class="text-gray-700 font-medium"
            >Đang tải cấu hình ứng dụng...</span
          >
        </div>
        <div class="bg-gray-200 rounded-full h-2 max-w-md mx-auto">
          <div
            id="loadingProgress"
            class="bg-red-500 h-2 rounded-full transition-all duration-300"
            style="width: 0%"
          ></div>
        </div>
      </div>
      <% } %>

      <form
        id="exam-form"
        class="p-3 sm:p-6 <% if (typeof encryptedQuestionsConfig !== 'undefined' && encryptedQuestionsConfig) { %>hidden<% } %>"
        onsubmit="return submitExam()"
      >
        <div class="space-y-6 sm:space-y-8" id="questions-container">
          <% if (typeof questions !== 'undefined' && questions) { %> <%
          questions.forEach((question, qIndex) => { %>
          <div
            class="question-container bg-white rounded-lg border border-gray-200 p-3 sm:p-4 shadow-sm"
          >
            <div class="mb-3">
              <h3 class="text-base sm:text-lg font-medium text-gray-800">
                Câu <%= qIndex + 1 %>: <%= question.text %>
              </h3>
              <% if (question.image) { %>
              <div class="mt-2">
                <img
                  src="<%= question.image %>"
                  alt="Hình minh họa câu hỏi"
                  class="max-w-full h-auto rounded"
                />
              </div>
              <% } %>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-4">
              <% if (!question.type || question.type === 'multiple-choice') { %>
              <% /* Xác định mảng đáp án để hiển thị - ưu tiên answers, nếu
              không có thì tìm options */ %> <% let answerOptions =
              question.answers || question.options || []; %> <% if
              (answerOptions && answerOptions.length > 0) { %> <%
              answerOptions.forEach((option, oIndex) => { %> <% /* Chuyển đổi
              index thành ký tự A, B, C, D... */ %> <% const optionLetter =
              String.fromCharCode(65 + oIndex); /* 65 là mã ASCII của 'A' */ %>
              <div class="relative">
                <input
                  type="radio"
                  id="q<%= qIndex %>_o<%= oIndex %>"
                  name="answer_<%= question._id %>"
                  value="<%= oIndex %>"
                  class="absolute opacity-0 w-full h-full cursor-pointer z-10"
                />
                <label
                  for="q<%= qIndex %>_o<%= oIndex %>"
                  class="flex items-center w-full p-3 sm:p-4 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors text-gray-700"
                >
                  <span
                    class="inline-flex items-center justify-center h-6 w-6 rounded-full bg-gray-100 text-gray-700 font-medium mr-2 flex-shrink-0"
                  >
                    <%= optionLetter %>
                  </span>
                  <span
                    ><% optionText = typeof option === "object" ? option.text :
                    option; // Loại bỏ tiền tố "A.", "B.", "C.", "D." nếu có
                    cleanOptionText = optionText; if
                    (/^\s*(?:[A-D]|[1-9])[\.\)][\s\u00A0]*/i.test(cleanOptionText))
                    { cleanOptionText =
                    cleanOptionText.replace(/^\s*(?:[A-D]|[1-9])[\.\)][\s\u00A0]*/i,
                    ""); } %><%= cleanOptionText %></span
                  >
                </label>
              </div>
              <% }); } else { %>
              <div class="text-red-500 italic col-span-2">
                Không có phương án trả lời cho câu hỏi này.
              </div>
              <% } %> <% } else if (question.type === 'text') { %>
              <div class="col-span-2">
                <textarea
                  name="answer_<%= question._id %>"
                  rows="3"
                  class="shadow-sm focus:ring-red-500 focus:border-red-500 block w-full sm:text-sm border-gray-300 rounded-md"
                  placeholder="Nhập câu trả lời của bạn..."
                ></textarea>
              </div>
              <% } %>
            </div>
          </div>
          <% }); %> <% } %>
        </div>

        <div
          class="mt-6 sm:mt-8 pt-4 sm:pt-5 border-t border-gray-200 flex justify-between"
        >
          <button
            type="button"
            id="exitButton"
            class="bg-white py-2 px-3 sm:px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
          >
            Thoát
          </button>
          <button
            type="submit"
            class="bg-red-600 py-2 px-4 sm:px-5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
          >
            Nộp bài
          </button>
        </div>
      </form>
    </div>

    <!-- Kết quả bài thi - Sẽ hiển thị sau khi nộp bài -->
    <div id="result-container" class="hidden">
      <div class="result-display text-center py-4 px-4 sm:px-6">
        <div class="mb-6 sm:mb-8">
          <div
            class="inline-flex items-center justify-center w-16 sm:w-20 h-16 sm:h-20 rounded-full bg-red-100 text-red-600 mb-4"
          >
            <i class="fas fa-trophy text-3xl sm:text-4xl"></i>
          </div>
          <h2 class="text-xl sm:text-2xl font-bold text-gray-800 mb-2">
            Hoàn thành bài thi!
          </h2>
          <p class="text-gray-600">Điểm số của bạn</p>
          <div
            class="text-4xl sm:text-5xl font-bold text-red-600 mt-2"
            id="examScore"
          >
            0/10
          </div>
        </div>

        <div
          class="bg-gray-50 rounded-lg p-3 sm:p-4 mb-5 sm:mb-6 mx-auto max-w-md"
        >
          <div class="flex items-center justify-between mb-2">
            <span class="text-gray-700">Số câu đúng:</span>
            <span class="font-medium text-green-600" id="correctAnswers"
              >0/0</span
            >
          </div>
          <div class="flex items-center justify-between mb-2">
            <span class="text-gray-700">Thời gian làm bài:</span>
            <span class="font-medium text-blue-600" id="examTime">0:00</span>
          </div>
        </div>

        <div
          id="summaryContainer"
          class="grid grid-cols-4 sm:grid-cols-6 gap-2 max-w-md mx-auto mb-5 sm:mb-6"
        >
          <!-- Question summary will be added here -->
        </div>

        <div class="mx-auto max-w-3xl mb-5 sm:mb-6 mt-4">
          <div class="bg-gray-50 rounded-lg p-4 mb-2">
            <h3 class="text-base sm:text-lg font-medium text-gray-800 mb-4">
              Xem lại câu trả lời
            </h3>
            <div id="answerResults" class="max-h-96 overflow-y-auto pr-2">
              <!-- Review questions will be added here -->
            </div>
          </div>
        </div>

        <div
          class="flex flex-col sm:flex-row justify-center space-y-2 sm:space-y-0 sm:space-x-4"
        >
          <button
            onclick="window.location.href='/home'"
            class="bg-red-600 py-2 px-6 rounded-full text-white font-medium hover:bg-red-700 focus:outline-none transition-colors"
          >
            Về trang chủ
          </button>
          <button
            id="retryWrongAnswers"
            class="bg-blue-600 py-2 px-6 rounded-full text-white font-medium hover:bg-blue-700 focus:outline-none transition-colors"
          >
            Làm lại câu hỏi sai
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Modal tiếp tục/làm mới bài thi Google Form -->
<div
  id="continueGoogleFormModal"
  class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden flex items-center justify-center"
  style="z-index: 9999"
>
  <div
    class="bg-white rounded-lg shadow-xl w-full max-w-md mx-4 overflow-hidden"
  >
    <div class="px-6 py-4 border-b border-gray-200 text-center">
      <div
        class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4"
      >
        <i class="fas fa-question-circle text-blue-500 text-2xl"></i>
      </div>
      <h3 class="text-xl font-semibold text-gray-800">
        Bạn có bài thi đang dở dang
      </h3>
    </div>
    <div class="p-6 text-center">
      <p class="text-gray-600 mb-4">
        Bạn có một bài thi chưa hoàn thành với
        <span class="font-bold text-red-600" id="timeRemainingGoogleForm"
          >--:--:--</span
        >
        còn lại.
      </p>
      <p class="text-sm text-gray-500 mb-6">
        Bạn muốn làm tiếp bài cũ hay bắt đầu bài mới?
      </p>

      <div class="flex flex-col sm:flex-row gap-3">
        <button
          onclick="continueGoogleFormExam()"
          class="flex-1 inline-flex items-center justify-center px-4 py-3 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
        >
          <i class="fas fa-play mr-2"></i>
          Làm tiếp bài cũ
        </button>
        <button
          onclick="startNewGoogleFormExam()"
          class="flex-1 inline-flex items-center justify-center px-4 py-3 bg-red-600 text-white text-sm font-medium rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors"
        >
          <i class="fas fa-redo mr-2"></i>
          Bắt đầu bài mới
        </button>
      </div>

      <button
        onclick="closeContinueGoogleFormModal()"
        class="mt-3 w-full text-sm text-gray-500 hover:text-gray-700 focus:outline-none"
      >
        Đóng
      </button>
    </div>
  </div>
</div>

<!-- Popup Cảnh báo Thoát/Reload -->
<div
  id="exitConfirmPopup"
  class="fixed inset-0 flex items-center justify-center z-50 hidden"
>
  <div class="fixed inset-0 bg-black opacity-50"></div>
  <div class="bg-white rounded-lg shadow-xl p-6 max-w-md w-full z-10 relative">
    <div class="mb-4 text-center">
      <div
        class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-yellow-100 mb-4"
      >
        <i class="fas fa-exclamation-triangle text-yellow-600 text-2xl"></i>
      </div>
      <h3 class="text-lg font-medium text-gray-900" id="exitConfirmTitle">
        Bạn có chắc chắn muốn rời khỏi?
      </h3>
      <p class="text-gray-500 mt-2" id="exitConfirmMessage">
        Rời khỏi trang này sẽ kết thúc bài thi của bạn và không thể hoàn tác.
      </p>
    </div>
    <div class="flex justify-center space-x-3">
      <button
        id="exitConfirmStayBtn"
        class="bg-gray-200 hover:bg-gray-300 text-gray-800 py-2 px-4 rounded-lg transition-colors"
      >
        Ở lại làm bài
      </button>
      <button
        id="exitConfirmLeaveBtn"
        class="bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-lg transition-colors"
      >
        Kết thúc bài thi
      </button>
    </div>
  </div>
</div>

<!-- Dữ liệu bài thi -->
<script type="application/json" id="exam-data">
  <%- JSON.stringify({
    id: exam._id,
    duration: exam.duration,
    questions: questions
  }) %>
</script>

<script>
  // Lưu trữ thông tin bài thi
  // Lấy dữ liệu từ JSON script
  const examDataElement = document.getElementById("exam-data");
  const examData = JSON.parse(examDataElement.textContent);

  // Thêm CSS để tạo kiểu cho các đáp án
  document.addEventListener("DOMContentLoaded", function () {
    // Thêm style cho các đáp án
    const style = document.createElement("style");
    style.textContent = `
      .question-container input[type="radio"] + label {
        border: 1px solid #e2e8f0;
        border-radius: 0.5rem;
        padding: 1rem;
        transition: all 0.3s ease;
        display: block;
        width: 100%;
        text-align: left;
        font-weight: 500;
      }
      
      .question-container input[type="radio"]:checked + label {
            border-color: rgb(191 191 255);
            background-color: rgb(226 226 254 / 50%);
            box-shadow: 0 0 0 2px rgb(181 198 239);
      }
      
      .question-container input[type="radio"]:hover + label {
        background-color: #f9fafb;
      }
    `;
    document.head.appendChild(style);

    // Check for existing Google Form data in localStorage
    // Use longer delay when coming from exam selection
    const isFromExamSelection =
      document.referrer.includes("/courses/") ||
      document.referrer.includes("/exam/");
    const checkDelay = isFromExamSelection ? 1000 : 500;

    setTimeout(() => {
      if (!localStorageChecked) {
        checkForExistingGoogleFormData();
      }
    }, checkDelay);

    // Additional check after examData is fully loaded (for encrypted questions)
    setTimeout(() => {
      if (
        examData &&
        examData.questions &&
        examData.questions.length > 0 &&
        !localStorageChecked
      ) {
        checkForExistingGoogleFormData();
      }
    }, 2000); // 2 seconds delay for encrypted questions

    // Add event listeners for radio buttons to save data
    setupAnswerChangeListeners();
  });

  // Biến lưu trữ trạng thái
  let timeRemaining = examData.duration * 60;
  let timerInterval;
  let isSubmitting = false; // Trạng thái đang nộp bài
  let isExitPopupVisible = false; // Trạng thái hiển thị popup
  let startTime = Date.now(); // Thêm thời gian bắt đầu làm bài
  let wrongQuestions = []; // Mảng lưu các câu hỏi sai

  // Thêm biến theo dõi câu hỏi hiện tại
  let currentQuestion = 1;
  let totalQuestions = examData.questions.length;

  // Extract exam ID with fallback mechanisms
  function getExamId() {
    // Try examData first
    if (examData && examData.id) {
      return examData.id;
    }

    // Fallback: extract from URL path
    const pathParts = window.location.pathname.split("/");
    const urlExamId = pathParts[2]; // /exam/{examId}/google-form

    if (urlExamId) {
      return urlExamId;
    }

    return null;
  }

  const CURRENT_EXAM_ID = getExamId();

  // Validate exam ID before using
  if (!CURRENT_EXAM_ID) {
    console.error(
      "❌ CRITICAL: Không thể xác định examId - localStorage sẽ không hoạt động!"
    );
  }

  const GOOGLE_FORM_STORAGE_KEY = `googleFormExam_${CURRENT_EXAM_ID}`;

  // Check if examData is properly loaded
  if (!examData || !examData.id) {
    console.warn("⚠️ ExamData chưa load hoặc không hợp lệ, sử dụng fallback");
  }

  // Object to store user answers
  let userAnswers = {};

  // Flag to prevent multiple localStorage checks
  let localStorageChecked = false;

  // LocalStorage functions for Google Form exam
  function saveGoogleFormDataToStorage() {
    if (!CURRENT_EXAM_ID) {
      console.error("❌ Không thể lưu localStorage: examId không hợp lệ");
      return;
    }

    try {
      const examFormData = {
        examId: CURRENT_EXAM_ID,
        userAnswers: userAnswers,
        currentQuestion: currentQuestion,
        startTime: startTime,
        timeRemaining: timeRemaining,
        timestamp: Date.now(),
        totalQuestions: totalQuestions,
        // Save question IDs to ensure consistent order when restoring
        questionIds: examData?.questions
          ? examData.questions.map((q) => q._id)
          : [],
        // Save complete question data to preserve order and content
        questionsData: examData?.questions || [],
        examDuration: examData?.duration || 45,
      };

      // Encrypt the data before storing if encryption is available
      if (typeof encryptStringOptimized === "function") {
        const encryptedData = encryptStringOptimized(
          JSON.stringify(examFormData)
        );
        localStorage.setItem(
          GOOGLE_FORM_STORAGE_KEY,
          JSON.stringify(encryptedData)
        );
      } else {
        console.warn("⚠️ Không tìm thấy function mã hóa, lưu dạng plain text");
        localStorage.setItem(
          GOOGLE_FORM_STORAGE_KEY,
          JSON.stringify(examFormData)
        );
      }
    } catch (error) {
      console.error("❌ Lỗi lưu dữ liệu Google Form localStorage:", error);
    }
  }

  function loadGoogleFormDataFromStorage() {
    if (!CURRENT_EXAM_ID) {
      console.error("❌ Không thể load localStorage: examId không hợp lệ");
      return null;
    }

    try {
      // Try primary storage key first
      let storedData = localStorage.getItem(GOOGLE_FORM_STORAGE_KEY);
      let usedKey = GOOGLE_FORM_STORAGE_KEY;

      // If not found, try to find any Google Form localStorage for this exam
      if (!storedData) {
        // Extract examId from URL path as fallback
        const pathExamId = window.location.pathname.split("/")[2];
        if (pathExamId && pathExamId !== CURRENT_EXAM_ID) {
          const alternativeKey = `googleFormExam_${pathExamId}`;

          storedData = localStorage.getItem(alternativeKey);
          if (storedData) {
            usedKey = alternativeKey;
          }
        }

        // Try to find any matching Google Form localStorage
        if (!storedData) {
          for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.startsWith("googleFormExam_")) {
              const data = localStorage.getItem(key);
              try {
                const parsed = JSON.parse(data);
                // Check if it's encrypted data or direct data
                let examFormData = parsed;
                if (parsed.encryptedData) {
                  // Skip validation for encrypted data for now

                  storedData = data;
                  usedKey = key;
                  break;
                } else if (parsed.examId) {
                  if (parsed.examId === CURRENT_EXAM_ID) {
                    storedData = data;
                    usedKey = key;
                    break;
                  }
                }
              } catch (e) {}
            }
          }
        }
      }

      if (!storedData) {
        return null;
      }

      // If we found data with alternative key, migrate it to primary key
      if (usedKey !== GOOGLE_FORM_STORAGE_KEY && storedData) {
        try {
          localStorage.setItem(GOOGLE_FORM_STORAGE_KEY, storedData);
          localStorage.removeItem(usedKey);
        } catch (e) {
          console.warn("⚠️ Không thể migrate localStorage:", e);
        }
      }

      let examFormData;

      // Try to decrypt if it's encrypted
      try {
        const encryptedObj = JSON.parse(storedData);
        if (
          encryptedObj.encryptedData &&
          typeof decryptStringOptimized === "function"
        ) {
          const decryptedText = decryptStringOptimized(
            encryptedObj.encryptedData,
            encryptedObj.token,
            encryptedObj.salt
          );
          examFormData = JSON.parse(decryptedText);
        } else {
          // Fallback to plain text
          examFormData = JSON.parse(storedData);
        }
      } catch (e) {
        // If decryption fails, try plain text
        examFormData = JSON.parse(storedData);
      }

      // Validate data and check if it's still valid (not expired)
      if (examFormData && examFormData.timestamp) {
        const elapsed = Date.now() - examFormData.timestamp;
        const maxAge = 60 * 60 * 1000; // 60 minutes

        if (elapsed < maxAge && examFormData.examId === CURRENT_EXAM_ID) {
          return examFormData;
        } else {
          clearGoogleFormDataFromStorage();
          return null;
        }
      }

      return examFormData;
    } catch (error) {
      console.error("❌ Lỗi tải dữ liệu Google Form localStorage:", error);
      clearGoogleFormDataFromStorage();
      return null;
    }
  }

  function clearGoogleFormDataFromStorage() {
    if (!CURRENT_EXAM_ID) {
      console.warn("⚠️ Không thể clear localStorage: examId không hợp lệ");
      return;
    }

    try {
      // Clear primary key
      localStorage.removeItem(GOOGLE_FORM_STORAGE_KEY);

      // Also clear any alternative Google Form keys for this exam
      const pathExamId = window.location.pathname.split("/")[2];
      if (pathExamId && pathExamId !== CURRENT_EXAM_ID) {
        const alternativeKey = `googleFormExam_${pathExamId}`;
        localStorage.removeItem(alternativeKey);
      }

      // Clean up any orphaned Google Form localStorage for this examId
      const keysToRemove = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith("googleFormExam_")) {
          try {
            const data = localStorage.getItem(key);
            const parsed = JSON.parse(data);
            if (!parsed.encryptedData && parsed.examId === CURRENT_EXAM_ID) {
              keysToRemove.push(key);
            }
          } catch (e) {
            // Skip invalid data
          }
        }
      }

      keysToRemove.forEach((key) => {
        localStorage.removeItem(key);
      });
    } catch (error) {
      console.error("❌ Lỗi xóa dữ liệu Google Form localStorage:", error);
    }
  }

  function restoreGoogleFormDataFromStorage() {
    const savedData = loadGoogleFormDataFromStorage();

    // Early return if no saved data
    if (!savedData) {
      return false;
    }

    // Validate question consistency (handle shuffled questions & encrypted data)
    if (
      savedData.questionIds &&
      savedData.questionIds.length > 0 &&
      examData?.questions &&
      examData.questions.length > 0
    ) {
      const currentQuestionIds = examData.questions.map((q) => q._id);
      const savedQuestionIds = savedData.questionIds;

      // Check if we have the same questions (order doesn't matter for shuffled exams)
      const currentIdSet = new Set(currentQuestionIds);
      const savedIdSet = new Set(savedQuestionIds);

      // Check if length and all IDs match (regardless of order)
      const lengthMatch = currentQuestionIds.length === savedQuestionIds.length;
      const idsMatch =
        savedQuestionIds.every((id) => currentIdSet.has(id)) &&
        currentQuestionIds.every((id) => savedIdSet.has(id));

      if (!lengthMatch || !idsMatch) {
        console.warn(
          "⚠️ Danh sách câu hỏi Google Form đã thay đổi (khác bộ câu hỏi), không thể khôi phục localStorage"
        );

        clearGoogleFormDataFromStorage();
        return false;
      }

      // Check if questions are in same order or just shuffled
      const sameOrder = currentQuestionIds.every(
        (id, index) => id === savedQuestionIds[index]
      );

      if (sameOrder) {
        console.log("✅ Thứ tự câu hỏi Google Form nhất quán");
      } else {
        console.log("🔀 Câu hỏi đã được shuffle");

        // For shuffled questions, answers are stored by questionId, so no remapping needed
        console.log("✅ Answers được lưu");
      }
    } else if (!examData?.questions || examData.questions.length === 0) {
      console.log(
        "⚠️ ExamData.questions chưa sẵn sàng hoặc encrypted, bỏ qua validation thứ tự câu hỏi"
      );

      // For encrypted questions or async loading, skip validation but allow restore
      // The restore function will handle finding questions properly
    } else if (!savedData.questionIds || savedData.questionIds.length === 0) {
      console.log(
        "⚠️ Saved data không có questionIds, có thể là data cũ hoặc encrypted"
      );
      console.log("📋 Cho phép khôi phục dữ liệu vì examId đã match");
    }

    console.log("🔄 Khôi phục dữ liệu Google Form");

    // Restore user answers
    userAnswers = savedData.userAnswers || {};

    // Restore current question
    if (savedData.currentQuestion !== undefined) {
      currentQuestion = savedData.currentQuestion;
    }

    // Restore time
    if (savedData.timeRemaining !== undefined) {
      timeRemaining = savedData.timeRemaining;
    }

    // Update progress bar first
    updateProgressBar();

    // Update UI to show selected answers with multiple attempts
    setTimeout(() => {
      restoreSelectedAnswers();

      // Try again after a short delay if needed
      setTimeout(() => {
        const checkedCount = document.querySelectorAll(
          'input[type="radio"]:checked'
        ).length;
        const expectedCount = Object.keys(userAnswers).length;

        if (checkedCount < expectedCount) {
          console.warn(
            `⚠️ Chỉ khôi phục được ${checkedCount}/${expectedCount} đáp án, thử lại...`
          );
          restoreSelectedAnswers();

          // Final check
          setTimeout(() => {
            const finalCheckedCount = document.querySelectorAll(
              'input[type="radio"]:checked'
            ).length;
            console.log(
              `🔍 Kết quả cuối cùng: ${finalCheckedCount}/${expectedCount} đáp án được khôi phục`
            );
          }, 100);
        }
      }, 200);
    }, 50);

    console.log("✅ Đã khôi phục dữ liệu Google Form thành công");
    return true;
  }

  function restoreSelectedAnswers() {
    if (!userAnswers || Object.keys(userAnswers).length === 0) {
      return;
    }

    // Multiple attempts with increasing delays to handle async DOM updates
    function attemptRestore(attempt = 1, maxAttempts = 5) {
      const delay = attempt * 200; // 200ms, 400ms, 600ms, 800ms, 1000ms

      setTimeout(() => {
        let restoredCount = 0;
        const totalAnswers = Object.keys(userAnswers).length;

        // Restore selected radio buttons
        Object.entries(userAnswers).forEach(([questionId, selectedIndex]) => {
          console.log(
            `🔍 [Attempt ${attempt}] Đang tìm radio button cho câu ${questionId}, đáp án ${selectedIndex}`
          );

          // Try multiple strategies to find the radio button
          let radioButton = null;

          // Strategy 1: Direct name matching (works for most cases)
          radioButton = document.querySelector(
            `input[name="answer_${questionId}"][value="${selectedIndex}"]`
          );

          // Strategy 2: Try alternative naming patterns
          if (!radioButton) {
            radioButton = document.querySelector(
              `input[name="question${questionId}"][value="${selectedIndex}"]`
            );
          }

          // Strategy 3: Try data attribute approach
          if (!radioButton) {
            radioButton = document.querySelector(
              `input[data-question-id="${questionId}"][value="${selectedIndex}"]`
            );
          }

          // Strategy 4: Search within question containers
          if (!radioButton) {
            const questionContainer =
              document.querySelector(`[data-question-id="${questionId}"]`) ||
              document.querySelector(`#question-${questionId}`) ||
              document.querySelector(`#q${questionId}`);

            if (questionContainer) {
              radioButton = questionContainer.querySelector(
                `input[type="radio"][value="${selectedIndex}"]`
              );
            }
          }

          // Strategy 5: Manual search through all radios (for shuffled/encrypted questions)
          if (!radioButton) {
            const allRadios = document.querySelectorAll('input[type="radio"]');
            for (const radio of allRadios) {
              // Check various ways the question ID might be stored
              const radioQuestionId =
                radio.name.replace(/^(answer_|question)/, "") ||
                radio.getAttribute("data-question-id") ||
                radio
                  .closest("[data-question-id]")
                  ?.getAttribute("data-question-id");

              if (
                radioQuestionId === questionId &&
                radio.value === String(selectedIndex)
              ) {
                radioButton = radio;
                console.log(`🔍 Found via manual search for Q${questionId}`);
                break;
              }
            }
          }

          if (radioButton) {
            radioButton.checked = true;
            restoredCount++;
            console.log(
              `✅ [Attempt ${attempt}] Đã khôi phục đáp án câu hỏi ${questionId}: ${selectedIndex}`
            );

            // Trigger change event for UI updates
            radioButton.dispatchEvent(new Event("change", { bubbles: true }));

            // Add visual indication
            const questionContainer = radioButton.closest(
              ".question-container, .question-item, .mb-6"
            );
            if (questionContainer) {
              questionContainer.classList.add("answered");
            }
          } else {
            console.error(
              `❌ [Attempt ${attempt}] Không tìm thấy radio button cho câu ${questionId}, đáp án ${selectedIndex}`
            );

            // Enhanced debugging
            if (attempt === 1) {
              const allRadiosForQuestion = document.querySelectorAll(
                `input[name="answer_${questionId}"], input[name="question${questionId}"], input[data-question-id="${questionId}"]`
              );
              console.log(
                `🔍 Có ${allRadiosForQuestion.length} radio buttons tìm thấy cho câu ${questionId}:`
              );
              allRadiosForQuestion.forEach((radio, idx) => {
                console.log(
                  `  - Radio ${idx}: name="${radio.name}", value="${radio.value}", id="${radio.id}"`
                );
              });

              // Show available values for this question
              if (allRadiosForQuestion.length > 0) {
                const availableValues = Array.from(allRadiosForQuestion).map(
                  (r) => r.value
                );
                console.log(
                  `Available values: [${availableValues.join(
                    ", "
                  )}], Looking for: ${selectedIndex}`
                );
              }
            }
          }
        });

        console.log(
          `📊 [Attempt ${attempt}] Đã khôi phục ${restoredCount}/${totalAnswers} đáp án`
        );

        // If not all answers restored and we have more attempts
        if (restoredCount < totalAnswers && attempt < maxAttempts) {
          console.log(
            `🔄 Chưa khôi phục hết, thử lại attempt ${attempt + 1}...`
          );
          attemptRestore(attempt + 1, maxAttempts);
        } else {
          if (restoredCount === totalAnswers) {
            console.log("🎉 Tất cả đáp án đã được khôi phục thành công!");
          } else {
            console.warn(
              `⚠️ Chỉ khôi phục được ${restoredCount}/${totalAnswers} đáp án sau ${maxAttempts} lần thử. Có thể do questions được shuffle hoặc cấu trúc DOM thay đổi.`
            );
          }

          // Update progress and UI
          updateQuestionProgress();
        }
      }, delay);
    }

    // Start the restoration attempts
    attemptRestore(1, 5);
  }

  // Modal functions for Google Form
  function showContinueGoogleFormModal(savedData) {
    const modal = document.getElementById("continueGoogleFormModal");
    const timeDisplay = document.getElementById("timeRemainingGoogleForm");

    // Format remaining time
    const remainingSeconds = Math.max(0, savedData.timeRemaining);
    const hours = Math.floor(remainingSeconds / 3600);
    const minutes = Math.floor((remainingSeconds % 3600) / 60);
    const seconds = remainingSeconds % 60;

    timeDisplay.textContent = `${hours.toString().padStart(2, "0")}:${minutes
      .toString()
      .padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;

    modal.classList.remove("hidden");
  }

  function closeContinueGoogleFormModal() {
    const modal = document.getElementById("continueGoogleFormModal");
    modal.classList.add("hidden");
  }

  function continueGoogleFormExam() {
    closeContinueGoogleFormModal();

    // Get saved data from localStorage
    const savedData = loadGoogleFormDataFromStorage();

    if (
      savedData &&
      savedData.questionsData &&
      savedData.questionsData.length > 0
    ) {
      // Update global examData with saved questions to preserve order
      if (typeof examData !== "undefined") {
        examData.questions = savedData.questionsData;
        examData.duration = savedData.examDuration || examData.duration;
      }

      // Clear existing questions and render from localStorage
      const container = document.getElementById("questions-container");
      if (container) {
        console.log("🎨 Render questions");
        generateQuestionsFromSavedData(savedData.questionsData);
      }

      // Show the exam form
      const examForm = document.getElementById("exam-form");
      if (examForm) {
        examForm.classList.remove("hidden");
      }

      // Restore other data after a short delay to ensure DOM is ready
      setTimeout(() => {
        const restored = restoreGoogleFormDataFromStorage();
        if (restored) {
          console.log(
            "✅ Đã khôi phục và tiếp tục bài thi Google Form với thứ tự cũ"
          );

          // Start timer with remaining time
          startTimer();
          // Start periodic save
          startPeriodicSave();

          // Debug current state
          debugCurrentState();
        }
      }, 200);
    } else {
      console.log(
        "⚠️ Không có question data trong localStorage, fallback về cách cũ"
      );

      // Fallback to regular restore
      const restored = restoreGoogleFormDataFromStorage();
      if (restored) {
        console.log("✅ Đã khôi phục và tiếp tục bài thi Google Form");

        // Start timer with remaining time
        startTimer();
        // Start periodic save
        startPeriodicSave();

        // Debug current state
        debugCurrentState();
      } else {
        console.error("❌ Không thể khôi phục dữ liệu, bắt đầu bài mới");
        startNewGoogleFormExam();
      }
    }
  }

  function debugCurrentState() {
    // List all checked radios
    document
      .querySelectorAll('input[type="radio"]:checked')
      .forEach((radio, idx) => {
        console.log(`  ${idx + 1}. ${radio.name} = ${radio.value} (checked)`);
      });

    // List all Google Form localStorage keys

    let foundGoogleFormKeys = 0;
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith("googleFormExam_")) {
        foundGoogleFormKeys++;
        const data = localStorage.getItem(key);
        try {
          const parsed = JSON.parse(data);
          const examId = parsed.encryptedData ? "encrypted" : parsed.examId;
          const answersCount = parsed.encryptedData
            ? "encrypted"
            : Object.keys(parsed.userAnswers || {}).length;
          const timeRemaining = parsed.encryptedData
            ? "encrypted"
            : parsed.timeRemaining;
        } catch (e) {
          console.log(`invalid data`);
        }
      }
    }

    if (foundGoogleFormKeys === 0) {
      console.log("  📭 Không có dữ liệu");
    }
  }

  // Function to generate questions from saved localStorage data
  function generateQuestionsFromSavedData(questionsData) {
    const container = document.getElementById("questions-container");
    if (!container) {
      return;
    }

    container.innerHTML = "";

    questionsData.forEach((question, qIndex) => {
      const questionHTML = createQuestionHTML(question, qIndex);
      container.appendChild(questionHTML);
    });

    // Reinitialize event listeners for new questions
    setupAnswerChangeListeners();
  }

  function startNewGoogleFormExam() {
    closeContinueGoogleFormModal();

    // Clear old data
    clearGoogleFormDataFromStorage();
    userAnswers = {};

    // Reset state
    currentQuestion = 1;
    timeRemaining = examData.duration * 60;
    startTime = Date.now();

    // Clear any selected answers
    document
      .querySelectorAll('input[type="radio"]:checked')
      .forEach((radio) => {
        radio.checked = false;
      });

    // Show the exam form with server data (normal flow)
    const examForm = document.getElementById("exam-form");
    if (examForm) {
      examForm.classList.remove("hidden");
    }

    // Start timer
    startTimer();
    updateProgressBar();

    // Start periodic save (this will save the initial state with current question order)
    startPeriodicSave();
  }

  function checkForExistingGoogleFormData() {
    // Prevent duplicate checks if already processed
    if (localStorageChecked) {
      return false;
    }

    debugCurrentState();

    // *** CLEAR OLD EXAM DATA FIRST ***
    if (typeof clearAllExamDataFromStorage === "function") {
      const currentExamInfo = {
        type: "googleForm",
        examId: CURRENT_EXAM_ID,
      };
      const cleanupResult = clearAllExamDataFromStorage(currentExamInfo);
    } else {
      console.warn("⚠️ Function clearAllExamDataFromStorage không tìm thấy");
    }

    const savedData = loadGoogleFormDataFromStorage();

    if (savedData && savedData.timeRemaining > 0) {
      // Hide loading and prevent server rendering
      const loadingIndicator = document.getElementById("loadingIndicator");
      const examForm = document.getElementById("exam-form");

      if (loadingIndicator) loadingIndicator.style.display = "none";
      if (examForm) examForm.classList.add("hidden");

      // Store saved data globally for easy access
      window.savedExamDataForContinue = savedData;

      // Don't auto-start timer yet, wait for user choice
      showContinueGoogleFormModal(savedData);
      localStorageChecked = true;
      return true; // Found existing data, prevent server rendering
    } else {
      if (savedData) {
        console.log("⏰ Dữ liệu đã hết hạn hoặc hết thời gian");
      } else {
        console.log("📭 Không tìm thấy dữ liệu");
      }
      console.log("🚀bắt đầu bài thi mới");
      localStorageChecked = true;
      return false; // No existing data, allow server rendering
    }
  }

  function setupAnswerChangeListeners() {
    // Add event listeners to all radio buttons
    // Use a flag to prevent duplicate listeners
    document.querySelectorAll('input[type="radio"]').forEach((radio) => {
      // Check if listener already exists
      if (!radio.dataset.listenerAdded) {
        radio.addEventListener("change", function () {
          if (this.checked) {
            // Extract question ID and selected option index
            const questionId = this.name.replace("answer_", "");
            const selectedIndex = parseInt(this.value);

            // Store user answer
            userAnswers[questionId] = selectedIndex;

            // Save to localStorage
            saveGoogleFormDataToStorage();

            // Update progress (count answered questions)
            updateQuestionProgress();
          }
        });

        // Mark as having listener
        radio.dataset.listenerAdded = "true";
      }
    });
  }

  function updateQuestionProgress() {
    // Count answered questions
    const answeredCount = Object.keys(userAnswers).length;
    currentQuestion = Math.min(answeredCount + 1, totalQuestions);

    // Update progress bar
    updateProgressBar();
  }

  // Save data periodically (every 30 seconds)
  function startPeriodicSave() {
    setInterval(() => {
      if (Object.keys(userAnswers).length > 0) {
        saveGoogleFormDataToStorage();
      }
    }, 30000); // 30 seconds
  }

  // Hàm cập nhật thanh tiến độ
  function updateProgressBar() {
    const progressPercent = (currentQuestion / totalQuestions) * 100;
    document.getElementById("progressBar").style.width = `${progressPercent}%`;
    document.getElementById(
      "questionCounter"
    ).textContent = `Câu hỏi ${currentQuestion}/${totalQuestions}`;
  }

  // Hàm hiển thị popup xác nhận
  function showExitConfirmPopup(title, message, isTimeUp = false) {
    const popup = document.getElementById("exitConfirmPopup");
    const titleElement = document.getElementById("exitConfirmTitle");
    const messageElement = document.getElementById("exitConfirmMessage");
    const stayButton = document.getElementById("exitConfirmStayBtn");
    const leaveButton = document.getElementById("exitConfirmLeaveBtn");

    // Cập nhật nội dung
    titleElement.textContent = title || "Bạn có chắc chắn muốn rời khỏi?";
    messageElement.textContent =
      message ||
      "Rời khỏi trang này sẽ kết thúc bài thi của bạn và không thể hoàn tác.";

    if (isTimeUp) {
      stayButton.style.display = "none"; // Ẩn nút nếu hết giờ
    } else {
      stayButton.style.display = "inline-block"; // Hiện nút cho các trường hợp khác
    }
    // Hiển thị popup
    popup.classList.remove("hidden");
    isExitPopupVisible = true;

    // Xử lý sự kiện nút
    stayButton.onclick = function () {
      popup.classList.add("hidden");
      isExitPopupVisible = false;

      // Nếu đang sử dụng nút Back, hãy thêm một mục vào lịch sử để vô hiệu hóa nút Back
      history.pushState(null, null, window.location.pathname);
    };

    leaveButton.onclick = function () {
      isSubmitting = true; // Đánh dấu là đang nộp bài
      popup.classList.add("hidden");
      isExitPopupVisible = false;

      // Hiển thị kết quả bài thi
      if (document.getElementById("exam-container").style.display !== "none") {
        submitExam();
      }
    };
  }

  // Xử lý sự kiện khi người dùng sử dụng nút Back/Forward của trình duyệt
  window.addEventListener("popstate", function (event) {
    if (!isSubmitting && !isExitPopupVisible) {
      // Ngăn chặn hành vi mặc định của trình duyệt
      event.preventDefault();

      // Hiển thị popup xác nhận
      showExitConfirmPopup(
        "Bạn đang cố gắng rời khỏi trang",
        "Sử dụng nút Quay lại của trình duyệt sẽ kết thúc bài thi của bạn. Bạn có muốn tiếp tục?"
      );

      // Thêm một mục vào lịch sử để ngăn chặn việc quay lại
      history.pushState(null, null, window.location.pathname);
    }
  });

  // Xử lý sự kiện khi người dùng tải lại trang hoặc đóng tab
  window.addEventListener("beforeunload", function (event) {
    if (!isSubmitting) {
      // Ghi chú: Popup tùy chỉnh không hoạt động với beforeunload
      // Các trình duyệt hiện đại giới hạn những gì có thể làm với sự kiện này vì lý do bảo mật
      // Thay vào đó, chúng tôi sẽ hiển thị hộp thoại xác nhận tiêu chuẩn của trình duyệt
      event.preventDefault();
      event.returnValue =
        "Bạn có chắc chắn muốn rời khỏi trang? Việc này sẽ kết thúc bài thi của bạn.";
      return event.returnValue;
    }
  });

  // Xử lý sự kiện khi người dùng nhấn các phím tắt để tải lại trang
  document.addEventListener("keydown", function (event) {
    // Phát hiện Ctrl+R, F5, hoặc Ctrl+F5
    if (
      !isSubmitting &&
      !isExitPopupVisible &&
      ((event.ctrlKey && event.key === "r") ||
        event.key === "F5" ||
        (event.ctrlKey && event.key === "F5"))
    ) {
      // Ngăn chặn hành vi mặc định
      event.preventDefault();

      // Hiển thị popup xác nhận
      showExitConfirmPopup(
        "Bạn đang cố gắng tải lại trang",
        "Tải lại trang sẽ kết thúc bài thi của bạn. Bạn có muốn tiếp tục?"
      );

      return false;
    }
  });

  // Ngăn chặn menu chuột phải để tránh người dùng chọn "Tải lại" từ menu
  document.addEventListener("contextmenu", function (event) {
    // Chỉ ngăn chặn trong khi làm bài thi
    if (!isSubmitting && !isExitPopupVisible) {
      event.preventDefault();
      return false;
    }
  });

  // Thêm một mục vào lịch sử ngay khi trang tải để ngăn chặn nút Back
  history.pushState(null, null, window.location.pathname);

  // Bắt đầu đếm thời gian
  function startTimer() {
    timerInterval = setInterval(updateTimer, 1000);
    updateTimer();
  }

  // Cập nhật hiển thị thời gian
  function updateTimer() {
    if (timeRemaining <= 0) {
      // Hết thời gian, tự động nộp bài

      clearInterval(timerInterval);

      // Hiển thị popup thông báo hết giờ
      showExitConfirmPopup(
        "Đã hết thời gian làm bài!",
        "Thời gian làm bài đã kết thúc. Bài thi của bạn sẽ được nộp tự động.",
        true
      );

      // Tự động nhấn nút nộp bài sau 5 giây nếu người dùng không phản hồi
      setTimeout(function () {
        if (isExitPopupVisible) {
          document.getElementById("exitConfirmLeaveBtn").click();
        }
      }, 5000);

      return;
    }

    const hours = Math.floor(timeRemaining / 3600);
    const minutes = Math.floor((timeRemaining % 3600) / 60);
    const seconds = timeRemaining % 60;

    document.getElementById("timer").textContent = `${hours
      .toString()
      .padStart(2, "0")}:${minutes.toString().padStart(2, "0")}:${seconds
      .toString()
      .padStart(2, "0")}`;

    // Thay đổi màu khi gần hết thời gian
    if (timeRemaining <= 300) {
      // 5 phút cuối
      document.getElementById("examTimer").classList.add("bg-red-600");
      document.getElementById("examTimer").classList.remove("bg-red-700");

      // Nhấp nháy khi còn dưới 1 phút
      if (timeRemaining <= 60) {
        document.getElementById("examTimer").classList.toggle("bg-red-800");
      }
    }

    timeRemaining--;
  }

  // Nộp bài thi
  function submitExam() {
    // Clear localStorage when submitting exam
    clearGoogleFormDataFromStorage();

    // Kiểm tra cờ isSubmitting nhưng vẫn tiếp tục xử lý
    if (isSubmitting) {
    }

    // Đánh dấu là đang nộp bài để không hiển thị cảnh báo
    isSubmitting = true;

    // Dừng bộ đếm thời gian
    clearInterval(timerInterval);

    // Tính thời gian làm bài
    const endTime = Date.now();
    const totalTimeMs = endTime - startTime;
    const totalTimeSeconds = Math.floor(totalTimeMs / 1000);
    const minutes = Math.floor(totalTimeSeconds / 60);
    const seconds = totalTimeSeconds % 60;
    const formattedTime = `${minutes}:${seconds.toString().padStart(2, "0")}`;
    document.getElementById("examTime").textContent = formattedTime;

    // Thu thập câu trả lời
    const formData = new FormData(document.getElementById("exam-form"));
    const answers = {};

    // Xử lý và chấm điểm
    let correctCount = 0;
    let questionResults = []; // Mảng lưu kết quả từng câu

    examData.questions.forEach((question, qIndex) => {
      const answerId = `answer_${question._id}`;
      const userAnswer = formData.get(answerId);
      answers[question._id] = userAnswer;

      let isCorrect = false;
      let userSelectedOption = null;
      let correctOption = null;

      // Kiểm tra đáp án đúng (giả định là option có isCorrect = true là đáp án đúng)
      if (
        (!question.type || question.type === "multiple-choice") &&
        userAnswer !== null
      ) {
        const selectedOptionIndex = parseInt(userAnswer);
        const answerOptions = question.answers || question.options || [];

        if (
          selectedOptionIndex >= 0 &&
          selectedOptionIndex < answerOptions.length
        ) {
          userSelectedOption = answerOptions[selectedOptionIndex];

          if (userSelectedOption && userSelectedOption.isCorrect) {
            isCorrect = true;
            correctCount++;
          }

          // Tìm đáp án đúng
          correctOption = answerOptions.find((opt) => opt.isCorrect);
        }
      }

      // Lưu kết quả của câu hỏi
      questionResults.push({
        questionIndex: qIndex,
        question: question,
        userAnswer: userAnswer,
        selectedOptionIndex: userAnswer !== null ? parseInt(userAnswer) : null,
        isCorrect: isCorrect,
        userSelectedOption: userSelectedOption,
        correctOption: correctOption,
      });
    });

    // Tính điểm
    const totalQuestions = examData.questions.length;
    const score = Math.round((correctCount / totalQuestions) * 10 * 100) / 100;

    // Hiển thị kết quả
    document.getElementById(
      "correctAnswers"
    ).textContent = `${correctCount}/${totalQuestions}`;
    document.getElementById("examScore").textContent = `${score}/10`;

    // Đếm số câu chưa trả lời
    const skippedQuestions = questionResults.filter(
      (result) => result.selectedOptionIndex === null
    ).length;

    // Tạo phần tử hiển thị số câu chưa trả lời nếu có
    if (skippedQuestions > 0) {
      const skippedElement = document.createElement("div");
      skippedElement.className = "flex items-center justify-between mb-2";
      skippedElement.innerHTML = `
        <span class="text-gray-700">Số câu chưa trả lời:</span>
        <span class="font-medium text-gray-600">${skippedQuestions}/${totalQuestions}</span>
      `;

      // Chèn vào trước thời gian làm bài
      const summaryContainer = document.querySelector(
        "#result-container .bg-gray-50.rounded-lg"
      );
      const timeElement = summaryContainer.querySelector(
        ".flex.items-center.justify-between:last-child"
      );
      summaryContainer.insertBefore(skippedElement, timeElement);
    }

    // Cập nhật tiến độ để hiển thị hoàn thành
    currentQuestion = totalQuestions;
    updateProgressBar();

    // Lưu kết quả bài thi vào lịch sử
    saveExamHistory(correctCount, totalQuestions, totalTimeSeconds);

    // Cập nhật danh sách câu hỏi sai
    updateWrongQuestions(questionResults);

    // Tạo biểu đồ tóm tắt dạng dots
    const summaryContainer = document.getElementById("summaryContainer");
    summaryContainer.innerHTML = "";

    questionResults.forEach((result, index) => {
      const questionDot = document.createElement("div");

      // Xác định lớp CSS dựa trên trạng thái trả lời
      let dotClass = "";
      let tooltipText = "";

      if (result.selectedOptionIndex === null) {
        // Chưa trả lời
        dotClass = "bg-gray-100 text-gray-800 border border-gray-300";
        tooltipText = `Câu ${index + 1}: Chưa trả lời`;
      } else if (result.isCorrect) {
        // Trả lời đúng
        dotClass = "bg-green-100 text-green-800 border border-green-300";
        tooltipText = `Câu ${index + 1}: Đúng`;
      } else {
        // Trả lời sai
        dotClass = "bg-red-100 text-red-800 border border-red-300";
        tooltipText = `Câu ${index + 1}: Sai`;
      }

      questionDot.className = `h-8 w-8 rounded-full flex items-center justify-center text-xs font-medium ${dotClass}`;
      questionDot.textContent = index + 1;

      // Tooltip với chi tiết
      questionDot.title = tooltipText;

      // Click vào số câu hỏi để cuộn đến câu đó trong phần xem lại
      questionDot.style.cursor = "pointer";
      questionDot.addEventListener("click", () => {
        const reviewContainer = document.getElementById("answerResults");
        const questionReview = document.getElementById(
          `question-review-${index}`
        );

        if (reviewContainer && questionReview) {
          // Cuộn đến vị trí câu hỏi trong container scroll
          questionReview.scrollIntoView({
            behavior: "smooth",
            block: "center",
            inline: "nearest",
          });
          questionReview.classList.add("bg-red-50");
          setTimeout(() => {
            questionReview.classList.remove("bg-red-50");
          }, 1500);
        }
      });

      summaryContainer.appendChild(questionDot);
    });

    // Hiển thị chi tiết từng câu
    const resultsContainer = document.getElementById("answerResults");
    resultsContainer.innerHTML = "";

    // Phần xem lại luôn hiển thị với khả năng scroll

    // Tạo nội dung chi tiết từng câu hỏi - sử dụng định dạng giống quizizz
    questionResults.forEach((result, index) => {
      const question = result.question;
      const answerOptions = question.answers || question.options || [];

      const reviewItem = document.createElement("div");
      reviewItem.id = `question-review-${index}`;
      reviewItem.className =
        "border-b border-gray-200 pb-4 mb-4 transition-colors duration-300";

      let answerOptionsHtml = "";
      if (answerOptions && answerOptions.length > 0) {
        answerOptionsHtml = `
          <div class="grid grid-cols-1 gap-2 text-left">
            ${answerOptions
              .map((opt, i) => {
                const optText = typeof opt === "object" ? opt.text || "" : opt;

                // Loại bỏ tiền tố "A.", "B.", "C.", "D." nếu có
                let cleanOptText = optText;
                const prefixRegex = /^\s*(?:[A-D]|[1-9])[\.\)][\s\u00A0]*/i;
                if (prefixRegex.test(cleanOptText)) {
                  cleanOptText = cleanOptText.replace(prefixRegex, "");
                }

                let className = "p-2 rounded border";

                // Kiểm tra xem người dùng đã chọn đáp án chưa
                if (result.selectedOptionIndex === null) {
                  // Chưa có đáp án được chọn
                  // Chỉ đánh dấu đáp án đúng
                  if (typeof opt === "object" && opt.isCorrect) {
                    className += " bg-blue-50 border-blue-300 text-blue-800";
                  } else {
                    className += " border-gray-200 text-gray-600";
                  }
                } else {
                  // Đã có đáp án được chọn
                  // Đánh dấu đáp án người dùng đã chọn
                  if (i === result.selectedOptionIndex) {
                    className += result.isCorrect
                      ? " bg-green-100 border-green-500 text-green-800"
                      : " bg-red-100 border-red-500 text-red-800";
                  }
                  // Đánh dấu đáp án đúng nếu người dùng chọn sai
                  else if (
                    !result.isCorrect &&
                    typeof opt === "object" &&
                    opt.isCorrect
                  ) {
                    className +=
                      " bg-green-100 border-green-500 text-green-800";
                  } else {
                    className += " border-gray-200 text-gray-600";
                  }
                }

                return `<div class="${className}">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 h-6 w-6 rounded-full bg-red-100 text-red-600 flex items-center justify-center mr-3">
                      ${String.fromCharCode(65 + i)}
                    </div>
                    <div>${cleanOptText}</div>
                    ${
                      result.selectedOptionIndex !== null &&
                      i === result.selectedOptionIndex
                        ? `<div class="ml-auto ${
                            result.isCorrect ? "text-green-600" : "text-red-600"
                          }">
                        <i class="fas ${
                          result.isCorrect ? "fa-check" : "fa-times"
                        }"></i>
                      </div>`
                        : ""
                    }
                    ${
                      (result.selectedOptionIndex === null ||
                        !result.isCorrect) &&
                      typeof opt === "object" &&
                      opt.isCorrect
                        ? `<div class="ml-auto ${
                            result.selectedOptionIndex === null
                              ? "text-blue-600"
                              : "text-green-600"
                          }">
                        <i class="fas fa-${
                          result.selectedOptionIndex === null
                            ? "info-circle"
                            : "check"
                        }"></i>
                      </div>`
                        : ""
                    }
                  </div>
                </div>`;
              })
              .join("")}
          </div>
        `;
      }

      // Tìm đáp án đúng
      const correctOption = answerOptions.find(
        (opt) => typeof opt === "object" && opt.isCorrect
      );
      let correctText = correctOption
        ? correctOption.text
        : "Không tìm thấy đáp án đúng";

      // Loại bỏ tiền tố cho đáp án đúng
      const prefixRegex = /^\s*(?:[A-D]|[1-9])[\.\)][\s\u00A0]*/i;
      if (prefixRegex.test(correctText)) {
        correctText = correctText.replace(prefixRegex, "");
      }

      // Tìm đáp án người dùng đã chọn
      const selectedOption =
        result.selectedOptionIndex !== null
          ? answerOptions[result.selectedOptionIndex]
          : null;
      let selectedText = selectedOption
        ? typeof selectedOption === "object"
          ? selectedOption.text
          : selectedOption
        : "Không xác định";

      // Loại bỏ tiền tố cho đáp án đã chọn
      if (prefixRegex.test(selectedText)) {
        selectedText = selectedText.replace(prefixRegex, "");
      }

      reviewItem.innerHTML = `
        <div class="text-left mb-2">
          <h4 class="font-medium text-gray-800">Câu ${index + 1}: ${
        question.text
      }</h4>
          ${
            question.image
              ? `<img src="${question.image}" alt="Hình ảnh câu hỏi" class="max-w-full h-auto rounded my-2">`
              : ""
          }
        </div>
        ${answerOptionsHtml}
        <div class="mt-2 text-sm ${
          result.isCorrect ? "text-green-600" : "text-red-600"
        }">
          ${
            result.isCorrect
              ? '<i class="fas fa-check mr-1"></i>Đáp án đúng'
              : result.selectedOptionIndex === null
              ? '<i class="fas fa-exclamation-circle mr-1"></i>Chưa có câu trả lời. Đáp án đúng: ' +
                correctText
              : '<i class="fas fa-times mr-1"></i>Đáp án sai. Đáp án đúng: ' +
                correctText
          }
        </div>
      `;

      resultsContainer.appendChild(reviewItem);
    });

    // Ẩn phần bài thi
    document.getElementById("exam-container").classList.add("hidden");

    // Hiển thị phần kết quả
    document.getElementById("result-container").classList.remove("hidden");

    return false; // Ngăn form submit
  }

  // Khởi động timer khi trang tải xong
  document.addEventListener("DOMContentLoaded", function () {
    // Bắt đầu đếm thời gian

    // Cập nhật tiến độ ban đầu
    updateProgressBar();

    // Thêm sự kiện cho các input radio để cập nhật câu hỏi hiện tại
    const radioInputs = document.querySelectorAll('input[type="radio"]');
    radioInputs.forEach((input) => {
      input.addEventListener("change", function () {
        // Lấy số thứ tự câu hỏi từ ID của input
        const questionId = this.id.split("_")[0].substring(1);
        // Cập nhật câu hỏi hiện tại nếu cao hơn giá trị hiện tại
        currentQuestion = Math.max(currentQuestion, parseInt(questionId) + 1);
        if (currentQuestion > totalQuestions) {
          currentQuestion = totalQuestions;
        }
        // Cập nhật thanh tiến độ
        updateProgressBar();
      });
    });

    // Thêm sự kiện cho các textarea (nếu có)
    const textareas = document.querySelectorAll("textarea");
    textareas.forEach((textarea) => {
      textarea.addEventListener("input", function () {
        // Lấy số thứ tự câu hỏi từ tên của textarea
        const questionName = this.name;
        const qIndex = Array.from(
          document.querySelectorAll(".question-container")
        ).findIndex((container) =>
          container.querySelector(`[name="${questionName}"]`)
        );

        if (qIndex !== -1) {
          // Cập nhật câu hỏi hiện tại nếu cao hơn giá trị hiện tại
          currentQuestion = Math.max(currentQuestion, qIndex + 2);
          if (currentQuestion > totalQuestions) {
            currentQuestion = totalQuestions;
          }
          // Cập nhật thanh tiến độ
          updateProgressBar();
        }
      });
    });

    // Xử lý sự kiện khi người dùng nhấn nút nộp bài
    document
      .getElementById("exam-form")
      .addEventListener("submit", function (e) {
        e.preventDefault();

        if (!isSubmitting && !isExitPopupVisible) {
          // Hiển thị popup xác nhận
          showExitConfirmPopup(
            "Xác nhận nộp bài",
            "Bạn có chắc chắn muốn nộp bài? Thời gian làm bài còn lại sẽ bị hủy."
          );
        }

        return false;
      });

    // Xử lý sự kiện nút thoát
    document
      .getElementById("exitButton")
      .addEventListener("click", function () {
        if (!isSubmitting && !isExitPopupVisible) {
          // Hiển thị popup xác nhận thoát
          showExitConfirmPopup(
            "Xác nhận thoát bài thi",
            "Bạn có chắc chắn muốn thoát? Mọi câu trả lời sẽ bị hủy và không thể hoàn tác."
          );
        }
      });

    // Xử lý sự kiện nút làm lại câu hỏi sai
    document
      .getElementById("retryWrongAnswers")
      .addEventListener("click", function () {
        // Kiểm tra xem có câu hỏi sai nào không
        if (!wrongQuestions || wrongQuestions.length === 0) {
          alert("Bạn đã làm đúng tất cả các câu hỏi!");
          return;
        }

        try {
          // Lưu thông tin câu hỏi sai vào localStorage
          const wrongQuestionsData = {
            examId: examData.id,
            questions: wrongQuestions,
          };

          // Sử dụng localStorage thường (không dùng SecureStorage)
          localStorage.removeItem("wrongQuestions");
          localStorage.setItem(
            "wrongQuestions",
            JSON.stringify(wrongQuestionsData)
          );

          // Kiểm tra dữ liệu đã được lưu đúng cách chưa
          const stored = localStorage.getItem("wrongQuestions");
          const savedData = stored ? JSON.parse(stored) : null;

          if (!savedData) {
            throw new Error("Không thể lưu dữ liệu vào localStorage.");
          }

          if (!savedData.questions || savedData.questions.length === 0) {
            throw new Error("Dữ liệu câu hỏi sai không hợp lệ.");
          }

          // Chuyển hướng đến trang làm lại
          window.location.href = `/exam/retry-wrong-questions?examId=${examData.id}&examType=google-form`;
        } catch (error) {
          console.error("Lỗi khi chuẩn bị làm lại câu hỏi sai:", error);

          // Hiển thị thông báo lỗi chi tiết hơn
          alert(
            `Đã xảy ra lỗi khi chuẩn bị làm lại câu hỏi sai: ${
              error.message
            }\n\nThông tin debug:\n- Số câu hỏi sai: ${
              wrongQuestions ? wrongQuestions.length : "undefined"
            }\n- LocalStorage khả dụng: ${
              typeof localStorage !== "undefined"
            }\n\nVui lòng mở Developer Tools để xem thêm thông tin hoặc liên hệ hỗ trợ.`
          );
        }
      });
  });

  // Hàm để cập nhật danh sách câu hỏi sai - đưa ra khỏi DOMContentLoaded để có thể truy cập ở bất kỳ đâu
  function updateWrongQuestions(questionResults) {
    wrongQuestions = [];

    // Lọc ra các câu hỏi sai (không bao gồm câu chưa trả lời)
    questionResults.forEach((result) => {
      // Chỉ tính câu đã trả lời nhưng trả lời sai
      if (result.selectedOptionIndex !== null && !result.isCorrect) {
        // Thêm câu hỏi vào danh sách sai
        wrongQuestions.push({
          ...result.question,
          userSelectedIndex: result.selectedOptionIndex,
        });
      }
    });

    // Ẩn nút làm lại nếu không có câu hỏi sai nào
    if (wrongQuestions.length === 0) {
      document.getElementById("retryWrongAnswers").style.display = "none";
    } else {
      // Hiển thị nút làm lại nếu có câu hỏi sai
      document.getElementById("retryWrongAnswers").style.display =
        "inline-flex";
    }
  }

  // Hàm để lưu kết quả bài thi vào lịch sử
  function saveExamHistory(score, totalQuestions, duration) {
    // Kiểm tra nếu người dùng đã đăng nhập
    if (!document.querySelector("#profile-button")) {
      return;
    }

    // Chuẩn bị dữ liệu để gửi API
    const historyData = {
      examId: examData.id,
      examName: "<%= exam.name %>",
      score: score,
      totalQuestions: totalQuestions,
      duration: duration, // Thời gian làm bài (giây)
      examType: "google-form",
    };

    // Gửi request API để lưu lịch sử
    fetch("/exam/save-history", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(historyData),
    })
      .then((response) => response.json())
      .then((data) => {
        if (data.success) {
          console.log("Đã lưu lịch sử bài thi");
        } else {
          console.error("Lỗi khi lưu lịch sử");
        }
      })
      .catch((error) => {
        console.error("Lỗi khi gửi request lưu lịch sử:", error);
      });
  }
</script>

<!-- Data configuration for encrypted questions -->
<% if (typeof encryptedQuestionsConfig !== 'undefined' &&
encryptedQuestionsConfig) { %>
<script type="application/json" id="encrypted-config">
  <%- JSON.stringify(encryptedQuestionsConfig) %>
</script>
<% } %>

<!-- Load encryption utilities -->
<script src="/js/client-encryption.js"></script>

<!-- Script xử lý dữ liệu mã hóa -->
<script>
  document.addEventListener("DOMContentLoaded", async function () {
    // Check localStorage FIRST before processing encrypted data

    // Check for existing data early
    const hasLocalStorageData = checkForExistingGoogleFormData();

    if (hasLocalStorageData) {
      return; // Stop here, let user choose continue or new
    }

    // Check if we have encrypted questions to process
    const encryptedConfigElement = document.getElementById("encrypted-config");

    if (encryptedConfigElement) {
      try {
        const encryptedConfig = JSON.parse(encryptedConfigElement.textContent);
        const loadingIndicator = document.getElementById("loadingIndicator");
        const examForm = document.getElementById("exam-form");
        const loadingProgress = document.getElementById("loadingProgress");

        // Progress callback function
        function updateProgress(message, progress) {
          if (loadingProgress) {
            loadingProgress.style.width = progress + "%";
          }
        }

        // Process encrypted configuration
        const questionsData = await loadAppConfiguration(
          encryptedConfig,
          updateProgress
        );

        // Generate questions HTML only if no localStorage data was found
        if (questionsData && Array.isArray(questionsData)) {
          generateQuestionsHTML(questionsData);

          // Update global examData if needed
          if (typeof examData !== "undefined") {
            examData.questions = questionsData;
          }

          // Update question counter
          updateQuestionCounter(questionsData.length);

          // Save initial state to localStorage for new exam
          setTimeout(() => {
            saveGoogleFormDataToStorage();
          }, 1000);
        }

        // Hide loading and show form
        if (loadingIndicator) loadingIndicator.style.display = "none";
        if (examForm) examForm.classList.remove("hidden");

        // Start new exam flow
        startTimer();
        startPeriodicSave();
      } catch (error) {
        console.error("❌ Lỗi khi xử lý cấu hình câu hỏi:", error);

        // Show error message
        const loadingIndicator = document.getElementById("loadingIndicator");
        if (loadingIndicator) {
          loadingIndicator.innerHTML = `
            <div class="p-6 text-center">
              <div class="flex items-center justify-center space-x-3 mb-4">
                <i class="fas fa-exclamation-triangle text-red-500 text-2xl"></i>
                <span class="text-red-700 font-medium">Không thể tải cấu hình ứng dụng</span>
              </div>
              <p class="text-gray-600 mb-4">Vui lòng thử tải lại trang hoặc liên hệ hỗ trợ.</p>
              <button onclick="window.location.reload()" class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600">
                Tải lại trang
              </button>
            </div>
          `;
        }
      }
    } else {
      // For non-encrypted questions

      // Hide loading and show form
      const loadingIndicator = document.getElementById("loadingIndicator");
      const examForm = document.getElementById("exam-form");

      if (loadingIndicator) loadingIndicator.style.display = "none";
      if (examForm) examForm.classList.remove("hidden");

      // Start new exam flow for non-encrypted
      startTimer();
      startPeriodicSave();

      // Save initial state to localStorage
      setTimeout(() => {
        saveGoogleFormDataToStorage();
      }, 1000);
    }
  });

  // Function to generate questions HTML from decrypted data
  function generateQuestionsHTML(questions) {
    const container = document.getElementById("questions-container");
    if (!container) return;

    container.innerHTML = "";

    questions.forEach((question, qIndex) => {
      const questionHTML = createQuestionHTML(question, qIndex);
      container.appendChild(questionHTML);
    });

    // Reinitialize event listeners for new questions
    initializeQuestionEventListeners();
  }

  // Function to create individual question HTML
  function createQuestionHTML(question, qIndex) {
    const questionDiv = document.createElement("div");
    questionDiv.className =
      "question-container bg-white rounded-lg border border-gray-200 p-3 sm:p-4 shadow-sm";

    let questionContent = `
      <div class="mb-3">
        <h3 class="text-base sm:text-lg font-medium text-gray-800">
          Câu ${qIndex + 1}: ${question.text || ""}
        </h3>
    `;

    if (question.image) {
      questionContent += `
        <div class="mt-2">
          <img src="${question.image}" alt="Hình minh họa câu hỏi" class="max-w-full h-auto rounded" />
        </div>
      `;
    }

    questionContent += `</div><div class="grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-4">`;

    // Handle different question types
    if (!question.type || question.type === "multiple-choice") {
      const answerOptions = question.answers || question.options || [];

      if (answerOptions && answerOptions.length > 0) {
        answerOptions.forEach((option, oIndex) => {
          const optionLetter = String.fromCharCode(65 + oIndex);
          const optionText = typeof option === "object" ? option.text : option;

          // Loại bỏ tiền tố "A.", "B.", "C.", "D." nếu có
          let cleanOptionText = optionText;
          const prefixRegex = /^\s*(?:[A-D]|[1-9])[\.\)][\s\u00A0]*/i;
          if (prefixRegex.test(cleanOptionText)) {
            cleanOptionText = cleanOptionText.replace(prefixRegex, "");
          }

          questionContent += `
            <div class="relative">
              <input type="radio" id="q${qIndex}_o${oIndex}" name="answer_${question._id}" value="${oIndex}" 
                     class="absolute opacity-0 w-full h-full cursor-pointer z-10" />
              <label for="q${qIndex}_o${oIndex}" 
                     class="flex items-center w-full p-3 sm:p-4 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors text-gray-700">
                <span class="inline-flex items-center justify-center h-6 w-6 rounded-full bg-gray-100 text-gray-700 font-medium mr-2 flex-shrink-0">
                  ${optionLetter}
                </span>
                <span>${cleanOptionText}</span>
              </label>
            </div>
          `;
        });
      } else {
        questionContent += `<div class="text-red-500 italic col-span-2">Không có phương án trả lời cho câu hỏi này.</div>`;
      }
    } else if (question.type === "text") {
      questionContent += `
        <div class="col-span-2">
          <textarea name="answer_${question._id}" rows="3" 
                    class="shadow-sm focus:ring-red-500 focus:border-red-500 block w-full sm:text-sm border-gray-300 rounded-md"
                    placeholder="Nhập câu trả lời của bạn..."></textarea>
        </div>
      `;
    }

    questionContent += `</div>`;
    questionDiv.innerHTML = questionContent;

    return questionDiv;
  }

  // Function to reinitialize event listeners for new questions
  function initializeQuestionEventListeners() {
    // Use the same logic as setupAnswerChangeListeners for encrypted questions
    setupAnswerChangeListeners();

    // Also handle textareas if any
    const textareas = document.querySelectorAll("textarea");
    textareas.forEach((textarea) => {
      textarea.addEventListener("input", function () {
        const questionId = this.name.replace("answer_", "");
        const textValue = this.value.trim();

        if (textValue) {
          // Store user answer for text questions
          userAnswers[questionId] = textValue;

          // Save to localStorage
          saveGoogleFormDataToStorage();

          // Update progress
          updateQuestionProgress();
        } else {
          // Remove answer if empty
          delete userAnswers[questionId];
          saveGoogleFormDataToStorage();
          updateQuestionProgress();
        }
      });
    });
  }

  // Function to update question counter
  function updateQuestionCounter(totalQuestionsCount) {
    const counter = document.getElementById("questionCounter");
    if (counter) {
      counter.textContent = `Câu hỏi 1/${totalQuestionsCount}`;
    }

    // Update global totalQuestions if defined
    if (typeof totalQuestions !== "undefined") {
      totalQuestions = totalQuestionsCount;
    }
  }
</script>

<!-- Script bảo mật toàn cục -->
<script src="/js/security-measures.js"></script>
<script>
  // Thiết lập bảo mật ngay khi DOM sẵn sàng
  document.addEventListener("DOMContentLoaded", function () {
    // Tự động phát hiện ngữ cảnh và thiết lập bảo mật với chuyển hướng khi phát hiện DevTools
    GlobalSecurityMeasures({
      contextMessage: "làm bài Google Form",
      redirectOnDevTools: true, // Bật chuyển hướng khi phát hiện DevTools
      redirectUrl: "/home", // Chuyển về trang chủ
      devToolsThreshold: 200, // Tăng ngưỡng để giảm false positive
    });
  });
</script>
