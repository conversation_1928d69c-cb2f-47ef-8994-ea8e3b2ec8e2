import Session, { ISession } from "../models/Session";
import User, { IUser } from "../models/User";
import jwt from "jsonwebtoken";
import * as sseService from "./sseService";
import { Request } from "express";
import UAParser from "ua-parser-js";

// X<PERSON> lý thông tin thiết bị từ User-Agent
export const parseDeviceInfo = (req: Request) => {
  const parser = new UAParser(req.headers["user-agent"]);
  const browser = parser.getBrowser();
  const os = parser.getOS();
  const device = parser.getDevice();

  return {
    userAgent: req.headers["user-agent"] || "unknown",
    ip: req.ip || req.socket.remoteAddress || "unknown",
    browser: browser.name ? `${browser.name} ${browser.version}` : "unknown",
    os: os.name ? `${os.name} ${os.version}` : "unknown",
    deviceName: device.model || device.vendor || "unknown device",
  };
};

// Tạo JWT token
export const generateToken = (user: IUser): string => {
  const secret = process.env.JWT_SECRET || "default_secret";
  const expiration = "90d";

  return jwt.sign({ id: user._id, email: user.email }, secret, {
    expiresIn: expiration,
  });
};

// Tạo phiên đăng nhập mới
export const createSession = async (
  user: IUser,
  clientId: string,
  req: Request
): Promise<{ token: string; session: ISession }> => {
  // Tạo JWT token
  const token = generateToken(user);

  // Xử lý thông tin thiết bị
  const deviceInfo = parseDeviceInfo(req);

  // Tạo phiên đăng nhập
  const session = await Session.createSession(
    user._id,
    token,
    clientId,
    deviceInfo
  );

  // console.log(
  //   `Created new session for user ${user._id}, device: ${deviceInfo.deviceName}`
  // );

  // Cập nhật token vào User model cho tương thích ngược
  await User.findByIdAndUpdate(user._id, {
    activeToken: token,
    lastActiveDevice: clientId,
  });

  // Đảm bảo cập nhật activeDevices trong SSE service
  await sseService.setCurrentDeviceAsActive(user._id.toString(), clientId);

  return { token, session };
};

// Đăng xuất các thiết bị khác
export const logoutOtherDevices = async (
  userId: string,
  currentClientId: string
): Promise<void> => {
  // Tìm tất cả các phiên khác
  const otherSessions = await Session.find({
    userId,
    clientId: { $ne: currentClientId },
  });

  // Xóa từng phiên
  for (const session of otherSessions) {
    await Session.deleteOne({ _id: session._id });
  }

  // Thông báo cho các thiết bị khác
  await sseService.logoutOtherDevices(userId, currentClientId);

  // console.log(`Đã xóa các phiên đăng nhập khác cho user ${userId}`);
};

// Tìm phiên hợp lệ theo token
export const findValidSession = async (
  token: string
): Promise<ISession | null> => {
  return Session.findValidSessionByToken(token);
};

// Đăng xuất từ một phiên cụ thể
export const logoutSession = async (token: string): Promise<void> => {
  // Tìm phiên
  const session = await Session.findOne({ token });

  if (session) {
    // Xóa hoàn toàn phiên thay vì chỉ đánh dấu không hoạt động
    await Session.deleteOne({ _id: session._id });

    // Nếu là thiết bị hiện tại, cập nhật User model
    if (session.isCurrentDevice) {
      await User.findByIdAndUpdate(session.userId, {
        activeToken: null,
        lastActiveDevice: null,
      });
    }

    // console.log(
    //   `Đã xóa hoàn toàn phiên đăng nhập cho user ${session.userId}, thiết bị: ${
    //     session.deviceInfo.deviceName || "unknown"
    //   }`
    // );
  }
};

// Đăng xuất hoàn toàn một phiên theo ID
export const deleteSessionById = async (
  sessionId: string
): Promise<boolean> => {
  try {
    const session = await Session.findById(sessionId);

    if (!session) {
      return false;
    }

    // Xóa hoàn toàn phiên
    await Session.deleteOne({ _id: sessionId });

    // console.log(
    //   `Đã xóa hoàn toàn phiên đăng nhập ID ${sessionId} cho user ${session.userId}`
    // );

    return true;
  } catch (error) {
    // console.error(`Lỗi khi xóa phiên đăng nhập: ${error}`);
    return false;
  }
};

// Liệt kê tất cả các phiên của một người dùng
export const getUserSessions = async (userId: string): Promise<ISession[]> => {
  return Session.find({ userId, isActive: true }).sort({ createdAt: -1 });
};

// Cập nhật thời gian hoạt động cuối cùng
export const updateLastActiveTime = async (token: string): Promise<void> => {
  await Session.updateOne({ token }, { $set: { lastActive: new Date() } });
};

// Lập lịch dọn dẹp các phiên hết hạn
export const scheduleSessionCleanup = (): NodeJS.Timeout => {
  // Dọn dẹp các phiên hết hạn hàng ngày
  const interval = setInterval(async () => {
    try {
      const result = await Session.removeExpiredSessions();
      // console.log(`Cleaned up ${result.deletedCount} expired sessions`);
    } catch (error) {
      // console.error("Error cleaning up expired sessions:", error);
    }
  }, 24 * 60 * 60 * 1000); // Mỗi 24 giờ

  return interval;
};

export default {
  createSession,
  logoutOtherDevices,
  findValidSession,
  logoutSession,
  getUserSessions,
  updateLastActiveTime,
  scheduleSessionCleanup,
  parseDeviceInfo,
  generateToken,
  deleteSessionById,
};
