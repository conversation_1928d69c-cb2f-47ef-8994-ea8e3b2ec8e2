import mongoose from "mongoose";
import dotenv from "dotenv";
dotenv.config();

// Chỉ sử dụng một kết nối duy nhất cho cả ứng dụng
mongoose
  // .connect("******************************************************")
  .connect("mongodb://localhost:27017/google-auth")
  .then(() => console.log("Kết nối thành công đến cơ sở dữ liệu!"))
  .catch((err) => console.error("Lỗi kết nối đến cơ sở dữ liệu:", err));
// .connect(process.env.MONGODB_URI as string)

// Export connection để sử dụng nếu cần thiết
export default mongoose.connection;
