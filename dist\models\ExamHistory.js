"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importStar(require("mongoose"));
const userProgress_service_1 = require("../services/userProgress.service");
// Schema cho ExamHistory
const ExamHistorySchema = new mongoose_1.Schema({
    examId: { type: mongoose_1.Schema.Types.ObjectId, ref: "Exam", required: true },
    examName: { type: String, required: true },
    userId: { type: mongoose_1.Schema.Types.ObjectId, ref: "User", required: true },
    score: { type: Number, required: true },
    totalQuestions: { type: Number, required: true },
    duration: { type: Number, required: true }, // Thời gian làm bài (giây)
    examType: {
        type: String,
        enum: ["quizizz", "google-form"],
        required: true,
    },
    completedAt: { type: Date, default: Date.now },
}, { timestamps: true });
// Middleware sau khi lưu ExamHistory để cập nhật tiến độ học tập
ExamHistorySchema.post("save", function (doc) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            // Kiểm tra xem có bản ghi trùng lặp không
            const completedTime = doc.completedAt;
            const duplicateCount = yield mongoose_1.default.model("ExamHistory").countDocuments({
                examId: doc.examId,
                userId: doc.userId,
                _id: { $ne: doc._id }, // Không tính bản ghi hiện tại
                completedAt: {
                    // Trong khoảng 10 giây
                    $gte: new Date(completedTime.getTime() - 10000),
                    $lte: new Date(completedTime.getTime() + 10000),
                },
            });
            if (duplicateCount > 0) {
                // Tìm bản ghi có điểm cao nhất trong khoảng thời gian này
                const bestRecord = yield mongoose_1.default
                    .model("ExamHistory")
                    .findOne({
                    examId: doc.examId,
                    userId: doc.userId,
                    completedAt: {
                        $gte: new Date(completedTime.getTime() - 10000),
                        $lte: new Date(completedTime.getTime() + 10000),
                    },
                })
                    .sort({ score: -1 })
                    .limit(1);
                // Nếu bản ghi hiện tại không phải là bản ghi có điểm cao nhất, bỏ qua việc cập nhật tiến độ
                if (bestRecord && bestRecord._id.toString() !== doc._id.toString()) {
                    return;
                }
            }
            // Tiếp tục cập nhật tiến độ học tập
            yield (0, userProgress_service_1.updateUserProgress)(doc);
        }
        catch (error) {
            console.error("Lỗi cập nhật tiến độ học tập:", error);
        }
    });
});
// Model cho ExamHistory
exports.default = mongoose_1.default.model("ExamHistory", ExamHistorySchema);
//# sourceMappingURL=ExamHistory.js.map