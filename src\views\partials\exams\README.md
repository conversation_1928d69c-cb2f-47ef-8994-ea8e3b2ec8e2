# Cấu trúc Partials cho Exams

Thư mục này chứa các partial files được tách ra từ file `src/views/courses/exams.ejs` để dễ dàng quản lý và bảo trì code.

## Cấu trúc Files

```
src/views/partials/exams/
├── exam-list.ejs      # <PERSON><PERSON><PERSON> "Danh sách đề thi"
├── practice-exam.ejs  # <PERSON>ần "Thi thử"
├── outline.ejs        # Phần "Đề cương"
├── modals.ejs         # Các modal dialogs
├── scripts.ejs        # JavaScript functions và scripts
└── README.md          # File này
```

## Chi tiết từng file

### 1. `exam-list.ejs`

- **<PERSON><PERSON><PERSON> đích**: Hiển thị danh sách các đề thi của môn học
- **Chức năng**:
  - Hiển thị bảng danh sách đề thi
  - Trạng thái đề thi (active/inactive)
  - <PERSON><PERSON> làm bài thi
- **Dependencies**: <PERSON><PERSON>n `exams` array từ controller

### 2. `practice-exam.ejs`

- **<PERSON><PERSON><PERSON> đích**: <PERSON><PERSON><PERSON>n cho phần thi thử
- **Chức năng**:
  - Thông tin thi thử (số câu hỏi, thời gian)
  - Nút bắt đầu thi thử
  - Lịch sử thi thử
- **Dependencies**: Cần `questionsByExam` từ controller

### 3. `outline.ejs`

- **Mục đích**: Hiển thị đề cương môn học
- **Chức năng**:
  - Tìm kiếm câu hỏi
  - Lọc theo đề thi
  - Hiển thị/ẩn đáp án
  - Infinite scrolling
- **Dependencies**: Cần `questionsByExam` hoặc `encryptedQuestions`

### 4. `modals.ejs`

- **Mục đích**: Chứa tất cả modal dialogs
- **Chức năng**:
  - Modal chọn hình thức làm bài (Google Form/Quizizz)
  - Modal tiếp tục/làm mới bài thi thử
  - Modal chi tiết lịch sử thi thử

### 5. `scripts.ejs`

- **Mục đích**: Tất cả JavaScript functions và event handlers
- **Chức năng**:
  - Functions cho exam modals
  - Functions cho practice exam
  - Practice history management
  - Security measures
  - API calls

## Sử dụng trong file chính

File `src/views/courses/exams.ejs` hiện tại sử dụng các partials như sau:

```ejs
<!-- Tab Content -->
<% if (currentTab === 'exams') { %>
  <%- include('../partials/exams/exam-list') %>
<% } else if (currentTab === 'practice') { %>
  <%- include('../partials/exams/practice-exam') %>
<% } else if (currentTab === 'outline') { %>
  <%- include('../partials/exams/outline') %>
<% } %>

<!-- Include modals -->
<%- include('../partials/exams/modals') %>

<!-- Include scripts -->
<%- include('../partials/exams/scripts') %>
```

## Lợi ích của cấu trúc mới

### 1. **Dễ bảo trì**

- Mỗi phần có file riêng, dễ tìm và chỉnh sửa
- Giảm conflict khi nhiều developer làm việc cùng lúc

### 2. **Tái sử dụng**

- Các partial có thể được sử dụng ở nhiều nơi khác
- Modals có thể được include vào các trang khác

### 3. **Tổ chức rõ ràng**

- Logic được phân chia theo chức năng
- JavaScript được tập trung trong một file

### 4. **Performance**

- Có thể lazy load từng phần nếu cần
- Dễ dàng optimize từng component riêng biệt

## Variables cần thiết

Các partials này cần các variables sau từ controller:

### Tất cả partials:

- `product` - Thông tin sản phẩm/môn học
- `student` - Thông tin sinh viên
- `currentTab` - Tab hiện tại đang được chọn

### Cho exam-list.ejs:

- `exams` - Array danh sách đề thi

### Cho practice-exam.ejs và outline.ejs:

- `questionsByExam` - Array câu hỏi theo đề thi
- `encryptedQuestions` (optional) - Câu hỏi đã mã hóa
- `encryptionKey` (optional) - Key giải mã
- `encryptionIV` (optional) - IV giải mã

## Thêm chức năng mới

Khi cần thêm chức năng mới:

1. **HTML/EJS**: Thêm vào partial tương ứng
2. **JavaScript**: Thêm function vào `scripts.ejs`
3. **Modal**: Thêm vào `modals.ejs`
4. **Styles**: Thêm vào phần `<style>` trong file chính

## Notes

- Tất cả các partial đều sử dụng Tailwind CSS classes
- JavaScript functions được định nghĩa trong global scope
- Security measures được áp dụng dựa vào `currentTab`
- Responsive design được implement cho mobile/tablet
