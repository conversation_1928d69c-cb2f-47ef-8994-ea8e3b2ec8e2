{"version": 3, "file": "userProgress.service.js", "sourceRoot": "", "sources": ["../../src/services/userProgress.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;AASA,gDA+IC;AAKD,wDA+FC;AAKD,gDAgCC;AAKD,8CAoDC;AAKD,0DA8EC;AAKD,wDAoBC;AAtcD,uCAAiC;AACjC,0EAAqE;AACrE,kEAAyC;AACzC,0DAAkC;AAClC,wEAAgD;AAEhD;;GAEG;AACH,SAAsB,kBAAkB,CACtC,WAAgB;;QAEhB,IAAI,CAAC;YACH,uBAAuB;YACvB,MAAM,IAAI,GAAG,MAAM,cAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;YACrD,IAAI,CAAC,IAAI;gBAAE,OAAO,IAAI,CAAC;YAEvB,mCAAmC;YACnC,MAAM,OAAO,GAAG,MAAM,kBAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACvD,IAAI,CAAC,OAAO;gBAAE,OAAO,IAAI,CAAC;YAE1B,sCAAsC;YACtC,IAAI,YAAY,GAAG,MAAM,sBAAY,CAAC,OAAO,CAAC;gBAC5C,OAAO,EAAE,WAAW,CAAC,MAAM;gBAC3B,UAAU,EAAE,IAAI,CAAC,SAAS;aAC3B,CAAC,CAAC;YAEH,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,YAAY,GAAG,IAAI,sBAAY,CAAC;oBAC9B,OAAO,EAAE,WAAW,CAAC,MAAM;oBAC3B,UAAU,EAAE,IAAI,CAAC,SAAS;oBAC1B,kBAAkB,EAAE,EAAE;oBACtB,qBAAqB,EAAE,CAAC;oBACxB,eAAe,EAAE,OAAO,CAAC,aAAa;oBACtC,mBAAmB,EAAE,CAAC;oBACtB,YAAY,EAAE,IAAI,IAAI,EAAE;iBACzB,CAAC,CAAC;YACL,CAAC;YAED,8BAA8B;YAC9B,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAC/B,CAAC,WAAW,CAAC,KAAK,GAAG,GAAG,CAAC,GAAG,WAAW,CAAC,cAAc,CACvD,CAAC;YAEF,wCAAwC;YACxC,MAAM,gBAAgB,GAAG,YAAY,CAAC,kBAAkB,CAAC,MAAM,CAC7D,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,EAAE,KAAK,WAAW,CAAC,MAAM,CAAC,QAAQ,EAAE,CAC1E,CAAC;YAEF,sEAAsE;YACtE,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAChC,OAAO,CAAC,GAAG,CACT,aAAa,gBAAgB,CAAC,MAAM,iCAAiC,WAAW,CAAC,MAAM,EAAE,CAC1F,CAAC;gBAEF,+BAA+B;gBAC/B,MAAM,WAAW,GAAG,gBAAgB,CAAC,MAAM,CACzC,CAAC,IAAI,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,EAChE,gBAAgB,CAAC,CAAC,CAAC,CACpB,CAAC;gBAEF,oCAAoC;gBACpC,YAAY,CAAC,kBAAkB,GAAG,YAAY,CAAC,kBAAkB,CAAC,MAAM,CACtE,CAAC,OAAO,EAAE,EAAE,CACV,OAAO,CAAC,OAAO,CAAC,QAAQ,EAAE,KAAK,WAAW,CAAC,MAAM,CAAC,QAAQ,EAAE,CAC/D,CAAC;gBAEF,4BAA4B;gBAC5B,YAAY,CAAC,kBAAkB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBAElD,4BAA4B;gBAC5B,YAAY,CAAC,qBAAqB;oBAChC,YAAY,CAAC,kBAAkB,CAAC,MAAM,CACpC,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE,CAAC,GAAG,GAAG,OAAO,CAAC,eAAe,EAC/C,CAAC,CACF,CAAC;gBAEJ,OAAO,CAAC,GAAG,CACT,qEAAqE,WAAW,CAAC,KAAK,EAAE,CACzF,CAAC;YACJ,CAAC;YAED,+DAA+D;YAC/D,MAAM,SAAS,GAAG,YAAY,CAAC,kBAAkB,CAAC,SAAS,CACzD,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,EAAE,KAAK,WAAW,CAAC,MAAM,CAAC,QAAQ,EAAE,CAC1E,CAAC;YAEF,uCAAuC;YACvC,IAAI,YAAY,GAAG,KAAK,CAAC;YAEzB,IAAI,SAAS,IAAI,CAAC,EAAE,CAAC;gBACnB,2DAA2D;gBAC3D,MAAM,cAAc,GAAG,YAAY,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;gBAClE,IAAI,WAAW,CAAC,KAAK,GAAG,cAAc,CAAC,KAAK,EAAE,CAAC;oBAC7C,OAAO,CAAC,GAAG,CACT,0BAA0B,WAAW,CAAC,KAAK,MAAM,cAAc,CAAC,KAAK,EAAE,CACxE,CAAC;oBAEF,mCAAmC;oBACnC,YAAY,CAAC,kBAAkB,CAAC,SAAS,CAAC,GAAG;wBAC3C,OAAO,EAAE,WAAW,CAAC,MAAM;wBAC3B,KAAK,EAAE,WAAW,CAAC,KAAK;wBACxB,eAAe,EAAE,cAAc;wBAC/B,eAAe,EAAE,WAAW,CAAC,cAAc;wBAC3C,YAAY,EAAE,WAAW,CAAC,WAAW;qBACtC,CAAC;oBAEF,YAAY,GAAG,IAAI,CAAC;gBACtB,CAAC;qBAAM,CAAC;gBACR,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,wCAAwC;gBACxC,YAAY,CAAC,kBAAkB,CAAC,IAAI,CAAC;oBACnC,OAAO,EAAE,WAAW,CAAC,MAAM;oBAC3B,KAAK,EAAE,WAAW,CAAC,KAAK;oBACxB,eAAe,EAAE,cAAc;oBAC/B,eAAe,EAAE,WAAW,CAAC,cAAc;oBAC3C,YAAY,EAAE,WAAW,CAAC,WAAW;iBACtC,CAAC,CAAC;gBAEH,YAAY,GAAG,IAAI,CAAC;YACtB,CAAC;YAED,uCAAuC;YACvC,IAAI,YAAY,EAAE,CAAC;gBACjB,2DAA2D;gBAC3D,YAAY,CAAC,qBAAqB;oBAChC,YAAY,CAAC,kBAAkB,CAAC,MAAM,CACpC,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE,CAAC,GAAG,GAAG,OAAO,CAAC,eAAe,EAC/C,CAAC,CACF,CAAC;gBAEJ,OAAO,CAAC,GAAG,CACT,iCAAiC,YAAY,CAAC,qBAAqB,IAAI,YAAY,CAAC,eAAe,EAAE,CACtG,CAAC;gBAEF,6BAA6B;gBAC7B,YAAY,CAAC,mBAAmB,GAAG,IAAI,CAAC,KAAK,CAC3C,CAAC,YAAY,CAAC,qBAAqB,GAAG,YAAY,CAAC,eAAe,CAAC;oBACjE,GAAG,CACN,CAAC;gBACF,YAAY,CAAC,YAAY,GAAG,IAAI,IAAI,EAAE,CAAC;gBAEvC,eAAe;gBACf,MAAM,YAAY,CAAC,IAAI,EAAE,CAAC;YAC5B,CAAC;YAED,OAAO,YAAY,CAAC;QACtB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;YAC9C,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;CAAA;AAED;;GAEG;AACH,SAAsB,sBAAsB,CAC1C,MAAc,EACd,SAAiB;;QAEjB,IAAI,CAAC;YACH,mDAAmD;YACnD,MAAM,OAAO,GAAG,MAAM,sBAAY,CAAC,SAAS,CAAC;gBAC3C;oBACE,MAAM,EAAE;wBACN,OAAO,EAAE,IAAI,gBAAK,CAAC,QAAQ,CAAC,MAAM,CAAC;wBACnC,UAAU,EAAE,IAAI,gBAAK,CAAC,QAAQ,CAAC,SAAS,CAAC;qBAC1C;iBACF;gBACD;oBACE,OAAO,EAAE;wBACP,IAAI,EAAE,UAAU;wBAChB,UAAU,EAAE,YAAY;wBACxB,YAAY,EAAE,KAAK;wBACnB,EAAE,EAAE,aAAa;qBAClB;iBACF;gBACD,EAAE,OAAO,EAAE,EAAE,IAAI,EAAE,cAAc,EAAE,0BAA0B,EAAE,IAAI,EAAE,EAAE;gBACvE;oBACE,UAAU,EAAE;wBACV,QAAQ,EAAE;4BACR,IAAI,EAAE;gCACJ,KAAK,EAAE,qBAAqB;gCAC5B,EAAE,EAAE,SAAS;gCACb,EAAE,EAAE,mBAAmB;6BACxB;yBACF;qBACF;iBACF;gBACD;oBACE,OAAO,EAAE;wBACP,IAAI,EAAE,OAAO;wBACb,GAAG,EAAE,EAAE,QAAQ,EAAE,WAAW,EAAE;wBAC9B,QAAQ,EAAE;4BACR,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,EAAE,GAAG,EAAE,CAAC,MAAM,EAAE,YAAY,CAAC,EAAE,EAAE,EAAE;4BACtD,EAAE,QAAQ,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE;yBAClC;wBACD,EAAE,EAAE,UAAU;qBACf;iBACF;gBACD;oBACE,QAAQ,EAAE;wBACR,GAAG,EAAE,CAAC;wBACN,UAAU,EAAE,CAAC;wBACb,YAAY,EAAE,mBAAmB;wBACjC,qBAAqB,EAAE,CAAC;wBACxB,eAAe,EAAE,CAAC;wBAClB,mBAAmB,EAAE,CAAC;wBACtB,YAAY,EAAE,CAAC;wBACf,kBAAkB,EAAE,CAAC;wBACrB,QAAQ,EAAE,CAAC;qBACZ;iBACF;aACF,CAAC,CAAC;YAEH,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACzB,MAAM,OAAO,GAAG,MAAM,kBAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;gBAClD,OAAO;oBACL,UAAU,EAAE,SAAS;oBACrB,YAAY,EAAE,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,KAAI,SAAS;oBACxC,qBAAqB,EAAE,CAAC;oBACxB,eAAe,EAAE,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,aAAa,KAAI,CAAC;oBAC5C,mBAAmB,EAAE,CAAC;oBACtB,kBAAkB,EAAE,EAAE;iBACvB,CAAC;YACJ,CAAC;YAED,uCAAuC;YACvC,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YAC1B,MAAM,OAAO,GAA8B,EAAE,CAAC;YAE9C,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,IAAS,EAAE,EAAE;gBACpC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;YAC3C,CAAC,CAAC,CAAC;YAEH,8BAA8B;YAC9B,MAAM,CAAC,kBAAkB,GAAG,MAAM,CAAC,kBAAkB,CAAC,GAAG,CACvD,CAAC,OAAY,EAAE,EAAE,CAAC,iCACb,OAAO,KACV,SAAS,EAAE,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,IAAI,SAAS,IAC3D,CACH,CAAC;YAEF,OAAO,MAAM,CAAC,QAAQ,CAAC;YACvB,OAAO,MAAM,CAAC,QAAQ,CAAC;YAEvB,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;YAC7C,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;CAAA;AAED;;GAEG;AACH,SAAsB,kBAAkB,CAAC,MAAc;;QACrD,IAAI,CAAC;YACH,mDAAmD;YACnD,OAAO,MAAM,sBAAY,CAAC,SAAS,CAAC;gBAClC,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,IAAI,gBAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,EAAE;gBACnD;oBACE,OAAO,EAAE;wBACP,IAAI,EAAE,UAAU;wBAChB,UAAU,EAAE,YAAY;wBACxB,YAAY,EAAE,KAAK;wBACnB,EAAE,EAAE,aAAa;qBAClB;iBACF;gBACD,EAAE,OAAO,EAAE,cAAc,EAAE;gBAC3B;oBACE,QAAQ,EAAE;wBACR,UAAU,EAAE,CAAC;wBACb,YAAY,EAAE,mBAAmB;wBACjC,aAAa,EAAE,oBAAoB;wBACnC,qBAAqB,EAAE,CAAC;wBACxB,eAAe,EAAE,CAAC;wBAClB,mBAAmB,EAAE,CAAC;wBACtB,eAAe,EAAE,EAAE,KAAK,EAAE,qBAAqB,EAAE;wBACjD,YAAY,EAAE,CAAC;qBAChB;iBACF;gBACD,EAAE,KAAK,EAAE,EAAE,mBAAmB,EAAE,CAAC,CAAC,EAAE,EAAE;aACvC,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACpD,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;CAAA;AAED;;GAEG;AACH,SAA8B,2BAA2B,CACvD,MAAc,EACd,UAAoB;;QAEpB,IAAI,CAAC;YACH,yCAAyC;YACzC,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,IAAI,gBAAK,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;YAEjE,oEAAoE;YACpE,MAAM,gBAAgB,GAAG,MAAM,sBAAY,CAAC,IAAI,CAAC;gBAC/C,OAAO,EAAE,IAAI,gBAAK,CAAC,QAAQ,CAAC,MAAM,CAAC;aACpC,CAAC,CAAC,IAAI,EAAE,CAAC;YAEV,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAChC,OAAO,gBAAgB,CAAC;YAC1B,CAAC;YAED,mDAAmD;YACnD,MAAM,OAAO,GAAG,MAAM,sBAAY,CAAC,SAAS,CAAC;gBAC3C;oBACE,MAAM,EAAE;wBACN,OAAO,EAAE,IAAI,gBAAK,CAAC,QAAQ,CAAC,MAAM,CAAC;wBACnC,UAAU,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE;qBAC/B;iBACF;gBACD;oBACE,OAAO,EAAE;wBACP,IAAI,EAAE,UAAU;wBAChB,UAAU,EAAE,YAAY;wBACxB,YAAY,EAAE,KAAK;wBACnB,EAAE,EAAE,aAAa;qBAClB;iBACF;gBACD,EAAE,OAAO,EAAE,cAAc,EAAE;gBAC3B;oBACE,QAAQ,EAAE;wBACR,UAAU,EAAE,CAAC;wBACb,YAAY,EAAE,mBAAmB;wBACjC,aAAa,EAAE,oBAAoB;wBACnC,qBAAqB,EAAE,CAAC;wBACxB,eAAe,EAAE,CAAC;wBAClB,mBAAmB,EAAE,CAAC;wBACtB,eAAe,EAAE,EAAE,KAAK,EAAE,qBAAqB,EAAE;wBACjD,YAAY,EAAE,CAAC;qBAChB;iBACF;aACF,CAAC,CAAC;YAEH,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;CAAA;AAED;;GAEG;AACH,SAAsB,uBAAuB,CAAC,MAAc;;QAC1D,IAAI,CAAC;YACH,gCAAgC;YAChC,MAAM,QAAQ,GAAG,MAAM,kBAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAExC,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;gBAC/B,gCAAgC;gBAChC,MAAM,KAAK,GAAG,MAAM,cAAI,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;gBAE1D,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC;oBAAE,SAAS;gBAEjC,iFAAiF;gBACjF,MAAM,aAAa,GAAG,MAAM,qBAAW,CAAC,SAAS,CAAC;oBAChD;wBACE,MAAM,EAAE;4BACN,MAAM,EAAE,IAAI,gBAAK,CAAC,QAAQ,CAAC,MAAM,CAAC;4BAClC,MAAM,EAAE,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;yBAC/C;qBACF;oBACD;wBACE,KAAK,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE;qBACrB;oBACD;wBACE,MAAM,EAAE;4BACN,GAAG,EAAE,SAAS;4BACd,KAAK,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE;4BAC3B,cAAc,EAAE,EAAE,MAAM,EAAE,iBAAiB,EAAE;4BAC7C,WAAW,EAAE,EAAE,MAAM,EAAE,cAAc,EAAE;yBACxC;qBACF;iBACF,CAAC,CAAC;gBAEH,uDAAuD;gBACvD,IAAI,mBAAmB,GAAG,CAAC,CAAC;gBAC5B,MAAM,YAAY,GAAG,EAAE,CAAC;gBAExB,KAAK,MAAM,OAAO,IAAI,aAAa,EAAE,CAAC;oBACpC,mBAAmB;oBACnB,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAC/B,CAAC,OAAO,CAAC,KAAK,GAAG,GAAG,CAAC,GAAG,OAAO,CAAC,cAAc,CAC/C,CAAC;oBACF,mBAAmB,IAAI,cAAc,CAAC;oBAEtC,YAAY,CAAC,IAAI,CAAC;wBAChB,OAAO,EAAE,OAAO,CAAC,GAAG;wBACpB,KAAK,EAAE,OAAO,CAAC,KAAK;wBACpB,eAAe,EAAE,cAAc;wBAC/B,eAAe,EAAE,OAAO,CAAC,cAAc;wBACvC,YAAY,EAAE,OAAO,CAAC,WAAW;qBAClC,CAAC,CAAC;gBACL,CAAC;gBAED,yBAAyB;gBACzB,MAAM,kBAAkB,GAAG,IAAI,CAAC,KAAK,CACnC,CAAC,mBAAmB,GAAG,OAAO,CAAC,aAAa,CAAC,GAAG,GAAG,CACpD,CAAC;gBAEF,gCAAgC;gBAChC,MAAM,sBAAY,CAAC,gBAAgB,CACjC,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,CAAC,GAAG,EAAE,EAC5C;oBACE,OAAO,EAAE,MAAM;oBACf,UAAU,EAAE,OAAO,CAAC,GAAG;oBACvB,kBAAkB,EAAE,YAAY;oBAChC,qBAAqB,EAAE,mBAAmB;oBAC1C,eAAe,EAAE,OAAO,CAAC,aAAa;oBACtC,mBAAmB,EAAE,kBAAkB;oBACvC,YAAY,EAAE,IAAI,IAAI,EAAE;iBACzB,EACD,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,CAC5B,CAAC;YACJ,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YACtD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;CAAA;AAED;;GAEG;AACH,SAAsB,sBAAsB;;QAC1C,IAAI,CAAC;YACH,6DAA6D;YAC7D,MAAM,IAAI,GAAG,OAAO,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC;YAC/C,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAElC,oCAAoC;YACpC,MAAM,SAAS,GAAG,EAAE,CAAC;YACrB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,IAAI,SAAS,EAAE,CAAC;gBACjD,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC;gBAC5C,MAAM,OAAO,CAAC,GAAG,CACf,KAAK,CAAC,GAAG,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,uBAAuB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAC5D,CAAC;YACJ,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;YAC7D,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;CAAA"}