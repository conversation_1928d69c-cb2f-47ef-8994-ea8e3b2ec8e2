// Variables để lưu trữ trạng thái
let selectedExamType = null;
let currentExamId = null;

// Hàm mở modal chọn hình thức làm bài thi
function openExamModal(examId, examName) {
  currentExamId = examId;
  document.getElementById("examId").value = examId;
  document.getElementById(
    "modalTitle"
  ).textContent = `Chọn hình thức làm bài: ${examName}`;
  document.getElementById("examModal").classList.remove("hidden");
  selectedExamType = null;
  document.getElementById("startExamBtn").disabled = true;

  // Reset selection
  document.querySelectorAll(".option-card").forEach((card) => {
    card.classList.remove("border-indigo-500", "bg-indigo-50");
  });
}

// Hàm đóng modal
function closeExamModal() {
  document.getElementById("examModal").classList.add("hidden");
}

// Hàm chọn loại bài thi
function selectExamType(type) {
  selectedExamType = type;
  document.getElementById("startExamBtn").disabled = false;

  // Reset all cards
  document.querySelectorAll(".option-card").forEach((card) => {
    card.classList.remove("border-indigo-500", "bg-indigo-50");
  });

  // Highlight selected card
  const selectedCard = document.querySelector(
    `.option-card:nth-child(${type === "google-form" ? "1" : "2"})`
  );
  selectedCard.classList.add("border-indigo-500", "bg-indigo-50");
}

// Hàm bắt đầu làm bài thi
function startExam() {
  if (!selectedExamType || !currentExamId) return;

  // Lấy trạng thái của các checkbox
  const shuffleQuestions = document.getElementById("shuffleQuestions").checked;
  const shuffleAnswers = document.getElementById("shuffleAnswers").checked;

  // Tạo query parameters cho các tùy chọn
  const queryParams = new URLSearchParams();
  if (shuffleQuestions) {
    queryParams.set("shuffleQuestions", "true");
  }
  if (shuffleAnswers) {
    queryParams.set("shuffleAnswers", "true");
  }

  const queryString = queryParams.toString();
  const urlSuffix = queryString ? `?${queryString}` : "";

  // Chuyển hướng dựa trên loại bài thi đã chọn
  if (selectedExamType === "google-form") {
    window.location.href = `/exam/${currentExamId}/google-form${urlSuffix}`;
  } else if (selectedExamType === "quizizz") {
    window.location.href = `/exam/${currentExamId}/quizizz${urlSuffix}`;
  }

  closeExamModal();
}

// Log để kiểm tra script đã được tải
console.log("Exam page script loaded successfully!");
