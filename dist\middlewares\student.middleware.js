"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkStudentCourseAccess = exports.validateStudentCourse = exports.getStudentCourses = exports.validateStudent = void 0;
const student_1 = __importDefault(require("../models/student"));
const errorhandler_1 = require("../util/errorhandler");
/**
 * Middleware xác thực sinh viên có tồn tại trong hệ thống
 */
const validateStudent = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { studentId } = req.params;
        const student = yield student_1.default.findById(studentId);
        if (!student) {
            return (0, errorhandler_1.responseError)(res, "Sinh viên không tồn tại", 404);
        }
        // Lưu thông tin sinh viên vào request để sử dụng ở các middleware tiếp theo
        req.student = student;
        next();
    }
    catch (error) {
        return (0, errorhandler_1.responseError)(res, error.message, 400);
    }
});
exports.validateStudent = validateStudent;
/**
 * Middleware lấy danh sách khóa học mà sinh viên đã tham gia
 */
const getStudentCourses = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { studentId } = req.params;
        // Tìm tất cả các khóa học (products) mà sinh viên đã tham gia
        const coursesData = yield student_1.default.find({ _id: studentId })
            .populate("productId", "name status")
            .lean();
        if (!coursesData || coursesData.length === 0) {
            return (0, errorhandler_1.responseError)(res, "Sinh viên chưa tham gia khóa học nào", 404);
        }
        // Lấy danh sách productId từ kết quả
        const courses = coursesData.map((data) => data.productId);
        // Lưu danh sách khóa học vào request để sử dụng ở các middleware tiếp theo
        req.studentCourses = courses;
        // Nếu được gọi như một API độc lập, trả về kết quả
        if (req.url.endsWith("/courses")) {
            return res.status(200).json({
                message: "Success",
                data: courses,
            });
        }
        // Nếu không thì chuyển đến middleware tiếp theo
        next();
    }
    catch (error) {
        return (0, errorhandler_1.responseError)(res, error.message, 400);
    }
});
exports.getStudentCourses = getStudentCourses;
/**
 * Middleware xác thực sinh viên có quyền truy cập khóa học
 */
const validateStudentCourse = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { studentId, productId } = req.params;
        // Kiểm tra xem sinh viên có thuộc khóa học này hay không
        const enrollment = yield student_1.default.findOne({
            _id: studentId,
            productId,
        });
        if (!enrollment) {
            return (0, errorhandler_1.responseError)(res, "Sinh viên không có quyền truy cập khóa học này", 403);
        }
        next();
    }
    catch (error) {
        return (0, errorhandler_1.responseError)(res, error.message, 400);
    }
});
exports.validateStudentCourse = validateStudentCourse;
/**
 * Middleware kiểm tra xem người dùng đã đăng nhập và là sinh viên của một môn học cụ thể
 */
const checkStudentCourseAccess = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // Kiểm tra đã đăng nhập chưa
        if (!res.locals.user) {
            return res.redirect("/login?error=Vui lòng đăng nhập để truy cập");
        }
        // Xử lý đặc biệt cho trang làm lại câu hỏi sai
        if (req.path === "/retry-wrong-questions") {
            const { examId } = req.query;
            if (!examId) {
                return res.status(400).render("error", {
                    message: "ID bài thi không hợp lệ",
                    error: { status: 400 },
                    user: res.locals.user,
                });
            }
            // Lấy thông tin bài thi từ examId
            const exam = yield Promise.resolve().then(() => __importStar(require("../models/exam"))).then((module) => module.default);
            const examData = yield exam.findById(examId);
            if (!examData) {
                return res.status(404).render("error", {
                    message: "Không tìm thấy bài thi",
                    error: { status: 404 },
                    user: res.locals.user,
                });
            }
            // Lấy productId từ bài thi
            const productId = examData.productId.toString();
            // Tìm sinh viên theo email
            const email = res.locals.user.email;
            const student = yield Promise.resolve().then(() => __importStar(require("../models/student"))).then((module) => module.default);
            // Tìm xem sinh viên có đăng ký môn học này không
            const studentData = yield student.findOne({
                email: email,
                productId: productId,
            });
            if (!studentData) {
                return res.status(403).render("error", {
                    message: "Bạn không có quyền truy cập trang này",
                    error: {
                        status: 403,
                        description: "Bạn không được đăng ký vào môn học này",
                    },
                    user: res.locals.user,
                });
            }
            // Nếu qua tất cả kiểm tra, cho phép truy cập
            req.student = studentData;
            return next();
        }
        // Xử lý bình thường cho các trường hợp khác
        // Lấy productId từ params hoặc từ bài thi
        let productId = req.params.productId;
        const examId = req.params.examId;
        // Nếu không có productId nhưng có examId, lấy productId từ bài thi
        if (!productId && examId) {
            const exam = yield Promise.resolve().then(() => __importStar(require("../models/exam"))).then((module) => module.default);
            const examData = yield exam.findById(examId);
            if (!examData) {
                return res.status(404).render("error", {
                    message: "Không tìm thấy bài thi",
                    error: { status: 404 },
                    user: res.locals.user,
                });
            }
            productId = examData.productId.toString();
        }
        if (!productId) {
            return res.status(400).render("error", {
                message: "ID môn học không hợp lệ",
                error: { status: 400 },
                user: res.locals.user,
            });
        }
        // Tìm sinh viên theo email
        const email = res.locals.user.email;
        const student = yield Promise.resolve().then(() => __importStar(require("../models/student"))).then((module) => module.default);
        // Tìm xem sinh viên có đăng ký môn học này không
        const studentData = yield student.findOne({
            email: email,
            productId: productId,
        });
        if (!studentData) {
            return res.status(403).render("error", {
                message: "Bạn không có quyền truy cập trang này",
                error: {
                    status: 403,
                    description: "Bạn không được đăng ký vào môn học này",
                },
                user: res.locals.user,
            });
        }
        // Nếu qua tất cả kiểm tra, cho phép truy cập
        req.student = studentData;
        next();
    }
    catch (error) {
        console.error("Lỗi khi kiểm tra quyền truy cập:", error);
        return res.status(500).render("error", {
            message: "Đã xảy ra lỗi khi xử lý yêu cầu",
            error: { status: 500 },
            user: res.locals.user,
        });
    }
});
exports.checkStudentCourseAccess = checkStudentCourseAccess;
//# sourceMappingURL=student.middleware.js.map