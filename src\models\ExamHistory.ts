import mongoose, { Document, Schema } from "mongoose";
import { updateUserProgress } from "../services/userProgress.service";

// Interface cho ExamHistory
export interface IExamHistory extends Document {
  examId: mongoose.Types.ObjectId;
  examName: string;
  userId: mongoose.Types.ObjectId;
  score: number;
  totalQuestions: number;
  duration: number; // Thời gian làm bài (giây)
  examType: "quizizz" | "google-form"; // Loại bài thi
  completedAt: Date;
  createdAt: Date;
  updatedAt: Date;
}

// Schema cho ExamHistory
const ExamHistorySchema: Schema = new Schema(
  {
    examId: { type: Schema.Types.ObjectId, ref: "Exam", required: true },
    examName: { type: String, required: true },
    userId: { type: Schema.Types.ObjectId, ref: "User", required: true },
    score: { type: Number, required: true },
    totalQuestions: { type: Number, required: true },
    duration: { type: Number, required: true }, // Thời gian làm bài (giây)
    examType: {
      type: String,
      enum: ["quizizz", "google-form"],
      required: true,
    },
    completedAt: { type: Date, default: Date.now },
  },
  { timestamps: true }
);

// Middleware sau khi lưu ExamHistory để cập nhật tiến độ học tập
ExamHistorySchema.post("save", async function (doc) {
  try {
    // Kiểm tra xem có bản ghi trùng lặp không
    const completedTime = doc.completedAt as Date;
    const duplicateCount = await mongoose.model("ExamHistory").countDocuments({
      examId: doc.examId,
      userId: doc.userId,
      _id: { $ne: doc._id }, // Không tính bản ghi hiện tại
      completedAt: {
        // Trong khoảng 10 giây
        $gte: new Date(completedTime.getTime() - 10000),
        $lte: new Date(completedTime.getTime() + 10000),
      },
    });

    if (duplicateCount > 0) {
      // Tìm bản ghi có điểm cao nhất trong khoảng thời gian này
      const bestRecord = await mongoose
        .model("ExamHistory")
        .findOne({
          examId: doc.examId,
          userId: doc.userId,
          completedAt: {
            $gte: new Date(completedTime.getTime() - 10000),
            $lte: new Date(completedTime.getTime() + 10000),
          },
        })
        .sort({ score: -1 })
        .limit(1);

      // Nếu bản ghi hiện tại không phải là bản ghi có điểm cao nhất, bỏ qua việc cập nhật tiến độ
      if (bestRecord && bestRecord._id.toString() !== doc._id.toString()) {
        return;
      }
    }

    // Tiếp tục cập nhật tiến độ học tập
    await updateUserProgress(doc);
  } catch (error) {
    console.error("Lỗi cập nhật tiến độ học tập:", error);
  }
});

// Model cho ExamHistory
export default mongoose.model<IExamHistory>("ExamHistory", ExamHistorySchema);
