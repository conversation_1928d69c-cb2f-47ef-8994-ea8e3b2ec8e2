<div class="max-w-md mx-auto">
  <div class="bg-white rounded-lg shadow-md p-8">
    <div class="mb-6 text-center">
      <i class="fas fa-user-plus text-5xl text-green-500 mb-4"></i>
      <h1 class="text-2xl font-bold text-gray-800">Đăng ký tài khoản</h1>
      <p class="text-gray-600 mt-2">Tạo tài khoản mới để sử dụng hệ thống</p>
    </div>

    <!-- Register Form -->
    <form id="register-form" class="space-y-4">
      <div>
        <label
          for="displayName"
          class="block text-sm font-medium text-gray-700 mb-2"
          >Tên hiển thị</label
        >
        <input
          type="text"
          id="displayName"
          name="displayName"
          required
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
          placeholder="Nhập tên hiển thị"
        />
      </div>

      <div>
        <label for="email" class="block text-sm font-medium text-gray-700 mb-2"
          >Email</label
        >
        <input
          type="email"
          id="email"
          name="email"
          required
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
          placeholder="Nhập email của bạn"
        />
      </div>

      <div>
        <label
          for="password"
          class="block text-sm font-medium text-gray-700 mb-2"
          >Mật khẩu</label
        >
        <input
          type="password"
          id="password"
          name="password"
          required
          minlength="6"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
          placeholder="Nhập mật khẩu (ít nhất 6 ký tự)"
        />
      </div>

      <div>
        <label
          for="confirmPassword"
          class="block text-sm font-medium text-gray-700 mb-2"
          >Xác nhận mật khẩu</label
        >
        <input
          type="password"
          id="confirmPassword"
          name="confirmPassword"
          required
          minlength="6"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
          placeholder="Nhập lại mật khẩu"
        />
      </div>

      <div class="flex items-center">
        <input
          id="agree-terms"
          name="agree-terms"
          type="checkbox"
          required
          class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
        />
        <label for="agree-terms" class="ml-2 block text-sm text-gray-700">
          Tôi đồng ý với
          <a href="#" class="text-green-600 hover:text-green-500"
            >Điều khoản dịch vụ</a
          >
          và
          <a href="#" class="text-green-600 hover:text-green-500"
            >Chính sách bảo mật</a
          >
        </label>
      </div>

      <button
        type="submit"
        class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors"
      >
        <span id="register-text">Đăng ký</span>
        <span id="register-spinner" class="hidden">
          <i class="fas fa-spinner fa-spin mr-2"></i>Đang đăng ký...
        </span>
      </button>
    </form>

    <div class="mt-6">
      <div class="relative">
        <div class="absolute inset-0 flex items-center">
          <div class="w-full border-t border-gray-300"></div>
        </div>
        <div class="relative flex justify-center text-sm">
          <span class="px-2 bg-white text-gray-500">Hoặc</span>
        </div>
      </div>

      <div class="mt-6">
        <a
          href="#"
          id="google-register-btn"
          class="w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 transition-colors"
        >
          <img
            src="https://developers.google.com/identity/images/g-logo.png"
            alt="Google"
            class="w-5 h-5 mr-3"
          />
          Đăng ký bằng Google
        </a>
      </div>
    </div>

    <div class="mt-6 text-center">
      <p class="text-sm text-gray-600">
        Đã có tài khoản?
        <a
          href="/login"
          class="font-medium text-green-600 hover:text-green-500"
        >
          Đăng nhập ngay
        </a>
      </p>
    </div>

    <div class="mt-6 text-xs text-gray-500 text-center">
      <p>
        Bằng việc đăng ký, bạn đồng ý với các
        <a href="#" class="text-green-600 hover:underline"
          >Điều khoản dịch vụ</a
        >
        và
        <a href="#" class="text-green-600 hover:underline"
          >Chính sách bảo mật</a
        >
        của chúng tôi.
      </p>
    </div>
  </div>

  <div class="text-center mt-4">
    <a href="/login" class="text-green-600 hover:underline">
      <i class="fas fa-arrow-left mr-1"></i> Quay lại đăng nhập
    </a>
  </div>
</div>

<script>
  document.addEventListener("DOMContentLoaded", function () {
    const registerForm = document.getElementById("register-form");
    const googleRegisterBtn = document.getElementById("google-register-btn");
    const registerText = document.getElementById("register-text");
    const registerSpinner = document.getElementById("register-spinner");

    // Register Form
    registerForm.addEventListener("submit", async function (e) {
      e.preventDefault();

      const displayName = document.getElementById("displayName").value;
      const email = document.getElementById("email").value;
      const password = document.getElementById("password").value;
      const confirmPassword = document.getElementById("confirmPassword").value;
      const agreeTerms = document.getElementById("agree-terms").checked;

      // Validate
      if (password !== confirmPassword) {
        showAlert("Mật khẩu xác nhận không khớp", "error");
        return;
      }

      if (!agreeTerms) {
        showAlert("Vui lòng đồng ý với điều khoản dịch vụ", "error");
        return;
      }

      // Show loading
      registerText.classList.add("hidden");
      registerSpinner.classList.remove("hidden");

      try {
        const response = await fetch("/auth/register", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ displayName, email, password }),
        });

        const data = await response.json();

        if (data.success) {
          showAlert(data.message, "success");
          // Chuyển hướng sau 2 giây
          setTimeout(() => {
            window.location.href = "/login";
          }, 2000);
        } else {
          showAlert(data.message, "error");
        }
      } catch (error) {
        console.error("Register error:", error);
        showAlert("Lỗi kết nối. Vui lòng thử lại.", "error");
      } finally {
        // Hide loading
        registerText.classList.remove("hidden");
        registerSpinner.classList.add("hidden");
      }
    });

    // Google Register
    googleRegisterBtn.addEventListener("click", function (e) {
      e.preventDefault();

      // Lấy clientId từ localStorage nếu có, hoặc tạo mới
      let clientId = localStorage.getItem("clientId");
      if (!clientId) {
        clientId =
          "client_" +
          Date.now() +
          "_" +
          Math.random().toString(36).substring(2, 10);
        localStorage.setItem("clientId", clientId);
      }

      // Chuyển hướng đến URL đăng nhập Google với clientId
      window.location.href = `/auth/google?clientId=${clientId}`;
    });

    // Utility function to show alerts
    function showAlert(message, type) {
      // Remove existing alerts
      const existingAlert = document.querySelector(".alert");
      if (existingAlert) {
        existingAlert.remove();
      }

      const alertClass =
        type === "error"
          ? "bg-red-50 text-red-600"
          : "bg-green-50 text-green-600";
      const iconClass =
        type === "error" ? "fa-exclamation-circle" : "fa-check-circle";

      const alert = document.createElement("div");
      alert.className = `alert ${alertClass} p-4 rounded-md mb-6`;
      alert.innerHTML = `
        <i class="fas ${iconClass} mr-2"></i>
        ${message}
      `;

      // Insert alert after the title
      const title = document.querySelector(".mb-6");
      title.parentNode.insertBefore(alert, title.nextSibling);

      // Auto remove after 5 seconds (except success messages)
      if (type !== "success") {
        setTimeout(() => {
          if (alert.parentNode) {
            alert.remove();
          }
        }, 5000);
      }
    }
  });
</script>
