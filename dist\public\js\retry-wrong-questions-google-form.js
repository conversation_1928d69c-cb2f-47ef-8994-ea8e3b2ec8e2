// Bi<PERSON><PERSON> toàn cục
let examData = {};
let wrongQuestions = [];
let currentQuestionIndex = 0;
let answeredQuestions = 0;
let timerInterval = null;
let startTime = null;
let elapsedTime = 0;
let isSubmitting = false;
let isExitPopupVisible = false;
let timeExpired = false;
let userAnswers = [];
let questionResults = [];
// Khởi tạo khi trang tải xong
document.addEventListener("DOMContentLoaded", function () {
    try {
        // Lấy dữ liệu bài thi từ JSON
        try {
            examData = JSON.parse(document.getElementById("exam-data").textContent);
        }
        catch (error) {
            throw new Error("Không thể đọc dữ liệu bài thi");
        }
        // Lấy dữ liệu câu hỏi sai từ localStorage thường
        try {
            const wrongQuestionsJson = localStorage.getItem("wrongQuestions");
            if (!wrongQuestionsJson) {
                throw new Error("Không tìm thấy dữ liệu câu hỏi sai trong localStorage");
            }
            const wrongQuestionsData = JSON.parse(wrongQuestionsJson);
            // Kiểm tra dữ liệu có đúng định dạng không
            if (!wrongQuestionsData.questions ||
                !Array.isArray(wrongQuestionsData.questions)) {
                throw new Error("Dữ liệu câu hỏi sai không đúng định dạng");
            }
            // Kiểm tra ID bài thi khớp với ID hiện tại
            if (wrongQuestionsData.examId !== (examData.examId || examData.id)) {
                throw new Error("ID bài thi không khớp với dữ liệu câu hỏi sai đã lưu");
            }
            // Tiếp tục với dữ liệu từ localStorage
            initializeExam(wrongQuestionsData);
        }
        catch (error) {
            console.error("Lỗi dữ liệu:", error);
            // Hiển thị thông báo lỗi với thông tin chi tiết
            showError(`
        
      `);
        }
    }
    catch (generalError) {
        showError(`
      <p class="mb-6">Đã xảy ra lỗi không xác định: ${generalError.message}</p>
      <p class="mb-4">Vui lòng quay lại trang chủ và thử lại sau.</p>
    `);
    }
});
// Hàm hiển thị lỗi
function showError(message) {
    // Ẩn container câu hỏi
    document.getElementById("exam-container").classList.add("hidden");
    // Tạo thông báo lỗi
    const errorDiv = document.createElement("div");
    errorDiv.className = "bg-white rounded-lg shadow-sm p-6 text-center";
    errorDiv.innerHTML = `
    <div class="mx-auto flex items-center justify-center h-24 w-24 rounded-full bg-red-100 mb-6">
      <i class="fas fa-exclamation-circle text-red-600 text-4xl"></i>
    </div>
    <h2 class="text-2xl font-bold text-gray-800 mb-4">ID bài thi không hợp lệ</h2>
    ${message}
    <a href="/home" class="inline-block bg-indigo-600 text-white px-6 py-2 rounded-full font-medium hover:bg-indigo-700 transition-colors">
      Về trang chủ
    </a>
  `;
    // Chèn thông báo lỗi vào trang
    const mainContainer = document.querySelector(".flex-grow.flex.flex-col");
    mainContainer.appendChild(errorDiv);
}
// Khởi tạo bài thi
function initializeExam(wrongQuestionsData) {
    wrongQuestions = wrongQuestionsData.questions;
    // Kiểm tra có câu hỏi không
    if (!wrongQuestions || wrongQuestions.length === 0) {
        showError(`
      <p class="mb-6">Không có câu hỏi sai trong bài thi này.</p>
      <p class="mb-4">Có thể bạn đã trả lời đúng tất cả các câu hỏi. Chúc mừng!</p>
    `);
        return;
    }
    // Cập nhật thông tin bài thi
    document.getElementById("total-questions").textContent =
        wrongQuestions.length;
    document.getElementById("totalWrongQuestions").textContent =
        wrongQuestions.length;
    document.getElementById("total-questions-2").textContent =
        wrongQuestions.length;
    // Khởi tạo mảng kết quả người dùng
    userAnswers = Array(wrongQuestions.length).fill(null);
    // Bắt đầu đếm thời gian
    startTimer();
    // Hiển thị câu hỏi đầu tiên
    displayQuestion(0);
    // Thiết lập các sự kiện
    setupEventListeners();
}
// Hiển thị câu hỏi theo index
function displayQuestion(index) {
    if (index < 0 || index >= wrongQuestions.length)
        return;
    currentQuestionIndex = index;
    const question = wrongQuestions[index];
    // Cập nhật số thứ tự câu hỏi
    document.getElementById("current-question").textContent = index + 1;
    // Lấy container câu hỏi
    const questionContainer = document.getElementById("questions-container");
    // Xóa tất cả các lớp CSS hiện có từ container
    questionContainer.className = "";
    questionContainer.classList.add("question-card", "mb-4", "flex-grow", "flex", "flex-col");
    // Cấu trúc HTML của câu hỏi
    let questionHTML = `
    <div class="bg-white rounded-t-lg p-6 flex-grow overflow-y-auto">
      <div class="mb-4">
        <h2 class="text-xl font-bold text-gray-800 mb-2">${question.text}</h2>
        ${question.image
        ? `<img src="${question.image}" alt="Hình ảnh câu hỏi" class="max-w-full h-auto rounded my-2">`
        : ""}
      </div>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4" id="options-container">
  `;
    // Tạo các lựa chọn
    if (question.options && question.options.length > 0) {
        question.options.forEach((option, optIndex) => {
            const optionText = typeof option === "object" ? option.text : option;
            // Loại bỏ tiền tố "A.", "B.", "C.", "D." nếu có
            let cleanOptionText = optionText;
            // Sử dụng regex để tìm và loại bỏ tiền tố theo nhiều định dạng:
            // 1. Chữ cái + dấu chấm: A. B. C. D.
            // 2. Chữ cái + dấu ngoặc đóng: A) B) C) D)
            // 3. Số + dấu chấm: 1. 2. 3. 4.
            // 4. Số + dấu ngoặc đóng: 1) 2) 3) 4)
            const prefixRegex = /^\s*(?:[A-D]|[1-9])[\.\)][\s\u00A0]*/i;
            if (prefixRegex.test(cleanOptionText)) {
                cleanOptionText = cleanOptionText.replace(prefixRegex, "");
            }
            const optionClasses = [
                "p-4",
                "rounded-lg",
                "border-2",
                "border-gray-200",
                "cursor-pointer",
                "hover:bg-gray-50",
                "text-gray-700",
                "transition-colors",
                "option-button",
            ];
            // Thêm các lớp màu sắc dựa trên vị trí
            const colorClasses = optIndex === 0
                ? ["option-red", "hover:bg-red-50", "hover:border-red-300"]
                : optIndex === 1
                    ? ["option-blue", "hover:bg-blue-50", "hover:border-blue-300"]
                    : optIndex === 2
                        ? ["option-green", "hover:bg-green-50", "hover:border-green-300"]
                        : ["option-yellow", "hover:bg-yellow-50", "hover:border-yellow-300"];
            optionClasses.push(...colorClasses);
            // Thêm lớp selected nếu đã chọn
            if (userAnswers[index] === optIndex) {
                optionClasses.push("selected");
                optionClasses.push("border-purple-500");
                optionClasses.push("bg-purple-50");
            }
            const optionLetter = String.fromCharCode(65 + optIndex); // A, B, C, D
            questionHTML += `
        <div 
          class="${optionClasses.join(" ")}"
          data-option-index="${optIndex}"
          onclick="selectOption(${optIndex})"
        >
          <div class="flex items-center">
            <span class="inline-flex items-center justify-center h-6 w-6 rounded-full bg-gray-100 text-gray-700 font-medium mr-3 flex-shrink-0">
              ${optionLetter}
            </span>
            <span class="flex-1">${cleanOptionText}</span>
          </div>
        </div>
      `;
        });
    }
    questionHTML += `
      </div>
    </div>
    <div class="bg-gray-100 rounded-b-lg p-4 flex justify-between items-center">
      <button 
        id="prev-button" 
        class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ${index === 0 ? "opacity-50 cursor-not-allowed" : ""}"
        ${index === 0 ? "disabled" : ""}
        onclick="navigateQuestion(-1)"
      >
        <i class="fas fa-arrow-left mr-1"></i> Câu trước
      </button>
      <div class="text-sm text-gray-500">
        <span id="question-counter">${index + 1}/${wrongQuestions.length}</span>
      </div>
      <button 
        id="next-button" 
        class="bg-indigo-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        onclick="navigateQuestion(1)"
      >
        ${index === wrongQuestions.length - 1
        ? "Xem kết quả"
        : 'Câu kế <i class="fas fa-arrow-right ml-1"></i>'}
      </button>
    </div>
  `;
    questionContainer.innerHTML = questionHTML;
    // Cập nhật thanh tiến độ
    updateProgress();
    // Hiển thị nút Nộp bài nếu đã trả lời tất cả câu hỏi
    if (answeredQuestions === wrongQuestions.length &&
        index === wrongQuestions.length - 1) {
        document.getElementById("next-button").textContent = "Nộp bài";
    }
}
// Chọn đáp án
function selectOption(optionIndex) {
    if (isSubmitting)
        return;
    // Lưu câu trả lời của người dùng
    userAnswers[currentQuestionIndex] = optionIndex;
    // Đánh dấu là đã trả lời nếu chưa
    if (userAnswers.filter((ans) => ans !== null).length > answeredQuestions) {
        answeredQuestions = userAnswers.filter((ans) => ans !== null).length;
    }
    // Cập nhật giao diện các lựa chọn
    const optionsContainer = document.getElementById("options-container");
    const options = optionsContainer.querySelectorAll(".option-button");
    options.forEach((option, index) => {
        // Xóa tất cả các lớp selected
        option.classList.remove("selected", "border-purple-500", "bg-purple-50");
        // Thêm lớp selected cho lựa chọn được chọn
        if (index === optionIndex) {
            option.classList.add("selected", "border-purple-500", "bg-purple-50");
        }
    });
    // Cập nhật thanh tiến độ
    updateProgress();
    // Cập nhật nút tiếp theo nếu là câu cuối cùng
    if (currentQuestionIndex === wrongQuestions.length - 1) {
        // Hiển thị nút Nộp bài
        document.getElementById("next-button").textContent = "Nộp bài";
    }
    // Đã tắt tính năng tự động chuyển câu - người dùng phải tự ấn nút điều hướng
}
// Di chuyển qua lại giữa các câu hỏi
function navigateQuestion(direction) {
    if (isSubmitting)
        return;
    const newIndex = currentQuestionIndex + direction;
    // Kiểm tra khi nhấn nút "Tiếp theo" ở câu cuối cùng
    if (direction > 0 && currentQuestionIndex === wrongQuestions.length - 1) {
        // Nộp bài nếu đã trả lời tất cả câu hỏi
        if (answeredQuestions === wrongQuestions.length) {
            submitExam();
            return;
        }
        else {
            // Hiển thị thông báo nếu chưa trả lời hết
            showWarningPopup();
        }
        return;
    }
    // Kiểm tra phạm vi hợp lệ
    if (newIndex >= 0 && newIndex < wrongQuestions.length) {
        displayQuestion(newIndex);
        updateProgress();
    }
}
// Hiển thị popup cảnh báo chưa trả lời hết câu hỏi
function showWarningPopup() {
    // Lấy danh sách các câu hỏi chưa trả lời
    const unansweredIndexes = userAnswers
        .map((answer, index) => (answer === null ? index + 1 : null))
        .filter((index) => index !== null);
    // Hiển thị popup
    const warningPopup = document.getElementById("warning-popup");
    const unansweredList = document.getElementById("unanswered-questions");
    unansweredList.innerHTML = "";
    unansweredIndexes.forEach((index) => {
        const item = document.createElement("span");
        item.className =
            "inline-block bg-red-100 text-red-800 px-2 py-1 rounded m-1 cursor-pointer";
        item.textContent = index;
        item.onclick = function () {
            hideWarningPopup();
            displayQuestion(index - 1);
        };
        unansweredList.appendChild(item);
    });
    warningPopup.classList.remove("hidden");
}
// Ẩn popup cảnh báo
function hideWarningPopup() {
    document.getElementById("warning-popup").classList.add("hidden");
}
// Hiển thị popup xác nhận nộp bài
function showSubmitConfirmPopup() {
    document.getElementById("exitConfirmPopup").classList.remove("hidden");
}
// Ẩn popup xác nhận nộp bài
function hideSubmitConfirmPopup() {
    document.getElementById("exitConfirmPopup").classList.add("hidden");
}
// Cập nhật tiến độ làm bài
function updateProgress() {
    // Cập nhật số câu đã trả lời
    document.getElementById("answered-questions").textContent = answeredQuestions;
    // Cập nhật thanh tiến độ
    const progressBar = document.getElementById("progressBar");
    const progressPercentage = (answeredQuestions / wrongQuestions.length) * 100;
    progressBar.style.width = `${progressPercentage}%`;
    // Cập nhật danh sách câu hỏi
    const questionList = document.getElementById("question-list");
    questionList.innerHTML = "";
    wrongQuestions.forEach((_, index) => {
        const item = document.createElement("div");
        item.className = "question-item";
        // Thêm các lớp CSS dựa trên trạng thái
        if (index === currentQuestionIndex) {
            item.classList.add("current");
        }
        if (userAnswers[index] !== null) {
            item.classList.add("answered");
        }
        item.onclick = function () {
            displayQuestion(index);
        };
        item.textContent = index + 1;
        questionList.appendChild(item);
    });
}
// Khởi động bộ đếm thời gian
function startTimer() {
    startTime = new Date();
    timerInterval = setInterval(function () {
        const currentTime = new Date();
        elapsedTime = Math.floor((currentTime - startTime) / 1000);
        // Kiểm tra nếu hết thời gian
        if (examData.duration > 0 && elapsedTime >= examData.duration * 60) {
            clearInterval(timerInterval);
            timeExpired = true;
            submitExam();
            return;
        }
        // Cập nhật hiển thị thời gian
        updateTimerDisplay();
    }, 1000);
    // Cập nhật hiển thị thời gian ban đầu
    updateTimerDisplay();
}
// Cập nhật hiển thị thời gian
function updateTimerDisplay() {
    const timer = document.getElementById("timer");
    let remainingTime;
    if (examData.duration > 0) {
        // Nếu có thời hạn, hiển thị thời gian còn lại
        const totalSeconds = examData.duration * 60;
        remainingTime = Math.max(0, totalSeconds - elapsedTime);
    }
    else {
        // Nếu không có thời hạn, hiển thị thời gian đã trôi qua
        remainingTime = elapsedTime;
    }
    const hours = Math.floor(remainingTime / 3600);
    const minutes = Math.floor((remainingTime % 3600) / 60);
    const seconds = remainingTime % 60;
    timer.textContent = `${hours.toString().padStart(2, "0")}:${minutes
        .toString()
        .padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
    // Thay đổi màu khi sắp hết thời gian
    if (examData.duration > 0 && remainingTime <= 300) {
        timer.parentElement.classList.add("animate-pulse", "bg-red-600");
    }
}
// Thiết lập các sự kiện
function setupEventListeners() {
    // Xử lý sự kiện nút hủy nộp bài
    document
        .getElementById("exitConfirmStayBtn")
        .addEventListener("click", function () {
        hideSubmitConfirmPopup();
    });
    // Xử lý sự kiện nút xác nhận nộp bài
    document
        .getElementById("exitConfirmLeaveBtn")
        .addEventListener("click", function () {
        submitExam();
    });
    // Xử lý sự kiện nút ở lại làm bài trong cảnh báo
    document
        .getElementById("continue-button")
        .addEventListener("click", function () {
        hideWarningPopup();
    });
    // Xử lý sự kiện nút nộp bài trong cảnh báo
    document
        .getElementById("submit-anyway")
        .addEventListener("click", function () {
        hideWarningPopup();
        showSubmitConfirmPopup();
    });
    // Thêm sự kiện ngăn người dùng rời khỏi trang
    window.addEventListener("beforeunload", function (e) {
        if (!isSubmitting && !timeExpired) {
            e.preventDefault();
            e.returnValue = "";
            return "";
        }
    });
    // Phần xem lại đáp án luôn hiển thị với scroll, không cần toggle
}
// Xử lý nộp bài
function submitExam() {
    if (isSubmitting)
        return false;
    isSubmitting = true;
    clearInterval(timerInterval);
    // Ẩn popup nếu đang hiển thị
    hideSubmitConfirmPopup();
    hideWarningPopup();
    // Tính toán kết quả
    questionResults = [];
    let correctCount = 0;
    wrongQuestions.forEach((question, index) => {
        const selectedOptionIndex = userAnswers[index];
        // Xử lý trường hợp người dùng không chọn đáp án
        if (selectedOptionIndex === null) {
            questionResults.push({
                question,
                selectedOptionIndex: null,
                isCorrect: false,
            });
            return;
        }
        // Tìm đáp án đúng
        const correctOptionIndex = question.options.findIndex((opt) => typeof opt === "object" && opt.isCorrect);
        // Kiểm tra đáp án
        const isCorrect = selectedOptionIndex === correctOptionIndex;
        if (isCorrect) {
            correctCount++;
        }
        questionResults.push({
            question,
            selectedOptionIndex,
            isCorrect,
        });
    });
    // Cập nhật giao diện kết quả
    document.getElementById("exam-container").classList.add("hidden");
    document.getElementById("result-container").classList.remove("hidden");
    // Ẩn footer để không bị overlap với kết quả
    const footer = document.querySelector("footer");
    if (footer) {
        footer.style.display = "none";
    }
    // Cập nhật thông tin kết quả
    document.getElementById("correct-count").textContent = correctCount;
    document.getElementById("total-count").textContent = wrongQuestions.length;
    document.getElementById("score-percentage").textContent = Math.round((correctCount / wrongQuestions.length) * 100);
    document.getElementById("examTime").textContent = formatTime(elapsedTime);
    // Cập nhật danh sách câu hỏi tóm tắt
    updateSummaryList();
    // Cập nhật danh sách xem lại câu trả lời
    updateReviewList();
    // Xóa dữ liệu câu hỏi sai khỏi localStorage
    if (typeof SecureStorage !== "undefined") {
        SecureStorage.removeItem("wrongQuestions");
    }
    else {
        localStorage.removeItem("wrongQuestions");
    }
    // Ngăn chặn việc chuyển trang
    return false;
}
// Cập nhật danh sách câu hỏi tóm tắt
function updateSummaryList() {
    const summaryGrid = document.getElementById("summaryContainer");
    summaryGrid.innerHTML = "";
    questionResults.forEach((result, index) => {
        const summaryItem = document.createElement("div");
        if (result.isCorrect) {
            summaryItem.className =
                "bg-green-100 text-green-800 text-center py-1 px-2 rounded";
            summaryItem.innerHTML = `<span>${index + 1}</span>`;
        }
        else {
            summaryItem.className =
                "bg-red-100 text-red-800 text-center py-1 px-2 rounded";
            summaryItem.innerHTML = `<span>${index + 1}</span>`;
        }
        summaryItem.addEventListener("click", function () {
            // Cuộn đến câu hỏi tương ứng trong phần xem lại
            const reviewItem = document.getElementById(`review-item-${index}`);
            if (reviewItem) {
                // Cuộn đến vị trí câu hỏi trong container scroll
                reviewItem.scrollIntoView({
                    behavior: "smooth",
                    block: "center",
                    inline: "nearest",
                });
                reviewItem.classList.add("highlight");
                setTimeout(() => {
                    reviewItem.classList.remove("highlight");
                }, 1500);
            }
        });
        summaryGrid.appendChild(summaryItem);
    });
}
// Cập nhật danh sách xem lại câu trả lời
function updateReviewList() {
    const reviewList = document.getElementById("answerResults");
    reviewList.innerHTML = "";
    questionResults.forEach((result, index) => {
        const reviewItem = document.createElement("div");
        reviewItem.className = "mb-6 pb-4 border-b border-gray-200";
        reviewItem.id = `review-item-${index}`;
        const question = result.question;
        const answerOptions = question.options;
        // Tạo HTML cho các lựa chọn
        let optionsHTML = '<div class="grid grid-cols-1 md:grid-cols-2 gap-3 mt-3">';
        answerOptions.forEach((option, optIndex) => {
            const optionText = typeof option === "object" ? option.text : option;
            // Loại bỏ tiền tố "A.", "B.", "C.", "D." nếu có
            let cleanOptionText = optionText;
            const prefixRegex = /^\s*(?:[A-D]|[1-9])[\.\)][\s\u00A0]*/i;
            if (prefixRegex.test(cleanOptionText)) {
                cleanOptionText = cleanOptionText.replace(prefixRegex, "");
            }
            const optionLetter = String.fromCharCode(65 + optIndex); // A, B, C, D
            const isCorrect = typeof option === "object" && option.isCorrect;
            const isSelected = optIndex === result.selectedOptionIndex;
            let optionClass = "p-3 rounded-lg border";
            if (isSelected && isCorrect) {
                optionClass += " bg-green-50 border-green-500 text-green-800";
            }
            else if (isSelected && !isCorrect) {
                optionClass += " bg-red-50 border-red-500 text-red-800";
            }
            else if (isCorrect) {
                optionClass += " bg-green-50 border-green-300 text-green-800";
            }
            else {
                optionClass += " bg-gray-50 border-gray-200 text-gray-800";
            }
            optionsHTML += `
        <div class="${optionClass}">
          <div class="flex items-center">
            <span class="inline-flex items-center justify-center h-6 w-6 rounded-full bg-gray-100 text-gray-700 font-medium mr-3 flex-shrink-0">
              ${optionLetter}
            </span>
            <div class="flex-1">${cleanOptionText}</div>
            ${isSelected && isCorrect
                ? '<div class="ml-2 text-green-600"><i class="fas fa-check"></i></div>'
                : isSelected && !isCorrect
                    ? '<div class="ml-2 text-red-600"><i class="fas fa-times"></i></div>'
                    : isCorrect
                        ? '<div class="ml-2 text-green-600"><i class="fas fa-check"></i></div>'
                        : ""}
          </div>
        </div>
      `;
        });
        optionsHTML += "</div>";
        // Tìm đáp án đúng
        const correctOption = answerOptions.find((opt) => typeof opt === "object" && opt.isCorrect);
        const correctText = correctOption
            ? correctOption.text
            : "Không tìm thấy đáp án đúng";
        // Tạo badge cho trạng thái câu hỏi
        const statusBadge = result.isCorrect
            ? `<span class="bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded">Làm đúng</span>`
            : `<span class="bg-red-100 text-red-800 text-xs font-medium px-2 py-1 rounded">Làm sai</span>`;
        reviewItem.innerHTML = `
      <div class="flex items-center justify-between mb-3">
        <h3 class="text-lg font-medium text-gray-900">Câu ${index + 1}</h3>
        ${statusBadge}
      </div>
      <div class="mb-3">
        <p class="text-gray-700">${question.text}</p>
        ${question.image
            ? `<img src="${question.image}" alt="Hình ảnh câu hỏi" class="max-w-full h-auto rounded my-2">`
            : ""}
      </div>
      ${optionsHTML}
      ${!result.isCorrect && result.selectedOptionIndex !== null
            ? `<div class="mt-3 text-sm text-red-600">
            <i class="fas fa-info-circle mr-1"></i>
            Đáp án đúng: ${correctText}
           </div>`
            : ""}
      ${result.selectedOptionIndex === null
            ? `<div class="mt-3 text-sm text-orange-600">
            <i class="fas fa-exclamation-triangle mr-1"></i>
            Bạn chưa trả lời câu hỏi này. Đáp án đúng: ${correctText}
           </div>`
            : ""}
    `;
        reviewList.appendChild(reviewItem);
    });
}
// Format thời gian từ giây thành chuỗi hh:mm:ss
function formatTime(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, "0")}:${secs
            .toString()
            .padStart(2, "0")}`;
    }
    else {
        return `${minutes}:${secs.toString().padStart(2, "0")}`;
    }
}
//# sourceMappingURL=retry-wrong-questions-google-form.js.map