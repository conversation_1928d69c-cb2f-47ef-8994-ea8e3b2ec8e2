<!-- <PERSON><PERSON> nhớ -->
<div class="bg-gray-50 rounded-lg p-3 sm:p-6">
  <div class="flex items-center justify-between mb-6">
    <h2 class="text-xl font-semibold text-gray-700 flex items-center">
      <i class="fas fa-bookmark mr-2 text-indigo-500"></i> Ghi nhớ
    </h2>
    <div class="text-sm text-gray-500">
      Tổng:
      <span class="font-medium" id="totalMemoryQuestionsCount">0</span>
    </div>
  </div>

  <% if (!memoryQuestions || memoryQuestions.length === 0) { %>
  <div class="bg-blue-50 border-l-4 border-blue-400 p-4">
    <div class="flex">
      <div class="flex-shrink-0">
        <i class="fas fa-info-circle text-blue-400"></i>
      </div>
      <div class="ml-3">
        <p class="text-sm text-blue-700">
          B<PERSON><PERSON> chưa có câu hỏi ghi nhớ nào. <PERSON><PERSON><PERSON> thêm câu hỏi từ tab <PERSON><PERSON> cương.
        </p>
      </div>
    </div>
  </div>
  <% } else { %>
  
  <!-- Controls: Search, Filter dropdown và Button luyện tập -->
  <div class="mb-4 sm:mb-6 space-y-3 sm:space-y-4">
    <!-- Row 1: Search box -->
    <div class="flex items-center">
      <div class="relative flex-1">
        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <i class="fas fa-search text-gray-400"></i>
        </div>
        <input
          type="text"
          id="memorySearchInput"
          placeholder="Tìm kiếm câu hỏi..."
          class="block w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm"
        />
        <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
          <button
            onclick="clearMemorySearch()"
            class="text-gray-400 hover:text-gray-600 focus:outline-none p-1"
            id="clearMemorySearchBtn"
            style="display: none"
          >
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- Row 2: Filters -->
    <div class="flex items-center gap-2">
      <!-- Icon filter -->
      <div class="flex-shrink-0">
        <i class="fas fa-filter text-indigo-500 text-sm"></i>
      </div>

      <!-- Dropdown lọc theo nguồn -->
      <select
        id="memorySourceFilter"
        onchange="filterMemoryBySource()"
        class="px-2 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 bg-white text-xs sm:text-sm flex-1 min-w-0"
      >
        <option value="all">Tất cả nguồn</option>
        <option value="manual">Ghi nhớ thủ công</option>
      </select>

      <!-- Dropdown lọc theo đề thi -->
      <select
        id="memoryExamFilter"
        onchange="filterMemoryByExam()"
        class="px-2 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 bg-white text-xs sm:text-sm flex-1 min-w-0"
      >
        <option value="all">Tất cả đề thi</option>
        <!-- Options sẽ được thêm bằng JavaScript -->
      </select>
    </div>

    <!-- Row 3: Actions -->
    <div class="flex items-center justify-between">
      <div>
        <button id="bulkDeleteBtn" onclick="MemoryExam.deleteBulkMemoryQuestions()" class="px-3 py-2 bg-red-500 text-white rounded-lg text-sm hover:bg-red-600 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
          <i class="fas fa-trash-alt mr-2"></i> Xóa đã chọn (<span id="selectedCount">0</span>)
        </button>
      </div>
      <div class="flex gap-2">
        <button onclick="MemoryExam.startMemoryPractice()" class="px-4 py-2 bg-green-600 text-white rounded-lg text-sm hover:bg-green-700 transition-all">
          <i class="fas fa-play-circle mr-2"></i> Luyện nhanh
        </button>
        <button onclick="startQuizizzPractice()" class="px-4 py-2 bg-purple-600 text-white rounded-lg text-sm hover:bg-purple-700 transition-all">
          <i class="fas fa-gamepad mr-2"></i> Quizizz
        </button>
      </div>
    </div>

    <!-- Search results info -->
    <div
      id="memorySearchInfo"
      class="text-sm text-gray-600 bg-blue-50 px-3 py-2 rounded-lg"
      style="display: none"
    >
      <span id="memorySearchResultCount" class="hidden">0</span>
    </div>
  </div>

  <!-- Stats summary -->
  <div class="bg-white rounded-lg border border-gray-200 p-4 mb-4">
    <div class="grid grid-cols-3 gap-3">
      <div class="bg-blue-50 rounded-lg p-3 text-center">
        <div class="text-sm text-gray-500 mb-1">Tổng câu hỏi</div>
        <div class="text-xl font-semibold text-blue-700" id="memoryStatsTotalQuestions">0</div>
      </div>
      <div class="bg-green-50 rounded-lg p-3 text-center">
        <div class="text-sm text-gray-500 mb-1">Độ chính xác</div>
        <div class="text-xl font-semibold text-green-700" id="memoryStatsAccuracy">0%</div>
      </div>
      <div class="bg-purple-50 rounded-lg p-3 text-center">
        <div class="text-sm text-gray-500 mb-1">Đã luyện tập</div>
        <div class="text-xl font-semibold text-purple-700" id="memoryStatsPracticed">0</div>
      </div>
    </div>
  </div>

  <!-- Scrollable container cho danh sách câu hỏi -->
  <div
    id="memoryQuestionsScrollContainer"
    class="bg-gray-50 rounded-lg border border-gray-200 shadow-inner"
    style="height: 65vh; overflow-y: auto"
  >
    <div id="memoryQuestionsContainer" class="p-1 sm:p-4">
      <!-- Loading state -->
      <div class="text-center py-8" id="memoryLoadingIndicator">
        <div class="text-indigo-600 mb-2">
          <i class="fas fa-cog text-2xl animate-spin"></i>
        </div>
        <p class="text-gray-600">Đang tải câu hỏi ghi nhớ...</p>
        <div class="mt-2 w-8 h-8 mx-auto">
          <svg
            class="animate-spin"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              class="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              stroke-width="4"
            ></circle>
            <path
              class="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
        </div>
      </div>

      <!-- Sẽ được điền bởi JavaScript -->
    </div>
  </div>
  <% } %>
</div>

<!-- Memory practice modal -->
<div id="memoryPracticeModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
  <div class="bg-white rounded-lg shadow-xl w-full max-w-lg mx-4">
    <div class="p-6 border-b border-gray-200">
      <h3 class="text-lg font-semibold text-gray-800 flex items-center">
        <i class="fas fa-dumbbell text-green-500 mr-2"></i>
        Thiết lập luyện tập
      </h3>
    </div>
    <div class="p-6">
      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Số câu hỏi</label>
          <select id="questionCount" class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm">
            <option value="5">5 câu</option>
            <option value="10" selected>10 câu</option>
            <option value="15">15 câu</option>
            <option value="20">20 câu</option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Thời gian</label>
          <select id="timeLimit" class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm">
            <option value="10">10 phút</option>
            <option value="15" selected>15 phút</option>
            <option value="20">20 phút</option>
            <option value="30">30 phút</option>
          </select>
        </div>
        <div>
          <label class="flex items-center">
            <input type="checkbox" id="shuffleQuestions" class="form-checkbox h-5 w-5 text-indigo-600" checked>
            <span class="ml-2 text-sm text-gray-700">Trộn thứ tự câu hỏi</span>
          </label>
        </div>
        <div>
          <label class="flex items-center">
            <input type="checkbox" id="shuffleAnswers" class="form-checkbox h-5 w-5 text-indigo-600" checked>
            <span class="ml-2 text-sm text-gray-700">Trộn thứ tự đáp án</span>
          </label>
        </div>
      </div>
    </div>
    <div class="p-6 border-t border-gray-200 flex justify-between">
      <button onclick="MemoryExam.closeMemoryPracticeModal()" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
        Hủy
      </button>
      <button onclick="MemoryExam.startMemoryExam()" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700">
        <i class="fas fa-play-circle mr-2"></i> Bắt đầu luyện tập
      </button>
    </div>
  </div>
</div>

<!-- Thêm meta tag để JavaScript biết là đang ở tab memory -->
<meta name="current-tab" content="memory">
<meta name="product-id" content="<%= product._id %>"
