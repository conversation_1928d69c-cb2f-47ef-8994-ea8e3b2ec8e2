<!-- <PERSON>hi nhớ -->
<div class="bg-gray-50 rounded-lg p-3 sm:p-6">
  <div class="flex items-center justify-between mb-6">
    <h2 class="text-xl font-semibold text-gray-700 flex items-center">
      <i class="fas fa-bookmark mr-2 text-indigo-500"></i> Ghi nhớ
    </h2>
  </div>

  <% if (!memoryQuestions || memoryQuestions.length === 0) { %>
  <div class="bg-blue-50 border-l-4 border-blue-400 p-4">
    <div class="flex">
      <div class="flex-shrink-0">
        <i class="fas fa-info-circle text-blue-400"></i>
      </div>
      <div class="ml-3">
        <p class="text-sm text-blue-700">
          Bạn chưa có câu hỏi ghi nhớ nào. Hãy thêm câu hỏi từ tab <PERSON>ề cư<PERSON>ng.
        </p>
      </div>
    </div>
  </div>
  <% } else { %>
  
  <!-- Controls: Search, Filter dropdown và Button luyện tập -->
  <div class="mb-4 sm:mb-6 space-y-3 sm:space-y-4">
    <!-- Row 1: Search box -->
    <div class="flex items-center">
      <div class="relative flex-1">
        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <i class="fas fa-search text-gray-400"></i>
        </div>
        <input
          type="text"
          id="memorySearchInput"
          placeholder="Tìm kiếm câu hỏi..."
          class="block w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm"
        />
        <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
          <button
            onclick="clearMemorySearch()"
            class="text-gray-400 hover:text-gray-600 focus:outline-none p-1"
            id="clearMemorySearchBtn"
            style="display: none"
          >
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- Row 2: Filters -->
    <div class="flex items-center gap-2">
      <!-- Icon filter -->
      <div class="flex-shrink-0">
        <i class="fas fa-filter text-indigo-500 text-sm"></i>
      </div>

      <!-- Dropdown lọc theo đề thi -->
      <select
        id="memoryExamFilter"
        onchange="filterMemoryByExam()"
        class="px-2 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 bg-white text-xs sm:text-sm flex-1 min-w-0"
      >
        <option value="all">Tất cả đề thi</option>
        <!-- Options sẽ được thêm bằng JavaScript -->
      </select>

      <!-- Dropdown lọc theo nhóm -->
      <select
        id="memoryGroupFilter"
        onchange="filterMemoryByGroup()"
        class="px-2 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 bg-white text-xs sm:text-sm flex-1 min-w-0"
      >
        <option value="all">Tất cả nhóm</option>
        <option value="ungrouped">Chưa phân nhóm</option>
        <!-- Options sẽ được thêm bằng JavaScript -->
      </select>
    </div>

    <!-- Row 3: Actions -->
    <div class="flex items-center justify-between">
      <div>
        <button id="bulkDeleteBtn" onclick="MemoryExam.deleteBulkMemoryQuestions()" class="px-3 py-2 bg-red-500 text-white rounded-lg text-sm hover:bg-red-600 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
          <i class="fas fa-trash-alt mr-2"></i> Xóa đã chọn (<span id="selectedCount">0</span>)
        </button>
      </div>
      <div class="flex gap-2">
        <button onclick="MemoryExam.startMemoryPractice()" class="px-4 py-2 bg-green-600 text-white rounded-lg text-sm hover:bg-green-700 transition-all">
          <i class="fas fa-play-circle mr-2"></i> Luyện nhanh
        </button>
        <button onclick="openGroupModal()" class="px-4 py-2 bg-blue-600 text-white rounded-lg text-sm hover:bg-blue-700 transition-all">
          <i class="fas fa-layer-group mr-2"></i> Nhóm câu hỏi
        </button>
      </div>
    </div>

    <!-- Search results info -->
    <div
      id="memorySearchInfo"
      class="text-sm text-gray-600 bg-blue-50 px-3 py-2 rounded-lg"
      style="display: none"
    >
      <span id="memorySearchResultCount" class="hidden">0</span>
    </div>
  </div>



  <!-- Scrollable container cho danh sách câu hỏi -->
  <div
    id="memoryQuestionsScrollContainer"
    class="bg-gray-50 rounded-lg border border-gray-200 shadow-inner"
    style="height: 65vh; overflow-y: auto"
  >
    <div id="memoryQuestionsContainer" class="p-1 sm:p-4">
      <!-- Loading state -->
      <div class="text-center py-8" id="memoryLoadingIndicator">
        <div class="text-indigo-600 mb-2">
          <i class="fas fa-cog text-2xl animate-spin"></i>
        </div>
        <p class="text-gray-600">Đang tải câu hỏi ghi nhớ...</p>
        <div class="mt-2 w-8 h-8 mx-auto">
          <svg
            class="animate-spin"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              class="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              stroke-width="4"
            ></circle>
            <path
              class="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
        </div>
      </div>

      <!-- Sẽ được điền bởi JavaScript -->
    </div>
  </div>
  <% } %>
</div>

<!-- Memory practice modal -->
<div id="memoryPracticeModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
  <div class="bg-white rounded-lg shadow-xl w-full max-w-lg mx-4">
    <div class="p-6 border-b border-gray-200">
      <h3 class="text-lg font-semibold text-gray-800 flex items-center">
        <i class="fas fa-dumbbell text-green-500 mr-2"></i>
        Thiết lập luyện tập
      </h3>
    </div>
    <div class="p-6">
      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Số câu hỏi</label>
          <select id="questionCount" class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm">
            <option value="5">5 câu</option>
            <option value="10" selected>10 câu</option>
            <option value="15">15 câu</option>
            <option value="20">20 câu</option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Thời gian</label>
          <select id="timeLimit" class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm">
            <option value="10">10 phút</option>
            <option value="15" selected>15 phút</option>
            <option value="20">20 phút</option>
            <option value="30">30 phút</option>
          </select>
        </div>
        <div>
          <label class="flex items-center">
            <input type="checkbox" id="shuffleQuestions" class="form-checkbox h-5 w-5 text-indigo-600" checked>
            <span class="ml-2 text-sm text-gray-700">Trộn thứ tự câu hỏi</span>
          </label>
        </div>
        <div>
          <label class="flex items-center">
            <input type="checkbox" id="shuffleAnswers" class="form-checkbox h-5 w-5 text-indigo-600" checked>
            <span class="ml-2 text-sm text-gray-700">Trộn thứ tự đáp án</span>
          </label>
        </div>
      </div>
    </div>
    <div class="p-6 border-t border-gray-200 flex justify-between">
      <button onclick="MemoryExam.closeMemoryPracticeModal()" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
        Hủy
      </button>
      <button onclick="MemoryExam.startMemoryExam()" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700">
        <i class="fas fa-play-circle mr-2"></i> Bắt đầu luyện tập
      </button>
    </div>
  </div>
</div>

<!-- Group Management Modal -->
<div id="groupModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
  <div class="bg-white rounded-lg shadow-xl w-full max-w-4xl mx-4 max-h-[90vh] overflow-hidden">
    <!-- Modal Header -->
    <div class="p-6 border-b border-gray-200">
      <div class="flex justify-between items-center">
        <h3 class="text-lg font-semibold text-gray-800 flex items-center">
          <i class="fas fa-layer-group text-blue-500 mr-2"></i>
          Quản lý nhóm câu hỏi
        </h3>
        <button onclick="closeGroupModal()" class="text-gray-400 hover:text-gray-600 focus:outline-none">
          <i class="fas fa-times text-xl"></i>
        </button>
      </div>
    </div>

    <!-- Modal Content -->
    <div class="p-6 overflow-y-auto max-h-[70vh]">
      <!-- Group Creation Section -->
      <div class="mb-6 bg-blue-50 p-4 rounded-lg">
        <h4 class="font-medium text-gray-800 mb-3">
          <i class="fas fa-plus-circle text-blue-500 mr-2"></i>
          Tạo/Chỉnh sửa nhóm
        </h4>
        <div class="flex gap-3">
          <input 
            type="text" 
            id="groupNameInput" 
            placeholder="Nhập tên nhóm..." 
            class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
          <button 
            onclick="assignSelectedToGroup()" 
            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all"
          >
            <i class="fas fa-plus mr-2"></i>Thêm vào nhóm
          </button>
          <button 
            onclick="ungroupSelected()" 
            class="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-all"
          >
            <i class="fas fa-minus mr-2"></i>Bỏ nhóm
          </button>
        </div>
      </div>

      <!-- Current Groups Section -->
      <div class="mb-6">
        <h4 class="font-medium text-gray-800 mb-3">
          <i class="fas fa-list text-green-500 mr-2"></i>
          Nhóm hiện tại
        </h4>
        <div id="currentGroupsList" class="space-y-2">
          <!-- Will be populated by JavaScript -->
        </div>
      </div>

      <!-- Questions List Section -->
      <div>
        <div class="flex justify-between items-center mb-3">
          <div>
            <h4 class="font-medium text-gray-800">
              <i class="fas fa-question-circle text-indigo-500 mr-2"></i>
              Câu hỏi khả dụng
            </h4>
            <p class="text-xs text-gray-500 mt-1">
              <i class="fas fa-info-circle mr-1"></i>
              Chỉ hiển thị câu hỏi chưa được phân nhóm (mỗi câu hỏi chỉ thuộc 1 nhóm)
            </p>
          </div>
          <div class="flex gap-2">
            <button onclick="selectAllQuestions()" class="text-sm text-blue-600 hover:text-blue-800">
              Chọn tất cả
            </button>
            <span class="text-gray-300">|</span>
            <button onclick="deselectAllQuestions()" class="text-sm text-gray-600 hover:text-gray-800">
              Bỏ chọn tất cả
            </button>
          </div>
        </div>
        <div id="questionsGroupList" class="border border-gray-200 rounded-lg p-4 max-h-60 overflow-y-auto">
          <!-- Will be populated by JavaScript -->
        </div>
      </div>
    </div>

    <!-- Modal Footer -->
    <div class="p-6 border-t border-gray-200 bg-gray-50">
      <div class="flex justify-between items-center">
        <div class="text-sm text-gray-600">
          <span id="selectedQuestionsCount">0</span> câu hỏi được chọn
        </div>
        <button onclick="closeGroupModal()" class="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-all">
          Đóng
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Group Detail Modal -->
<div id="groupDetailModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
  <div class="bg-white rounded-lg shadow-xl w-full max-w-5xl mx-4 max-h-[90vh] overflow-hidden">
    <!-- Modal Header -->
    <div class="p-6 border-b border-gray-200 bg-gradient-to-r from-purple-500 to-indigo-600">
      <div class="flex justify-between items-center">
        <div class="flex items-center text-white">
          <i class="fas fa-layer-group text-2xl mr-3"></i>
          <div>
            <h3 class="text-xl font-semibold" id="groupDetailTitle">Chi tiết nhóm</h3>
            <p class="text-purple-100 text-sm" id="groupDetailSubtitle">0 câu hỏi</p>
          </div>
        </div>
        <button onclick="closeGroupDetailModal()" class="text-white hover:text-purple-200 focus:outline-none">
          <i class="fas fa-times text-xl"></i>
        </button>
      </div>
    </div>

    <!-- Modal Content -->
    <div class="overflow-y-auto max-h-[70vh]">
      <!-- Group Actions Bar -->
      <div class="p-4 bg-gray-50 border-b border-gray-200">
        <div class="flex flex-wrap gap-3 items-center justify-between">
          <div class="flex items-center gap-3">
            <span class="text-sm font-medium text-gray-700">Thao tác nhóm:</span>
            <button onclick="editGroupName()" class="px-3 py-2 bg-yellow-500 text-white rounded-lg text-sm hover:bg-yellow-600 transition-all">
              <i class="fas fa-edit mr-2"></i>Đổi tên
            </button>
            <button onclick="removeQuestionsFromGroup()" class="px-3 py-2 bg-orange-500 text-white rounded-lg text-sm hover:bg-orange-600 transition-all">
              <i class="fas fa-minus mr-2"></i>Bỏ khỏi nhóm
            </button>
            <button onclick="deleteGroupFromDetail()" class="px-3 py-2 bg-red-500 text-white rounded-lg text-sm hover:bg-red-600 transition-all">
              <i class="fas fa-trash mr-2"></i>Xóa nhóm
            </button>
          </div>
          <div class="flex items-center gap-2">
            <span class="text-sm text-gray-600">Đã chọn:</span>
            <span id="selectedInDetailCount" class="px-2 py-1 bg-indigo-100 text-indigo-700 rounded-full text-sm font-medium">0</span>
            <button onclick="selectAllInDetail()" class="text-sm text-blue-600 hover:text-blue-800">Chọn tất cả</button>
            <span class="text-gray-300">|</span>
            <button onclick="deselectAllInDetail()" class="text-sm text-gray-600 hover:text-gray-800">Bỏ chọn</button>
          </div>
        </div>
      </div>

      <!-- Questions List -->
      <div id="groupDetailQuestions" class="p-6">
        <!-- Will be populated by JavaScript -->
      </div>
    </div>

    <!-- Modal Footer -->
    <div class="p-6 border-t border-gray-200 bg-gray-50">
      <div class="flex justify-between items-center">
        <div class="text-sm text-gray-600">
          <i class="fas fa-info-circle mr-1"></i>
          Có thể quản lý câu hỏi trực tiếp từ đây
        </div>
        <button onclick="closeGroupDetailModal()" class="px-6 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-all">
          Đóng
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Thêm meta tag để JavaScript biết là đang ở tab memory -->
<meta name="current-tab" content="memory">
<meta name="product-id" content="<%= product._id %>"
