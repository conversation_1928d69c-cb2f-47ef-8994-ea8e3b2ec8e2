<div class="max-w-md mx-auto">
  <div class="bg-white rounded-lg shadow-md p-8">
    <div class="mb-6 text-center">
      <i class="fas fa-shield-alt text-5xl text-purple-500 mb-4"></i>
      <h1 class="text-2xl font-bold text-gray-800">Đặt lại mật khẩu</h1>
      <p class="text-gray-600 mt-2">Nhập mật khẩu mới cho tài khoản của bạn</p>
    </div>

    <!-- Reset Password Form -->
    <form id="reset-password-form" class="space-y-4">
      <input type="hidden" id="token" name="token" value="<%= token || '' %>" />

      <div>
        <label
          for="newPassword"
          class="block text-sm font-medium text-gray-700 mb-2"
          >Mật khẩu mới</label
        >
        <input
          type="password"
          id="newPassword"
          name="newPassword"
          required
          minlength="6"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
          placeholder="Nhập mật khẩu mới (ít nhất 6 ký tự)"
        />
      </div>

      <div>
        <label
          for="confirmPassword"
          class="block text-sm font-medium text-gray-700 mb-2"
          >Xác nhận mật khẩu mới</label
        >
        <input
          type="password"
          id="confirmPassword"
          name="confirmPassword"
          required
          minlength="6"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
          placeholder="Nhập lại mật khẩu mới"
        />
      </div>

      <div class="text-sm text-gray-600 bg-gray-50 p-3 rounded-md">
        <h4 class="font-medium mb-2">Yêu cầu mật khẩu:</h4>
        <ul class="list-disc list-inside space-y-1 text-xs">
          <li>Ít nhất 6 ký tự</li>
          <li>Nên chứa cả chữ hoa và chữ thường</li>
          <li>Nên chứa số và ký tự đặc biệt</li>
        </ul>
      </div>

      <button
        type="submit"
        class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors"
      >
        <span id="submit-text">Đặt lại mật khẩu</span>
        <span id="submit-spinner" class="hidden">
          <i class="fas fa-spinner fa-spin mr-2"></i>Đang cập nhật...
        </span>
      </button>
    </form>

    <div class="mt-6 text-center">
      <p class="text-sm text-gray-600">
        Nhớ mật khẩu rồi?
        <a
          href="/login"
          class="font-medium text-purple-600 hover:text-purple-500"
        >
          Quay lại đăng nhập
        </a>
      </p>
    </div>
  </div>

  <div class="text-center mt-4">
    <a href="/login" class="text-purple-600 hover:underline">
      <i class="fas fa-arrow-left mr-1"></i> Quay lại đăng nhập
    </a>
  </div>
</div>

<script>
  document.addEventListener("DOMContentLoaded", function () {
    const resetPasswordForm = document.getElementById("reset-password-form");
    const submitText = document.getElementById("submit-text");
    const submitSpinner = document.getElementById("submit-spinner");
    const token = document.getElementById("token").value;

    // Check if token exists
    if (!token) {
      showAlert(
        "Token không hợp lệ. Vui lòng yêu cầu đặt lại mật khẩu mới.",
        "error"
      );
      setTimeout(() => {
        window.location.href = "/forgot-password";
      }, 3000);
      return;
    }

    resetPasswordForm.addEventListener("submit", async function (e) {
      e.preventDefault();

      const newPassword = document.getElementById("newPassword").value;
      const confirmPassword = document.getElementById("confirmPassword").value;

      // Validate
      if (newPassword !== confirmPassword) {
        showAlert("Mật khẩu xác nhận không khớp", "error");
        return;
      }

      if (newPassword.length < 6) {
        showAlert("Mật khẩu phải có ít nhất 6 ký tự", "error");
        return;
      }

      // Show loading
      submitText.classList.add("hidden");
      submitSpinner.classList.remove("hidden");

      try {
        const response = await fetch("/auth/reset-password", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ token, newPassword }),
        });

        const data = await response.json();

        if (data.success) {
          showAlert(data.message, "success");
          // Chuyển hướng sau 3 giây
          setTimeout(() => {
            window.location.href = "/login";
          }, 3000);
        } else {
          showAlert(data.message, "error");
        }
      } catch (error) {
        console.error("Reset password error:", error);
        showAlert("Lỗi kết nối. Vui lòng thử lại.", "error");
      } finally {
        // Hide loading
        submitText.classList.remove("hidden");
        submitSpinner.classList.add("hidden");
      }
    });

    // Password strength indicator
    const newPasswordInput = document.getElementById("newPassword");
    newPasswordInput.addEventListener("input", function () {
      const password = this.value;
      // You can add password strength indicator here
    });

    // Utility function to show alerts
    function showAlert(message, type) {
      // Remove existing alerts
      const existingAlert = document.querySelector(".alert");
      if (existingAlert) {
        existingAlert.remove();
      }

      const alertClass =
        type === "error"
          ? "bg-red-50 text-red-600"
          : "bg-green-50 text-green-600";
      const iconClass =
        type === "error" ? "fa-exclamation-circle" : "fa-check-circle";

      const alert = document.createElement("div");
      alert.className = `alert ${alertClass} p-4 rounded-md mb-6`;
      alert.innerHTML = `
        <i class="fas ${iconClass} mr-2"></i>
        ${message}
      `;

      // Insert alert after the title
      const title = document.querySelector(".mb-6");
      title.parentNode.insertBefore(alert, title.nextSibling);

      // Auto remove after 10 seconds for success messages, 5 for errors
      const timeout = type === "success" ? 10000 : 5000;
      setTimeout(() => {
        if (alert.parentNode) {
          alert.remove();
        }
      }, timeout);
    }
  });
</script>
