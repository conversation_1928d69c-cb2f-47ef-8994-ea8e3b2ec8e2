"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importDefault(require("mongoose"));
const exam_1 = __importDefault(require("../models/exam"));
const question_1 = __importDefault(require("../models/question"));
const dotenv_1 = __importDefault(require("dotenv"));
// Load environment variables
dotenv_1.default.config();
/**
 * Kết nối đến cơ sở dữ liệu MongoDB
 */
function connectToDatabase() {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const mongoUri = process.env.MONGODB_URI || "mongodb://localhost:27017/google-auth";
            yield mongoose_1.default.connect(mongoUri);
            console.log("Đã kết nối thành công đến MongoDB");
        }
        catch (error) {
            console.error("Lỗi kết nối đến MongoDB:", error);
            process.exit(1);
        }
    });
}
/**
 * Cập nhật số lượng câu hỏi cho mỗi bài thi
 */
function updateExamQuestionCounts() {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            // Lấy tất cả các bài thi
            const exams = yield exam_1.default.find({});
            console.log(`Tìm thấy ${exams.length} bài thi cần cập nhật`);
            // Xử lý từng bài thi
            for (const exam of exams) {
                // Đếm số lượng câu hỏi cho bài thi này
                const questionCount = yield question_1.default.countDocuments({ examId: exam._id });
                // Cập nhật trường count của bài thi
                yield exam_1.default.updateOne({ _id: exam._id }, { $set: { count: questionCount } });
                console.log(`Bài thi "${exam.name}" (ID: ${exam._id}) - Số câu hỏi: ${questionCount}`);
            }
            console.log("Đã cập nhật số lượng câu hỏi cho tất cả bài thi thành công!");
        }
        catch (error) {
            console.error("Lỗi khi cập nhật số lượng câu hỏi:", error);
        }
        finally {
            // Ngắt kết nối cơ sở dữ liệu sau khi hoàn thành
            yield mongoose_1.default.disconnect();
            console.log("Đã ngắt kết nối khỏi MongoDB");
        }
    });
}
/**
 * Hàm chính để chạy script
 */
function main() {
    return __awaiter(this, void 0, void 0, function* () {
        yield connectToDatabase();
        yield updateExamQuestionCounts();
    });
}
// Chạy script
main().catch((error) => {
    console.error("Lỗi không mong muốn:", error);
    process.exit(1);
});
//# sourceMappingURL=updateExamQuestionCount.js.map