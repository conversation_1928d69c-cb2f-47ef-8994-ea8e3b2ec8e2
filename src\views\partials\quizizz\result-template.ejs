<!-- Template cho kết quả -->
<template id="resultTemplate">
  <div class="result-display text-center py-4">
    <div class="mb-6 sm:mb-8">
      <div
        class="inline-flex items-center justify-center w-16 sm:w-20 h-16 sm:h-20 rounded-full bg-purple-100 text-purple-600 mb-4 dark:bg-purple-900 dark:text-purple-300"
      >
        <i class="fas fa-trophy text-3xl sm:text-4xl"></i>
      </div>
      <h2 class="text-xl sm:text-2xl font-bold mb-2">Ho<PERSON>n thành bài thi!</h2>
      <p class="text-gray-600 dark:text-gray-400">Điểm số của bạn</p>
      <div
        class="text-4xl sm:text-5xl font-bold text-purple-600 mt-2 dark:text-purple-400"
        id="finalScore"
      >
        0/10
      </div>
    </div>

    <div
      class="bg-gray-50 rounded-lg p-3 sm:p-4 mb-5 sm:mb-6 mx-auto max-w-md dark:bg-gray-800"
    >
      <div class="flex items-center justify-between mb-2">
        <span class="text-gray-700 dark:text-gray-300">Số câu đúng:</span>
        <span
          class="font-medium text-green-600 dark:text-green-400"
          id="totalCorrect"
          >0/0</span
        >
      </div>
      <div class="flex items-center justify-between mb-2">
        <span class="text-gray-700 dark:text-gray-300">Thời gian làm bài:</span>
        <span
          class="font-medium text-blue-600 dark:text-blue-400"
          id="totalTime"
          >0:00</span
        >
      </div>
    </div>

    <div
      id="summaryContainer"
      class="grid grid-cols-4 sm:grid-cols-6 gap-2 max-w-md mx-auto mb-5 sm:mb-6"
    >
      <!-- Question summary will be added here -->
    </div>

    <div class="mx-auto max-w-3xl mb-5 sm:mb-6 mt-4">
      <div class="bg-gray-50 rounded-lg p-4 mb-2 dark:bg-gray-800">
        <h3 class="text-base sm:text-lg font-medium mb-4">
          Xem lại câu trả lời
        </h3>
        <div id="reviewContainer" class="max-h-96 overflow-y-auto pr-2">
          <!-- Review questions will be added here -->
        </div>
      </div>
    </div>

    <div
      class="flex flex-col sm:flex-row justify-center space-y-2 sm:space-y-0 sm:space-x-4"
    >
      <button
        onclick="window.location.href='/home'"
        class="bg-purple-600 py-2 px-6 rounded-full text-white font-medium hover:bg-purple-700 focus:outline-none transition-colors"
      >
        Về trang chủ
      </button>
      <button
        id="retryWrongAnswers"
        class="bg-blue-600 py-2 px-6 rounded-full text-white font-medium hover:bg-blue-700 focus:outline-none transition-colors"
      >
        Làm lại câu hỏi sai
      </button>
    </div>
  </div>
</template>
