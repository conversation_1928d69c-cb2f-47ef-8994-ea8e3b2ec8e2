<!-- <PERSON><PERSON> lịch sử làm bài thi -->
<div class="max-w-6xl mx-auto px-4 py-8">
  <h1 class="text-2xl md:text-3xl font-bold text-gray-800 mb-6">
    L<PERSON>ch sử làm bài thi
  </h1>

  <!-- B<PERSON> lọc và tùy chọn hiển thị -->
  <div class="bg-white rounded-lg shadow-sm p-4 mb-6 flex flex-col sm:flex-row sm:items-center justify-between">
    <div class="mb-3 sm:mb-0">
      <span class="text-gray-700 text-sm mr-2">Số kết quả mỗi trang:</span>
      <select id="resultsPerPage" class="border border-gray-300 rounded px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500">
        <option value="5" <%= pagination && pagination.limit == 5 ? 'selected' : '' %>>5</option>
        <option value="10" <%= pagination && pagination.limit == 10 ? 'selected' : '' %>>10</option>
        <option value="20" <%= pagination && pagination.limit == 20 ? 'selected' : '' %>>20</option>
        <option value="50" <%= pagination && pagination.limit == 50 ? 'selected' : '' %>>50</option>
      </select>
    </div>
    <% if (pagination && pagination.totalRecords > 0) { %>
    <div class="text-sm text-gray-600">
      Tổng số bài thi: <span class="font-medium text-indigo-600"><%= pagination.totalRecords %></span>
    </div>
    <% } %>
  </div>

  <% if (examHistories && examHistories.length > 0) { %>
  <div class="bg-white rounded-lg shadow overflow-hidden">
    <!-- Header table -->
    <div
      class="hidden md:flex bg-gray-100 p-4 border-b border-gray-200 font-medium text-gray-600"
    >
      <div class="w-1/3 md:w-2/5">Tên bài thi</div>
      <div class="w-1/6 md:w-1/5 text-center">Loại bài thi</div>
      <div class="w-1/6 md:w-1/5 text-center">Điểm số</div>
      <div class="w-1/6 md:w-1/5 text-center">Thời gian làm</div>
      <div class="w-1/6 md:w-1/5 text-center">Ngày làm</div>
    </div>

    <!-- Table body -->
    <% examHistories.forEach(history => { %>
    <div
      class="flex flex-col md:flex-row border-b border-gray-200 hover:bg-gray-50 transition-colors"
    >
      <!-- Mobile header -->
      <div class="md:hidden bg-gray-100 p-3 font-medium text-gray-600">
        Thông tin bài thi
      </div>

      <!-- Content -->
      <div class="p-4 flex flex-col md:flex-row w-full">
        <div class="w-full md:w-2/5 mb-2 md:mb-0">
          <h3 class="font-medium text-indigo-600"><%= history.examName %></h3>
        </div>

        <div class="w-full md:w-1/5 mb-2 md:mb-0 flex md:justify-center">
          <div class="md:hidden font-medium text-gray-500 mr-2">
            Loại bài thi:
          </div>
          <div>
            <% if (history.examType === "quizizz") { %>
            <span
              class="bg-purple-100 text-purple-800 text-xs py-1 px-2 rounded-full"
              >Quizizz</span
            >
            <% } else { %>
            <span
              class="bg-blue-100 text-blue-800 text-xs py-1 px-2 rounded-full"
              >Google Form</span
            >
            <% } %>
          </div>
        </div>

        <div class="w-full md:w-1/5 mb-2 md:mb-0 flex md:justify-center">
          <div class="md:hidden font-medium text-gray-500 mr-2">Điểm số:</div>
          <div class="font-medium text-gray-700">
            <%= (history.score * 10 / history.totalQuestions).toFixed(2) %>/10
          </div>
        </div>

        <div class="w-full md:w-1/5 mb-2 md:mb-0 flex md:justify-center">
          <div class="md:hidden font-medium text-gray-500 mr-2">
            Thời gian làm:
          </div>
          <div class="text-gray-700">
            <% const minutes = Math.floor(history.duration / 60); const seconds
            = history.duration % 60; %> <%= minutes %>:<%=
            seconds.toString().padStart(2, '0') %>
          </div>
        </div>

        <div class="w-full md:w-1/5 flex md:justify-center">
          <div class="md:hidden font-medium text-gray-500 mr-2">Ngày làm:</div>
          <div class="text-gray-700">
            <%= new Date(history.completedAt).toLocaleDateString('vi-VN') %>
          </div>
        </div>
      </div>
    </div>
    <% }) %>

    <!-- Phân trang -->
    <% if (pagination && pagination.totalPages > 1) { %>
    <div
      class="bg-gray-50 px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6"
    >
      <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
        <div>
          <p class="text-sm text-gray-700">
            Hiển thị
            <span class="font-medium"
              ><%= (pagination.page - 1) * pagination.limit + 1 %></span
            >
            đến
            <span class="font-medium"
              ><%= Math.min(pagination.page * pagination.limit,
              pagination.totalRecords) %></span
            >
            trong
            <span class="font-medium"><%= pagination.totalRecords %></span> kết
            quả
          </p>
        </div>
        <div>
          <nav
            class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px"
            aria-label="Pagination"
          >
            <!-- Nút Trước -->
            <% if (pagination.hasPrev) { %>
            <a href="/exam/history?page=<%= pagination.page - 1 %>&limit=<%= pagination.limit %>" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
              <span class="sr-only">Trang trước</span>
              <i class="fas fa-chevron-left h-5 w-5"></i>
            </a>
            <% } else { %>
            <span class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-gray-100 text-sm font-medium text-gray-400 cursor-not-allowed">
              <span class="sr-only">Trang trước</span>
              <i class="fas fa-chevron-left h-5 w-5"></i>
            </span>
            <% } %>
            
            <!-- Các trang -->
            <% 
            const startPage = Math.max(1, pagination.page - 2);
            const endPage = Math.min(pagination.totalPages, startPage + 4);
            
            for (let i = startPage; i <= endPage; i++) { 
            %>
              <% if (i === pagination.page) { %>
              <span class="relative inline-flex items-center px-4 py-2 border border-indigo-500 bg-indigo-50 text-sm font-medium text-indigo-600">
                <%= i %>
              </span>
              <% } else { %>
              <a href="/exam/history?page=<%= i %>&limit=<%= pagination.limit %>" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                <%= i %>
              </a>
              <% } %>
            <% } %>
            
            <!-- Nút Tiếp -->
            <% if (pagination.hasNext) { %>
            <a href="/exam/history?page=<%= pagination.page + 1 %>&limit=<%= pagination.limit %>" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
              <span class="sr-only">Trang sau</span>
              <i class="fas fa-chevron-right h-5 w-5"></i>
            </a>
            <% } else { %>
            <span
              class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-gray-100 text-sm font-medium text-gray-400 cursor-not-allowed"
            >
              <span class="sr-only">Trang sau</span>
              <i class="fas fa-chevron-right h-5 w-5"></i>
            </span>
            <% } %>
          </nav>
        </div>
      </div>

      <!-- Phiên bản mobile -->
      <div class="flex justify-between w-full sm:hidden">
        <% if (pagination.hasPrev) { %>
        <a
          href="/exam/history?page=<%= pagination.page - 1 %>&limit=<%= pagination.limit %>"
          class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
        >
          Trang trước
        </a>
        <% } else { %>
        <button
          disabled
          class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-400 bg-gray-100 cursor-not-allowed"
        >
          Trang trước
        </button>
        <% } %>

        <div class="text-sm text-gray-700 pt-2">
          Trang <%= pagination.page %> / <%= pagination.totalPages %>
        </div>

        <% if (pagination.hasNext) { %>
        <a
          href="/exam/history?page=<%= pagination.page + 1 %>&limit=<%= pagination.limit %>"
          class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
        >
          Trang sau
        </a>
        <% } else { %>
        <button
          disabled
          class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-400 bg-gray-100 cursor-not-allowed"
        >
          Trang sau
        </button>
        <% } %>
      </div>
    </div>
    <% } %>
  </div>
  <% } else { %>
  <div class="bg-white rounded-lg shadow p-8 text-center">
    <div class="flex justify-center mb-4">
      <div class="bg-gray-100 rounded-full p-4">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-12 w-12 text-gray-400"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M12 4v16m8-8H4"
          />
        </svg>
      </div>
    </div>
    <h2 class="text-xl font-medium text-gray-700 mb-2">
      Chưa có lịch sử làm bài
    </h2>
    <p class="text-gray-500 mb-6">
      Bạn chưa hoàn thành bài thi nào. Hãy thử làm một bài thi.
    </p>
    <a
      href="/courses"
      class="inline-flex items-center px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors"
    >
      <i class="fas fa-book mr-2"></i> Xem danh sách môn học
    </a>
  </div>
  <% } %>
</div>

<!-- Script xử lý phân trang -->
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Xử lý thay đổi số lượng kết quả trên mỗi trang
    const resultsPerPageSelect = document.getElementById('resultsPerPage');
    if (resultsPerPageSelect) {
      resultsPerPageSelect.addEventListener('change', function() {
        const limit = this.value;
        // Chuyển về trang 1 khi thay đổi số lượng hiển thị
        window.location.href = `/exam/history?page=1&limit=${limit}`;
      });
    }
  });
</script>
 