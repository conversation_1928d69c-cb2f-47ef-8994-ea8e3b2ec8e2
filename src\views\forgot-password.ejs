<div class="max-w-md mx-auto">
  <div class="bg-white rounded-lg shadow-md p-8">
    <div class="mb-6 text-center">
      <i class="fas fa-key text-5xl text-blue-500 mb-4"></i>
      <h1 class="text-2xl font-bold text-gray-800">Quên mật khẩu</h1>
      <p class="text-gray-600 mt-2">
        Nhập email của bạn để nhận liên kết đặt lại mật khẩu
      </p>
    </div>

    <!-- Forgot Password Form -->
    <form id="forgot-password-form" class="space-y-4">
      <div>
        <label for="email" class="block text-sm font-medium text-gray-700 mb-2"
          >Email</label
        >
        <input
          type="email"
          id="email"
          name="email"
          required
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          placeholder="Nhập email của bạn"
        />
      </div>

      <button
        type="submit"
        class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
      >
        <span id="submit-text">Gửi liên kết đặt lại</span>
        <span id="submit-spinner" class="hidden">
          <i class="fas fa-spinner fa-spin mr-2"></i>Đang gửi...
        </span>
      </button>
    </form>

    <div class="mt-6 text-center">
      <p class="text-sm text-gray-600 mb-4">
        Nhớ mật khẩu rồi?
        <a href="/login" class="font-medium text-blue-600 hover:text-blue-500">
          Quay lại đăng nhập
        </a>
      </p>

      <p class="text-xs text-gray-500">
        Chưa có tài khoản?
        <a href="/register" class="text-blue-600 hover:text-blue-500">
          Đăng ký ngay
        </a>
      </p>
    </div>
  </div>

  <div class="text-center mt-4">
    <a href="/login" class="text-blue-600 hover:underline">
      <i class="fas fa-arrow-left mr-1"></i> Quay lại đăng nhập
    </a>
  </div>
</div>

<script>
  document.addEventListener("DOMContentLoaded", function () {
    const forgotPasswordForm = document.getElementById("forgot-password-form");
    const submitText = document.getElementById("submit-text");
    const submitSpinner = document.getElementById("submit-spinner");

    forgotPasswordForm.addEventListener("submit", async function (e) {
      e.preventDefault();

      const email = document.getElementById("email").value;

      // Show loading
      submitText.classList.add("hidden");
      submitSpinner.classList.remove("hidden");

      try {
        const response = await fetch("/auth/forgot-password", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ email }),
        });

        const data = await response.json();

        if (data.success) {
          showAlert(data.message, "success");
          // Clear form
          document.getElementById("email").value = "";
        } else {
          // Xử lý rate limit với countdown
          if (response.status === 429 && data.remainingTime) {
            showAlertWithCountdown(data.message, data.remainingTime, "warning");
            disableSubmitButton(data.remainingTime);
          } else {
            showAlert(data.message, "error");
          }
        }
      } catch (error) {
        console.error("Forgot password error:", error);
        showAlert("Lỗi kết nối. Vui lòng thử lại.", "error");
      } finally {
        // Hide loading
        submitText.classList.remove("hidden");
        submitSpinner.classList.add("hidden");
      }
    });

    // Utility function to show alerts
    function showAlert(message, type) {
      // Remove existing alerts
      const existingAlert = document.querySelector(".alert");
      if (existingAlert) {
        existingAlert.remove();
      }

      const alertClass =
        type === "error"
          ? "bg-red-50 text-red-600"
          : type === "warning"
          ? "bg-yellow-50 text-yellow-600"
          : "bg-green-50 text-green-600";
      const iconClass =
        type === "error"
          ? "fa-exclamation-circle"
          : type === "warning"
          ? "fa-exclamation-triangle"
          : "fa-check-circle";

      const alert = document.createElement("div");
      alert.className = `alert ${alertClass} p-4 rounded-md mb-6`;
      alert.innerHTML = `
        <i class="fas ${iconClass} mr-2"></i>
        ${message}
      `;

      // Insert alert after the title
      const title = document.querySelector(".mb-6");
      title.parentNode.insertBefore(alert, title.nextSibling);

      // Auto remove after 10 seconds for success messages, 5 for errors
      const timeout = type === "success" ? 10000 : 5000;
      setTimeout(() => {
        if (alert.parentNode) {
          alert.remove();
        }
      }, timeout);
    }

    // Utility function to show alerts with countdown
    function showAlertWithCountdown(message, remainingTime, type) {
      // Remove existing alerts
      const existingAlert = document.querySelector(".alert");
      if (existingAlert) {
        existingAlert.remove();
      }

      const alertClass =
        type === "warning"
          ? "bg-yellow-50 text-yellow-600"
          : "bg-red-50 text-red-600";
      const iconClass =
        type === "warning"
          ? "fa-exclamation-triangle"
          : "fa-exclamation-circle";

      const alert = document.createElement("div");
      alert.className = `alert ${alertClass} p-4 rounded-md mb-6`;
      alert.innerHTML = `
        <i class="fas ${iconClass} mr-2"></i>
        Vui lòng đợi <span id="countdown" class="font-bold">${remainingTime}</span> giây trước khi gửi lại yêu cầu đặt lại mật khẩu.
      `;

      // Insert alert after the title
      const title = document.querySelector(".mb-6");
      title.parentNode.insertBefore(alert, title.nextSibling);

      // Countdown timer
      let timeLeft = remainingTime;
      const countdownInterval = setInterval(() => {
        timeLeft--;
        const countdownElement = document.getElementById("countdown");
        if (countdownElement) {
          countdownElement.textContent = timeLeft;
        }

        if (timeLeft <= 0) {
          clearInterval(countdownInterval);
          if (alert.parentNode) {
            alert.remove();
          }
        }
      }, 1000);

      // Auto remove after countdown finishes
      setTimeout(() => {
        clearInterval(countdownInterval);
        if (alert.parentNode) {
          alert.remove();
        }
      }, remainingTime * 1000);
    }

    // Function to disable submit button during cooldown
    function disableSubmitButton(remainingTime) {
      const submitButton = document.querySelector('button[type="submit"]');
      const originalText = submitButton.innerHTML;

      submitButton.disabled = true;
      submitButton.classList.add("opacity-50", "cursor-not-allowed");

      let timeLeft = remainingTime;
      const buttonInterval = setInterval(() => {
        timeLeft--;
        submitButton.innerHTML = `<span id="button-countdown">${timeLeft}s</span>`;

        if (timeLeft <= 0) {
          clearInterval(buttonInterval);
          submitButton.disabled = false;
          submitButton.classList.remove("opacity-50", "cursor-not-allowed");
          submitButton.innerHTML = originalText;
        }
      }, 1000);
    }
  });
</script>
