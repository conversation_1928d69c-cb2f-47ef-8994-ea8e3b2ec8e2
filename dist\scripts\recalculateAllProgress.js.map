{"version": 3, "file": "recalculateAllProgress.js", "sourceRoot": "", "sources": ["../../src/scripts/recalculateAllProgress.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA,wDAAgC;AAChC,oDAA4B;AAC5B,2EAA0E;AAE1E,6BAA6B;AAC7B,gBAAM,CAAC,MAAM,EAAE,CAAC;AAEhB,qBAAqB;AACrB,kBAAQ;KACL,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,uCAAuC,CAAC;KAC3E,IAAI,CAAC,GAAS,EAAE;IACf,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;IAE7C,OAAO,CAAC,GAAG,CACT,kEAAkE,CACnE,CAAC;IAEF,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,MAAM,IAAA,6CAAsB,GAAE,CAAC;QAE9C,IAAI,MAAM,EAAE,CAAC;YACX,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;QACxD,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,KAAK,CAAC,sCAAsC,CAAC,CAAC;QACxD,CAAC;IACH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;IAC9D,CAAC;IAED,eAAe;IACf,MAAM,kBAAQ,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;IAClC,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;IAC1C,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC,CAAA,CAAC;KACD,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE;IACb,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,GAAG,CAAC,CAAC;IAC7C,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC,CAAC"}