"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.decryptObject = exports.encryptObject = exports.decryptObjectOptimized = exports.encryptObjectOptimized = exports.streamDecrypt = exports.streamEncrypt = exports.decryptData = exports.encryptData = exports.decryptDataOptimized = exports.encryptDataOptimized = exports.generateRandomIV = exports.generateRandomKey = void 0;
const crypto_1 = __importDefault(require("crypto"));
/**
 * Tạo key ngẫu nhiên cho mã hóa
 */
const generateRandomKey = () => {
    return crypto_1.default.randomBytes(32).toString("hex"); // 256-bit key
};
exports.generateRandomKey = generateRandomKey;
/**
 * Tạo IV (Initialization Vector) ngẫu nhiên
 */
const generateRandomIV = () => {
    return crypto_1.default.randomBytes(16).toString("hex"); // 128-bit IV
};
exports.generateRandomIV = generateRandomIV;
/**
 * Tối ưu XOR encryption với batch processing
 * Sử dụng Uint8Array cho performance tốt hơn
 */
const encryptDataOptimized = (data, key, iv) => {
    try {
        // Use Buffer for maximum performance and compatibility
        const dataBytes = Buffer.from(data, "utf8");
        const combinedKey = key + iv;
        // Pre-allocate Buffer for better performance
        const encrypted = Buffer.alloc(dataBytes.length);
        const keyLength = combinedKey.length;
        // Ultra-optimized loop using Buffer operations
        for (let i = 0; i < dataBytes.length; i++) {
            encrypted[i] = dataBytes[i] ^ combinedKey.charCodeAt(i % keyLength);
        }
        // Convert to hex string efficiently using Buffer.toString
        return encrypted.toString("hex");
    }
    catch (error) {
        console.error("Lỗi khi mã hóa:", error);
        throw new Error("Không thể mã hóa dữ liệu");
    }
};
exports.encryptDataOptimized = encryptDataOptimized;
/**
 * Tối ưu XOR decryption với batch processing
 */
const decryptDataOptimized = (encryptedData, key, iv) => {
    try {
        const combinedKey = key + iv;
        const keyLength = combinedKey.length;
        // Convert hex to Buffer directly (most efficient)
        const encrypted = Buffer.from(encryptedData, "hex");
        // Pre-allocate Buffer for better performance
        const decrypted = Buffer.alloc(encrypted.length);
        // Ultra-optimized XOR using Buffer operations
        for (let i = 0; i < encrypted.length; i++) {
            decrypted[i] = encrypted[i] ^ combinedKey.charCodeAt(i % keyLength);
        }
        // Convert back to string using Buffer.toString (fastest)
        return decrypted.toString("utf8");
    }
    catch (error) {
        console.error("Lỗi khi giải mã:", error);
        throw new Error("Không thể giải mã dữ liệu");
    }
};
exports.decryptDataOptimized = decryptDataOptimized;
/**
 * Legacy XOR encryption (backward compatibility)
 */
const encryptData = (data, key, iv) => {
    try {
        const utf8Bytes = Buffer.from(data, "utf8");
        const combinedKey = key + iv;
        let encrypted = "";
        for (let i = 0; i < utf8Bytes.length; i++) {
            const dataByte = utf8Bytes[i];
            const keyByte = combinedKey.charCodeAt(i % combinedKey.length);
            const encryptedByte = dataByte ^ keyByte;
            encrypted += encryptedByte.toString(16).padStart(2, "0");
        }
        return encrypted;
    }
    catch (error) {
        console.error("Lỗi khi mã hóa:", error);
        throw new Error("Không thể mã hóa dữ liệu");
    }
};
exports.encryptData = encryptData;
/**
 * Legacy XOR decryption (backward compatibility)
 */
const decryptData = (encryptedData, key, iv) => {
    try {
        const combinedKey = key + iv;
        const bytes = [];
        for (let i = 0; i < encryptedData.length; i += 2) {
            const encByte = parseInt(encryptedData.substr(i, 2), 16);
            const keyByte = combinedKey.charCodeAt((i / 2) % combinedKey.length);
            const decByte = encByte ^ keyByte;
            bytes.push(decByte);
        }
        const buffer = Buffer.from(bytes);
        return buffer.toString("utf8");
    }
    catch (error) {
        console.error("Lỗi khi giải mã:", error);
        throw new Error("Không thể giải mã dữ liệu");
    }
};
exports.decryptData = decryptData;
/**
 * ChaCha20-like simple stream cipher (ultra-fast)
 * Sử dụng cho dữ liệu lớn
 */
const streamEncrypt = (data, key, iv) => {
    try {
        const encoder = new TextEncoder();
        const dataBytes = encoder.encode(data);
        const keyBytes = new TextEncoder().encode(key.substring(0, 32));
        const ivBytes = new TextEncoder().encode(iv.substring(0, 16));
        // Simple stream generation (pseudo-ChaCha20)
        const stream = new Uint8Array(dataBytes.length);
        let counter = 0;
        for (let i = 0; i < dataBytes.length; i += 64) {
            // Generate 64-byte block
            const block = generateStreamBlock(keyBytes, ivBytes, counter++);
            const blockSize = Math.min(64, dataBytes.length - i);
            for (let j = 0; j < blockSize; j++) {
                stream[i + j] = block[j];
            }
        }
        // XOR with stream
        const encrypted = new Uint8Array(dataBytes.length);
        for (let i = 0; i < dataBytes.length; i++) {
            encrypted[i] = dataBytes[i] ^ stream[i];
        }
        return Array.from(encrypted, (byte) => byte.toString(16).padStart(2, "0")).join("");
    }
    catch (error) {
        console.error("Lỗi khi mã hóa stream:", error);
        throw new Error("Không thể mã hóa dữ liệu");
    }
};
exports.streamEncrypt = streamEncrypt;
/**
 * Stream decryption (ChaCha20-like)
 */
const streamDecrypt = (encryptedData, key, iv) => {
    try {
        const keyBytes = new TextEncoder().encode(key.substring(0, 32));
        const ivBytes = new TextEncoder().encode(iv.substring(0, 16));
        const dataLength = encryptedData.length / 2;
        // Parse encrypted data
        const encrypted = new Uint8Array(dataLength);
        for (let i = 0; i < dataLength; i++) {
            encrypted[i] = parseInt(encryptedData.substr(i * 2, 2), 16);
        }
        // Generate same stream
        const stream = new Uint8Array(dataLength);
        let counter = 0;
        for (let i = 0; i < dataLength; i += 64) {
            const block = generateStreamBlock(keyBytes, ivBytes, counter++);
            const blockSize = Math.min(64, dataLength - i);
            for (let j = 0; j < blockSize; j++) {
                stream[i + j] = block[j];
            }
        }
        // XOR to decrypt
        const decrypted = new Uint8Array(dataLength);
        for (let i = 0; i < dataLength; i++) {
            decrypted[i] = encrypted[i] ^ stream[i];
        }
        const decoder = new TextDecoder();
        return decoder.decode(decrypted);
    }
    catch (error) {
        console.error("Lỗi khi giải mã stream:", error);
        throw new Error("Không thể giải mã dữ liệu");
    }
};
exports.streamDecrypt = streamDecrypt;
/**
 * Generate pseudo-random stream block
 */
function generateStreamBlock(key, iv, counter) {
    const block = new Uint8Array(64);
    // Simple mixing function
    for (let i = 0; i < 64; i++) {
        const keyIndex = i % key.length;
        const ivIndex = i % iv.length;
        const counterByte = (counter + i) & 0xff;
        block[i] = (key[keyIndex] ^ iv[ivIndex] ^ counterByte ^ (i * 123)) & 0xff;
    }
    return block;
}
/**
 * Mã hóa object với thuật toán tối ưu
 * Tự động chọn thuật toán dựa trên kích thước data
 */
const encryptObjectOptimized = (obj) => {
    const token = (0, exports.generateRandomKey)();
    const salt = (0, exports.generateRandomIV)();
    const jsonData = JSON.stringify(obj);
    // Chọn thuật toán - based on performance testing, XOR tối ưu là nhanh nhất
    let appConfigData;
    let algorithm;
    // Luôn sử dụng XOR tối ưu vì nó nhanh nhất trong mọi trường hợp test
    appConfigData = (0, exports.encryptDataOptimized)(jsonData, token, salt);
    algorithm = "xor-optimized";
    return {
        appConfigData,
        token,
        salt,
        algorithm,
    };
};
exports.encryptObjectOptimized = encryptObjectOptimized;
/**
 * Giải mã object với detection thuật toán
 */
const decryptObjectOptimized = (appConfigData, token, salt, algorithm) => {
    let decryptedJson;
    // Tự động detect algorithm nếu không được cung cấp
    if (!algorithm) {
        algorithm = "xor-optimized"; // Default fallback
    }
    switch (algorithm) {
        case "stream":
            decryptedJson = (0, exports.streamDecrypt)(appConfigData, token, salt);
            break;
        case "xor-optimized":
            decryptedJson = (0, exports.decryptDataOptimized)(appConfigData, token, salt);
            break;
        default:
            // Fallback to legacy
            decryptedJson = (0, exports.decryptData)(appConfigData, token, salt);
    }
    return JSON.parse(decryptedJson);
};
exports.decryptObjectOptimized = decryptObjectOptimized;
/**
 * Legacy object encryption (backward compatibility)
 */
const encryptObject = (obj) => {
    const key = (0, exports.generateRandomKey)();
    const iv = (0, exports.generateRandomIV)();
    const jsonData = JSON.stringify(obj);
    const encryptedData = (0, exports.encryptData)(jsonData, key, iv);
    return {
        encryptedData,
        key,
        iv,
    };
};
exports.encryptObject = encryptObject;
/**
 * Legacy object decryption (backward compatibility)
 */
const decryptObject = (encryptedData, key, iv) => {
    const decryptedJson = (0, exports.decryptData)(encryptedData, key, iv);
    return JSON.parse(decryptedJson);
};
exports.decryptObject = decryptObject;
//# sourceMappingURL=encryption.js.map