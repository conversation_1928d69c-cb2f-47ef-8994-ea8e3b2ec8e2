# Ứng dụng Xá<PERSON> thực Google với JWT và Giới hạn Thiết bị

Ứng dụng Node.js sử dụng passport-google-oauth2.0 để xác thực người dùng, JWT cho phiên đăng nhập, và MongoDB để lưu trữ người dùng. Đặc biệt, ứng dụng chỉ cho phép mỗi tài khoản đăng nhập trên một thiết bị tại một thời điểm.

## Tính năng

- Đăng nhập với Google OAuth 2.0
- X<PERSON>c thực bằng JWT thay vì session
- Lưu thông tin người dùng trong MongoDB
- Giới hạn đăng nhập cho mỗi tài khoản (chỉ một thiết bị tại một thời điểm)
- Đ<PERSON>ng xuất sẽ cho phép đăng nhập lại từ thiết bị khác

## Tính năng theo dõi tiến độ học tập

Hệ thống cung cấp tính năng theo dõi tiến độ học tập của người dùng theo từng môn học. Tiến độ được tính dựa trên số câu trả lời đúng chia cho tổng số câu hỏi của môn học.

### Cách hoạt động

1. Khi người dùng hoàn thành một bài kiểm tra (quizizz hoặc google form), hệ thống sẽ tự động cập nhật tiến độ học tập.
2. Hệ thống lưu lại điểm số cao nhất của người dùng cho mỗi đề thi.
3. Tiến độ học tập được tính bằng: (Tổng số câu trả lời đúng / Tổng số câu hỏi của môn học) \* 100%.

### Cập nhật tiến độ học tập

Để cập nhật lại tiến độ học tập của tất cả người dùng:

```bash
npm run recalculate-all-progress
```

Để cập nhật lại tiến độ học tập của một người dùng cụ thể:

```bash
npm run recalculate-user-progress <userId>
```

### Mô hình dữ liệu

Tiến độ học tập được lưu trong collection `userprogresses` với cấu trúc:

```javascript
{
  "user_id": ObjectId("681c1060aaa14feffa0b7d80"),
  "product_id": ObjectId("6724ec945e8643ec69643f9e"),
  "best_exam_attempts": [
    {
      "exam_id": ObjectId("67f1dc87a8aaf7fa4ff68de2"),
      "score": 70,
      "correct_answers": 7,
      "total_questions": 10,
      "completed_at": ISODate("2025-05-14T08:00:00.000Z")
    }
  ],
  "total_correct_answers": 7,
  "total_questions": 100,
  "progress_percentage": 7,
  "last_updated": ISODate("2025-05-14T08:00:00.000Z")
}
```

## Yêu cầu

- Node.js
- MongoDB
- Tài khoản Google Developer và Google OAuth 2.0 credentials

## Cài đặt

1. Clone repository

2. Cài đặt các gói phụ thuộc

```
npm install
```

3. Tạo file .env trong thư mục gốc và cấu hình các biến môi trường

```
PORT=5000
MONGODB_URI=mongodb://localhost:27017/google-auth
GOOGLE_CLIENT_ID=your_client_id
GOOGLE_CLIENT_SECRET=your_client_secret
CALLBACK_URL=http://localhost:5000/auth/google/callback
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRATION=90d
```

4. Khởi động ứng dụng

```
npm run dev
```

## Hướng dẫn sử dụng

1. Truy cập http://localhost:5000 để bắt đầu
2. Nhấp vào "Đăng nhập với Google" để xác thực
3. Sau khi xác thực thành công, bạn sẽ được chuyển hướng đến trang profile
4. Để đăng xuất, gọi API `/auth/logout`
5. Nếu bạn cố gắng đăng nhập từ thiết bị khác khi chưa đăng xuất, hệ thống sẽ từ chối và yêu cầu đăng xuất thiết bị cũ trước

## API Endpoints

- GET `/auth/google`: Bắt đầu quá trình xác thực Google
- GET `/auth/google/callback`: Callback URL cho xác thực Google
- GET `/auth/logout`: Đăng xuất (xóa token hoạt động)
- GET `/auth/status`: Kiểm tra trạng thái đăng nhập hiện tại
- GET `/profile`: Trang profile của người dùng (được bảo vệ)

## Lưu ý về giới hạn thiết bị

Ứng dụng lưu trữ JWT token đang hoạt động trong cơ sở dữ liệu. Khi người dùng đăng nhập, một token mới được tạo và lưu vào trường `activeToken`. Mọi yêu cầu đều phải có token khớp với `activeToken` trong cơ sở dữ liệu. Nếu một thiết bị khác cố gắng đăng nhập, hệ thống sẽ kiểm tra xem người dùng đã có `activeToken` chưa và từ chối nếu đã có.

Khi người dùng đăng xuất, `activeToken` sẽ bị xóa, cho phép đăng nhập từ một thiết bị khác.
