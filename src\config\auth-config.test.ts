/**
 * Simple test file to verify authentication configuration values
 * Run with: npx ts-node src/config/auth-config.test.ts
 */

import { AuthConfig, AuthUtils, AuthConfigInfo } from "./auth-config";

console.log("🔐 Authentication Configuration Test");
console.log("=====================================");

// Test configuration values
console.log("\n📊 Configuration Values:");
console.log(`Default Login Duration: ${AuthConfigInfo.durations.defaultLogin}`);
console.log(`JWT Expiration: ${AuthConfigInfo.durations.jwtExpiration}`);
console.log(
  `Session Expiration: ${AuthConfigInfo.durations.sessionExpiration}`
);
console.log(`Cookie Max Age: ${AuthConfigInfo.durations.cookieMaxAge}`);
console.log(
  `Reset Token Expiration: ${AuthConfigInfo.durations.resetTokenExpiration}`
);

// Test helper functions
console.log("\n🔧 Helper Functions:");
console.log(
  `JWT Expiration in Seconds: ${AuthUtils.getJwtExpirationInSeconds()}`
);
console.log(`JWT Expiration String: ${AuthUtils.getJwtExpirationString()}`);

// Test cookie options
console.log("\n🍪 Cookie Options:");
console.log(JSON.stringify(AuthUtils.getCookieOptions(), null, 2));

// Test consistency
console.log("\n✅ Configuration Consistency:");
console.log(
  `All configured for 90 days: ${AuthConfigInfo.summary.allConfiguredFor90Days}`
);

// Test expiration calculation
const expirationDate = AuthUtils.getExpirationDate();
const daysFromNow = Math.round(
  (expirationDate.getTime() - Date.now()) / (1000 * 60 * 60 * 24)
);
console.log(`Days until expiration: ${daysFromNow}`);

// Verify 90-day configuration
const expectedDays = 90;
const isCorrect = daysFromNow === expectedDays;
console.log(`✅ 90-day configuration verified: ${isCorrect}`);

if (isCorrect) {
  console.log(
    "\n🎉 All authentication timeouts are correctly configured for 90 days!"
  );
} else {
  console.log("\n❌ Configuration error detected!");
  process.exit(1);
}

export {};
