import mongoose, { Schema, Document } from "mongoose";

export interface MemoryQuestion extends Document {
  userId: mongoose.Types.ObjectId;
  productId: mongoose.Types.ObjectId;
  questionId: mongoose.Types.ObjectId;
  examId?: mongoose.Types.ObjectId;
  source: "manual" | "auto";
  practiceCount: number;
  correctCount: number;
  lastPracticed?: Date;
  createdAt: Date;
  isActive: boolean;
  group?: string; // Group name for organizing questions
}

const memoryQuestionSchema = new Schema({
  userId: {
    type: Schema.Types.ObjectId,
    ref: "User",
    required: true,
    index: true,
  },
  productId: {
    type: Schema.Types.ObjectId,
    ref: "Product",
    required: true,
    index: true,
  },
  questionId: { type: Schema.Types.ObjectId, ref: "Question", required: true },
  examId: { type: Schema.Types.ObjectId, ref: "Exam" }, // Optional - để trace nguồn gốc câu hỏi
  source: {
    type: String,
    enum: ["manual", "auto"],
    required: true,
    default: "manual",
  },
  practiceCount: { type: Number, default: 0 },
  correctCount: { type: Number, default: 0 }, // Số lần trả lời đúng
  lastPracticed: { type: Date },
  createdAt: { type: Date, default: Date.now },
  isActive: { type: Boolean, default: true },
  group: { type: String, default: null }, // Group name for organizing questions
});

// Compound index để tối ưu queries
memoryQuestionSchema.index({ userId: 1, productId: 1, isActive: 1 });
memoryQuestionSchema.index({ userId: 1, questionId: 1 }, { unique: true });
memoryQuestionSchema.index({ userId: 1, productId: 1, group: 1 }); // Index for group-based queries

export default mongoose.model<MemoryQuestion>(
  "MemoryQuestion",
  memoryQuestionSchema
);
