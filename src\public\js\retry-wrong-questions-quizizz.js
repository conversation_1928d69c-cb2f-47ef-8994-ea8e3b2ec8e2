// Biến toàn cục
let examData = {};
let wrongQuestions = [];
let currentQuestionIndex = 0;
let answeredQuestions = 0;
let timerInterval = null;
let startTime = null;
let elapsedTime = 0;
let isSubmitting = false;
let timeExpired = false;
let userAnswers = [];
let questionResults = [];

// Khởi tạo khi trang tải xong
document.addEventListener("DOMContentLoaded", function () {
  // Lấy dữ liệu bài thi từ JSON
  try {
    examData = JSON.parse(document.getElementById("exam-data").textContent);
  } catch (error) {
    console.error("Lỗi khi phân tích dữ liệu bài thi:", error);
    return;
  }

  // Lấy dữ liệu câu hỏi sai từ localStorage thường

  const rawData = localStorage.getItem("wrongQuestions");

  let wrongQuestionsData = null;

  try {
    wrongQuestionsData = rawData ? JSON.parse(rawData) : null;
  } catch (parseError) {
    console.error("❌ Lỗi parse JSON từ localStorage:", parseError);
    wrongQuestionsData = null;
  }

  if (
    !wrongQuestionsData ||
    wrongQuestionsData.examId !== (examData.examId || examData.id)
  ) {
    alert(
      "Không tìm thấy dữ liệu câu hỏi sai hoặc dữ liệu không khớp với bài thi hiện tại!"
    );
    window.location.href = "/home";
    return;
  }

  wrongQuestions = wrongQuestionsData.questions;

  // Cập nhật thông tin bài thi
  document.getElementById("total-questions").textContent =
    wrongQuestions.length;
  document.getElementById("total-wrong-questions").textContent =
    wrongQuestions.length;

  // Khởi tạo mảng kết quả người dùng
  userAnswers = Array(wrongQuestions.length).fill(null);

  // Bắt đầu đếm thời gian
  startTimer();

  // Hiển thị câu hỏi đầu tiên
  displayQuestion(0);

  // Thiết lập các sự kiện
  setupEventListeners();
});

// Hiển thị câu hỏi theo index
function displayQuestion(index) {
  if (index < 0 || index >= wrongQuestions.length) return;

  currentQuestionIndex = index;
  const question = wrongQuestions[index];

  // Cập nhật số thứ tự câu hỏi
  document.getElementById("current-question").textContent = index + 1;

  // Lấy container câu hỏi
  const questionContainer = document.getElementById("question-container");

  // Xóa tất cả các lớp CSS hiện có từ container
  questionContainer.className = "";
  questionContainer.classList.add(
    "question-card",
    "mb-4",
    "h-full",
    "flex",
    "flex-col"
  );

  // Tìm đáp án đúng
  const correctOptionIndex = question.options.findIndex(
    (opt) => typeof opt === "object" && opt.isCorrect
  );

  // Cấu trúc HTML của câu hỏi - Tối ưu bố cục
  let questionHTML = `
    <div class="bg-white rounded-t-lg flex flex-col">
      <div class="p-4 pb-3">
        <h2 class="text-xl font-bold text-gray-800 mb-2">${question.text}</h2>
        ${
          question.image
            ? `<img src="${question.image}" alt="Hình ảnh câu hỏi" class="max-w-full h-auto rounded mb-2">`
            : ""
        }
      </div>
      <div class="px-4 pb-4 grid grid-cols-1 md:grid-cols-2 gap-3" id="options-container">
  `;

  // Tạo các lựa chọn
  if (question.options && question.options.length > 0) {
    question.options.forEach((option, optIndex) => {
      const optionText = typeof option === "object" ? option.text : option;

      // Loại bỏ tiền tố "A.", "B.", "C.", "D." nếu có
      let cleanOptionText = optionText;
      // Sử dụng regex để tìm và loại bỏ tiền tố theo nhiều định dạng:
      // 1. Chữ cái + dấu chấm: A. B. C. D.
      // 2. Chữ cái + dấu ngoặc đóng: A) B) C) D)
      // 3. Số + dấu chấm: 1. 2. 3. 4.
      // 4. Số + dấu ngoặc đóng: 1) 2) 3) 4)
      const prefixRegex = /^\s*(?:[A-D]|[1-9])[\.\)][\s\u00A0]*/i;
      if (prefixRegex.test(cleanOptionText)) {
        cleanOptionText = cleanOptionText.replace(prefixRegex, "");
      }

      const optionLetter = String.fromCharCode(65 + optIndex); // A, B, C, D

      const optionClasses = [
        "p-3",
        "rounded-lg",
        "border-2",
        "border-gray-200",
        "cursor-pointer",
        "hover:bg-gray-50",
        "text-gray-700",
        "transition-colors",
        "option-button",
        "relative",
      ];

      // Thêm các lớp màu sắc dựa trên vị trí
      const colorClasses =
        optIndex === 0
          ? ["option-red", "hover:bg-red-50", "hover:border-red-300"]
          : optIndex === 1
          ? ["option-blue", "hover:bg-blue-50", "hover:border-blue-300"]
          : optIndex === 2
          ? ["option-green", "hover:bg-green-50", "hover:border-green-300"]
          : ["option-yellow", "hover:bg-yellow-50", "hover:border-yellow-300"];

      optionClasses.push(...colorClasses);

      // Kiểm tra nếu người dùng đã chọn đáp án này
      const isSelected = userAnswers[index] === optIndex;
      const isCorrectOption = optIndex === correctOptionIndex;
      const isAnswered = userAnswers[index] !== null;

      // Thêm lớp và màu sắc dựa trên trạng thái
      if (isSelected) {
        if (isCorrectOption) {
          // Đáp án được chọn là đúng
          optionClasses.push("selected", "border-green-500", "bg-green-50");
        } else {
          // Đáp án được chọn là sai
          optionClasses.push("selected", "border-red-500", "bg-red-50");
        }
      } else if (isAnswered && isCorrectOption) {
        // Hiển thị đáp án đúng khi người dùng đã chọn sai
        optionClasses.push("border-green-500", "bg-green-50");
      }

      // Tạo nội dung hiển thị cho đáp án
      let iconHTML = "";

      // Nếu người dùng đã trả lời, hiển thị các biểu tượng đúng/sai
      if (isAnswered) {
        if (isSelected && isCorrectOption) {
          // Đáp án đúng và được chọn
          iconHTML =
            '<span class="absolute top-2 right-2 text-green-600"><i class="fas fa-check-circle"></i></span>';
        } else if (isSelected && !isCorrectOption) {
          // Đáp án sai và được chọn
          iconHTML =
            '<span class="absolute top-2 right-2 text-red-600"><i class="fas fa-times-circle"></i></span>';
        } else if (isCorrectOption) {
          // Đáp án đúng nhưng không được chọn
          iconHTML =
            '<span class="absolute top-2 right-2 text-green-600"><i class="fas fa-check-circle"></i></span>';
        }
      }

      questionHTML += `
        <div 
          class="${optionClasses.join(" ")}"
          data-option-index="${optIndex}"
          onclick="selectOption(${optIndex})"
        >
          <div class="flex items-center">
            <span class="inline-flex items-center justify-center h-6 w-6 rounded-full bg-gray-100 text-gray-700 font-medium mr-3 flex-shrink-0">
              ${optionLetter}
            </span>
            <span class="flex-1">${cleanOptionText}</span>
          </div>
          ${iconHTML}
        </div>
      `;
    });
  }

  questionHTML += `
      </div>
    </div>
    <div class="bg-gray-100 rounded-b-lg p-3 flex justify-between items-center mb-4">
      <button 
        id="prev-button" 
        class="bg-white py-2 px-3 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ${
          index === 0 ? "opacity-50 cursor-not-allowed" : ""
        }"
        ${index === 0 ? "disabled" : ""}
        onclick="navigateQuestion(-1)"
      >
        <i class="fas fa-arrow-left mr-1"></i> Câu trước
      </button>
      <div class="text-sm text-gray-500">
        <span id="question-counter">${index + 1}/${wrongQuestions.length}</span>
      </div>
      <button 
        id="next-button" 
        class="bg-indigo-600 py-2 px-3 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        onclick="navigateQuestion(1)"
      >
        ${
          index === wrongQuestions.length - 1
            ? "Xem kết quả"
            : 'Câu kế <i class="fas fa-arrow-right ml-1"></i>'
        }
      </button>
    </div>
  `;

  questionContainer.innerHTML = questionHTML;

  // Cập nhật thanh tiến độ
  updateProgress();

  // Hiển thị nút Xem kết quả nếu đã trả lời tất cả câu hỏi
  if (
    answeredQuestions === wrongQuestions.length &&
    index === wrongQuestions.length - 1
  ) {
    document.getElementById("next-button").textContent = "Xem kết quả";
  }

  // Vô hiệu hóa các lựa chọn nếu người dùng đã chọn đáp án
  if (userAnswers[index] !== null) {
    const options = document.querySelectorAll(".option-button");
    options.forEach((option) => {
      option.style.pointerEvents = "none";
      option.classList.add("cursor-default");
      option.classList.remove("hover:bg-gray-50", "cursor-pointer");
    });
  }
}

// Chọn đáp án
function selectOption(optionIndex) {
  if (isSubmitting) return;

  // Lưu câu trả lời của người dùng
  userAnswers[currentQuestionIndex] = optionIndex;

  // Đánh dấu là đã trả lời nếu chưa
  if (userAnswers.filter((ans) => ans !== null).length > answeredQuestions) {
    answeredQuestions = userAnswers.filter((ans) => ans !== null).length;
  }

  // Cập nhật giao diện các lựa chọn
  const optionsContainer = document.getElementById("options-container");
  const options = optionsContainer.querySelectorAll(".option-button");

  // Lấy thông tin câu hỏi hiện tại
  const question = wrongQuestions[currentQuestionIndex];

  // Tìm đáp án đúng
  const correctOptionIndex = question.options.findIndex(
    (opt) => typeof opt === "object" && opt.isCorrect
  );

  // Kiểm tra đáp án đã chọn có đúng không
  const isCorrect = optionIndex === correctOptionIndex;

  options.forEach((option, index) => {
    // Xóa tất cả các lớp trước khi thêm lớp mới
    option.classList.remove(
      "selected",
      "border-purple-500",
      "bg-purple-50",
      "border-green-500",
      "bg-green-50",
      "border-red-500",
      "bg-red-50"
    );

    // Xóa biểu tượng đúng/sai nếu có
    const iconElement = option.querySelector(".result-icon");
    if (iconElement) {
      iconElement.remove();
    }

    if (index === optionIndex) {
      // Đánh dấu lựa chọn người dùng
      if (isCorrect) {
        // Đáp án đúng - hiển thị màu xanh
        option.classList.add("selected", "border-green-500", "bg-green-50");

        // Thêm biểu tượng đúng
        const icon = document.createElement("span");
        icon.className = "result-icon absolute top-2 right-2 text-green-600";
        icon.innerHTML = '<i class="fas fa-check-circle"></i>';
        option.style.position = "relative";
        option.appendChild(icon);
      } else {
        // Đáp án sai - hiển thị màu đỏ
        option.classList.add("selected", "border-red-500", "bg-red-50");

        // Thêm biểu tượng sai
        const icon = document.createElement("span");
        icon.className = "result-icon absolute top-2 right-2 text-red-600";
        icon.innerHTML = '<i class="fas fa-times-circle"></i>';
        option.style.position = "relative";
        option.appendChild(icon);
      }
    } else if (index === correctOptionIndex && !isCorrect) {
      // Hiển thị đáp án đúng khi người dùng chọn sai
      option.classList.add("border-green-500", "bg-green-50");

      // Thêm biểu tượng đúng
      const icon = document.createElement("span");
      icon.className = "result-icon absolute top-2 right-2 text-green-600";
      icon.innerHTML = '<i class="fas fa-check-circle"></i>';
      option.style.position = "relative";
      option.appendChild(icon);
    }
  });

  // Cập nhật thanh tiến độ
  updateProgress();

  // Hiển thị nút Xem kết quả nếu đã trả lời tất cả câu hỏi
  if (currentQuestionIndex === wrongQuestions.length - 1) {
    document.getElementById("next-button").textContent = "Xem kết quả";
  }

  // Vô hiệu hóa các lựa chọn sau khi người dùng đã chọn
  options.forEach((option) => {
    option.style.pointerEvents = "none";
    option.classList.add("cursor-default");
    option.classList.remove("hover:bg-gray-50", "cursor-pointer");
  });
}

// Di chuyển qua lại giữa các câu hỏi
function navigateQuestion(direction) {
  if (isSubmitting) return;

  const newIndex = currentQuestionIndex + direction;

  // Kiểm tra khi nhấn nút "Tiếp theo" ở câu cuối cùng
  if (direction > 0 && currentQuestionIndex === wrongQuestions.length - 1) {
    // Nộp bài nếu đã trả lời tất cả câu hỏi
    if (answeredQuestions === wrongQuestions.length) {
      submitExam();
    } else {
      // Hiển thị thông báo nếu chưa trả lời hết
      showWarningPopup();
    }
    return;
  }

  // Kiểm tra phạm vi hợp lệ
  if (newIndex >= 0 && newIndex < wrongQuestions.length) {
    displayQuestion(newIndex);
    updateProgress();
  }
}

// Hiển thị popup cảnh báo chưa trả lời hết câu hỏi
function showWarningPopup() {
  // Lấy danh sách các câu hỏi chưa trả lời
  const unansweredIndexes = userAnswers
    .map((answer, index) => (answer === null ? index + 1 : null))
    .filter((index) => index !== null);

  // Hiển thị popup
  const warningPopup = document.getElementById("warning-popup");
  const unansweredList = document.getElementById("unanswered-questions");

  // Cập nhật nút nộp bài
  document.getElementById("submit-anyway").textContent = "Xem kết quả luôn";

  unansweredList.innerHTML = "";
  unansweredIndexes.forEach((index) => {
    const item = document.createElement("span");
    item.className =
      "inline-block bg-red-100 text-red-800 px-2 py-1 rounded m-1 cursor-pointer";
    item.textContent = index;
    item.onclick = function () {
      hideWarningPopup();
      displayQuestion(index - 1);
    };
    unansweredList.appendChild(item);
  });

  warningPopup.classList.remove("hidden");
}

// Ẩn popup cảnh báo
function hideWarningPopup() {
  document.getElementById("warning-popup").classList.add("hidden");
}

// Hiển thị popup xác nhận nộp bài
function showSubmitConfirmPopup() {
  // Cập nhật tiêu đề và nội dung
  document.querySelector("#submit-confirm-popup h3").textContent =
    "Xác nhận xem kết quả";
  document.querySelector("#submit-confirm-popup p").textContent =
    "Bạn có chắc chắn muốn xem kết quả? Thao tác này không thể hoàn tác.";

  // Cập nhật nút xác nhận
  document.getElementById("confirm-submit").textContent = "Xem kết quả";

  // Hiển thị popup
  document.getElementById("submit-confirm-popup").classList.remove("hidden");
}

// Ẩn popup xác nhận nộp bài
function hideSubmitConfirmPopup() {
  document.getElementById("submit-confirm-popup").classList.add("hidden");
}

// Cập nhật tiến độ làm bài
function updateProgress() {
  // Cập nhật số câu đã trả lời
  document.getElementById("answered-questions").textContent = answeredQuestions;

  // Cập nhật thanh tiến độ
  const progressBar = document.getElementById("progress-bar");
  const progressPercentage = (answeredQuestions / wrongQuestions.length) * 100;
  progressBar.style.width = `${progressPercentage}%`;

  // Cập nhật danh sách câu hỏi
  const questionList = document.getElementById("question-list");
  questionList.innerHTML = "";

  wrongQuestions.forEach((_, index) => {
    const item = document.createElement("div");
    item.className = "question-item";

    // Thêm các lớp CSS dựa trên trạng thái
    if (index === currentQuestionIndex) {
      item.classList.add("current");
    }

    if (userAnswers[index] !== null) {
      item.classList.add("answered");
    }

    item.onclick = function () {
      displayQuestion(index);
    };

    item.textContent = index + 1;
    questionList.appendChild(item);
  });
}

// Khởi động bộ đếm thời gian
function startTimer() {
  startTime = new Date();

  timerInterval = setInterval(function () {
    const currentTime = new Date();
    elapsedTime = Math.floor((currentTime - startTime) / 1000);

    // Kiểm tra nếu hết thời gian
    if (examData.duration > 0 && elapsedTime >= examData.duration * 60) {
      clearInterval(timerInterval);
      timeExpired = true;
      submitExam();
      return;
    }

    // Cập nhật hiển thị thời gian
    updateTimerDisplay();
  }, 1000);

  // Cập nhật hiển thị thời gian ban đầu
  updateTimerDisplay();
}

// Cập nhật hiển thị thời gian
function updateTimerDisplay() {
  const timer = document.getElementById("timer");
  let remainingTime;

  if (examData.duration > 0) {
    // Nếu có thời hạn, hiển thị thời gian còn lại
    const totalSeconds = examData.duration * 60;
    remainingTime = Math.max(0, totalSeconds - elapsedTime);
  } else {
    // Nếu không có thời hạn, hiển thị thời gian đã trôi qua
    remainingTime = elapsedTime;
  }

  const hours = Math.floor(remainingTime / 3600);
  const minutes = Math.floor((remainingTime % 3600) / 60);
  const seconds = remainingTime % 60;

  timer.textContent = `${hours.toString().padStart(2, "0")}:${minutes
    .toString()
    .padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;

  // Thay đổi màu khi sắp hết thời gian
  if (examData.duration > 0 && remainingTime <= 300) {
    timer.parentElement.classList.add("animate-pulse", "bg-red-600");
  }
}

// Thiết lập các sự kiện
function setupEventListeners() {
  // Xử lý sự kiện nút hủy nộp bài
  document
    .getElementById("cancel-submit")
    .addEventListener("click", function () {
      hideSubmitConfirmPopup();
    });

  // Xử lý sự kiện nút xác nhận nộp bài
  document
    .getElementById("confirm-submit")
    .addEventListener("click", function () {
      submitExam();
    });

  // Xử lý sự kiện nút ở lại làm bài trong cảnh báo
  document
    .getElementById("continue-button")
    .addEventListener("click", function () {
      hideWarningPopup();
    });

  // Xử lý sự kiện nút nộp bài trong cảnh báo
  document
    .getElementById("submit-anyway")
    .addEventListener("click", function () {
      hideWarningPopup();
      showSubmitConfirmPopup();
    });

  // Thêm sự kiện ngăn người dùng rời khỏi trang
  window.addEventListener("beforeunload", function (e) {
    if (!isSubmitting && !timeExpired) {
      e.preventDefault();
      e.returnValue = "";
      return "";
    }
  });

  // Xử lý sự kiện nút xem lại đáp án
  document
    .getElementById("review-toggle")
    .addEventListener("click", function () {
      const reviewList = document.getElementById("review-list");
      const icon = this.querySelector("i");

      if (reviewList.classList.contains("hidden")) {
        reviewList.classList.remove("hidden");
        icon.classList.remove("fa-chevron-down");
        icon.classList.add("fa-chevron-up");
      } else {
        reviewList.classList.add("hidden");
        icon.classList.remove("fa-chevron-up");
        icon.classList.add("fa-chevron-down");
      }
    });
}

// Xử lý nộp bài
function submitExam() {
  if (isSubmitting) return;

  isSubmitting = true;
  clearInterval(timerInterval);

  // Ẩn popup nếu đang hiển thị
  hideSubmitConfirmPopup();
  hideWarningPopup();

  // Tính toán kết quả
  questionResults = [];
  let correctCount = 0;

  wrongQuestions.forEach((question, index) => {
    const selectedOptionIndex = userAnswers[index];

    // Xử lý trường hợp người dùng không chọn đáp án
    if (selectedOptionIndex === null) {
      questionResults.push({
        question,
        selectedOptionIndex: null,
        isCorrect: false,
      });
      return;
    }

    // Tìm đáp án đúng
    const correctOptionIndex = question.options.findIndex(
      (opt) => typeof opt === "object" && opt.isCorrect
    );

    // Kiểm tra đáp án
    const isCorrect = selectedOptionIndex === correctOptionIndex;

    if (isCorrect) {
      correctCount++;
    }

    questionResults.push({
      question,
      selectedOptionIndex,
      isCorrect,
    });
  });

  // Cập nhật giao diện kết quả
  document.getElementById("exam-container").classList.add("hidden");
  document.getElementById("result-container").classList.remove("hidden");

  // Ẩn footer để không bị overlap với kết quả
  const footer = document.querySelector("footer");
  if (footer) {
    footer.style.display = "none";
  }

  // Cập nhật thông tin kết quả
  document.getElementById("correct-count").textContent = correctCount;
  document.getElementById("total-count").textContent = wrongQuestions.length;
  document.getElementById("score-percentage").textContent = Math.round(
    (correctCount / wrongQuestions.length) * 100
  );
  document.getElementById("time-taken").textContent = formatTime(elapsedTime);

  // Cập nhật danh sách câu hỏi tóm tắt
  updateSummaryList();

  // Cập nhật danh sách xem lại câu trả lời
  updateReviewList();

  // Xóa dữ liệu câu hỏi sai khỏi localStorage
  if (typeof SecureStorage !== "undefined") {
    SecureStorage.removeItem("wrongQuestions");
  } else {
    localStorage.removeItem("wrongQuestions");
  }
}

// Cập nhật danh sách câu hỏi tóm tắt
function updateSummaryList() {
  const summaryGrid = document.getElementById("summary-grid");
  summaryGrid.innerHTML = "";

  questionResults.forEach((result, index) => {
    const summaryItem = document.createElement("div");

    if (result.isCorrect) {
      summaryItem.className =
        "bg-green-100 text-green-800 text-center py-1 px-2 rounded";
      summaryItem.innerHTML = `<span>${index + 1}</span>`;
    } else {
      summaryItem.className =
        "bg-red-100 text-red-800 text-center py-1 px-2 rounded";
      summaryItem.innerHTML = `<span>${index + 1}</span>`;
    }

    summaryItem.addEventListener("click", function () {
      // Cuộn đến câu hỏi tương ứng trong phần xem lại
      const reviewItem = document.getElementById(`review-item-${index}`);
      if (reviewItem) {
        // Cuộn đến vị trí câu hỏi trong container scroll
        reviewItem.scrollIntoView({
          behavior: "smooth",
          block: "center",
          inline: "nearest",
        });
        reviewItem.classList.add("highlight");

        setTimeout(() => {
          reviewItem.classList.remove("highlight");
        }, 1500);
      }
    });

    summaryGrid.appendChild(summaryItem);
  });
}

// Cập nhật danh sách xem lại câu trả lời
function updateReviewList() {
  const reviewList = document.getElementById("review-list");
  reviewList.innerHTML = "";

  questionResults.forEach((result, index) => {
    const reviewItem = document.createElement("div");
    reviewItem.className = "mb-6 pb-4 border-b border-gray-200";
    reviewItem.id = `review-item-${index}`;

    const question = result.question;
    const answerOptions = question.options;

    // Tạo HTML cho các lựa chọn
    let optionsHTML =
      '<div class="grid grid-cols-1 md:grid-cols-2 gap-3 mt-3">';

    answerOptions.forEach((option, optIndex) => {
      const optionText = typeof option === "object" ? option.text : option;

      // Loại bỏ tiền tố "A.", "B.", "C.", "D." nếu có
      let cleanOptionText = optionText;
      const prefixRegex = /^\s*(?:[A-D]|[1-9])[\.\)][\s\u00A0]*/i;
      if (prefixRegex.test(cleanOptionText)) {
        cleanOptionText = cleanOptionText.replace(prefixRegex, "");
      }

      const optionLetter = String.fromCharCode(65 + optIndex); // A, B, C, D

      const isCorrect = typeof option === "object" && option.isCorrect;
      const isSelected = optIndex === result.selectedOptionIndex;

      let optionClass = "p-3 rounded-lg border";

      if (isSelected && isCorrect) {
        optionClass += " bg-green-50 border-green-500 text-green-800";
      } else if (isSelected && !isCorrect) {
        optionClass += " bg-red-50 border-red-500 text-red-800";
      } else if (isCorrect) {
        optionClass += " bg-green-50 border-green-300 text-green-800";
      } else {
        optionClass += " bg-gray-50 border-gray-200 text-gray-800";
      }

      optionsHTML += `
        <div class="${optionClass}">
          <div class="flex items-center">
            <span class="inline-flex items-center justify-center h-6 w-6 rounded-full bg-gray-100 text-gray-700 font-medium mr-3 flex-shrink-0">
              ${optionLetter}
            </span>
            <div class="flex-1">${cleanOptionText}</div>
            ${
              isSelected && isCorrect
                ? '<div class="ml-2 text-green-600"><i class="fas fa-check"></i></div>'
                : isSelected && !isCorrect
                ? '<div class="ml-2 text-red-600"><i class="fas fa-times"></i></div>'
                : isCorrect
                ? '<div class="ml-2 text-green-600"><i class="fas fa-check"></i></div>'
                : ""
            }
          </div>
        </div>
      `;
    });

    optionsHTML += "</div>";

    // Tìm đáp án đúng
    const correctOption = answerOptions.find(
      (opt) => typeof opt === "object" && opt.isCorrect
    );
    let correctText = correctOption
      ? correctOption.text
      : "Không tìm thấy đáp án đúng";

    // Loại bỏ tiền tố cho đáp án đúng
    const prefixRegex = /^\s*(?:[A-D]|[1-9])[\.\)][\s\u00A0]*/i;
    if (prefixRegex.test(correctText)) {
      correctText = correctText.replace(prefixRegex, "");
    }

    // Tạo badge cho trạng thái câu hỏi
    const statusBadge = result.isCorrect
      ? `<span class="bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded">Làm đúng</span>`
      : `<span class="bg-red-100 text-red-800 text-xs font-medium px-2 py-1 rounded">Làm sai</span>`;

    reviewItem.innerHTML = `
      <div class="flex items-center justify-between mb-3">
        <h3 class="text-lg font-medium text-gray-900">Câu ${index + 1}</h3>
        ${statusBadge}
      </div>
      <div class="mb-3">
        <p class="text-gray-700">${question.text}</p>
        ${
          question.image
            ? `<img src="${question.image}" alt="Hình ảnh câu hỏi" class="max-w-full h-auto rounded my-2">`
            : ""
        }
      </div>
      ${optionsHTML}
      ${
        !result.isCorrect && result.selectedOptionIndex !== null
          ? `<div class="mt-3 text-sm text-red-600">
            <i class="fas fa-info-circle mr-1"></i>
            Đáp án đúng: ${correctText}
           </div>`
          : ""
      }
      ${
        result.selectedOptionIndex === null
          ? `<div class="mt-3 text-sm text-orange-600">
            <i class="fas fa-exclamation-triangle mr-1"></i>
            Bạn chưa trả lời câu hỏi này. Đáp án đúng: ${correctText}
           </div>`
          : ""
      }
    `;

    reviewList.appendChild(reviewItem);
  });
}

// Format thời gian từ giây thành chuỗi hh:mm:ss
function formatTime(seconds) {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, "0")}:${secs
      .toString()
      .padStart(2, "0")}`;
  } else {
    return `${minutes}:${secs.toString().padStart(2, "0")}`;
  }
}
