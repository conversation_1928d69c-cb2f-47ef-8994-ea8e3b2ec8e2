"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getUserExamHistory = exports.saveExamHistory = void 0;
const ExamHistory_1 = __importDefault(require("../models/ExamHistory"));
class ExamHistoryController {
    /**
     * Lưu kết quả bài thi vào lịch sử
     */
    saveExamHistory(req, res) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a;
            try {
                const { examId, examName, score, totalQuestions, duration, examType } = req.body;
                if (!examId ||
                    !examName ||
                    score === undefined ||
                    !totalQuestions ||
                    !duration ||
                    !examType) {
                    return res.status(400).json({
                        success: false,
                        message: "Thiếu thông tin cần thiết để lưu lịch sử bài thi",
                    });
                }
                // Lấy userId từ user đã đăng nhập
                const userId = (_a = res.locals.user) === null || _a === void 0 ? void 0 : _a._id;
                if (!userId) {
                    return res.status(401).json({
                        success: false,
                        message: "Người dùng chưa đăng nhập",
                    });
                }
                // Tạo bản ghi lịch sử mới
                const examHistory = new ExamHistory_1.default({
                    examId,
                    examName,
                    userId,
                    score,
                    totalQuestions,
                    duration,
                    examType,
                    completedAt: new Date(),
                });
                yield examHistory.save();
                return res.status(201).json({
                    success: true,
                    message: "Đã lưu lịch sử bài thi thành công",
                    examHistory,
                });
            }
            catch (error) {
                console.error("Lỗi khi lưu lịch sử bài thi:", error);
                return res.status(500).json({
                    success: false,
                    message: "Đã xảy ra lỗi khi lưu lịch sử bài thi",
                    error: error.message,
                });
            }
        });
    }
    /**
     * Lấy lịch sử làm bài thi của người dùng
     */
    getUserExamHistory(req, res) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a;
            try {
                // Lấy userId từ user đã đăng nhập
                const userId = (_a = res.locals.user) === null || _a === void 0 ? void 0 : _a._id;
                if (!userId) {
                    return res.redirect("/auth/login");
                }
                // Lấy tham số phân trang từ query
                const page = parseInt(req.query.page) || 1;
                const limit = parseInt(req.query.limit) || 10;
                const skip = (page - 1) * limit;
                // Đếm tổng số bản ghi để tính số trang
                const totalRecords = yield ExamHistory_1.default.countDocuments({ userId });
                const totalPages = Math.ceil(totalRecords / limit);
                // Lấy lịch sử thi với phân trang, sắp xếp theo thời gian làm mới nhất
                const examHistories = yield ExamHistory_1.default.find({ userId })
                    .sort({ completedAt: -1 })
                    .skip(skip)
                    .limit(limit)
                    .lean();
                return res.render("exam/history", {
                    user: res.locals.user,
                    examHistories,
                    pagination: {
                        page,
                        limit,
                        totalPages,
                        totalRecords,
                        hasNext: page < totalPages,
                        hasPrev: page > 1,
                    },
                    title: "Lịch sử làm bài thi",
                });
            }
            catch (error) {
                console.error("Lỗi khi lấy lịch sử bài thi:", error);
                return res.status(500).render("error", {
                    message: "Đã xảy ra lỗi khi lấy lịch sử bài thi",
                    error: { status: 500, stack: error.message },
                    user: res.locals.user,
                });
            }
        });
    }
}
// Export các hàm xử lý để sử dụng trong route
const saveExamHistory = (req, res) => new ExamHistoryController().saveExamHistory(req, res);
exports.saveExamHistory = saveExamHistory;
const getUserExamHistory = (req, res) => new ExamHistoryController().getUserExamHistory(req, res);
exports.getUserExamHistory = getUserExamHistory;
//# sourceMappingURL=examHistory.controller.js.map