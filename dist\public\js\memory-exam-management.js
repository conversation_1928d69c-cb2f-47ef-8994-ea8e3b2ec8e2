/**
 * memory-exam-management.js
 * Module quản lý CRUD operations cho danh sách câu hỏi ghi nhớ
 *
 * Pattern: IIFE với namespace isolation
 */
(function (window) {
    "use strict";
    // Private variables và state
    let _config = {
        productId: null,
        apiBase: "/exam/memory",
        domSelectors: {
            container: "#memoryQuestionsContainer",
            sourceFilter: "#memorySourceFilter",
            examFilter: "#memoryExamFilter",
            selectedCount: "#selectedCount",
            bulkDeleteBtn: "#bulkDeleteBtn",
            pagination: "#memoryPagination",
        },
    };
    let _state = {
        currentPage: 1,
        totalPages: 1,
        itemsPerPage: 20,
        examOptions: [],
        selectedItems: [],
    };
    // DOM cache để lưu trữ tham chiếu đến DOM elements
    let _dom = {};
    // Khởi tạo module
    function _init() {
        var _a;
        // Lấy productId từ URL hoặc meta tag
        _config.productId =
            ((_a = document
                .querySelector('meta[name="product-id"]')) === null || _a === void 0 ? void 0 : _a.getAttribute("content")) || window.location.pathname.split("/")[2];
        console.log("Memory Exam Management module initialized with productId:", _config.productId);
        // Khởi tạo DOM cache
        _initDomCache();
        // Gán các event listeners
        _setupEventListeners();
        // Tải dữ liệu exam options
        _loadExamOptions();
    }
    // Khởi tạo DOM cache
    function _initDomCache() {
        Object.keys(_config.domSelectors).forEach((key) => {
            const selector = _config.domSelectors[key];
            _dom[key] = document.querySelector(selector);
        });
    }
    // Thiết lập các event listeners
    function _setupEventListeners() {
        // Source filter
        if (_dom.sourceFilter) {
            _dom.sourceFilter.addEventListener("change", function () {
                if (window.MemoryExam) {
                    window.MemoryExam.setSourceFilter(this.value);
                }
            });
        }
        // Exam filter
        if (_dom.examFilter) {
            _dom.examFilter.addEventListener("change", function () {
                if (window.MemoryExam) {
                    window.MemoryExam.setExamFilter(this.value);
                }
            });
        }
        // Bulk delete button
        if (_dom.bulkDeleteBtn) {
            _dom.bulkDeleteBtn.addEventListener("click", function () {
                if (window.MemoryExam && window.MemoryExam.deleteBulkMemoryQuestions) {
                    window.MemoryExam.deleteBulkMemoryQuestions();
                }
            });
        }
        // Event delegation cho container chứa danh sách câu hỏi
        document.addEventListener("click", function (e) {
            // Xóa câu hỏi
            if (e.target.closest(".delete-memory-btn")) {
                e.preventDefault();
                const questionItem = e.target.closest(".memory-question-item");
                if (questionItem && questionItem.dataset.id) {
                    _deleteQuestion(questionItem.dataset.id);
                }
            }
            // Checkbox selection
            if (e.target.matches(".memory-question-checkbox")) {
                const questionItem = e.target.closest(".memory-question-item");
                if (questionItem && questionItem.dataset.id) {
                    _toggleSelection(questionItem.dataset.id, e.target.checked);
                }
            }
            // Clear search
            if (e.target.closest("#clearMemorySearchBtn")) {
                if (window.MemoryExam && window.MemoryExam.clearSearch) {
                    window.MemoryExam.clearSearch();
                }
            }
        });
    }
    // Tải danh sách các đề thi cho dropdown filter
    function _loadExamOptions() {
        fetch(`/exam/${_config.productId}/exams-list`)
            .then((response) => response.json())
            .then((data) => {
            if (data.success && Array.isArray(data.exams)) {
                _state.examOptions = data.exams;
                _updateExamFilterDropdown();
            }
        })
            .catch((error) => {
            console.error("Error loading exam options:", error);
        });
    }
    // Cập nhật dropdown filter đề thi
    function _updateExamFilterDropdown() {
        if (!_dom.examFilter)
            return;
        // Giữ lại option đầu tiên (All)
        const allOption = _dom.examFilter.querySelector('option[value="all"]');
        _dom.examFilter.innerHTML = "";
        _dom.examFilter.appendChild(allOption);
        // Thêm các options cho đề thi
        _state.examOptions.forEach((exam) => {
            const option = document.createElement("option");
            option.value = exam._id;
            option.textContent = exam.name;
            _dom.examFilter.appendChild(option);
        });
    }
    // Toggle selection của một câu hỏi
    function _toggleSelection(questionId, isSelected) {
        if (isSelected) {
            if (!_state.selectedItems.includes(questionId)) {
                _state.selectedItems.push(questionId);
            }
        }
        else {
            _state.selectedItems = _state.selectedItems.filter((id) => id !== questionId);
        }
        _updateSelectionUI();
    }
    // Xóa một câu hỏi
    function _deleteQuestion(questionId) {
        if (!window.MemoryExam)
            return;
        // Sử dụng method của module chính
        if (typeof window.MemoryExam.deleteQuestion === "function") {
            window.MemoryExam.deleteQuestion(questionId);
        }
    }
    // Cập nhật UI cho phần selection
    function _updateSelectionUI() {
        // Cập nhật số lượng đã chọn
        if (_dom.selectedCount) {
            _dom.selectedCount.textContent = _state.selectedItems.length;
        }
        // Enable/disable nút bulk delete
        if (_dom.bulkDeleteBtn) {
            _dom.bulkDeleteBtn.disabled = _state.selectedItems.length === 0;
        }
        // Cập nhật visual cho các items đã chọn
        document.querySelectorAll(".memory-question-item").forEach((item) => {
            const isSelected = _state.selectedItems.includes(item.dataset.id);
            if (isSelected) {
                item.classList.add("bg-indigo-50", "border-indigo-300");
            }
            else {
                item.classList.remove("bg-indigo-50", "border-indigo-300");
            }
            // Cập nhật checkbox
            const checkbox = item.querySelector(".memory-question-checkbox");
            if (checkbox) {
                checkbox.checked = isSelected;
            }
        });
    }
    // Chọn tất cả các câu hỏi
    function _selectAll() {
        const questionItems = document.querySelectorAll(".memory-question-item");
        questionItems.forEach((item) => {
            const questionId = item.dataset.id;
            if (questionId && !_state.selectedItems.includes(questionId)) {
                _state.selectedItems.push(questionId);
            }
            // Cập nhật checkbox
            const checkbox = item.querySelector(".memory-question-checkbox");
            if (checkbox) {
                checkbox.checked = true;
            }
        });
        _updateSelectionUI();
    }
    // Bỏ chọn tất cả các câu hỏi
    function _deselectAll() {
        _state.selectedItems = [];
        // Cập nhật checkbox
        document
            .querySelectorAll(".memory-question-checkbox")
            .forEach((checkbox) => {
            checkbox.checked = false;
        });
        _updateSelectionUI();
    }
    // Xử lý phân trang
    function _goToPage(pageNumber) {
        if (pageNumber < 1 || pageNumber > _state.totalPages)
            return;
        _state.currentPage = pageNumber;
        if (window.MemoryExam && typeof window.MemoryExam.loadPage === "function") {
            window.MemoryExam.loadPage(pageNumber);
        }
    }
    // Cập nhật UI phân trang
    function _updatePagination(currentPage, totalPages) {
        if (!_dom.pagination)
            return;
        _state.currentPage = currentPage;
        _state.totalPages = totalPages;
        let paginationHTML = "";
        // Previous button
        paginationHTML += `
      <button 
        class="px-3 py-2 rounded-md ${currentPage === 1
            ? "bg-gray-100 text-gray-400 cursor-not-allowed"
            : "bg-white text-gray-700 hover:bg-gray-50"}"
        ${currentPage === 1
            ? "disabled"
            : 'onclick="MemoryExamManagement.goToPage(' +
                (currentPage - 1) +
                ')"'}
      >
        <i class="fas fa-chevron-left"></i>
      </button>
    `;
        // Page numbers
        const showPages = 5; // Số trang hiển thị
        const startPage = Math.max(1, currentPage - Math.floor(showPages / 2));
        const endPage = Math.min(totalPages, startPage + showPages - 1);
        // First page
        if (startPage > 1) {
            paginationHTML += `
        <button 
          class="px-3 py-2 rounded-md bg-white text-gray-700 hover:bg-gray-50"
          onclick="MemoryExamManagement.goToPage(1)"
        >
          1
        </button>
      `;
            if (startPage > 2) {
                paginationHTML += `<span class="px-2 py-2">...</span>`;
            }
        }
        // Page numbers
        for (let i = startPage; i <= endPage; i++) {
            paginationHTML += `
        <button 
          class="px-3 py-2 rounded-md ${i === currentPage
                ? "bg-indigo-600 text-white"
                : "bg-white text-gray-700 hover:bg-gray-50"}"
          onclick="MemoryExamManagement.goToPage(${i})"
        >
          ${i}
        </button>
      `;
        }
        // Last page
        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                paginationHTML += `<span class="px-2 py-2">...</span>`;
            }
            paginationHTML += `
        <button 
          class="px-3 py-2 rounded-md bg-white text-gray-700 hover:bg-gray-50"
          onclick="MemoryExamManagement.goToPage(${totalPages})"
        >
          ${totalPages}
        </button>
      `;
        }
        // Next button
        paginationHTML += `
      <button 
        class="px-3 py-2 rounded-md ${currentPage === totalPages
            ? "bg-gray-100 text-gray-400 cursor-not-allowed"
            : "bg-white text-gray-700 hover:bg-gray-50"}"
        ${currentPage === totalPages
            ? "disabled"
            : 'onclick="MemoryExamManagement.goToPage(' +
                (currentPage + 1) +
                ')"'}
      >
        <i class="fas fa-chevron-right"></i>
      </button>
    `;
        _dom.pagination.innerHTML = paginationHTML;
    }
    // Export cho toàn cục
    function _exportGlobals() {
        // Những hàm cần expose ra ngoài để button gọi
        window.filterMemoryBySource = function () {
            const sourceFilter = document.getElementById("memorySourceFilter");
            if (sourceFilter && window.MemoryExam) {
                window.MemoryExam.setSourceFilter(sourceFilter.value);
            }
        };
        window.filterMemoryByExam = function () {
            const examFilter = document.getElementById("memoryExamFilter");
            if (examFilter && window.MemoryExam) {
                window.MemoryExam.setExamFilter(examFilter.value);
            }
        };
        window.clearMemorySearch = function () {
            if (window.MemoryExam && window.MemoryExam.clearSearch) {
                window.MemoryExam.clearSearch();
            }
        };
    }
    // Public API
    const publicAPI = {
        init: function () {
            _init();
            _exportGlobals();
        },
        selectAll: _selectAll,
        deselectAll: _deselectAll,
        goToPage: _goToPage,
        updatePagination: _updatePagination,
    };
    // Expose public API to window
    window.MemoryExamManagement = publicAPI;
    // Auto-initialize on DOMContentLoaded if we're on memory tab
    document.addEventListener("DOMContentLoaded", function () {
        var _a;
        const currentTab = (_a = document
            .querySelector('meta[name="current-tab"]')) === null || _a === void 0 ? void 0 : _a.getAttribute("content");
        if (currentTab === "memory") {
            publicAPI.init();
        }
    });
})(window);
//# sourceMappingURL=memory-exam-management.js.map