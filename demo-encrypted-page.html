<!DOCTYPE html>
<html lang="vi">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Demo Encryption - <PERSON><PERSON> li<PERSON><PERSON> hỏi <PERSON></title>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
        background-color: #f5f5f5;
      }
      .container {
        background: white;
        padding: 30px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }
      .status {
        padding: 15px;
        margin: 10px 0;
        border-radius: 4px;
        border-left: 4px solid #ccc;
      }
      .status.success {
        background-color: #d4edda;
        border-color: #28a745;
        color: #155724;
      }
      .status.error {
        background-color: #f8d7da;
        border-color: #dc3545;
        color: #721c24;
      }
      .status.info {
        background-color: #cce5ff;
        border-color: #0066cc;
        color: #004080;
      }
      pre {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 4px;
        overflow-x: auto;
        font-size: 12px;
      }
      button {
        background: #007bff;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 4px;
        cursor: pointer;
        margin: 5px;
      }
      button:hover {
        background: #0056b3;
      }
      .encrypted-data {
        word-break: break-all;
        font-family: monospace;
        font-size: 11px;
        background: #ffe6e6;
        padding: 10px;
        border-radius: 4px;
        margin: 10px 0;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>🔐 Demo Mã Hóa Dữ Liệu Câu Hỏi</h1>
      <p>
        Trang này mô phỏng cách dữ liệu câu hỏi được mã hóa ở backend và giải mã
        ở frontend.
      </p>

      <div id="status" class="status info">⏳ Đang chuẩn bị demo...</div>

      <button onclick="startDemo()">🚀 Bắt đầu Demo</button>
      <button onclick="testEncryption()">🧪 Test Encryption</button>
      <button onclick="clearResults()">🗑️ Xóa kết quả</button>

      <div id="results"></div>
    </div>

    <script>
      // Import encryption functions
      function decryptData(encryptedHex, keyHex, ivHex) {
        try {
          const combinedKey = keyHex + ivHex;
          const bytes = [];

          for (let i = 0; i < encryptedHex.length; i += 2) {
            const encByte = parseInt(encryptedHex.substr(i, 2), 16);
            const keyByte = combinedKey.charCodeAt(
              (i / 2) % combinedKey.length
            );
            const decByte = encByte ^ keyByte;
            bytes.push(decByte);
          }

          let decrypted = "";
          const uint8Array = new Uint8Array(bytes);

          if (typeof TextDecoder !== "undefined") {
            const decoder = new TextDecoder("utf-8");
            decrypted = decoder.decode(uint8Array);
          } else {
            decrypted = String.fromCharCode.apply(null, bytes);
          }

          return JSON.parse(decrypted);
        } catch (error) {
          console.error("Lỗi khi giải mã:", error);
          throw new Error("Không thể giải mã dữ liệu: " + error.message);
        }
      }

      // Simulate backend encryption (for demo)
      function encryptData(data, keyHex, ivHex) {
        const combinedKey = keyHex + ivHex;
        let encrypted = "";

        // Convert to UTF-8 bytes
        const encoder = new TextEncoder();
        const utf8Bytes = encoder.encode(data);

        for (let i = 0; i < utf8Bytes.length; i++) {
          const dataByte = utf8Bytes[i];
          const keyByte = combinedKey.charCodeAt(i % combinedKey.length);
          const encryptedByte = dataByte ^ keyByte;
          encrypted += encryptedByte.toString(16).padStart(2, "0");
        }

        return encrypted;
      }

      function generateRandomKey() {
        const chars = "0123456789abcdef";
        let result = "";
        for (let i = 0; i < 64; i++) {
          result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
      }

      function generateRandomIV() {
        const chars = "0123456789abcdef";
        let result = "";
        for (let i = 0; i < 32; i++) {
          result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
      }

      function updateStatus(message, type = "info") {
        const statusEl = document.getElementById("status");
        statusEl.className = `status ${type}`;
        statusEl.innerHTML = message;
      }

      function addResult(title, content, type = "info") {
        const resultsEl = document.getElementById("results");
        const resultDiv = document.createElement("div");
        resultDiv.innerHTML = `
                <h3>${title}</h3>
                <div class="status ${type}">
                    ${content}
                </div>
            `;
        resultsEl.appendChild(resultDiv);
      }

      function clearResults() {
        document.getElementById("results").innerHTML = "";
        updateStatus("✨ Đã xóa kết quả");
      }

      async function testEncryption() {
        updateStatus("🧪 Đang test encryption cơ bản...");

        try {
          const testData = "Xin chào! 你好! こんにちは! áéíóú";
          const key = generateRandomKey();
          const iv = generateRandomIV();

          addResult("📝 Dữ liệu gốc:", `<pre>${testData}</pre>`);
          addResult("🔑 Key:", `<div class="encrypted-data">${key}</div>`);
          addResult("🔑 IV:", `<div class="encrypted-data">${iv}</div>`);

          const encrypted = encryptData(testData, key, iv);
          addResult(
            "🔒 Dữ liệu mã hóa:",
            `<div class="encrypted-data">${encrypted}</div>`
          );

          const decrypted = decryptData(encrypted, key, iv);
          addResult(
            "🔓 Dữ liệu giải mã:",
            `<pre>${JSON.stringify(decrypted)}</pre>`
          );

          const isMatch = testData === decrypted;
          addResult(
            "✅ Kết quả test:",
            isMatch
              ? "THÀNH CÔNG! Dữ liệu trùng khớp hoàn toàn."
              : "THẤT BẠI! Dữ liệu không khớp.",
            isMatch ? "success" : "error"
          );

          updateStatus(
            isMatch
              ? "✅ Test encryption thành công!"
              : "❌ Test encryption thất bại!",
            isMatch ? "success" : "error"
          );
        } catch (error) {
          addResult("❌ Lỗi:", error.message, "error");
          updateStatus(
            "❌ Test encryption thất bại: " + error.message,
            "error"
          );
        }
      }

      async function startDemo() {
        clearResults();
        updateStatus("🚀 Bắt đầu demo với dữ liệu câu hỏi thực tế...");

        try {
          // Dữ liệu câu hỏi mẫu
          const sampleData = [
            {
              examName: "Đề 1: Kiểm tra tiếng Việt",
              questions: [
                {
                  _id: "sample1",
                  text: "Câu hỏi có dấu tiếng Việt: áéíóú ă â đ",
                  answers: [
                    { text: "Đáp án A có ký tự đặc biệt", isCorrect: true },
                    { text: "Đáp án B thông thường", isCorrect: false },
                    { text: "Đáp án C với emoji 🚀", isCorrect: false },
                    { text: "Đáp án D unicode 中文", isCorrect: false },
                  ],
                },
                {
                  _id: "sample2",
                  text: "Câu hỏi số 2 với nội dung dài hơn và nhiều ký tự Unicode: ℂ ℍ ℕ ℙ ℚ ℝ ℤ",
                  answers: [
                    { text: "Phương án A", isCorrect: false },
                    { text: "Phương án B ✓", isCorrect: true },
                  ],
                },
              ],
            },
            {
              examName: "Đề 2: Test English + Unicode",
              questions: [
                {
                  _id: "sample3",
                  text: "Mixed content: English, Tiếng Việt, 中文, 日本語, العربية",
                  answers: [
                    { text: "Answer with symbols: αβγδε", isCorrect: true },
                    { text: "Normal answer", isCorrect: false },
                  ],
                },
              ],
            },
          ];

          addResult(
            "📚 Dữ liệu câu hỏi gốc:",
            `<pre>${JSON.stringify(sampleData, null, 2)}</pre>`
          );

          // Mô phỏng backend: mã hóa dữ liệu
          updateStatus("🔒 Backend đang mã hóa dữ liệu...");
          const key = generateRandomKey();
          const iv = generateRandomIV();
          const jsonData = JSON.stringify(sampleData);

          addResult(
            "🔑 Key được tạo ngẫu nhiên:",
            `<div class="encrypted-data">${key}</div>`
          );
          addResult(
            "🔑 IV được tạo ngẫu nhiên:",
            `<div class="encrypted-data">${iv}</div>`
          );
          addResult(
            "📊 Kích thước JSON gốc:",
            `${jsonData.length} ký tự, UTF-8: ${
              new TextEncoder().encode(jsonData).length
            } bytes`
          );

          const encrypted = encryptData(jsonData, key, iv);
          addResult(
            "🔒 Dữ liệu đã mã hóa:",
            `<div class="encrypted-data">${encrypted}</div>`
          );
          addResult(
            "📊 Kích thước sau mã hóa:",
            `${encrypted.length} ký tự (hex)`
          );

          // Mô phỏng frontend: giải mã dữ liệu
          updateStatus("🔓 Frontend đang giải mã dữ liệu...");

          // Delay để tạo hiệu ứng
          await new Promise((resolve) => setTimeout(resolve, 1000));

          const decrypted = decryptData(encrypted, key, iv);
          addResult(
            "🔓 Dữ liệu đã giải mã:",
            `<pre>${JSON.stringify(decrypted, null, 2)}</pre>`
          );

          // Kiểm tra tính toàn vẹn
          const isMatch =
            JSON.stringify(sampleData) === JSON.stringify(decrypted);
          addResult(
            "🔍 Kiểm tra tính toàn vẹn:",
            isMatch
              ? "THÀNH CÔNG! Dữ liệu trùng khớp 100%"
              : "THẤT BẠI! Dữ liệu bị thay đổi",
            isMatch ? "success" : "error"
          );

          if (isMatch) {
            addResult(
              "🎉 Demo hoàn thành:",
              `✅ Mã hóa/giải mã thành công<br>
                         ✅ Hỗ trợ UTF-8 hoàn toàn<br>
                         ✅ Tính toàn vẹn dữ liệu đảm bảo<br>
                         ✅ Key ngẫu nhiên mỗi lần load`,
              "success"
            );
            updateStatus(
              "🎉 Demo thành công! Hệ thống mã hóa hoạt động tốt.",
              "success"
            );
          } else {
            updateStatus(
              "❌ Demo thất bại! Có lỗi trong quá trình mã hóa/giải mã.",
              "error"
            );
          }
        } catch (error) {
          addResult("❌ Lỗi trong demo:", error.message, "error");
          updateStatus("❌ Demo thất bại: " + error.message, "error");
        }
      }

      // Auto start demo on page load
      document.addEventListener("DOMContentLoaded", function () {
        updateStatus(
          '✅ Trang demo đã sẵn sàng. Nhấn "Bắt đầu Demo" để xem hoạt động.'
        );
      });
    </script>
  </body>
</html>
