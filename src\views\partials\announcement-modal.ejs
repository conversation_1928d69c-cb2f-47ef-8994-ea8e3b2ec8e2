<%# File này hiển thị một modal thông báo và xử lý các tương tác của nó %>

<div
  id="announcement-modal"
  class="fixed inset-0 z-50 overflow-auto flex hidden"
  data-exam-id="<%= typeof exam !== 'undefined' ? exam.id : '' %>"
>
  <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity"></div>
  <div
    class="relative bg-white w-full max-w-md m-auto flex-col flex rounded-lg shadow-lg"
  >
    <div
      class="py-4 px-6 border-b border-gray-200 flex justify-between items-center"
    >
      <h2 class="text-xl font-bold text-red-600">THÔNG BÁO</h2>
    </div>
    <div class="py-4 px-6 overflow-y-auto max-h-96">
      <div class="prose"><%- announcement.message %></div>
    </div>
    <div class="py-3 px-6 border-t border-gray-200 flex justify-between">
      <button
        id="close-modal"
        class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition duration-200"
      >
        Đóng
      </button>
      <button
        id="hide-modal"
        class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition duration-200"
      >
        Tắt trong 3h
      </button>
    </div>
  </div>
</div>

<script>
  (function () {
    const modal = document.getElementById("announcement-modal");
    if (!modal) return;

    // Logic xác định storageKey (không đổi)
    let storageKey = "hideModal";
    const examId = modal.getAttribute("data-exam-id");
    if (examId) {
      storageKey = `hideModal_exam_${examId}`;
    } else if (window.location.pathname === "/") {
      storageKey = "hideModal_homepage_guest";
    } else if (window.location.pathname === "/home") {
      storageKey = "hideModal_homepage_authenticated";
    }

    // THAY ĐỔI 2: Đảo ngược logic. Nếu cần hiển thị thì XÓA class "hidden"
    function shouldShowModal() {
      const hideUntilTimestamp = localStorage.getItem(storageKey);
      if (!hideUntilTimestamp) return true;
      const currentTime = new Date().getTime();
      return currentTime > parseInt(hideUntilTimestamp);
    }

    if (shouldShowModal()) {
      modal.classList.remove("hidden");
    }
  })();
</script>

<script>
  document.addEventListener("DOMContentLoaded", function () {
    const modal = document.getElementById("announcement-modal");
    if (!modal || modal.classList.contains("hidden")) {
      return;
    }

    document.body.style.overflow = "hidden";

    const modalOverlay = modal.querySelector(".fixed.inset-0.bg-black");
    const closeModalBtn = document.getElementById("close-modal");
    const hideModalBtn = document.getElementById("hide-modal");

    let storageKey = "hideModal";
    const examId = modal.getAttribute("data-exam-id");
    if (examId) {
      storageKey = `hideModal_exam_${examId}`;
    } else if (window.location.pathname === "/") {
      storageKey = "hideModal_homepage_guest";
    } else if (window.location.pathname === "/home") {
      storageKey = "hideModal_homepage_authenticated";
    }

    function hideModal() {
      modal.classList.add("hidden");
      document.body.style.overflow = "";
    }

    function hideModalFor3Hour() {
      const threeHourFromNow = new Date().getTime() + 60 * 60 * 3 * 1000;
      localStorage.setItem(storageKey, threeHourFromNow.toString());
      hideModal();
    }

    closeModalBtn.addEventListener("click", hideModal);
    hideModalBtn.addEventListener("click", hideModalFor3Hour);
    if (modalOverlay) {
      modalOverlay.addEventListener("click", hideModal);
    }

    document.addEventListener("keydown", function (event) {
      if (event.key === "Escape") {
        hideModal();
      }
    });

    const focusableElements = modal.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    if (focusableElements.length > 0) {
      const firstElement = focusableElements[0];
      const lastElement = focusableElements[focusableElements.length - 1];
      setTimeout(() => {
        firstElement.focus();
      }, 100);
      modal.addEventListener("keydown", function (e) {
        if (e.key === "Tab") {
          if (e.shiftKey) {
            if (document.activeElement === firstElement) {
              lastElement.focus();
              e.preventDefault();
            }
          } else {
            if (document.activeElement === lastElement) {
              firstElement.focus();
              e.preventDefault();
            }
          }
        }
      });
    }
  });
</script>
