const passport = require("passport");
/**
 * Cấu hình Passport với xử lý lỗi khả năng chịu lỗi
 * Bỏ qua cấu hình Google OAuth nếu thiếu biến môi trường
 */
const configurePassport = () => {
    try {
        // Nếu chúng ta có đầy đủ thông tin Google OAuth
        if (process.env.GOOGLE_CLIENT_ID &&
            process.env.GOOGLE_CLIENT_SECRET &&
            process.env.CALLBACK_URL) {
            // Import và cấu hình Google Strategy
            const configureGoogleStrategy = require("./passport").default;
            configureGoogleStrategy();
            console.log("Google OAuth đã được cấu hình thành công");
        }
        else {
            console.warn("Thiếu thông tin cấu hình Google OAuth. Tính năng đăng nhập với Google sẽ không hoạt động.");
        }
        // Ở đây bạn có thể cấu hình các strategy xác thực khác như local strategy
    }
    catch (error) {
        console.error("Lỗi khi cấu hình Passport:", error);
    }
};
module.exports = configurePassport;
//# sourceMappingURL=passportConfig.js.map