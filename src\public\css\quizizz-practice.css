/* Quizizz Practice CSS - Exact replica of Quizizz styling */

/* Root variables for Quizizz color scheme */
:root {
  /* Primary Quizizz colors */
  --quizizz-purple: #8854d0;
  --quizizz-purple-dark: #6c3fb5;
  --quizizz-purple-light: #a374e8;
  --quizizz-bg: #5a0773;
  --quizizz-bg-dark: #3b0764;
  --quizizz-bg-darker: #2d0a4e;
  
  /* Option colors (matching Quizizz exactly) */
  --quizizz-option-1: #e21b3c; /* Red */
  --quizizz-option-2: #1368ce; /* Blue */
  --quizizz-option-3: #d89e00; /* Yellow/Orange */
  --quizizz-option-4: #26890c; /* Green */
  
  /* Feedback colors */
  --quizizz-correct: #4caf50;
  --quizizz-incorrect: #f44336;
  --quizizz-pink: #ff4081;
  
  /* UI colors */
  --quizizz-white: #ffffff;
  --quizizz-white-transparent: rgba(255, 255, 255, 0.1);
  --quizizz-white-semi: rgba(255, 255, 255, 0.2);
  --quizizz-white-more: rgba(255, 255, 255, 0.3);
  
  /* Shadows and effects */
  --quizizz-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  --quizizz-shadow-hover: 0 8px 25px rgba(0, 0, 0, 0.25);
  --quizizz-shadow-strong: 0 12px 35px rgba(0, 0, 0, 0.35);
}

/* Base styles */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: "Nunito", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  background: linear-gradient(135deg, var(--quizizz-bg) 0%, var(--quizizz-bg-dark) 100%);
  color: var(--quizizz-white);
  overflow-x: hidden;
  min-height: 100vh;
}

/* Quizizz container */
.quizizz-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* Header styles */
.quizizz-header {
  padding: 16px 20px;
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--quizizz-white-semi);
}

.question-number {
  background: var(--quizizz-white-semi);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 8px 16px;
  font-weight: 600;
  font-size: 14px;
  border: 1px solid var(--quizizz-white-more);
}

.timer-display {
  background: var(--quizizz-white-transparent);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 8px 16px;
  font-weight: 600;
  font-size: 14px;
  border: 2px solid var(--quizizz-white-more);
  transition: all 0.3s ease;
}

.timer-display.warning {
  background: var(--quizizz-incorrect);
  border-color: var(--quizizz-incorrect);
  animation: pulse-warning 1s infinite;
}

.score-display {
  background: var(--quizizz-white-transparent);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 8px 16px;
  font-weight: 600;
  font-size: 14px;
  border: 1px solid var(--quizizz-white-more);
}

.streak-counter {
  background: linear-gradient(135deg, var(--quizizz-pink), #ff6b9d);
  border-radius: 20px;
  padding: 8px 16px;
  font-weight: 700;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 6px;
  box-shadow: var(--quizizz-shadow);
  animation: streak-glow 2s infinite alternate;
}

/* Progress bar */
.progress-bar-container {
  height: 8px;
  background: var(--quizizz-white-semi);
  border-radius: 4px;
  overflow: hidden;
  margin-top: 16px;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--quizizz-pink), var(--quizizz-purple));
  border-radius: 4px;
  transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.progress-bar::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: shimmer 2s infinite;
}

/* Question styles */
.question-text {
  font-size: clamp(24px, 4vw, 32px);
  font-weight: 700;
  line-height: 1.3;
  margin-bottom: 32px;
  text-align: center;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  animation: slide-in-down 0.6s ease-out;
}

.question-image {
  max-width: 100%;
  max-height: 300px;
  border-radius: 12px;
  box-shadow: var(--quizizz-shadow);
  margin: 0 auto 32px;
  display: block;
}

/* Option cards - Exact Quizizz styling */
.option-card {
  background: var(--quizizz-white-transparent);
  backdrop-filter: blur(10px);
  border: 2px solid var(--quizizz-white-semi);
  border-radius: 16px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  min-height: 80px;
  display: flex;
  align-items: center;
  box-shadow: var(--quizizz-shadow);
  animation: slide-in-up 0.5s ease-out;
}

.option-card:nth-child(1) { animation-delay: 0.1s; }
.option-card:nth-child(2) { animation-delay: 0.2s; }
.option-card:nth-child(3) { animation-delay: 0.3s; }
.option-card:nth-child(4) { animation-delay: 0.4s; }

.option-card:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: var(--quizizz-shadow-hover);
  border-color: var(--quizizz-white);
  background: var(--quizizz-white-semi);
}

.option-card.selected {
  background: var(--quizizz-white-semi);
  border-color: var(--quizizz-white);
  transform: translateY(-2px) scale(1.01);
  box-shadow: var(--quizizz-shadow-strong);
}

.option-card.correct {
  background: var(--quizizz-correct) !important;
  border-color: var(--quizizz-correct) !important;
  animation: correct-bounce 0.6s ease-out;
}

.option-card.incorrect {
  background: var(--quizizz-incorrect) !important;
  border-color: var(--quizizz-incorrect) !important;
  animation: incorrect-shake 0.6s ease-out;
}

.option-card.disabled {
  pointer-events: none;
  opacity: 0.7;
}

/* Option letters */
.option-letter {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--quizizz-white-semi);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 18px;
  margin-right: 16px;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.option-card.selected .option-letter {
  background: var(--quizizz-white);
  color: var(--quizizz-bg);
  transform: scale(1.1);
}

.option-card.correct .option-letter,
.option-card.incorrect .option-letter {
  background: var(--quizizz-white);
  color: var(--quizizz-bg);
}

/* Option text */
.option-text {
  font-size: 16px;
  font-weight: 600;
  line-height: 1.4;
  flex: 1;
}

/* Loading styles */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid var(--quizizz-white-more);
  border-top: 4px solid var(--quizizz-white);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 24px;
}

.loading-text {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 8px;
}

.loading-subtext {
  font-size: 16px;
  opacity: 0.8;
}

/* Feedback overlay */
.celebration-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.celebration-overlay.show {
  opacity: 1;
  pointer-events: all;
}

.feedback-icon {
  font-size: 120px;
  margin-bottom: 20px;
  animation: zoom-in 0.5s ease-out;
}

.feedback-icon.correct {
  color: var(--quizizz-correct);
  animation: celebration 0.8s ease-out;
}

.feedback-icon.incorrect {
  color: var(--quizizz-incorrect);
  animation: shake 0.6s ease-out;
}

.feedback-text {
  font-size: 36px;
  font-weight: 700;
  text-align: center;
  animation: slide-in-up 0.5s ease-out 0.2s both;
}

/* Results screen */
.results-container {
  text-align: center;
  animation: fade-in 0.8s ease-out;
}

.results-score {
  font-size: 72px;
  font-weight: 800;
  color: var(--quizizz-pink);
  margin-bottom: 16px;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  animation: bounce-in 0.8s ease-out;
}

.results-grade {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 32px;
  animation: slide-in-up 0.6s ease-out 0.2s both;
}

.results-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.stat-card {
  background: var(--quizizz-white-transparent);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 20px;
  border: 1px solid var(--quizizz-white-semi);
  animation: slide-in-up 0.5s ease-out;
}

.stat-value {
  font-size: 32px;
  font-weight: 700;
  color: var(--quizizz-pink);
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.8;
  font-weight: 600;
}

/* Buttons */
.quizizz-button {
  background: linear-gradient(135deg, var(--quizizz-purple), var(--quizizz-purple-dark));
  color: var(--quizizz-white);
  border: none;
  border-radius: 12px;
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: var(--quizizz-shadow);
  position: relative;
  overflow: hidden;
}

.quizizz-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--quizizz-shadow-hover);
}

.quizizz-button:active {
  transform: translateY(0);
}

.quizizz-button.primary {
  background: linear-gradient(135deg, var(--quizizz-pink), #ff6b9d);
}

.quizizz-button.secondary {
  background: var(--quizizz-white-semi);
  color: var(--quizizz-white);
}

/* Animations */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes pulse-warning {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

@keyframes streak-glow {
  0% { box-shadow: var(--quizizz-shadow); }
  100% { box-shadow: 0 4px 20px rgba(255, 64, 129, 0.4); }
}

@keyframes slide-in-down {
  0% {
    opacity: 0;
    transform: translateY(-30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in-up {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes zoom-in {
  0% {
    opacity: 0;
    transform: scale(0.5);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounce-in {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes correct-bounce {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes incorrect-shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  50% { transform: translateX(5px); }
  75% { transform: translateX(-3px); }
}

@keyframes celebration {
  0%, 100% { transform: scale(1) rotate(0deg); }
  25% { transform: scale(1.1) rotate(-5deg); }
  50% { transform: scale(1.2) rotate(5deg); }
  75% { transform: scale(1.1) rotate(-2deg); }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-10px); }
  50% { transform: translateX(10px); }
  75% { transform: translateX(-5px); }
}

/* Responsive design */
@media (max-width: 768px) {
  .quizizz-header {
    padding: 12px 16px;
  }
  
  .question-text {
    font-size: 24px;
    margin-bottom: 24px;
  }
  
  .option-card {
    padding: 16px;
    min-height: 70px;
  }
  
  .option-letter {
    width: 32px;
    height: 32px;
    font-size: 16px;
    margin-right: 12px;
  }
  
  .option-text {
    font-size: 14px;
  }
  
  .feedback-icon {
    font-size: 80px;
  }
  
  .feedback-text {
    font-size: 24px;
  }
  
  .results-score {
    font-size: 48px;
  }
  
  .results-grade {
    font-size: 24px;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Print styles */
@media print {
  body * {
    display: none !important;
    visibility: hidden !important;
  }
}
