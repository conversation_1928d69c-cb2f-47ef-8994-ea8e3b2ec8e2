{"version": 3, "file": "encryption.js", "sourceRoot": "", "sources": ["../../src/util/encryption.ts"], "names": [], "mappings": ";;;;;;AAAA,oDAA4B;AAE5B;;GAEG;AACI,MAAM,iBAAiB,GAAG,GAAW,EAAE;IAC5C,OAAO,gBAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,cAAc;AAC/D,CAAC,CAAC;AAFW,QAAA,iBAAiB,qBAE5B;AAEF;;GAEG;AACI,MAAM,gBAAgB,GAAG,GAAW,EAAE;IAC3C,OAAO,gBAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,aAAa;AAC9D,CAAC,CAAC;AAFW,QAAA,gBAAgB,oBAE3B;AAEF;;;GAGG;AACI,MAAM,oBAAoB,GAAG,CAClC,IAAY,EACZ,GAAW,EACX,EAAU,EACF,EAAE;IACV,IAAI,CAAC;QACH,uDAAuD;QACvD,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAC5C,MAAM,WAAW,GAAG,GAAG,GAAG,EAAE,CAAC;QAE7B,6CAA6C;QAC7C,MAAM,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QACjD,MAAM,SAAS,GAAG,WAAW,CAAC,MAAM,CAAC;QAErC,+CAA+C;QAC/C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC1C,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,UAAU,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC;QACtE,CAAC;QAED,0DAA0D;QAC1D,OAAO,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IACnC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAC;QACxC,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;IAC9C,CAAC;AACH,CAAC,CAAC;AAzBW,QAAA,oBAAoB,wBAyB/B;AAEF;;GAEG;AACI,MAAM,oBAAoB,GAAG,CAClC,aAAqB,EACrB,GAAW,EACX,EAAU,EACF,EAAE;IACV,IAAI,CAAC;QACH,MAAM,WAAW,GAAG,GAAG,GAAG,EAAE,CAAC;QAC7B,MAAM,SAAS,GAAG,WAAW,CAAC,MAAM,CAAC;QAErC,kDAAkD;QAClD,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;QAEpD,6CAA6C;QAC7C,MAAM,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QAEjD,8CAA8C;QAC9C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC1C,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,UAAU,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC;QACtE,CAAC;QAED,yDAAyD;QACzD,OAAO,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IACpC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;QACzC,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;IAC/C,CAAC;AACH,CAAC,CAAC;AA1BW,QAAA,oBAAoB,wBA0B/B;AAEF;;GAEG;AACI,MAAM,WAAW,GAAG,CAAC,IAAY,EAAE,GAAW,EAAE,EAAU,EAAU,EAAE;IAC3E,IAAI,CAAC;QACH,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAC5C,MAAM,WAAW,GAAG,GAAG,GAAG,EAAE,CAAC;QAC7B,IAAI,SAAS,GAAG,EAAE,CAAC;QAEnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC1C,MAAM,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;YAC9B,MAAM,OAAO,GAAG,WAAW,CAAC,UAAU,CAAC,CAAC,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC;YAC/D,MAAM,aAAa,GAAG,QAAQ,GAAG,OAAO,CAAC;YACzC,SAAS,IAAI,aAAa,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QAC3D,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAC;QACxC,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;IAC9C,CAAC;AACH,CAAC,CAAC;AAlBW,QAAA,WAAW,eAkBtB;AAEF;;GAEG;AACI,MAAM,WAAW,GAAG,CACzB,aAAqB,EACrB,GAAW,EACX,EAAU,EACF,EAAE;IACV,IAAI,CAAC;QACH,MAAM,WAAW,GAAG,GAAG,GAAG,EAAE,CAAC;QAC7B,MAAM,KAAK,GAAa,EAAE,CAAC;QAE3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACjD,MAAM,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YACzD,MAAM,OAAO,GAAG,WAAW,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC;YACrE,MAAM,OAAO,GAAG,OAAO,GAAG,OAAO,CAAC;YAClC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACtB,CAAC;QAED,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClC,OAAO,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IACjC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;QACzC,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;IAC/C,CAAC;AACH,CAAC,CAAC;AAtBW,QAAA,WAAW,eAsBtB;AAEF;;;GAGG;AACI,MAAM,aAAa,GAAG,CAC3B,IAAY,EACZ,GAAW,EACX,EAAU,EACF,EAAE;IACV,IAAI,CAAC;QACH,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;QAClC,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACvC,MAAM,QAAQ,GAAG,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QAChE,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QAE9D,6CAA6C;QAC7C,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QAChD,IAAI,OAAO,GAAG,CAAC,CAAC;QAEhB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC;YAC9C,yBAAyB;YACzB,MAAM,KAAK,GAAG,mBAAmB,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC;YAChE,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAErD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC;gBACnC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAC3B,CAAC;QACH,CAAC;QAED,kBAAkB;QAClB,MAAM,SAAS,GAAG,IAAI,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QACnD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC1C,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QAC1C,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,IAAI,EAAE,EAAE,CACpC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CACnC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACb,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;QAC/C,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;IAC9C,CAAC;AACH,CAAC,CAAC;AAtCW,QAAA,aAAa,iBAsCxB;AAEF;;GAEG;AACI,MAAM,aAAa,GAAG,CAC3B,aAAqB,EACrB,GAAW,EACX,EAAU,EACF,EAAE;IACV,IAAI,CAAC;QACH,MAAM,QAAQ,GAAG,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QAChE,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QAC9D,MAAM,UAAU,GAAG,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC;QAE5C,uBAAuB;QACvB,MAAM,SAAS,GAAG,IAAI,UAAU,CAAC,UAAU,CAAC,CAAC;QAC7C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC;YACpC,SAAS,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAC9D,CAAC;QAED,uBAAuB;QACvB,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,UAAU,CAAC,CAAC;QAC1C,IAAI,OAAO,GAAG,CAAC,CAAC;QAEhB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC;YACxC,MAAM,KAAK,GAAG,mBAAmB,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC;YAChE,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,UAAU,GAAG,CAAC,CAAC,CAAC;YAE/C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC;gBACnC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAC3B,CAAC;QACH,CAAC;QAED,iBAAiB;QACjB,MAAM,SAAS,GAAG,IAAI,UAAU,CAAC,UAAU,CAAC,CAAC;QAC7C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC;YACpC,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QAC1C,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;QAClC,OAAO,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IACnC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;QAChD,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;IAC/C,CAAC;AACH,CAAC,CAAC;AAzCW,QAAA,aAAa,iBAyCxB;AAEF;;GAEG;AACH,SAAS,mBAAmB,CAC1B,GAAe,EACf,EAAc,EACd,OAAe;IAEf,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,CAAC;IAEjC,yBAAyB;IACzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;QAC5B,MAAM,QAAQ,GAAG,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC;QAChC,MAAM,OAAO,GAAG,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC;QAC9B,MAAM,WAAW,GAAG,CAAC,OAAO,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;QAEzC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,GAAG,WAAW,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;IAC5E,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;;GAGG;AACI,MAAM,sBAAsB,GAAG,CACpC,GAAQ,EAMR,EAAE;IACF,MAAM,KAAK,GAAG,IAAA,yBAAiB,GAAE,CAAC;IAClC,MAAM,IAAI,GAAG,IAAA,wBAAgB,GAAE,CAAC;IAChC,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;IAErC,2EAA2E;IAC3E,IAAI,aAAqB,CAAC;IAC1B,IAAI,SAAiB,CAAC;IAEtB,qEAAqE;IACrE,aAAa,GAAG,IAAA,4BAAoB,EAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;IAC5D,SAAS,GAAG,eAAe,CAAC;IAE5B,OAAO;QACL,aAAa;QACb,KAAK;QACL,IAAI;QACJ,SAAS;KACV,CAAC;AACJ,CAAC,CAAC;AA1BW,QAAA,sBAAsB,0BA0BjC;AAEF;;GAEG;AACI,MAAM,sBAAsB,GAAG,CACpC,aAAqB,EACrB,KAAa,EACb,IAAY,EACZ,SAAkB,EACb,EAAE;IACP,IAAI,aAAqB,CAAC;IAE1B,mDAAmD;IACnD,IAAI,CAAC,SAAS,EAAE,CAAC;QACf,SAAS,GAAG,eAAe,CAAC,CAAC,mBAAmB;IAClD,CAAC;IAED,QAAQ,SAAS,EAAE,CAAC;QAClB,KAAK,QAAQ;YACX,aAAa,GAAG,IAAA,qBAAa,EAAC,aAAa,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;YAC1D,MAAM;QACR,KAAK,eAAe;YAClB,aAAa,GAAG,IAAA,4BAAoB,EAAC,aAAa,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;YACjE,MAAM;QACR;YACE,qBAAqB;YACrB,aAAa,GAAG,IAAA,mBAAW,EAAC,aAAa,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;IAC5D,CAAC;IAED,OAAO,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;AACnC,CAAC,CAAC;AA1BW,QAAA,sBAAsB,0BA0BjC;AAEF;;GAEG;AACI,MAAM,aAAa,GAAG,CAC3B,GAAQ,EAKR,EAAE;IACF,MAAM,GAAG,GAAG,IAAA,yBAAiB,GAAE,CAAC;IAChC,MAAM,EAAE,GAAG,IAAA,wBAAgB,GAAE,CAAC;IAC9B,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;IACrC,MAAM,aAAa,GAAG,IAAA,mBAAW,EAAC,QAAQ,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;IAErD,OAAO;QACL,aAAa;QACb,GAAG;QACH,EAAE;KACH,CAAC;AACJ,CAAC,CAAC;AAjBW,QAAA,aAAa,iBAiBxB;AAEF;;GAEG;AACI,MAAM,aAAa,GAAG,CAC3B,aAAqB,EACrB,GAAW,EACX,EAAU,EACL,EAAE;IACP,MAAM,aAAa,GAAG,IAAA,mBAAW,EAAC,aAAa,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;IAC1D,OAAO,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;AACnC,CAAC,CAAC;AAPW,QAAA,aAAa,iBAOxB"}