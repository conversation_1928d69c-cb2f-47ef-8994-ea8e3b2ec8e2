{"name": "auth-app", "version": "1.0.0", "description": "Ứng dụng xác thực Google với JWT và giới hạn đăng nhập đồng thời", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/server.js", "dev": "ts-node-dev --transpile-only --respawn src/server.ts", "watch": "tsc -w", "test": "echo \"Error: no test specified\" && exit 1", "update-all-counts": "ts-node src/scripts/updateAllCounts.ts", "recalculate-all-progress": "ts-node src/scripts/recalculateAllProgress.ts", "recalculate-user-progress": "ts-node src/scripts/recalculateUserProgress.ts", "init-user-progress": "ts-node src/scripts/initUserProgress.ts", "fix-duplicates": "ts-node src/scripts/fixDuplicateAttempts.ts"}, "keywords": ["google-oauth", "jwt", "mongodb", "passport"], "author": "", "license": "ISC", "dependencies": {"@types/bcryptjs": "^3.0.0", "@types/nodemailer": "^6.4.17", "@types/uuid": "^10.0.0", "bcryptjs": "^3.0.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.4.7", "ejs": "^3.1.10", "express": "^5.1.0", "express-ejs-layouts": "^2.5.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.13.2", "nodemailer": "^7.0.3", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "ua-parser-js": "^2.0.3", "uuid": "^11.1.0"}, "devDependencies": {"@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.17", "@types/express": "^5.0.1", "@types/express-ejs-layouts": "^2.5.4", "@types/jsonwebtoken": "^9.0.9", "@types/mongoose": "^5.11.97", "@types/node": "^22.14.0", "@types/passport": "^1.0.17", "@types/passport-google-oauth20": "^2.0.16", "nodemon": "^3.1.9", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}}