import mongoose from "mongoose";
import dotenv from "dotenv";
import Product from "../models/products";
import Exam from "../models/exam";
import Question from "../models/question";

// Load environment variables
dotenv.config();

/**
 * Kết nối đến cơ sở dữ liệu MongoDB
 */
async function connectToDatabase() {
  try {
    const mongoUri =
      process.env.MONGODB_URI || "mongodb://localhost:27017/google-auth";
    await mongoose.connect(mongoUri);
    console.log("Đã kết nối thành công đến MongoDB");
  } catch (error) {
    console.error("Lỗi kết nối đến MongoDB:", error);
    process.exit(1);
  }
}

/**
 * Cập nhật số lượng câu hỏi cho mỗi bài thi
 */
async function updateExamQuestionCounts() {
  try {
    console.log("===== ĐANG CẬP NHẬT SỐ LƯỢNG CÂU HỎI CHO CÁC ĐỀ THI =====");

    // Lấy tất cả các bài thi
    const exams = await Exam.find({});
    console.log(`Tìm thấy ${exams.length} bài thi cần cập nhật`);

    // Xử lý từng bài thi
    for (const exam of exams) {
      // Đếm số lượng câu hỏi cho bài thi này
      const questionCount = await Question.countDocuments({ examId: exam._id });

      // Cập nhật trường count của bài thi
      await Exam.updateOne(
        { _id: exam._id },
        { $set: { count: questionCount } }
      );

      console.log(
        `Bài thi "${exam.name}" (ID: ${exam._id}) - Số câu hỏi: ${questionCount}`
      );
    }

    console.log("Đã cập nhật số lượng câu hỏi cho tất cả bài thi thành công!");
  } catch (error) {
    console.error("Lỗi khi cập nhật số lượng câu hỏi cho đề thi:", error);
    throw error;
  }
}

/**
 * Cập nhật số lượng câu hỏi cho mỗi môn học
 */
async function updateProductQuestionCounts() {
  try {
    console.log("\n===== ĐANG CẬP NHẬT TỔNG SỐ CÂU HỎI CHO CÁC MÔN HỌC =====");

    // Lấy tất cả các môn học
    const products = await Product.find({});
    console.log(`Tìm thấy ${products.length} môn học cần cập nhật`);

    // Xử lý từng môn học
    for (const product of products) {
      // Lấy tất cả các đề thi thuộc môn học này
      const exams = await Exam.find({ productId: product._id });

      // Tính tổng số câu hỏi từ tất cả các đề thi
      const totalQuestionCount = exams.reduce(
        (total, exam) => total + (exam.count || 0),
        0
      );

      // Cập nhật trường countQuestion của môn học
      await Product.updateOne(
        { _id: product._id },
        { $set: { countQuestion: totalQuestionCount } }
      );

      console.log(
        `Môn học "${product.name}" (ID: ${product._id}) - Tổng số câu hỏi: ${totalQuestionCount} (từ ${exams.length} đề thi)`
      );
    }

    console.log("Đã cập nhật tổng số câu hỏi cho tất cả môn học thành công!");
  } catch (error) {
    console.error("Lỗi khi cập nhật số lượng câu hỏi cho môn học:", error);
    throw error;
  }
}

/**
 * Hàm chính để chạy script
 */
async function main() {
  try {
    await connectToDatabase();

    // Bước 1: Cập nhật số câu hỏi cho từng đề thi
    await updateExamQuestionCounts();

    // Bước 2: Cập nhật tổng số câu hỏi cho từng môn học
    await updateProductQuestionCounts();

    console.log("\n===== HOÀN THÀNH CẬP NHẬT =====");
  } catch (error) {
    console.error("Lỗi trong quá trình cập nhật:", error);
  } finally {
    // Ngắt kết nối cơ sở dữ liệu sau khi hoàn thành
    await mongoose.disconnect();
    console.log("Đã ngắt kết nối khỏi MongoDB");
  }
}

// Chạy script
main().catch((error) => {
  console.error("Lỗi không mong muốn:", error);
  process.exit(1);
});
