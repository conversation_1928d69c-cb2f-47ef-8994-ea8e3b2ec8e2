"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const dotenv_1 = __importDefault(require("dotenv"));
const mongoose_1 = __importDefault(require("mongoose"));
const UserProgress_1 = __importDefault(require("../models/UserProgress"));
dotenv_1.default.config();
// Lấy URI từ biến môi trường hoặc sử dụng URI mặc định
const MONGODB_URI = process.env.MONGODB_URI || "mongodb://localhost:27017/google-auth";
console.log("Đang kết nối đến MongoDB...");
// Kết nối MongoDB
mongoose_1.default
    .connect(MONGODB_URI)
    .then(() => console.log("Đã kết nối với MongoDB"))
    .catch((err) => {
    console.error("Lỗi kết nối MongoDB:", err);
    process.exit(1);
});
function fixDuplicateAttempts() {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            console.log("Bắt đầu sửa chữa dữ liệu trùng lặp...");
            // Lấy tất cả bản ghi UserProgress
            const allProgress = yield UserProgress_1.default.find({});
            console.log(`Tìm thấy ${allProgress.length} bản ghi UserProgress`);
            let fixedCount = 0;
            // Xử lý từng bản ghi
            for (const progress of allProgress) {
                // Tạo map để theo dõi các bài thi và điểm cao nhất
                const examMap = new Map();
                let hasDuplicates = false;
                // Kiểm tra trùng lặp
                for (const attempt of progress.best_exam_attempts) {
                    const examId = attempt.exam_id.toString();
                    if (examMap.has(examId)) {
                        hasDuplicates = true;
                        const existingAttempt = examMap.get(examId);
                        // Chỉ giữ lại điểm cao nhất
                        if (attempt.score > existingAttempt.score) {
                            examMap.set(examId, attempt);
                        }
                    }
                    else {
                        examMap.set(examId, attempt);
                    }
                }
                // Nếu có trùng lặp, cập nhật bản ghi
                if (hasDuplicates) {
                    console.log(`Phát hiện trùng lặp trong bản ghi: ${progress._id}`);
                    // Tạo mảng best_exam_attempts mới
                    const newAttempts = Array.from(examMap.values());
                    // Tính lại tổng số câu đúng từ tất cả các bài thi tốt nhất
                    const totalCorrectAnswers = newAttempts.reduce((sum, attempt) => sum + attempt.correct_answers, 0);
                    console.log(`Tính lại tổng số câu đúng cho bản ghi ${progress._id}: ${totalCorrectAnswers}/${progress.total_questions}`);
                    // Tính lại phần trăm tiến độ
                    const progressPercentage = Math.round((totalCorrectAnswers / progress.total_questions) * 100);
                    // Cập nhật bản ghi
                    yield UserProgress_1.default.updateOne({ _id: progress._id }, {
                        $set: {
                            best_exam_attempts: newAttempts,
                            total_correct_answers: totalCorrectAnswers,
                            progress_percentage: progressPercentage,
                            last_updated: new Date(),
                        },
                    });
                    console.log(`Đã sửa chữa bản ghi: ${progress._id}`);
                    fixedCount++;
                }
            }
            console.log(`Hoàn thành! Đã sửa chữa ${fixedCount}/${allProgress.length} bản ghi`);
        }
        catch (error) {
            console.error("Lỗi khi sửa chữa dữ liệu:", error);
        }
        finally {
            mongoose_1.default.disconnect();
        }
    });
}
// Chạy hàm sửa chữa
fixDuplicateAttempts();
//# sourceMappingURL=fixDuplicateAttempts.js.map