import crypto from "crypto";

/**
 * Tạo key ngẫu nhiên cho mã hóa
 */
export const generateRandomKey = (): string => {
  return crypto.randomBytes(32).toString("hex"); // 256-bit key
};

/**
 * Tạo IV (Initialization Vector) ngẫu nhiên
 */
export const generateRandomIV = (): string => {
  return crypto.randomBytes(16).toString("hex"); // 128-bit IV
};

/**
 * Tối ưu XOR encryption với batch processing
 * Sử dụng Uint8Array cho performance tốt hơn
 */
export const encryptDataOptimized = (
  data: string,
  key: string,
  iv: string
): string => {
  try {
    // Use Buffer for maximum performance and compatibility
    const dataBytes = Buffer.from(data, "utf8");
    const combinedKey = key + iv;

    // Pre-allocate Buffer for better performance
    const encrypted = Buffer.alloc(dataBytes.length);
    const keyLength = combinedKey.length;

    // Ultra-optimized loop using Buffer operations
    for (let i = 0; i < dataBytes.length; i++) {
      encrypted[i] = dataBytes[i] ^ combinedKey.charCodeAt(i % keyLength);
    }

    // Convert to hex string efficiently using Buffer.toString
    return encrypted.toString("hex");
  } catch (error) {
    console.error("Lỗi khi mã hóa:", error);
    throw new Error("Không thể mã hóa dữ liệu");
  }
};

/**
 * Tối ưu XOR decryption với batch processing
 */
export const decryptDataOptimized = (
  encryptedData: string,
  key: string,
  iv: string
): string => {
  try {
    const combinedKey = key + iv;
    const keyLength = combinedKey.length;

    // Convert hex to Buffer directly (most efficient)
    const encrypted = Buffer.from(encryptedData, "hex");

    // Pre-allocate Buffer for better performance
    const decrypted = Buffer.alloc(encrypted.length);

    // Ultra-optimized XOR using Buffer operations
    for (let i = 0; i < encrypted.length; i++) {
      decrypted[i] = encrypted[i] ^ combinedKey.charCodeAt(i % keyLength);
    }

    // Convert back to string using Buffer.toString (fastest)
    return decrypted.toString("utf8");
  } catch (error) {
    console.error("Lỗi khi giải mã:", error);
    throw new Error("Không thể giải mã dữ liệu");
  }
};

/**
 * Legacy XOR encryption (backward compatibility)
 */
export const encryptData = (data: string, key: string, iv: string): string => {
  try {
    const utf8Bytes = Buffer.from(data, "utf8");
    const combinedKey = key + iv;
    let encrypted = "";

    for (let i = 0; i < utf8Bytes.length; i++) {
      const dataByte = utf8Bytes[i];
      const keyByte = combinedKey.charCodeAt(i % combinedKey.length);
      const encryptedByte = dataByte ^ keyByte;
      encrypted += encryptedByte.toString(16).padStart(2, "0");
    }

    return encrypted;
  } catch (error) {
    console.error("Lỗi khi mã hóa:", error);
    throw new Error("Không thể mã hóa dữ liệu");
  }
};

/**
 * Legacy XOR decryption (backward compatibility)
 */
export const decryptData = (
  encryptedData: string,
  key: string,
  iv: string
): string => {
  try {
    const combinedKey = key + iv;
    const bytes: number[] = [];

    for (let i = 0; i < encryptedData.length; i += 2) {
      const encByte = parseInt(encryptedData.substr(i, 2), 16);
      const keyByte = combinedKey.charCodeAt((i / 2) % combinedKey.length);
      const decByte = encByte ^ keyByte;
      bytes.push(decByte);
    }

    const buffer = Buffer.from(bytes);
    return buffer.toString("utf8");
  } catch (error) {
    console.error("Lỗi khi giải mã:", error);
    throw new Error("Không thể giải mã dữ liệu");
  }
};

/**
 * ChaCha20-like simple stream cipher (ultra-fast)
 * Sử dụng cho dữ liệu lớn
 */
export const streamEncrypt = (
  data: string,
  key: string,
  iv: string
): string => {
  try {
    const encoder = new TextEncoder();
    const dataBytes = encoder.encode(data);
    const keyBytes = new TextEncoder().encode(key.substring(0, 32));
    const ivBytes = new TextEncoder().encode(iv.substring(0, 16));

    // Simple stream generation (pseudo-ChaCha20)
    const stream = new Uint8Array(dataBytes.length);
    let counter = 0;

    for (let i = 0; i < dataBytes.length; i += 64) {
      // Generate 64-byte block
      const block = generateStreamBlock(keyBytes, ivBytes, counter++);
      const blockSize = Math.min(64, dataBytes.length - i);

      for (let j = 0; j < blockSize; j++) {
        stream[i + j] = block[j];
      }
    }

    // XOR with stream
    const encrypted = new Uint8Array(dataBytes.length);
    for (let i = 0; i < dataBytes.length; i++) {
      encrypted[i] = dataBytes[i] ^ stream[i];
    }

    return Array.from(encrypted, (byte) =>
      byte.toString(16).padStart(2, "0")
    ).join("");
  } catch (error) {
    console.error("Lỗi khi mã hóa stream:", error);
    throw new Error("Không thể mã hóa dữ liệu");
  }
};

/**
 * Stream decryption (ChaCha20-like)
 */
export const streamDecrypt = (
  encryptedData: string,
  key: string,
  iv: string
): string => {
  try {
    const keyBytes = new TextEncoder().encode(key.substring(0, 32));
    const ivBytes = new TextEncoder().encode(iv.substring(0, 16));
    const dataLength = encryptedData.length / 2;

    // Parse encrypted data
    const encrypted = new Uint8Array(dataLength);
    for (let i = 0; i < dataLength; i++) {
      encrypted[i] = parseInt(encryptedData.substr(i * 2, 2), 16);
    }

    // Generate same stream
    const stream = new Uint8Array(dataLength);
    let counter = 0;

    for (let i = 0; i < dataLength; i += 64) {
      const block = generateStreamBlock(keyBytes, ivBytes, counter++);
      const blockSize = Math.min(64, dataLength - i);

      for (let j = 0; j < blockSize; j++) {
        stream[i + j] = block[j];
      }
    }

    // XOR to decrypt
    const decrypted = new Uint8Array(dataLength);
    for (let i = 0; i < dataLength; i++) {
      decrypted[i] = encrypted[i] ^ stream[i];
    }

    const decoder = new TextDecoder();
    return decoder.decode(decrypted);
  } catch (error) {
    console.error("Lỗi khi giải mã stream:", error);
    throw new Error("Không thể giải mã dữ liệu");
  }
};

/**
 * Generate pseudo-random stream block
 */
function generateStreamBlock(
  key: Uint8Array,
  iv: Uint8Array,
  counter: number
): Uint8Array {
  const block = new Uint8Array(64);

  // Simple mixing function
  for (let i = 0; i < 64; i++) {
    const keyIndex = i % key.length;
    const ivIndex = i % iv.length;
    const counterByte = (counter + i) & 0xff;

    block[i] = (key[keyIndex] ^ iv[ivIndex] ^ counterByte ^ (i * 123)) & 0xff;
  }

  return block;
}

/**
 * Mã hóa object với thuật toán tối ưu
 * Tự động chọn thuật toán dựa trên kích thước data
 */
export const encryptObjectOptimized = (
  obj: any
): {
  appConfigData: string;
  token: string;
  salt: string;
  algorithm: string;
} => {
  const token = generateRandomKey();
  const salt = generateRandomIV();
  const jsonData = JSON.stringify(obj);

  // Chọn thuật toán - based on performance testing, XOR tối ưu là nhanh nhất
  let appConfigData: string;
  let algorithm: string;

  // Luôn sử dụng XOR tối ưu vì nó nhanh nhất trong mọi trường hợp test
  appConfigData = encryptDataOptimized(jsonData, token, salt);
  algorithm = "xor-optimized";

  return {
    appConfigData,
    token,
    salt,
    algorithm,
  };
};

/**
 * Giải mã object với detection thuật toán
 */
export const decryptObjectOptimized = (
  appConfigData: string,
  token: string,
  salt: string,
  algorithm?: string
): any => {
  let decryptedJson: string;

  // Tự động detect algorithm nếu không được cung cấp
  if (!algorithm) {
    algorithm = "xor-optimized"; // Default fallback
  }

  switch (algorithm) {
    case "stream":
      decryptedJson = streamDecrypt(appConfigData, token, salt);
      break;
    case "xor-optimized":
      decryptedJson = decryptDataOptimized(appConfigData, token, salt);
      break;
    default:
      // Fallback to legacy
      decryptedJson = decryptData(appConfigData, token, salt);
  }

  return JSON.parse(decryptedJson);
};

/**
 * Legacy object encryption (backward compatibility)
 */
export const encryptObject = (
  obj: any
): {
  encryptedData: string;
  key: string;
  iv: string;
} => {
  const key = generateRandomKey();
  const iv = generateRandomIV();
  const jsonData = JSON.stringify(obj);
  const encryptedData = encryptData(jsonData, key, iv);

  return {
    encryptedData,
    key,
    iv,
  };
};

/**
 * Legacy object decryption (backward compatibility)
 */
export const decryptObject = (
  encryptedData: string,
  key: string,
  iv: string
): any => {
  const decryptedJson = decryptData(encryptedData, key, iv);
  return JSON.parse(decryptedJson);
};
