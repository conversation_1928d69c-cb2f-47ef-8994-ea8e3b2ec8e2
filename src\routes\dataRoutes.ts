import express from "express";
import { Student, Product, Exam, Question } from "../services/modelService";
import { authenticateToken } from "../middlewares/authMiddleware";
import asyncHandler from "../util/asynHandler";

const router = express.Router();

// === API cho Student ===
router.get("/students", async<PERSON>and<PERSON>(authenticateToken), async (req, res) => {
  try {
    const students = await Student.find();
    res.json(students);
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
});

router.get(
  "/students/:id",
  async<PERSON>and<PERSON>(authenticateToken),
  asyncHandler(async (req, res) => {
    try {
      const student = await Student.findById(req.params.id);
      if (!student) {
        return res.status(404).json({ message: "Không tìm thấy sinh viên" });
      }
      res.json(student);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  })
);

// === API cho Product ===
router.get("/products", async (req, res) => {
  try {
    const products = await Product.find({ status: "active" }).lean();
    res.json({ success: true, products });
  } catch (error) {
    console.error("Lỗi khi lấy danh sách sản phẩm:", error);
    res.status(500).json({ success: false, message: "Lỗi server" });
  }
});

router.get(
  "/products/:id",
  asyncHandler(authenticateToken),
  asyncHandler(async (req, res) => {
    try {
      const product = await Product.findById(req.params.id);
      if (!product) {
        return res.status(404).json({ message: "Không tìm thấy sản phẩm" });
      }
      res.json(product);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  })
);

// === API cho Exam ===
router.get("/exams", asyncHandler(authenticateToken), async (req, res) => {
  try {
    const exams = await Exam.find();
    res.json(exams);
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
});

router.get(
  "/exams/:id",
  asyncHandler(authenticateToken),
  asyncHandler(async (req, res) => {
    try {
      const exam = await Exam.findById(req.params.id);
      if (!exam) {
        return res.status(404).json({ message: "Không tìm thấy bài kiểm tra" });
      }
      res.json(exam);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  })
);

// === API cho Question ===
router.get("/questions", asyncHandler(authenticateToken), async (req, res) => {
  try {
    const questions = await Question.find();
    res.json(questions);
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
});

router.get(
  "/questions/:id",
  asyncHandler(authenticateToken),
  asyncHandler(async (req, res) => {
    try {
      const question = await Question.findById(req.params.id);
      if (!question) {
        return res.status(404).json({ message: "Không tìm thấy câu hỏi" });
      }
      res.json(question);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  })
);

// === API cho Question theo Exam ===
router.get(
  "/exams/:examId/questions",
  asyncHandler(authenticateToken),
  async (req, res) => {
    try {
      const questions = await Question.find({ examId: req.params.examId });
      res.json(questions);
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  }
);

export default router;
