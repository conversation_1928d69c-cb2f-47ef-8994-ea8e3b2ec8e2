<!DOCTYPE html>
<html lang="vi">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>
      Test HMC - Nền tảng học tập cho sinh viên Cao đẳng Y tế Hà Nội
    </title>

    <!-- <PERSON>a tags cho SEO và PWA -->
    <meta
      name="description"
      content="Cung cấp tài liệu ôn thi, đ<PERSON> cươ<PERSON>, gi<PERSON><PERSON> <PERSON>r<PERSON><PERSON>, đề thi thử và quizizz ôn thi cho sinh viên Cao đẳng Y tế Hà Nội. Học tập hiệu quả với đáp án chi tiết và hỗ trợ 24/7. Học tập hiệu quả với đáp án chi tiết và hỗ trợ 24/7. sv yhn, sv hmc"
    />
    <meta
      name="keywords"
      content="test hmc, sv yhn, sv hmc, cao đẳng y tế hà nội, tài liệu ôn thi hmc, đề cương hmc, gi<PERSON><PERSON> trình hmc, đ<PERSON> thi thử hmc, quizizz ôn thi, google form đáp án, ôn thi cao đẳng y tế hà nội, điều dưỡng, dược, y sỹ đa khoa, kỹ thuật xét nghiệm"
    />
    <meta name="author" content="Test HMC - Cao đẳng Y tế Hà Nội" />
    <meta name="theme-color" content="#4f46e5" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="HMC Test" />

    <!-- Favicon và web manifest -->
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
    <link rel="manifest" href="/site.webmanifest" />
    <link rel="shortcut icon" href="/favicon.ico" />

    <!-- Tailwind CSS từ CDN -->
    <script src="https://cdn.tailwindcss.com"></script>

    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />

    <!-- Google Site Verification -->
    <meta
      name="google-site-verification"
      content="VHbjkQkzVGwJxl5Ec_NbN8rNvHGDJ7XwrHs2XS6xvZI"
    />
    <style>
      body {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        display: flex;
        flex-direction: column;
        min-height: 100vh;
      }
      main {
        flex: 1;
      }
      /* Đảm bảo modal luôn hiển thị trên tất cả các phần tử khác */
      #session-expired-modal {
        z-index: 9999 !important;
      }
      /* Thêm animation cho modal */
      @keyframes fadeIn {
        from {
          opacity: 0;
        }
        to {
          opacity: 1;
        }
      }
      .modal-show {
        animation: fadeIn 0.3s ease-in-out;
      }

      /* Ngăn chặn in ấn bằng cách ẩn toàn bộ nội dung khi in */
      @media print {
        body * {
          display: none !important;
          visibility: hidden !important;
        }
      }

      /* Tối ưu modal chi tiết cho mobile */
      @media (max-width: 768px) {
        /* Giảm padding modal cho mobile */
        #practiceDetailModal .px-6 {
          padding-left: 0.75rem !important;
          padding-right: 0.75rem !important;
        }

        #practiceDetailModal .py-4 {
          padding-top: 0.75rem !important;
          padding-bottom: 0.75rem !important;
        }

        #practiceDetailModal .p-6 {
          padding: 0.75rem !important;
        }

        /* Tối ưu content container cho mobile */
        #practiceDetailContent {
          padding: 0.5rem !important;
        }

        /* Tối ưu từng câu hỏi cho mobile */
        #practiceDetailContent .border.rounded-lg {
          margin-left: -0.25rem;
          margin-right: -0.25rem;
          padding: 0.75rem !important;
        }

        /* Giảm khoảng cách giữa các phần tử */
        #practiceDetailContent .mb-4 {
          margin-bottom: 0.75rem !important;
        }

        #practiceDetailContent .space-y-2 > * + * {
          margin-top: 0.375rem !important;
        }

        /* Tối ưu grid tổng quan cho mobile */
        .question-overview-grid {
          padding: 0.5rem !important;
          margin: 0 -0.5rem !important;
        }

        /* Đảm bảo text size phù hợp trên mobile */
        #practiceDetailContent .text-sm {
          font-size: 0.825rem !important;
        }

        /* Tối ưu options cho mobile */
        #practiceDetailContent .flex.items-center.p-3 {
          padding: 0.5rem !important;
        }
      }

      /* Styles cho màn hình rất nhỏ (điện thoại dọc) */
      @media (max-width: 480px) {
        #practiceDetailModal .mx-4 {
          margin-left: 0.5rem !important;
          margin-right: 0.5rem !important;
        }

        #practiceDetailContent {
          padding: 0.25rem !important;
        }

        #practiceDetailContent .border.rounded-lg {
          margin-left: -0.125rem;
          margin-right: -0.125rem;
          padding: 0.5rem !important;
        }
      }
    </style>
  </head>
  <body class="bg-gray-50">
    <!-- Header -->
    <header
      id="header_container"
      class="bg-white/95 backdrop-blur-sm shadow-md sticky top-0 w-full z-50 transition-all duration-300"
    >
      <div
        class="container mx-auto px-4 py-3 flex justify-between items-center"
      >
        <!-- Logo -->
        <div class="text-xl font-bold text-indigo-600">
          <a href="/" class="flex items-center gap-1">
            <div class="flex items-center justify-center w-12 h-12">
              <i class="fas fa-school text-4xl text-indigo-600"></i>
            </div>
            <div class="flex flex-col">
              <span class="leading-tight text-indigo-600 font-bold"
                >Test HMC</span
              >
              <span class="text-xs text-indigo-600 font-normal leading-none"
                >Cao đẳng Y tế Hà Nội</span
              >
            </div>
          </a>
        </div>

        <!-- Navigation -->
        <nav><%- include('../partials/navigation') %></nav>
      </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8 mt-2"><%- body %></main>

    <!-- Footer -->
    <footer class="bg-gray-100 py-4">
      <div class="container mx-auto px-4 text-center text-gray-500 text-sm">
        <p>
          &copy; <%= new Date().getFullYear() %> Test HMC. Nơi học tập của bạn.
        </p>
      </div>
    </footer>

    <!-- Thông báo session hết hạn -->
    <div
      id="session-expired-modal"
      class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-70 z-[9999]"
      style="display: none"
    >
      <div
        class="bg-white rounded-lg shadow-xl p-6 max-w-md w-full mx-4 border-2 border-red-500"
      >
        <div class="text-center mb-4">
          <div
            class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-red-100 text-red-600 mb-4"
          >
            <i class="fas fa-exclamation-triangle text-3xl"></i>
          </div>
          <h3
            class="text-xl font-bold text-gray-900"
            id="session-expired-title"
          >
            Đăng nhập ở thiết bị khác
          </h3>
          <p
            class="text-md text-gray-700 mt-3 mb-5"
            id="session-expired-message"
          >
            Tài khoản của bạn vừa được đăng nhập ở thiết bị khác. Bạn đã bị đăng
            xuất.
          </p>
        </div>
        <div class="flex justify-center">
          <button
            id="session-expired-button"
            class="bg-red-600 text-white px-6 py-3 rounded-md hover:bg-red-700 transition-colors text-lg font-medium"
          >
            Đồng ý
          </button>
        </div>
      </div>
    </div>

    <!-- Javascript -->
    <script>
      // Debug chung
      const DEBUG = false; // Tắt debug mặc định
      function logDebug(...args) {
        if (DEBUG) {
          console.log("[DEBUG]", ...args);
        }
      }

      // Kiểm tra xem có phải đang ở trang đăng nhập với thông báo lỗi không
      function isOnLoginErrorPage() {
        return (
          window.location.pathname.startsWith("/login") &&
          window.location.search.includes("error=")
        );
      }

      // Hàm kiểm tra xem người dùng có đăng nhập không (sử dụng cả cookie và localStorage)
      function isUserLoggedIn() {
        try {
          // Nếu đang ở trang login với thông báo lỗi, không cần kiểm tra liên tục
          if (isOnLoginErrorPage()) {
            return false;
          }

          // Hiển thị tất cả cookie để debug khi cần
          if (DEBUG) logDebug("All cookies:", document.cookie);

          // Kiểm tra cookie JWT
          const jwtCookieExists = document.cookie
            .split(";")
            .some((cookie) => cookie.trim().startsWith("jwt="));

          // Kiểm tra localStorage
          const localAuthStatus = localStorage.getItem("auth_status");
          const localUserId = localStorage.getItem("user_id");

          if (DEBUG) {
            logDebug("JWT cookie exists:", jwtCookieExists);
            logDebug(
              "Local storage auth:",
              localAuthStatus,
              "userId:",
              localUserId
            );
          }

          // Người dùng được coi là đăng nhập nếu có cookie JWT HOẶC có dữ liệu xác thực trong localStorage
          return (
            jwtCookieExists || (localAuthStatus === "logged_in" && localUserId)
          );
        } catch (error) {
          console.error("Error checking auth status:", error);
          return false;
        }
      }

      // Xử lý dropdown menu và khởi tạo SSE - kích hoạt ngay khi tải trang
      document.addEventListener("DOMContentLoaded", function () {
        if (DEBUG)
          logDebug("DOM content loaded, page URL:", window.location.href);

        // Không kết nối SSE nếu đang ở trang đăng nhập với thông báo lỗi
        if (!isOnLoginErrorPage()) {
          // Khởi tạo SSE với hàm mới
          setTimeout(initializeSSEWithRetry, 500);
        }

        const profileButton = document.getElementById("profile-button");
        const profileDropdown = document.getElementById("profile-dropdown");

        if (profileButton && profileDropdown) {
          profileButton.addEventListener("click", function () {
            profileDropdown.classList.toggle("hidden");
          });

          // Đóng dropdown khi click bên ngoài
          document.addEventListener("click", function (event) {
            if (
              !profileButton.contains(event.target) &&
              !profileDropdown.contains(event.target)
            ) {
              profileDropdown.classList.add("hidden");
            }
          });
        }

        // Đảm bảo kết nối SSE được duy trì nếu đang ở trang đã đăng nhập
        if (!isOnLoginErrorPage()) {
          window.setInterval(checkAndRestoreSSEConnection, 30000); // Rút ngắn thời gian kiểm tra
        }
      });

      // Khởi tạo SSE với cơ chế thử lại tự động
      function initializeSSEWithRetry(retryCount = 0) {
        const maxRetries = 2; // Giảm số lần thử lại

        if (retryCount > maxRetries) {
          if (DEBUG)
            logDebug(`Failed to initialize SSE after ${maxRetries} attempts`);
          return;
        }

        // Nếu đang ở trang login error, bỏ qua
        if (isOnLoginErrorPage()) {
          return;
        }

        if (isUserLoggedIn()) {
          initializeSSEIfLoggedIn();
        } else {
          if (DEBUG)
            logDebug(
              `Not logged in (attempt ${retryCount + 1}/${
                maxRetries + 1
              }), retrying in 1 second...`
            );
          setTimeout(() => initializeSSEWithRetry(retryCount + 1), 1000);
        }
      }

      // Khởi tạo SSE nếu đã đăng nhập
      function initializeSSEIfLoggedIn() {
        logDebug("Initializing SSE connection - authenticated user detected");
        try {
          // Lấy clientId từ URL nếu có
          const urlParams = new URLSearchParams(window.location.search);
          let clientId = urlParams.get("clientId");

          // Nếu không có trong URL, kiểm tra localStorage
          if (!clientId) {
            clientId = localStorage.getItem("clientId");

            // Nếu vẫn không có, tạo mới
            if (!clientId) {
              clientId =
                "client_" +
                Date.now() +
                "_" +
                Math.random().toString(36).substring(2, 10);
              logDebug("Generated new clientId:", clientId);
            }
          }

          // Lưu lại clientId vào localStorage
          localStorage.setItem("clientId", clientId);
          logDebug("Using clientId for SSE connection:", clientId);

          // Đóng kết nối cũ nếu có
          if (window.currentSSEConnection) {
            logDebug("Closing existing SSE connection");
            try {
              window.currentSSEConnection.close();
            } catch (e) {
              console.error("Error closing existing connection:", e);
            }
          }

          // Khởi tạo kết nối mới
          connectSSE(clientId);
          return true;
        } catch (error) {
          console.error("Error during SSE initialization:", error);
          return false;
        }
      }

      // Kiểm tra và khôi phục kết nối SSE nếu cần
      function checkAndRestoreSSEConnection() {
        if (isUserLoggedIn()) {
          // Kiểm tra xem kết nối có tồn tại và có mở không
          if (
            !window.currentSSEConnection ||
            window.currentSSEConnection.readyState !== EventSource.OPEN
          ) {
            logDebug("SSE connection not active, restoring...");
            initializeSSEIfLoggedIn();
          } else {
            logDebug("SSE connection check: Connection active");
          }
        }
      }

      // Kết nối đến SSE endpoint và kiểm tra token định kỳ
      function connectSSE(clientId, retryCount = 0, maxRetries = 5) {
        logDebug(
          `Initializing SSE connection with clientId: ${clientId}, retry: ${retryCount}/${maxRetries}`
        );

        // Nếu đã vượt quá số lần thử kết nối tối đa
        if (retryCount > maxRetries) {
          console.error(
            `Exceeded maximum retries (${maxRetries}) for SSE connection. Giving up.`
          );
          return null;
        }

        try {
          // Kết nối SSE với clientId - sử dụng endpoint mới
          const eventSource = new EventSource(
            `/api/sse/events?clientId=${clientId}`
          );

          // Lưu kết nối để có thể đóng sau này
          window.currentSSEConnection = eventSource;

          // Lắng nghe tất cả sự kiện generic
          eventSource.onmessage = function (event) {
            logDebug("Received generic SSE message:", event.data);
            try {
              const data = JSON.parse(event.data);
              logDebug("Parsed generic data:", data);
            } catch (e) {
              console.error("Error parsing message data:", e);
            }
          };

          // Xử lý sự kiện kết nối
          eventSource.addEventListener("connected", function (event) {
            logDebug("Raw connected event received:", event.data);
            try {
              const data = JSON.parse(event.data);
              logDebug("SSE Connected:", data);

              // Reset retry count khi kết nối thành công
              window.sseRetryCount = 0;
            } catch (e) {
              console.error("Error parsing connected event:", e);
            }
          });

          // Xử lý sự kiện ping
          eventSource.addEventListener("ping", function (event) {
            // console.log("Ping received");
            // // làm sao để phát hiện đã đăng xuất nhưng không nhận được event forced-logout
            // if (isUserLoggedIn()) {
            //   console.log("Thiết bị đang đăng nhập");
            // } else {
            //   console.log("Thiết bị không đang đăng nhập");
            //   // chuyển hướng đến trang đăng nhập
            //   window.location.href = "/login";
            // }
          });

          // Xử lý sự kiện forced-logout
          eventSource.addEventListener("forced-logout", function (event) {
            logDebug("Raw forced-logout event received:", event.data);
            try {
              const data = JSON.parse(event.data);
              localStorage.setItem("forceLogoutEvent", Date.now());

              // Đảm bảo đóng kết nối SSE
              try {
                eventSource.close();
                logDebug("SSE connection closed successfully");
              } catch (e) {
                console.error("Error closing EventSource:", e);
              }

              // Xóa cookie để đăng xuất
              document.cookie =
                "jwt=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;";
              logDebug("JWT cookie cleared");

              // Xóa thông tin xác thực trong localStorage
              localStorage.removeItem("auth_status");
              localStorage.removeItem("user_id");
              logDebug("Auth data removed from localStorage");

              // Ghi log để debug
              console.warn(
                "FORCED LOGOUT EVENT RECEIVED - This device is being logged out because the account was used on another device"
              );

              // Hiển thị thông báo và chuyển hướng
              showLogoutNotification(
                data.message ||
                  "Tài khoản của bạn vừa được đăng nhập ở thiết bị khác. Bạn đã bị đăng xuất."
              );
            } catch (error) {
              console.error("Error handling forced logout:", error);
              // Fallback với thông báo đơn giản
              alert(
                "Phiên đăng nhập của bạn đã hết hạn. Vui lòng đăng nhập lại."
              );
              // Chuyển hướng đến trang đăng nhập trong trường hợp lỗi
              window.location.href =
                "/login?error=Đã có lỗi xảy ra, vui lòng đăng nhập lại";
            }
          });

          // Xử lý khi kết nối mở
          eventSource.onopen = function () {
            logDebug("SSE connection opened successfully");
            // Reset retry count khi kết nối thành công
            window.sseRetryCount = 0;
          };

          // Xử lý khi có lỗi kết nối
          eventSource.onerror = function (error) {
            console.error("SSE connection error:", error);

            // Tăng số lần thử kết nối lại
            const nextRetryCount = (window.sseRetryCount || 0) + 1;
            window.sseRetryCount = nextRetryCount;

            // Tính thời gian delay tăng dần (exponential backoff)
            const retryDelay = Math.min(
              5000 * Math.pow(1.5, nextRetryCount - 1),
              30000
            );

            logDebug(
              `SSE connection error. Retry ${nextRetryCount}/${maxRetries} scheduled in ${retryDelay}ms`
            );

            // Thử kết nối lại sau khoảng thời gian delay
            setTimeout(() => {
              if (eventSource.readyState === EventSource.CLOSED) {
                logDebug(
                  `Reconnecting SSE after error (attempt ${nextRetryCount}/${maxRetries})`
                );
                connectSSE(clientId, nextRetryCount, maxRetries);
              }
            }, retryDelay);
          };

          logDebug("EventSource created and event handlers attached");
          return eventSource;
        } catch (error) {
          console.error("Error creating EventSource:", error);

          // Tăng số lần thử kết nối lại
          const nextRetryCount = retryCount + 1;

          // Tính thời gian delay tăng dần (exponential backoff)
          const retryDelay = Math.min(
            5000 * Math.pow(1.5, nextRetryCount - 1),
            30000
          );

          logDebug(
            `Failed to create EventSource. Retry ${nextRetryCount}/${maxRetries} scheduled in ${retryDelay}ms`
          );

          // Thử kết nối lại sau khoảng thời gian delay
          setTimeout(() => {
            connectSSE(clientId, nextRetryCount, maxRetries);
          }, retryDelay);

          return null;
        }
      }

      // Hiển thị thông báo và tự động chuyển hướng
      function showLogoutNotification(message) {
        logDebug("Session expired, message:", message);

        // Lấy clientId TRƯỚC KHI clear data
        const clientId = localStorage.getItem("clientId");
        logDebug("Retrieved clientId before clearing:", clientId);

        // Hóa và ẩn toàn bộ nội dung trang hiện tại trước khi hiển thị modal
        hidePageContent();

        // Clear toàn bộ dữ liệu sau khi lấy clientId
        clearAllData();

        // Tạo URL chuyển hướng
        const redirectUrl = clientId
          ? `/login?error=Tài khoản của bạn đã được đăng nhập ở thiết bị khác&clientId=${clientId}`
          : `/login?error=Tài khoản của bạn đã được đăng nhập ở thiết bị khác`;

        logDebug("Redirect URL prepared:", redirectUrl);

        // Hàm redirect
        const doRedirect = () => {
          logDebug("Executing redirect to:", redirectUrl);
          try {
            window.location.href = redirectUrl;
          } catch (error) {
            console.error("Error during redirect:", error);
            // Fallback
            window.location.replace(redirectUrl);
          }
        };

        // Tự động chuyển hướng sau 1.5 giây
        setTimeout(() => {
          logDebug("Auto redirect after 1.5 seconds to:", redirectUrl);
          doRedirect();
        }, 1500);
      }

      // Hàm hóa và ẩn toàn bộ nội dung trang
      function hidePageContent() {
        // Kiểm tra xem overlay đã tồn tại chưa
        if (document.getElementById("logout-content-overlay")) {
          return; // Đã có overlay rồi, không tạo thêm
        }

        // Tạo overlay để che toàn bộ nội dung
        const overlay = document.createElement("div");
        overlay.id = "logout-content-overlay";
        overlay.style.cssText = `
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.95);
          z-index: 999999;
          display: flex;
          align-items: center;
          justify-content: center;
          backdrop-filter: blur(10px);
          transition: opacity 0.3s ease;
        `;

        // Thêm thông báo trong overlay
        overlay.innerHTML = `
          <div style="
            text-align: center;
            color: white;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 400px;
            padding: 2rem;
            animation: fadeIn 0.5s ease-in-out;
          ">
            <div style="
              display: inline-flex;
              align-items: center;
              justify-content: center;
              width: 80px;
              height: 80px;
              border-radius: 50%;
              background: rgba(239, 68, 68, 0.2);
              margin-bottom: 1.5rem;
            ">
              <i class="fas fa-sign-out-alt" style="font-size: 2.5rem; color: #EF4444;"></i>
            </div>
            <h2 style="
              font-size: 1.5rem;
              font-weight: bold;
              margin: 0 0 1rem 0;
              color: #EF4444;
            ">BẠN ĐÃ BỊ ĐĂNG XUẤT</h2>
            <p style="
              font-size: 1rem;
              margin: 0;
              opacity: 0.9;
              line-height: 1.5;
            ">Tài khoản của bạn đã được đăng nhập ở thiết bị khác</p>
            <div style="
              margin-top: 1.5rem;
              font-size: 0.9rem;
              opacity: 0.7;
            ">
              <i class="fas fa-spinner fa-spin" style="margin-right: 0.5rem;"></i>
              Đang chuyển hướng về trang đăng nhập...
            </div>
          </div>
        `;

        // Blur toàn bộ nội dung trang
        const body = document.body;

        // Thêm class blur cho body (trừ overlay)
        const allElements = body.querySelectorAll(
          "*:not(#logout-content-overlay):not(#logout-content-overlay *)"
        );
        allElements.forEach((el) => {
          if (el.id !== "logout-content-overlay") {
            el.style.filter = "blur(5px)";
            el.style.pointerEvents = "none";
            el.style.userSelect = "none";
          }
        });

        // Thêm overlay vào body
        document.body.appendChild(overlay);

        logDebug("Page content hidden and blurred");
      }

      // Hàm clear toàn bộ dữ liệu
      function clearAllData() {
        try {
          // Lưu lại clientId trước khi clear
          const clientId = localStorage.getItem("clientId");

          // Clear tất cả localStorage
          localStorage.clear();

          // Clear tất cả sessionStorage
          sessionStorage.clear();

          // Clear tất cả cookies bằng cách set expire date về quá khứ
          const cookies = document.cookie.split(";");
          for (let cookie of cookies) {
            const eqPos = cookie.indexOf("=");
            const name =
              eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();
            if (name) {
              // Clear cookie cho tất cả paths và domains
              document.cookie = `${name}=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT; Domain=${window.location.hostname};`;
              document.cookie = `${name}=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;`;
              document.cookie = `${name}=; Expires=Thu, 01 Jan 1970 00:00:01 GMT;`;
            }
          }

          // Khôi phục lại clientId để sử dụng cho lần đăng nhập tiếp theo
          if (clientId) {
            localStorage.setItem("clientId", clientId);
          }

          logDebug(
            "All data cleared successfully, clientId preserved:",
            clientId
          );
        } catch (error) {
          console.error("Error clearing data:", error);
        }
      }
    </script>

    <script src="/js/check-online.js"></script>
    <script src="/js/check-auth.js"></script>
    <script src="/js/security-measures.js"></script>

    <!-- Initialize Lucide Icons -->
    <script>
      function setupSecurityMeasures() {
        if (typeof GlobalSecurityMeasures === "function") {
          GlobalSecurityMeasures();
        }
      }
      setupSecurityMeasures();

      // Initialize Lucide icons when DOM is loaded
      document.addEventListener("DOMContentLoaded", function () {
        if (typeof lucide !== "undefined") {
          lucide.createIcons();
        }
      });
    </script>

    <!-- Scripts từ các trang con -->
    <%- script %>
  </body>
</html>
