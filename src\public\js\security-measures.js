// Security Measures - <PERSON><PERSON><PERSON> biện pháp bảo mật toàn cục
// File này chứa tất cả các biện pháp bảo mật có thể áp dụng cho mọi trang

/**
 * Thiết lập các biện pháp bảo mật toàn diện
 * @param {Object} options - <PERSON><PERSON><PERSON> tùy chọn cấu hình
 * @param {string} options.contextMessage - Thông báo ngữ cảnh (mặc định: "làm bài")
 * @param {boolean} options.enableDevToolsDetection - Bật/tắt phát hiện DevTools (mặc định: true)
 * @param {boolean} options.enableScreenshotBlocking - Bật/tắt chặn chụp màn hình (mặc định: true)
 * @param {boolean} options.enableRightClickBlocking - Bật/tắt chặn chuột phải (mặc định: true)
 * @param {boolean} options.enableCopyBlocking - Bật/tắt chặn sao chép (mặc định: true)
 * @param {boolean} options.enablePrintBlocking - Bật/tắt chặn in ấn (mặc định: true)
 * @param {number} options.devToolsThreshold - Ngưỡng phát hiện DevTools (mặc định: 160)
 * @param {boolean} options.redirectOnDevTools - Tự động chuyển hướng về trang chủ khi phát hiện DevTools (mặc định: false)
 * @param {string} options.redirectUrl - URL để chuyển hướng khi phát hiện DevTools (mặc định: "/home")
 */
function GlobalSecurityMeasures(options = {}) {
  // Cấu hình mặc định
  const config = {
    contextMessage: "làm bài",
    enableDevToolsDetection: true,
    enableScreenshotBlocking: true,
    enableRightClickBlocking: true,
    enableCopyBlocking: true,
    enablePrintBlocking: true,
    enableViewSourceBlocking: true,
    enableSavePageBlocking: true,
    enableDragDropBlocking: true,
    devToolsThreshold: 160,
    redirectOnDevTools: false,
    redirectUrl: "/home",
    ...options,
  };

  // Tự động phát hiện ngữ cảnh dựa trên trang hiện tại
  if (!options.contextMessage) {
    if (document.querySelector(".result-display")) {
      config.contextMessage = "xem kết quả";
    } else if (document.querySelector(".retry-wrong-questions")) {
      config.contextMessage = "làm lại câu sai";
    } else if (document.querySelector(".google-form-container")) {
      config.contextMessage = "làm bài Google Form";
    } else {
      config.contextMessage = "làm bài";
    }
  }

  // console.log(`Đang thiết lập bảo mật cho: ${config.contextMessage}`);

  // 1. Ngăn chặn chụp màn hình
  if (config.enableScreenshotBlocking) {
    setupScreenshotBlocking(config.contextMessage);
  }

  // 2. Ngăn chặn DevTools
  if (config.enableDevToolsDetection) {
    setupDevToolsBlocking(config.contextMessage);
  }

  // 3. Ngăn chặn chuột phải
  if (config.enableRightClickBlocking) {
    setupRightClickBlocking(config.contextMessage);
  }

  // 4. Ngăn chặn sao chép
  if (config.enableCopyBlocking) {
    setupCopyBlocking(config.contextMessage);
  }

  // 5. Ngăn chặn in ấn
  if (config.enablePrintBlocking) {
    setupPrintBlocking(config.contextMessage);
  }

  // 6. Ngăn chặn xem source
  if (config.enableViewSourceBlocking) {
    setupViewSourceBlocking(config.contextMessage);
  }

  // 7. Ngăn chặn lưu trang
  if (config.enableSavePageBlocking) {
    setupSavePageBlocking(config.contextMessage);
  }

  // 8. Ngăn chặn kéo thả
  if (config.enableDragDropBlocking) {
    setupDragDropBlocking();
  }

  // 9. Phát hiện DevTools bằng monitor
  if (config.enableDevToolsDetection) {
    setupDevToolsMonitoring(
      config.contextMessage,
      config.devToolsThreshold,
      config.redirectOnDevTools,
      config.redirectUrl
    );
  }

  // 10. Giám sát DOM changes
  setupDOMMonitoring();

  // console.log(
  //   `Đã thiết lập tất cả biện pháp bảo mật cho ${config.contextMessage}`
  // );
}

// Ngăn chặn chụp màn hình
function setupScreenshotBlocking(contextMessage) {
  document.addEventListener(
    "keyup",
    function (e) {
      // Windows: PrintScreen, Alt+PrintScreen
      if (e.key === "PrintScreen" || (e.altKey && e.key === "PrintScreen")) {
        e.preventDefault();
        // showSecurityWarning(
        //   `Chụp màn hình không được phép trong quá trình ${contextMessage}!`
        // );
        return false;
      }
      // Mac: Command+Shift+3, Command+Shift+4, Command+Shift+5
      if (
        e.metaKey &&
        e.shiftKey &&
        (e.key === "3" || e.key === "4" || e.key === "5")
      ) {
        e.preventDefault();
        // showSecurityWarning(
        //   `Chụp màn hình không được phép trong quá trình ${contextMessage}!`
        // );
        return false;
      }
    },
    true
  );
}

// Ngăn chặn DevTools
function setupDevToolsBlocking(contextMessage) {
  document.addEventListener(
    "keydown",
    function (e) {
      // Ngăn F12
      if (e.key === "F12") {
        e.preventDefault();
        // showSecurityWarning(
        //   `Không được phép sử dụng Developer Tools trong quá trình ${contextMessage}!`
        // );
        return false;
      }

      // Ngăn Ctrl+Shift+I / Command+Option+I
      if (
        (e.ctrlKey &&
          e.shiftKey &&
          (e.key === "I" || e.key.toLowerCase() === "i")) ||
        (e.metaKey && e.altKey && e.key.toLowerCase() === "i")
      ) {
        e.preventDefault();
        // showSecurityWarning(
        //   `Không được phép sử dụng Developer Tools trong quá trình ${contextMessage}!`
        // );
        return false;
      }

      // Ngăn Ctrl+Shift+J / Command+Option+J (Console)
      if (
        (e.ctrlKey &&
          e.shiftKey &&
          (e.key === "J" || e.key.toLowerCase() === "j")) ||
        (e.metaKey && e.altKey && e.key.toLowerCase() === "j")
      ) {
        e.preventDefault();
        // showSecurityWarning(
        //   `Không được phép sử dụng Console trong quá trình ${contextMessage}!`
        // );
        return false;
      }

      // Ngăn Ctrl+Shift+C / Command+Option+C (Inspector)
      if (
        (e.ctrlKey &&
          e.shiftKey &&
          (e.key === "C" || e.key.toLowerCase() === "c")) ||
        (e.metaKey && e.altKey && e.key.toLowerCase() === "c")
      ) {
        e.preventDefault();
        // showSecurityWarning(
        //   `Không được phép sử dụng Elements Inspector trong quá trình ${contextMessage}!`
        // );
        return false;
      }
    },
    true
  );
}

// Ngăn chặn chuột phải
function setupRightClickBlocking(contextMessage) {
  document.addEventListener("contextmenu", function (e) {
    e.preventDefault();
    // showSecurityWarning(
    //   `Không được phép sử dụng menu chuột phải trong quá trình ${contextMessage}!`
    // );
    return false;
  });
}

// Ngăn chặn sao chép
function setupCopyBlocking(contextMessage) {
  document.addEventListener("copy", function (e) {
    e.preventDefault();
    // showSecurityWarning(
    //   `Không được phép sao chép nội dung trong quá trình ${contextMessage}!`
    // );
    return false;
  });
}

// Ngăn chặn in ấn
function setupPrintBlocking(contextMessage) {
  // Ngăn Ctrl+P / Command+P
  document.addEventListener("keydown", function (e) {
    if (
      (e.ctrlKey && e.key.toLowerCase() === "p") ||
      (e.metaKey && e.key.toLowerCase() === "p")
    ) {
      e.preventDefault();
      // showSecurityWarning(
      //   `Không được phép in ấn trong quá trình ${contextMessage}!`
      // );
      return false;
    }
  });

  // Ngăn chặn in ấn từ menu
  window.addEventListener("beforeprint", function (e) {
    e.preventDefault();
    // showSecurityWarning(
    //   `Không được phép in ấn trong quá trình ${contextMessage}!`
    // );
    return false;
  });
}

// Ngăn chặn xem source
function setupViewSourceBlocking(contextMessage) {
  document.addEventListener("keydown", function (e) {
    // Ngăn Ctrl+U (xem nguồn)
    if (e.ctrlKey && e.key.toLowerCase() === "u") {
      e.preventDefault();
      // showSecurityWarning(
      //   `Không được phép xem mã nguồn trong quá trình ${contextMessage}!`
      // );
      return false;
    }
  });
}

// Ngăn chặn lưu trang
function setupSavePageBlocking(contextMessage) {
  document.addEventListener("keydown", function (e) {
    // Ngăn Ctrl+S / Command+S
    if (
      (e.ctrlKey && e.key.toLowerCase() === "s") ||
      (e.metaKey && e.key.toLowerCase() === "s")
    ) {
      e.preventDefault();
      // showSecurityWarning(
      //   `Không được phép lưu trang trong quá trình ${contextMessage}!`
      // );
      return false;
    }
  });
}

// Ngăn chặn kéo thả
function setupDragDropBlocking() {
  document.addEventListener("dragstart", function (e) {
    e.preventDefault();
    return false;
  });
}

// Phát hiện DevTools bằng monitoring
function setupDevToolsMonitoring(
  contextMessage,
  threshold = 160,
  redirectOnDevTools = false,
  redirectUrl = "/home"
) {
  let devtoolsOpen = false;
  let hasRedirected = false; // Đảm bảo chỉ redirect 1 lần

  // Phương pháp 1: Kiểm tra kích thước cửa sổ
  const checkWindowSize = function () {
    // Chỉ kiểm tra khi cửa sổ không ở chế độ fullscreen
    if (
      window.innerHeight === screen.height &&
      window.innerWidth === screen.width
    ) {
      return false; // Bỏ qua khi fullscreen
    }

    const widthDiff = window.outerWidth - window.innerWidth;
    const heightDiff = window.outerHeight - window.innerHeight;

    // Kiểm tra chặt chẽ hơn - DevTools thường tạo ra sự chênh lệch đáng kể
    // Tăng ngưỡng để giảm false positive
    const significantWidthDiff = widthDiff > threshold + 50; // Tăng thêm 50px
    const significantHeightDiff = heightDiff > threshold + 50; // Tăng thêm 50px

    return significantWidthDiff || significantHeightDiff;
  };

  // Phương pháp 2: Kiểm tra tỷ lệ devicePixelRatio và viewport
  const checkDevicePixelRatio = function () {
    return window.devicePixelRatio && window.devicePixelRatio !== 1;
  };

  // Phương pháp 3: Kiểm tra console.clear được override
  const checkConsoleOverride = function () {
    let devtools = false;
    const originalClear = console.clear;
    let clearCalled = false;

    // Override console.clear
    console.clear = function () {
      clearCalled = true;
    };

    // Gọi console.clear
    console.clear();
    devtools = clearCalled;

    // Khôi phục console.clear
    console.clear = originalClear;

    return devtools;
  };

  // Phương pháp 5: Kiểm tra thuộc tính screen
  const checkScreenDimensions = function () {
    // Kiểm tra chặt chẽ hơn về screen dimensions
    const screenHeightDiff = window.screen.height - window.screen.availHeight;
    const viewportHeightDiff = window.screen.availHeight - window.innerHeight;

    // Chỉ phát hiện khi có sự chênh lệch bất thường
    return screenHeightDiff > 100 && viewportHeightDiff > 200;
  };

  // Phương pháp 6: Kiểm tra Firebug và các DevTools khác
  const checkFirebugLike = function () {
    return !!(
      window.console &&
      (window.console.firebug ||
        window.console._commandLineAPI ||
        window.console.table)
    );
  };

  // Hàm kiểm tra tổng hợp tất cả phương pháp
  const checkDevTools = function () {
    let detectionCount = 0;
    let reliableDetections = 0; // Đếm các phương pháp phát hiện đáng tin cậy

    // Phương pháp 1: Kiểm tra kích thước cửa sổ (đáng tin cậy nhất)
    if (checkWindowSize()) {
      detectionCount++;
      reliableDetections++;
    }

    // Phương pháp 2: Kiểm tra Firebug và các DevTools extensions (đáng tin cậy)
    if (checkFirebugLike()) {
      detectionCount++;
      reliableDetections++;
    }

    // Phương pháp 3: Kiểm tra screen dimensions (ít đáng tin cậy hơn)
    if (checkScreenDimensions()) {
      detectionCount++;
    }

    // Phương pháp 4: Kiểm tra console override (ít gây gián đoạn hơn, nhưng ít tin cậy)
    try {
      if (checkConsoleOverride()) {
        detectionCount++;
      }
    } catch (e) {
      // Bỏ qua lỗi
    }

    // Phương pháp 5: Kiểm tra debugger timing (chỉ khi không có popup và có các dấu hiệu khác)
    if (
      !document.querySelector(".security-warning-overlay") &&
      detectionCount > 0
    ) {
      try {
        if (checkDebuggerTiming()) {
          detectionCount++;
        }
      } catch (e) {
        // Bỏ qua lỗi
      }
    }

    // YÊU CẦU NGHIÊM NGẶT HƠN:
    // - Phải có ít nhất 1 phương pháp đáng tin cậy (kích thước cửa sổ hoặc Firebug)
    // - VÀ tổng cộng ít nhất 3 phương pháp phát hiện thành công
    const isDevToolsDetected = reliableDetections >= 1 && detectionCount >= 3;

    if (isDevToolsDetected) {
      if (!devtoolsOpen) {
        // devtoolsOpen = true;
        // document.body.style.cursor = "none";
        // document.body.style.pointerEvents = "none";
        // // xóa hết các phần tử trong trang
        // document.querySelectorAll("*").forEach(function (element) {
        //   element.style.display = "none";
        // });
        // document.body.innerHTML = "";
        // // xóa hết các script trong trang
        // document.querySelectorAll("script").forEach(function (element) {
        //   element.remove();
        // });
        // if (redirectOnDevTools && !hasRedirected) {
        //   hasRedirected = true;
        //   // Chuyển hướng ngay lập tức khi phát hiện DevTools
        //   console.warn(
        //     "Phát hiện Developer Tools! Đang chuyển về trang chủ..."
        //   );
        //   // Dừng tất cả interval để tránh conflict
        //   if (window.securityInterval) {
        //     clearInterval(window.securityInterval);
        //   }
        //   // Chuyển hướng ngay lập tức
        //   // window.location.href = redirectUrl;
        //   // đóng devtools
        //   return;
        // } else {
        //   // Chỉ hiển thị cảnh báo nếu không bật chuyển hướng
        //   // showSecurityWarning(
        //   //   `Phát hiện Developer Tools đang mở. Vui lòng đóng Developer Tools để tiếp tục ${contextMessage}!`
        //   // );
        // }
      }
    } else {
      devtoolsOpen = false;
    }

    // Kiểm tra khả năng sửa đổi CSS (chỉ khi có dấu hiệu khác)
    if (detectionCount > 0) {
      try {
        const testElement = document.createElement("div");
        testElement.style.display = "none";
        document.body.appendChild(testElement);

        const testBeforeCSS = window
          .getComputedStyle(testElement)
          .getPropertyValue("display");
        if (testBeforeCSS !== "none") {
          if (redirectOnDevTools && !hasRedirected) {
            hasRedirected = true;
            // Chuyển hướng ngay lập tức nếu phát hiện CSS bị sửa đổi
            console.warn(
              "Phát hiện CSS đã bị sửa đổi! Đang chuyển về trang chủ..."
            );
            window.location.href = redirectUrl;
            return;
          } else {
            showSecurityWarning("Phát hiện CSS đã bị sửa đổi!");
          }
        }

        document.body.removeChild(testElement);
      } catch (e) {
        console.error("Lỗi kiểm tra CSS:", e);
      }
    }
  };

  // Kiểm tra ngay lập tức khi tải trang
  setTimeout(() => {
    checkDevTools();
  }, 1000); // Tăng thời gian chờ lên 2 giây để trang tải hoàn toàn

  // Thực hiện kiểm tra định kỳ mỗi 2 giây (giảm tần suất để ổn định hơn)
  const securityInterval = setInterval(checkDevTools, 1000);

  // Lưu interval ID để có thể xóa khi cần thiết
  window.securityInterval = securityInterval;

  // Lưu thông tin để có thể truy cập từ bên ngoài
  window.devToolsDetection = {
    isOpen: () => devtoolsOpen,
    hasRedirected: () => hasRedirected,
  };
}

// Giám sát DOM changes
function setupDOMMonitoring() {
  if (typeof MutationObserver !== "undefined") {
    const observer = new MutationObserver(function (mutations) {
      mutations.forEach(function (mutation) {
        if (mutation.addedNodes.length) {
          for (let i = 0; i < mutation.addedNodes.length; i++) {
            const node = mutation.addedNodes[i];
            if (node.nodeType === 1) {
              // Phần tử Node
              if (
                node.tagName === "IFRAME" ||
                (node.id && node.id.match(/firebug|react-devtools|__vue/i)) ||
                (node.className &&
                  typeof node.className === "string" &&
                  node.className.match(/firebug|react-devtools|__vue/i))
              ) {
                node.parentNode.removeChild(node);
                showSecurityWarning(
                  "Phát hiện DevTools Extension đang hoạt động!"
                );
              }
            }
          }
        }
      });
    });

    // Bắt đầu quan sát DOM
    observer.observe(document.documentElement, {
      childList: true,
      subtree: true,
    });

    // Lưu observer để có thể ngắt kết nối khi nộp bài
    window.securityObserver = observer;
  }
}

// Hiển thị cảnh báo bảo mật
function showSecurityWarning(message, callback) {
  // Kiểm tra xem đã có cảnh báo nào đang hiển thị không
  if (document.querySelector(".security-warning-overlay")) {
    return; // Không tạo cảnh báo mới nếu đã có
  }

  const warningEl = document.createElement("div");
  warningEl.className =
    "fixed inset-0 flex items-center justify-center z-50 security-warning-overlay";
  warningEl.innerHTML = `
    <div class="fixed inset-0 bg-black opacity-70"></div>
    <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4 z-10 relative security-warning">
      <div class="flex items-center mb-4 text-red-600">
        <i class="fas fa-exclamation-triangle text-3xl mr-3"></i>
        <h3 class="text-lg font-bold security-warning-title">Cảnh báo bảo mật</h3>
      </div>
      <p class="mb-6 text-gray-700 security-warning-message">${message}</p>
      <div class="text-right">
        <button id="securityWarningCloseBtn" class="bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-lg transition-colors security-warning-btn">
          Đã hiểu
        </button>
      </div>
    </div>
  `;

  document.body.appendChild(warningEl);

  // Xử lý đóng cảnh báo
  document
    .getElementById("securityWarningCloseBtn")
    .addEventListener("click", function () {
      document.body.removeChild(warningEl);
      if (callback) {
        callback();
      }
    });

  // Tự động đóng sau 5 giây
  setTimeout(function () {
    if (document.body.contains(warningEl)) {
      document.body.removeChild(warningEl);
      if (callback) {
        callback();
      }
    }
  }, 5000);
}

// Cleanup function - Dọn dẹp khi không cần thiết
function cleanupSecurityMeasures() {
  // Xóa interval kiểm tra DevTools
  if (window.securityInterval) {
    clearInterval(window.securityInterval);
    window.securityInterval = null;
  }

  // Ngắt kết nối MutationObserver
  if (window.securityObserver) {
    window.securityObserver.disconnect();
    window.securityObserver = null;
  }

  console.log("Đã dọn dẹp các biện pháp bảo mật");
}

// Export functions để có thể sử dụng trong các file khác
if (typeof module !== "undefined" && module.exports) {
  module.exports = {
    GlobalSecurityMeasures,
    cleanupSecurityMeasures,
    showSecurityWarning,
  };
}
