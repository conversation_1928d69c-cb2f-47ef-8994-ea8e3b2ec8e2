// Khởi tạo biến cần thiết
let currentQuestionIndex = 0;
let score = 0;
let userAnswers = [];
let startTime = Date.now();
let feedbackTimeout;
let examData = null;
let soundEnabled = true; // Biến kiểm soát bật/tắt âm thanh

// Âm thanh - sử dụng file local từ thư mục media
const correctSound = new Audio("/media/correct_answer.mp3");
correctSound.preload = "auto";

const wrongSound = new Audio("/media/wrong_answer.mp3");
wrongSound.preload = "auto";

// Kiểm tra trạng thái âm thanh đã nạp
correctSound.addEventListener("canplaythrough", () => {
  console.log("File âm thanh đúng đã sẵn sàng phát");
});

wrongSound.addEventListener("canplaythrough", () => {
  console.log("File âm thanh sai đã sẵn sàng phát");
});

// Kiểm tra lỗi nạp âm thanh
correctSound.addEventListener("error", (e) => {
  console.error("Lỗi nạp file âm thanh đúng:", e);
});

wrongSound.addEventListener("error", (e) => {
  console.error("Lỗi nạp file âm thanh sai:", e);
});

// Hàm khởi tạo dữ liệu bài thi
function initExamData(exam, questionsData) {
  examData = {
    id: exam,
    questions: questionsData,
  };

  // Chờ DOM tải xong
  document.addEventListener("DOMContentLoaded", () => {
    showQuestion(currentQuestionIndex);
    setupSoundToggle();

    // Kích hoạt autoplay
    document.addEventListener("click", initAudio, { once: true });
  });
}

// Kích hoạt audio để có thể tự động phát
function initAudio() {
  // Chạy âm thanh im lặng để kích hoạt autoplay
  const silentAudio = new Audio(
    "data:audio/mp3;base64,SUQzBAAAAAAAI1RTU0UAAAAPAAADTGF2ZjU4LjM1LjEwNAAAAAAAAAAAAAAA//tAwAAAAAAAAAAAAAAAAAAAAAAASW5mbwAAAA8AAAACAAABIADf/////////////////////////////////////////wAAADlMQU1FMy4xMDABzQAAAAAAAAAAFIAkCLhCAABAAAABIDVjN9YAAAAAAAAAAAAAAAAAAAAAAP/7QMQAAAf4XkAAAgAAA0gAAABBAAADSAAAAETEFNRTMuMTAwVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVf/7QMRVA8AAAaQAAAABg2gAAABFVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVQ=="
  );
  silentAudio
    .play()
    .then(() => {
      console.log("Audio autoplay đã được kích hoạt");
    })
    .catch((e) => {
      console.error("Không thể kích hoạt autoplay:", e);
    });

  // Nạp trước file âm thanh để đảm bảo sẵn sàng khi cần
  correctSound.load();
  wrongSound.load();
}

// Thêm chức năng bật/tắt âm thanh
function setupSoundToggle() {
  const headerControls = document.querySelector(
    ".text-white.flex.items-center.justify-between"
  );
  if (!headerControls) return;

  const soundToggle = document.createElement("div");
  soundToggle.className = "flex items-center mr-4";
  soundToggle.innerHTML = `
    <label class="inline-flex items-center cursor-pointer">
      <input type="checkbox" id="soundToggle" class="sr-only peer" checked>
      <div class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
      <span class="ms-3 text-sm font-medium text-white">Âm thanh</span>
    </label>
  `;

  // Thêm vào vị trí trước id=questionCounter
  const questionCounter = headerControls.querySelector("#questionCounter");
  headerControls.insertBefore(soundToggle, questionCounter);

  // Thêm sự kiện xử lý bật tắt âm thanh
  document
    .getElementById("soundToggle")
    .addEventListener("change", function () {
      soundEnabled = this.checked;
      console.log("Âm thanh đã được " + (soundEnabled ? "bật" : "tắt"));
    });
}

// Phát âm thanh theo loại
function playSound(isCorrect) {
  if (!soundEnabled) return;

  // Dừng tất cả âm thanh trước khi phát âm thanh mới
  stopAllSounds();

  try {
    // Reset âm thanh trước khi phát để đảm bảo phát từ đầu
    if (isCorrect) {
      correctSound.currentTime = 0;
      correctSound
        .play()
        .then(() => {
          console.log("Đã phát âm thanh đúng");
        })
        .catch((error) => {
          console.error("Lỗi phát âm thanh đúng:", error);
        });
    } else {
      wrongSound.currentTime = 0;
      wrongSound
        .play()
        .then(() => {
          console.log("Đã phát âm thanh sai");
        })
        .catch((error) => {
          console.error("Lỗi phát âm thanh sai:", error);
        });
    }
  } catch (error) {
    console.error("Lỗi khi phát âm thanh:", error);
  }
}

// Dừng tất cả âm thanh đang phát
function stopAllSounds() {
  try {
    if (correctSound) {
      correctSound.pause();
      correctSound.currentTime = 0;
    }
    if (wrongSound) {
      wrongSound.pause();
      wrongSound.currentTime = 0;
    }
  } catch (error) {
    console.error("Lỗi khi dừng âm thanh:", error);
  }
}

// Hiển thị câu hỏi hiện tại
function showQuestion(index) {
  // Dừng âm thanh nếu đang phát
  stopAllSounds();

  if (!examData || !examData.questions) return;

  const question = examData.questions[index];
  const questionTemplate = document.getElementById("questionTemplate");
  const questionContainer = document.getElementById("questionContainer");

  // Tạo bản sao từ template
  const questionEl = questionTemplate.content.cloneNode(true);

  // Cập nhật nội dung
  questionEl.querySelector("#questionText").textContent = `Câu ${index + 1}: ${
    question.text
  }`;

  // Kiểm tra và hiển thị hình ảnh nếu có
  const questionImage = questionEl.querySelector("#questionImage");
  if (question.image) {
    questionImage.classList.remove("hidden");
    questionImage.querySelector("img").src = question.image;
  }

  // Thêm các lựa chọn
  const optionsContainer = questionEl.querySelector("#optionsContainer");
  optionsContainer.innerHTML = "";

  if (question.type === "multiple-choice") {
    question.options.forEach((option, optIndex) => {
      const optionButton = document.createElement("button");
      optionButton.className =
        "p-4 border-2 border-gray-200 rounded-lg text-left transition-colors focus:outline-none";
      optionButton.dataset.index = optIndex; // Thêm data attribute để theo dõi chỉ số

      // Tạo nội dung cho từng lựa chọn
      optionButton.innerHTML = `
        <div class="flex items-center">
          <div class="flex-shrink-0 h-6 w-6 rounded-full bg-purple-100 text-purple-600 flex items-center justify-center mr-3">
            ${String.fromCharCode(65 + optIndex)}
          </div>
          <div>${option.text}</div>
        </div>
      `;

      // Xử lý sự kiện khi người dùng chọn
      optionButton.addEventListener("click", () =>
        selectAnswer(optIndex, option.isCorrect)
      );

      optionsContainer.appendChild(optionButton);
    });
  }

  // Cập nhật counter và progress
  document.getElementById("questionCounter").textContent = `Câu hỏi ${
    index + 1
  }/${examData.questions.length}`;
  document.getElementById("progressBar").style.width = `${
    ((index + 1) / examData.questions.length) * 100
  }%`;

  // Ẩn nút tiếp theo/kết thúc ban đầu
  questionEl.querySelector("#nextButton").classList.add("hidden");
  questionEl.querySelector("#finishButton").classList.add("hidden");

  // Xóa nội dung cũ và thêm câu hỏi mới
  questionContainer.innerHTML = "";
  questionContainer.appendChild(questionEl);
}

// Xử lý khi người dùng chọn đáp án
function selectAnswer(optionIndex, isCorrect) {
  // Vô hiệu hóa tất cả các nút lựa chọn
  const allOptions = document.querySelectorAll("#optionsContainer button");

  // Tìm đáp án đúng
  const correctOptionIndex = examData.questions[
    currentQuestionIndex
  ].options.findIndex((opt) => opt.isCorrect);

  // Phát âm thanh phù hợp
  playSound(isCorrect);

  if (isCorrect) {
    // Nếu đúng: Chỉ hiển thị đáp án đúng, ẩn hết các đáp án khác
    allOptions.forEach((button, index) => {
      if (index !== optionIndex) {
        // Ẩn nội dung nhưng giữ nguyên vị trí
        button.style.visibility = "hidden";
        // Giữ lại thẻ button nhưng làm mờ đi
        button.style.opacity = "0";
        // Vẫn giữ không gian chiếm chỗ
        button.style.display = "block";
      } else {
        // Highlight đáp án đúng
        button.disabled = true;
        button.classList.add(
          "bg-green-100",
          "border-green-500",
          "text-green-800"
        );

        // Thêm hiệu ứng scale
        button.style.transform = "scale(1.05)";
        button.style.transition = "transform 0.3s ease";

        // Thêm icon dấu tích xanh
        const checkIcon = document.createElement("span");
        checkIcon.className = "absolute top-2 right-2 text-green-600";
        checkIcon.innerHTML = '<i class="fas fa-check-circle"></i>';
        button.style.position = "relative";
        button.appendChild(checkIcon);
      }
    });

    score++;
  } else {
    // Nếu sai: Chỉ hiển thị đáp án đã chọn (sai) và đáp án đúng
    allOptions.forEach((button, index) => {
      if (index !== optionIndex && index !== correctOptionIndex) {
        // Ẩn nội dung nhưng giữ nguyên vị trí
        button.style.visibility = "hidden";
        // Giữ lại thẻ button nhưng làm mờ đi
        button.style.opacity = "0";
        // Vẫn giữ không gian chiếm chỗ
        button.style.display = "block";
      } else if (index === optionIndex) {
        // Highlight đáp án người dùng chọn (sai)
        button.disabled = true;
        button.classList.add("bg-red-100", "border-red-500", "text-red-800");

        // Thêm hiệu ứng scale
        button.style.transform = "scale(1.05)";
        button.style.transition = "transform 0.3s ease";

        // Thêm icon dấu X đỏ
        const xIcon = document.createElement("span");
        xIcon.className = "absolute top-2 right-2 text-red-600";
        xIcon.innerHTML = '<i class="fas fa-times-circle"></i>';
        button.style.position = "relative";
        button.appendChild(xIcon);
      } else if (index === correctOptionIndex) {
        // Highlight đáp án đúng
        button.disabled = true;
        button.classList.add(
          "bg-green-100",
          "border-green-500",
          "text-green-800"
        );

        // Thêm hiệu ứng scale
        button.style.transform = "scale(1.05)";
        button.style.transition = "transform 0.3s ease";

        // Thêm icon dấu tích xanh
        const checkIcon = document.createElement("span");
        checkIcon.className = "absolute top-2 right-2 text-green-600";
        checkIcon.innerHTML = '<i class="fas fa-check-circle"></i>';
        button.style.position = "relative";
        button.appendChild(checkIcon);
      }
    });
  }

  // Lưu câu trả lời của người dùng
  userAnswers.push({
    questionIndex: currentQuestionIndex,
    selectedIndex: optionIndex,
    isCorrect: isCorrect,
  });

  // Hiển thị feedback
  showAnswerFeedback(isCorrect);

  // Hiển thị nút tiếp theo sau 1 giây
  setTimeout(() => {
    const nextButton = document.getElementById("nextButton");
    const finishButton = document.getElementById("finishButton");

    // Kiểm tra xem đây có phải là câu hỏi cuối cùng không
    if (currentQuestionIndex === examData.questions.length - 1) {
      finishButton.classList.remove("hidden");
      finishButton.addEventListener("click", () => {
        stopAllSounds(); // Dừng âm thanh khi kết thúc bài thi
        showResult();
      });
    } else {
      nextButton.classList.remove("hidden");
      nextButton.addEventListener("click", () => {
        stopAllSounds(); // Dừng âm thanh khi chuyển câu hỏi
        currentQuestionIndex++;
        showQuestion(currentQuestionIndex);
      });
    }
  }, 1000);
}

// Hiển thị feedback cho đáp án
function showAnswerFeedback(isCorrect) {
  // Xóa bất kỳ timeout nào đang chạy
  if (feedbackTimeout) {
    clearTimeout(feedbackTimeout);
  }

  const template = document.getElementById("answerFeedbackTemplate");
  const feedbackEl = template.content.cloneNode(true);
  const feedbackContainer = document.createElement("div");

  // Cập nhật nội dung feedback
  const iconEl = feedbackEl.querySelector("#feedbackIcon");
  const titleEl = feedbackEl.querySelector("#feedbackTitle");
  const messageEl = feedbackEl.querySelector("#feedbackMessage");
  const correctAnswerEl = feedbackEl.querySelector("#correctAnswerText");

  if (isCorrect) {
    iconEl.className =
      "mx-auto flex items-center justify-center w-16 h-16 rounded-full mb-4 bg-green-100 text-green-500";
    iconEl.innerHTML = '<i class="fas fa-check text-3xl"></i>';
    titleEl.textContent = "Chính xác!";
    titleEl.className = "text-xl font-bold mb-2 text-green-600";
    messageEl.textContent = "Bạn đã trả lời đúng.";
  } else {
    iconEl.className =
      "mx-auto flex items-center justify-center w-16 h-16 rounded-full mb-4 bg-red-100 text-red-500";
    iconEl.innerHTML = '<i class="fas fa-times text-3xl"></i>';
    titleEl.textContent = "Chưa chính xác!";
    titleEl.className = "text-xl font-bold mb-2 text-red-600";
    messageEl.textContent = "Bạn đã trả lời sai.";

    // Hiển thị đáp án đúng
    const correctOption = examData.questions[currentQuestionIndex].options.find(
      (opt) => opt.isCorrect
    );
    if (correctOption) {
      correctAnswerEl.textContent = `Đáp án đúng: ${correctOption.text}`;
      correctAnswerEl.classList.remove("hidden");
    }
  }

  // Thêm vào DOM
  feedbackContainer.appendChild(feedbackEl);
  document.body.appendChild(feedbackContainer.firstElementChild);

  // Tự động ẩn sau 1 giây
  feedbackTimeout = setTimeout(() => {
    const feedbackElement = document.querySelector(
      "#answerFeedbackTemplate + div"
    );
    if (feedbackElement) {
      feedbackElement.remove();
    }
  }, 1000);
}

// Hiển thị kết quả cuối cùng
function showResult() {
  const template = document.getElementById("resultTemplate");
  const resultEl = template.content.cloneNode(true);
  const questionContainer = document.getElementById("questionContainer");

  // Tính toán thời gian làm bài
  const totalTime = Math.floor((Date.now() - startTime) / 1000);
  const minutes = Math.floor(totalTime / 60);
  const seconds = totalTime % 60;

  // Cập nhật điểm và thống kê
  const totalQuestions = examData.questions.length;
  const scoreValue = Math.round((score / totalQuestions) * 10 * 100) / 100;

  resultEl.querySelector("#finalScore").textContent = `${scoreValue}/10`;
  resultEl.querySelector(
    "#totalCorrect"
  ).textContent = `${score}/${totalQuestions}`;
  resultEl.querySelector("#totalTime").textContent = `${minutes}:${seconds
    .toString()
    .padStart(2, "0")}`;

  // Tạo tóm tắt các câu hỏi
  const summaryContainer = resultEl.querySelector("#summaryContainer");

  userAnswers.forEach((answer, index) => {
    const questionDot = document.createElement("div");
    questionDot.className = `h-8 w-8 rounded-full flex items-center justify-center text-xs font-medium ${
      answer.isCorrect
        ? "bg-green-100 text-green-800 border border-green-300"
        : "bg-red-100 text-red-800 border border-red-300"
    }`;
    questionDot.textContent = index + 1;

    // Tooltip với chi tiết
    questionDot.title = `Câu ${index + 1}: ${
      answer.isCorrect ? "Đúng" : "Sai"
    }`;

    summaryContainer.appendChild(questionDot);
  });

  // Hiển thị kết quả
  questionContainer.innerHTML = "";
  questionContainer.appendChild(resultEl);
}

// Debug helper
console.log("Quizizz page script loaded successfully!");
