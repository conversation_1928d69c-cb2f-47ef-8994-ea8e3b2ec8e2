<div class="max-w-4xl mx-auto px-1 sm:px-0">
  <div class="bg-white rounded-lg shadow-md p-2 sm:p-8">
    <!-- Thông tin khóa học -->
    <div class="mb-4 sm:mb-6 flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-800 mb-1">
          <%= product.name %>
        </h1>
        <div class="flex items-center text-sm text-gray-500">
          <span class="inline-flex items-center mr-4">
            <i class="fas fa-user mr-1"></i> <%= student.email %>
          </span>
          <span
            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= product.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' %>"
          >
            <%= product.status %>
          </span>
        </div>
      </div>
      <a
        href="/home"
        class="inline-flex items-center text-indigo-600 hover:text-indigo-800"
      >
        <i class="fas fa-arrow-left mr-1"></i> Quay lại
      </a>
    </div>

    <!-- Tab Navigation -->
    <div class="mb-6">
      <div class="border-b border-gray-200">
        <nav class="-mb-px flex space-x-4 sm:space-x-8" aria-label="Tabs">
          <a
            href="?tab=exams"
            class="<%= currentTab === 'exams' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' %> whitespace-nowrap py-3 px-1 border-b-2 font-medium text-xs sm:text-sm"
          >
            <i class="fas fa-file-alt mr-1 sm:mr-2"></i>
            <span class="hidden sm:inline">Danh sách đề thi</span>
            <span class="sm:hidden">Đề thi</span>
          </a>
          <a
            href="?tab=practice"
            class="<%= currentTab === 'practice' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' %> whitespace-nowrap py-3 px-1 border-b-2 font-medium text-xs sm:text-sm"
          >
            <i class="fas fa-dumbbell mr-1 sm:mr-2"></i>
            <span class="hidden sm:inline">Thi thử</span>
            <span class="sm:hidden">Thi thử</span>
          </a>
          <a
            href="?tab=outline"
            class="<%= currentTab === 'outline' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' %> whitespace-nowrap py-3 px-1 border-b-2 font-medium text-xs sm:text-sm"
          >
            <i class="fas fa-list-ul mr-1 sm:mr-2"></i>
            <span class="hidden sm:inline">Đề cương</span>
            <span class="sm:hidden">Đề cương</span>
          </a>
          <a
            href="?tab=memory"
            class="<%= currentTab === 'memory' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' %> whitespace-nowrap py-3 px-1 border-b-2 font-medium text-xs sm:text-sm"
          >
            <i class="fas fa-bookmark mr-1 sm:mr-2"></i>
            <span class="hidden sm:inline">Ghi nhớ</span>
            <span class="sm:hidden">Ghi nhớ</span>
          </a>
        </nav>
      </div>
    </div>

    <!-- Tab Content -->
    <% if (currentTab === 'exams') { %> <%-
    include('../partials/exams/exam-list') %> <% } else if (currentTab ===
    'practice') { %> <%- include('../partials/exams/practice-exam') %> <% } else
    if (currentTab === 'outline') { %> <%- include('../partials/exams/outline')
    %> <% } else if (currentTab === 'memory') { %> <%-
    include('../partials/exams/memory') %> <% } %>
  </div>
</div>

<!-- Include modals -->
<%- include('../partials/exams/modals') %>

<!-- Include scripts -->
<%- include('../partials/exams/scripts') %>

<!-- Add custom styles for exam page -->
<style>
  /* Loading indicator styling */
  #loadingIndicator {
    display: none;
    padding: 2rem 0;
  }

  /* Question item styling */
  .question-item {
    transition: all 0.3s ease;
    border: 2px solid transparent;
  }

  .question-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  /* Answer styling */
  .answer-option {
    transition: all 0.2s ease;
    cursor: pointer;
  }

  .answer-option:hover {
    background-color: #f8fafc;
    border-color: #cbd5e0;
  }

  .revealed-correct {
    background-color: #f0fff4 !important;
    border-color: #9ae6b4 !important;
    color: #22543d !important;
    font-weight: 600;
  }

  .revealed-incorrect {
    background-color: #fed7d7 !important;
    border-color: #fc8181 !important;
    color: #742a2a !important;
  }

  .answers-shown .answer-option {
    pointer-events: none;
  }

  /* Filter dropdown styling */
  #examFilter {
    min-width: 200px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
  }

  #examFilter:focus {
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  }

  /* Mobile responsive improvements */
  @media (max-width: 640px) {
    .max-w-4xl {
      margin-left: 0.25rem !important;
      margin-right: 0.25rem !important;
    }

    .bg-white.rounded-lg.shadow-md {
      border-radius: 0.375rem !important;
    }

    #questionsScrollContainer {
      height: 60vh !important;
      margin-left: -0.5rem !important;
      margin-right: -0.5rem !important;
      border-radius: 0.375rem !important;
    }

    .question-item {
      margin-left: -0.5rem !important;
      margin-right: -0.5rem !important;
      border-radius: 0.5rem !important;
    }

    #questionsContainer {
      padding-left: 0.5rem !important;
      padding-right: 0.5rem !important;
    }

    .mb-4.sm\\:mb-6 {
      margin-left: -0.5rem !important;
      margin-right: -0.5rem !important;
      padding-left: 0.5rem !important;
      padding-right: 0.5rem !important;
    }

    .mb-6 {
      margin-left: -0.5rem !important;
      margin-right: -0.5rem !important;
      padding-left: 0.5rem !important;
      padding-right: 0.5rem !important;
    }

    .bg-gray-50.rounded-lg {
      margin-left: -0.5rem !important;
      margin-right: -0.5rem !important;
      border-radius: 0.375rem !important;
    }

    main.container {
      padding-left: 0.5rem !important;
      padding-right: 0.5rem !important;
    }

    .mb-4.sm\\:mb-6.flex {
      margin-left: -0.5rem !important;
      margin-right: -0.5rem !important;
      padding-left: 0.5rem !important;
      padding-right: 0.5rem !important;
    }

    .flex.items-center.gap-2 {
      align-items: center !important;
    }

    #examFilter {
      max-width: none !important;
    }

    .question-item h4 {
      font-size: 0.95rem !important;
      line-height: 1.4 !important;
    }

    .answer-text {
      font-size: 0.875rem !important;
      line-height: 1.5 !important;
    }

    button {
      min-height: 44px;
      padding-top: 0.75rem !important;
      padding-bottom: 0.75rem !important;
    }

    #loadingIndicator {
      padding: 1rem 0 !important;
    }

    #loadingIndicator svg {
      width: 1rem !important;
      height: 1rem !important;
    }
  }

  @media (max-width: 768px) and (min-width: 641px) {
    #questionsScrollContainer {
      height: 65vh !important;
    }
  }

  .question-item {
    line-height: 1.6;
  }

  .answers-container .flex {
    align-items: flex-start;
    margin-bottom: 0.5rem;
  }

  @media (min-width: 640px) {
    .answers-container .flex {
      margin-bottom: 0.75rem;
    }
  }

  @media (max-width: 640px) {
    .nav a {
      padding: 1rem 0.5rem !important;
    }

    #questionsScrollContainer::-webkit-scrollbar {
      width: 4px;
    }

    .revealed-correct {
      padding: 0.25rem 0.5rem !important;
      border-radius: 0.375rem !important;
      font-weight: 600 !important;
    }

    input[type="text"],
    select {
      font-size: 16px !important;
    }
  }

  @media (max-width: 640px) {
    .overflow-x-auto table {
      font-size: 0.875rem;
    }

    .overflow-x-auto td {
      padding: 0.75rem 0.5rem !important;
    }

    .overflow-x-auto th {
      padding: 0.75rem 0.5rem !important;
    }
  }
</style>
