"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const mongoose_1 = __importDefault(require("mongoose"));
const asynHandler_1 = __importDefault(require("../util/asynHandler"));
const MemoryQuestion_1 = __importDefault(require("../models/MemoryQuestion"));
const question_1 = __importDefault(require("../models/question"));
const authMiddleware_1 = require("../middlewares/authMiddleware");
const router = express_1.default.Router();
/**
 * @route GET /exam/memory/:productId
 * @desc Lấy danh sách câu hỏi ghi nhớ của user
 * @access Private
 */
router.get("/:productId", (0, asynHandler_1.default)(authMiddleware_1.authenticateToken), (0, asynHandler_1.default)((req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const { productId } = req.params;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;
    try {
        // Lấy danh sách câu hỏi ghi nhớ của user
        const memoryQuestions = yield MemoryQuestion_1.default.find({
            userId: req.user._id,
            productId,
            isActive: true,
        })
            .skip(skip)
            .limit(limit)
            .sort({ createdAt: -1 });
        // Lấy thông tin chi tiết về câu hỏi
        const questionIds = memoryQuestions.map((q) => q.questionId);
        const questions = yield question_1.default.find({ _id: { $in: questionIds } });
        // Kết hợp thông tin câu hỏi với thông tin ghi nhớ
        const questionMap = new Map();
        questions.forEach((q) => questionMap.set(q._id.toString(), q));
        const enhancedMemoryQuestions = memoryQuestions.map((mq) => {
            const question = questionMap.get(mq.questionId.toString());
            return {
                _id: mq._id,
                userId: mq.userId,
                productId: mq.productId,
                questionId: mq.questionId,
                examId: mq.examId,
                source: mq.source,
                practiceCount: mq.practiceCount,
                correctCount: mq.correctCount,
                lastPracticed: mq.lastPracticed,
                createdAt: mq.createdAt,
                isActive: mq.isActive,
                group: mq.group, // Include group information
                questionData: question,
            };
        });
        // Tính toán thống kê
        const totalQuestions = yield MemoryQuestion_1.default.countDocuments({
            userId: req.user._id,
            productId,
            isActive: true,
        });
        const stats = yield MemoryQuestion_1.default.aggregate([
            {
                $match: {
                    userId: new mongoose_1.default.Types.ObjectId(req.user._id),
                    productId: new mongoose_1.default.Types.ObjectId(productId),
                    isActive: true,
                },
            },
            {
                $group: {
                    _id: null,
                    totalQuestions: { $sum: 1 },
                    totalPracticeCount: { $sum: "$practiceCount" },
                    totalCorrectCount: { $sum: "$correctCount" },
                },
            },
        ]);
        // Tính toán độ chính xác trung bình
        let averageAccuracy = 0;
        let practiceCount = 0;
        if (stats.length > 0 && stats[0].totalPracticeCount > 0) {
            averageAccuracy =
                (stats[0].totalCorrectCount / stats[0].totalPracticeCount) * 100;
            practiceCount = stats[0].totalPracticeCount;
        }
        // Tính tổng số trang
        const totalPages = Math.ceil(totalQuestions / limit);
        return res.json({
            success: true,
            data: {
                questions: enhancedMemoryQuestions,
                stats: {
                    totalQuestions,
                    averageAccuracy,
                    practiceCount,
                },
                pagination: {
                    currentPage: page,
                    totalPages,
                    limit,
                },
            },
        });
    }
    catch (error) {
        console.error("Error retrieving memory questions:", error);
        return res.status(500).json({
            success: false,
            message: "Đã có lỗi xảy ra khi lấy dữ liệu câu hỏi ghi nhớ",
        });
    }
})));
/**
 * @route POST /exam/memory/:productId/add
 * @desc Thêm câu hỏi vào ghi nhớ
 * @access Private
 */
router.post("/:productId/add", (0, asynHandler_1.default)(authMiddleware_1.authenticateToken), (0, asynHandler_1.default)((req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const { productId } = req.params;
    const { questionId, source = "manual" } = req.body;
    try {
        // Kiểm tra câu hỏi có tồn tại không
        const question = yield question_1.default.findById(questionId);
        if (!question) {
            return res.status(404).json({
                success: false,
                message: "Không tìm thấy câu hỏi",
            });
        }
        // Kiểm tra câu hỏi đã được thêm vào ghi nhớ chưa
        const existingQuestion = yield MemoryQuestion_1.default.findOne({
            userId: req.user._id,
            questionId,
        });
        if (existingQuestion) {
            // Nếu câu hỏi đã bị xóa trước đó (isActive = false), kích hoạt lại
            if (!existingQuestion.isActive) {
                existingQuestion.isActive = true;
                existingQuestion.source = source;
                yield existingQuestion.save();
                return res.json({
                    success: true,
                    data: existingQuestion,
                    message: "Đã thêm lại câu hỏi vào danh sách ghi nhớ",
                });
            }
            // Nếu câu hỏi đã tồn tại và đang active, trả về thông tin để client xử lý
            return res.status(409).json({
                success: false,
                message: "Câu hỏi đã có trong bộ ghi nhớ. Bạn có muốn xóa nó khỏi bộ ghi nhớ không?",
                duplicate: true,
                existingQuestion: {
                    id: existingQuestion._id,
                    questionId: existingQuestion.questionId,
                },
            });
        }
        // Tạo mới câu hỏi ghi nhớ
        const newMemoryQuestion = new MemoryQuestion_1.default({
            userId: req.user._id,
            productId,
            questionId,
            // examId: examId || undefined,
            source,
            practiceCount: 0,
            correctCount: 0,
            createdAt: new Date(),
            isActive: true,
        });
        yield newMemoryQuestion.save();
        return res.json({
            success: true,
            data: newMemoryQuestion,
            message: "Đã thêm câu hỏi vào danh sách ghi nhớ",
        });
    }
    catch (error) {
        console.error("Error adding memory question:", error);
        return res.status(500).json({
            success: false,
            message: "Đã có lỗi xảy ra khi thêm câu hỏi vào ghi nhớ",
        });
    }
})));
/**
 * @route DELETE /exam/memory/:productId/:questionId
 * @desc Xóa câu hỏi khỏi ghi nhớ
 * @access Private
 */
router.delete("/:productId/:questionId", (0, asynHandler_1.default)(authMiddleware_1.authenticateToken), (0, asynHandler_1.default)((req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const { productId, questionId } = req.params;
    try {
        // Tìm câu hỏi ghi nhớ bằng questionId thay vì _id
        const memoryQuestion = yield MemoryQuestion_1.default.findOne({
            questionId: questionId,
            userId: req.user._id,
            productId,
            isActive: true,
        });
        if (!memoryQuestion) {
            return res.status(404).json({
                success: false,
                message: "Không tìm thấy câu hỏi ghi nhớ",
            });
        }
        // Xóa mềm (soft delete)
        memoryQuestion.isActive = false;
        yield memoryQuestion.save();
        return res.json({
            success: true,
            message: "Đã xóa câu hỏi khỏi danh sách ghi nhớ",
        });
    }
    catch (error) {
        console.error("Error deleting memory question:", error);
        return res.status(500).json({
            success: false,
            message: "Đã có lỗi xảy ra khi xóa câu hỏi",
        });
    }
})));
/**
 * @route DELETE /exam/memory/:productId/bulk-delete
 * @desc Xóa nhiều câu hỏi khỏi ghi nhớ
 * @access Private
 */
router.delete("/:productId/bulk-delete", (0, asynHandler_1.default)(authMiddleware_1.authenticateToken), (0, asynHandler_1.default)((req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const { productId } = req.params;
    const { questionIds } = req.body;
    if (!Array.isArray(questionIds) || questionIds.length === 0) {
        return res.status(400).json({
            success: false,
            message: "Danh sách câu hỏi không hợp lệ",
        });
    }
    try {
        // Xóa mềm (soft delete) nhiều câu hỏi
        const result = yield MemoryQuestion_1.default.updateMany({
            _id: { $in: questionIds },
            userId: req.user._id,
            productId,
        }, {
            $set: { isActive: false },
        });
        return res.json({
            success: true,
            deletedCount: result.modifiedCount,
            message: `Đã xóa ${result.modifiedCount} câu hỏi khỏi danh sách ghi nhớ`,
        });
    }
    catch (error) {
        console.error("Error bulk deleting memory questions:", error);
        return res.status(500).json({
            success: false,
            message: "Đã có lỗi xảy ra khi xóa câu hỏi",
        });
    }
})));
/**
 * @route POST /exam/memory/:productId/practice/start
 * @desc Bắt đầu chế độ luyện nhanh
 * @access Private
 */
router.post("/:productId/practice/start", (0, asynHandler_1.default)(authMiddleware_1.authenticateToken), (0, asynHandler_1.default)((req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const { productId } = req.params;
    const { count = 10, timeLimit = 15, shuffleQuestions = true, shuffleAnswers = true, } = req.body;
    try {
        // Lấy danh sách câu hỏi ghi nhớ của user
        const memoryQuestions = yield MemoryQuestion_1.default.find({
            userId: req.user._id,
            productId,
            isActive: true,
        }).sort({ practiceCount: 1, createdAt: -1 }); // Ưu tiên câu ít luyện tập
        if (memoryQuestions.length === 0) {
            return res.status(404).json({
                success: false,
                message: "Không có câu hỏi ghi nhớ nào",
            });
        }
        // Lấy số câu hỏi tối đa theo yêu cầu hoặc theo số câu có sẵn
        const questionCount = Math.min(count, memoryQuestions.length);
        // Chọn ngẫu nhiên câu hỏi nếu cần
        const selectedMemoryQuestions = shuffleQuestions
            ? memoryQuestions
                .sort(() => Math.random() - 0.5)
                .slice(0, questionCount)
            : memoryQuestions.slice(0, questionCount);
        // Lấy thông tin chi tiết về câu hỏi
        const questionIds = selectedMemoryQuestions.map((q) => q.questionId);
        const questions = yield question_1.default.find({ _id: { $in: questionIds } });
        // Kết hợp thông tin câu hỏi với thông tin ghi nhớ
        const questionMap = new Map();
        questions.forEach((q) => questionMap.set(q._id.toString(), q));
        const selectedQuestions = selectedMemoryQuestions.map((mq) => {
            const question = questionMap.get(mq.questionId.toString());
            return {
                memoryQuestionId: mq._id,
                questionId: mq.questionId,
                text: question === null || question === void 0 ? void 0 : question.text,
                options: shuffleAnswers && (question === null || question === void 0 ? void 0 : question.answers)
                    ? [...question.answers].sort(() => Math.random() - 0.5)
                    : question === null || question === void 0 ? void 0 : question.answers,
                image: question === null || question === void 0 ? void 0 : question.image,
            };
        });
        // Tạo ID practice session (có thể sử dụng để lưu kết quả sau này)
        const practiceId = new mongoose_1.default.Types.ObjectId();
        // Lưu session vào cache hoặc database (tùy vào implementation)
        // ...
        return res.json({
            success: true,
            practiceId: practiceId,
            questions: selectedQuestions,
            timeLimit,
            message: "Đã tạo bài luyện tập",
        });
    }
    catch (error) {
        console.error("Error starting memory practice:", error);
        return res.status(500).json({
            success: false,
            message: "Đã có lỗi xảy ra khi tạo bài luyện tập",
        });
    }
})));
/**
 * @route POST /exam/memory/:productId/practice/submit
 * @desc Nộp kết quả luyện nhanh
 * @access Private
 */
router.post("/:productId/practice/submit", (0, asynHandler_1.default)(authMiddleware_1.authenticateToken), (0, asynHandler_1.default)((req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    const { productId } = req.params;
    const { answers, timeSpent, status = "completed" } = req.body;
    try {
        if (!answers || !Array.isArray(answers)) {
            return res.status(400).json({
                success: false,
                message: "Dữ liệu không hợp lệ",
            });
        }
        // Cập nhật thống kê luyện tập cho từng câu hỏi
        for (const [index, answer] of answers.entries()) {
            if (answer === null || answer === undefined)
                continue;
            const memoryQuestionId = (_a = req.body.memoryQuestionIds) === null || _a === void 0 ? void 0 : _a[index];
            if (!memoryQuestionId)
                continue;
            // Tìm câu hỏi ghi nhớ
            const memoryQuestion = yield MemoryQuestion_1.default.findOne({
                _id: memoryQuestionId,
                userId: req.user._id,
                productId,
                isActive: true,
            });
            if (!memoryQuestion)
                continue;
            // Tìm câu hỏi gốc để biết đáp án đúng
            const question = yield question_1.default.findById(memoryQuestion.questionId);
            if (!question)
                continue;
            // Xác định câu trả lời có đúng không
            const correctAnswerIndex = question.answers.findIndex((a) => a.isCorrect);
            const isCorrect = answer === correctAnswerIndex;
            // Cập nhật thống kê
            memoryQuestion.practiceCount += 1;
            if (isCorrect) {
                memoryQuestion.correctCount += 1;
            }
            memoryQuestion.lastPracticed = new Date();
            yield memoryQuestion.save();
        }
        return res.json({
            success: true,
            message: "Đã lưu kết quả luyện tập",
        });
    }
    catch (error) {
        console.error("Error submitting memory practice results:", error);
        return res.status(500).json({
            success: false,
            message: "Đã có lỗi xảy ra khi lưu kết quả luyện tập",
        });
    }
})));
/**
 * @route GET /exam/memory/:productId/memorized-ids
 * @desc Lấy danh sách IDs câu hỏi đã ghi nhớ của user
 * @access Private
 */
router.get("/:productId/memorized-ids", (0, asynHandler_1.default)(authMiddleware_1.authenticateToken), (0, asynHandler_1.default)((req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const { productId } = req.params;
    try {
        const memorizedQuestions = yield MemoryQuestion_1.default.find({
            userId: req.user._id,
            productId,
            isActive: true,
        }).select("questionId");
        const questionIds = memorizedQuestions.map((mq) => mq.questionId.toString());
        return res.json({
            success: true,
            questionIds,
        });
    }
    catch (error) {
        console.error("Error fetching memorized question IDs:", error);
        return res.status(500).json({
            success: false,
            message: "Đã có lỗi xảy ra khi lấy danh sách câu hỏi ghi nhớ",
        });
    }
})));
/**
 * @route GET /exam/memory/:productId/stats
 * @desc Thống kê luyện tập
 * @access Private
 */
router.get("/:productId/stats", (0, asynHandler_1.default)(authMiddleware_1.authenticateToken), (0, asynHandler_1.default)((req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const { productId } = req.params;
    try {
        const stats = yield MemoryQuestion_1.default.aggregate([
            {
                $match: {
                    userId: new mongoose_1.default.Types.ObjectId(req.user._id),
                    productId: new mongoose_1.default.Types.ObjectId(productId),
                    isActive: true,
                },
            },
            {
                $group: {
                    _id: null,
                    totalQuestions: { $sum: 1 },
                    totalPracticeCount: { $sum: "$practiceCount" },
                    totalCorrectCount: { $sum: "$correctCount" },
                },
            },
        ]);
        // Tính toán độ chính xác trung bình
        let averageAccuracy = 0;
        let practiceCount = 0;
        let totalQuestions = 0;
        if (stats.length > 0) {
            totalQuestions = stats[0].totalQuestions;
            practiceCount = stats[0].totalPracticeCount;
            if (practiceCount > 0) {
                averageAccuracy = (stats[0].totalCorrectCount / practiceCount) * 100;
            }
        }
        return res.json({
            success: true,
            data: {
                totalQuestions,
                practiceCount,
                averageAccuracy,
            },
        });
    }
    catch (error) {
        console.error("Error fetching memory stats:", error);
        return res.status(500).json({
            success: false,
            message: "Đã có lỗi xảy ra khi lấy thống kê luyện tập",
        });
    }
})));
/**
 * @route GET /exam/memory/:productId/groups
 * @desc Lấy danh sách tất cả groups và số lượng câu hỏi trong mỗi group
 * @access Private
 */
router.get("/:productId/groups", (0, asynHandler_1.default)(authMiddleware_1.authenticateToken), (0, asynHandler_1.default)((req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const { productId } = req.params;
    try {
        // Aggregate để lấy thống kê groups
        const groupStats = yield MemoryQuestion_1.default.aggregate([
            {
                $match: {
                    userId: new mongoose_1.default.Types.ObjectId(req.user._id),
                    productId: new mongoose_1.default.Types.ObjectId(productId),
                    isActive: true,
                },
            },
            {
                $group: {
                    _id: "$group",
                    count: { $sum: 1 },
                    questions: { $push: "$questionId" },
                },
            },
            {
                $sort: { _id: 1 },
            },
        ]);
        // Tách riêng ungrouped questions và grouped questions
        const ungroupedGroup = groupStats.find((g) => g._id === null);
        const groups = groupStats.filter((g) => g._id !== null);
        return res.json({
            success: true,
            data: {
                groups: groups.map((g) => ({
                    name: g._id,
                    count: g.count,
                    questionIds: g.questions,
                })),
                ungrouped: {
                    count: ungroupedGroup ? ungroupedGroup.count : 0,
                    questionIds: ungroupedGroup ? ungroupedGroup.questions : [],
                },
            },
        });
    }
    catch (error) {
        console.error("Error fetching memory groups:", error);
        return res.status(500).json({
            success: false,
            message: "Đã có lỗi xảy ra khi lấy danh sách nhóm",
        });
    }
})));
/**
 * @route GET /exam/memory/:productId/groups/:groupName
 * @desc Lấy chi tiết câu hỏi trong một group cụ thể
 * @access Private
 */
router.get("/:productId/groups/:groupName", (0, asynHandler_1.default)(authMiddleware_1.authenticateToken), (0, asynHandler_1.default)((req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const { productId, groupName } = req.params;
    try {
        // Lấy danh sách câu hỏi trong group
        const memoryQuestions = yield MemoryQuestion_1.default.find({
            userId: req.user._id,
            productId,
            group: groupName === "ungrouped" ? null : groupName,
            isActive: true,
        }).sort({ createdAt: -1 });
        // Lấy thông tin chi tiết câu hỏi
        const questionIds = memoryQuestions.map((q) => q.questionId);
        const questions = yield question_1.default.find({ _id: { $in: questionIds } });
        // Kết hợp thông tin
        const questionMap = new Map();
        questions.forEach((q) => questionMap.set(q._id.toString(), q));
        const enhancedQuestions = memoryQuestions.map((mq) => {
            const question = questionMap.get(mq.questionId.toString());
            return {
                _id: mq._id,
                questionId: mq.questionId,
                group: mq.group,
                practiceCount: mq.practiceCount,
                correctCount: mq.correctCount,
                lastPracticed: mq.lastPracticed,
                createdAt: mq.createdAt,
                questionData: question,
            };
        });
        return res.json({
            success: true,
            data: {
                groupName: groupName === "ungrouped" ? null : groupName,
                questions: enhancedQuestions,
                count: enhancedQuestions.length,
            },
        });
    }
    catch (error) {
        console.error("Error fetching group questions:", error);
        return res.status(500).json({
            success: false,
            message: "Đã có lỗi xảy ra khi lấy câu hỏi trong nhóm",
        });
    }
})));
/**
 * @route POST /exam/memory/:productId/groups
 * @desc Tạo hoặc cập nhật group assignments cho các câu hỏi
 * @access Private
 */
router.post("/:productId/groups", (0, asynHandler_1.default)(authMiddleware_1.authenticateToken), (0, asynHandler_1.default)((req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const { productId } = req.params;
    const { groupName, questionIds, action = "assign" } = req.body;
    if (!Array.isArray(questionIds) || questionIds.length === 0) {
        return res.status(400).json({
            success: false,
            message: "Danh sách câu hỏi không hợp lệ",
        });
    }
    try {
        let result;
        if (action === "assign") {
            // Gán câu hỏi vào group
            if (!groupName || groupName.trim() === "") {
                return res.status(400).json({
                    success: false,
                    message: "Tên nhóm không được để trống",
                });
            }
            result = yield MemoryQuestion_1.default.updateMany({
                questionId: { $in: questionIds },
                userId: req.user._id,
                productId,
                isActive: true,
            }, {
                $set: { group: groupName.trim() },
            });
            return res.json({
                success: true,
                message: `Đã thêm ${result.modifiedCount} câu hỏi vào nhóm "${groupName}"`,
                modifiedCount: result.modifiedCount,
            });
        }
        else if (action === "ungroup") {
            // Xóa group assignment (set về null)
            result = yield MemoryQuestion_1.default.updateMany({
                questionId: { $in: questionIds },
                userId: req.user._id,
                productId,
                isActive: true,
            }, {
                $set: { group: null },
            });
            return res.json({
                success: true,
                message: `Đã xóa ${result.modifiedCount} câu hỏi khỏi nhóm`,
                modifiedCount: result.modifiedCount,
            });
        }
        else {
            return res.status(400).json({
                success: false,
                message: "Action không hợp lệ. Sử dụng 'assign' hoặc 'ungroup'",
            });
        }
    }
    catch (error) {
        console.error("Error managing question groups:", error);
        return res.status(500).json({
            success: false,
            message: "Đã có lỗi xảy ra khi quản lý nhóm câu hỏi",
        });
    }
})));
/**
 * @route PUT /exam/memory/:productId/groups/:oldGroupName
 * @desc Đổi tên group
 * @access Private
 */
router.put("/:productId/groups/:oldGroupName", (0, asynHandler_1.default)(authMiddleware_1.authenticateToken), (0, asynHandler_1.default)((req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const { productId, oldGroupName } = req.params;
    const { newGroupName } = req.body;
    if (!newGroupName || newGroupName.trim() === "") {
        return res.status(400).json({
            success: false,
            message: "Tên nhóm mới không được để trống",
        });
    }
    try {
        const result = yield MemoryQuestion_1.default.updateMany({
            userId: req.user._id,
            productId,
            group: oldGroupName,
            isActive: true,
        }, {
            $set: { group: newGroupName.trim() },
        });
        if (result.modifiedCount === 0) {
            return res.status(404).json({
                success: false,
                message: "Không tìm thấy nhóm hoặc nhóm không có câu hỏi nào",
            });
        }
        return res.json({
            success: true,
            message: `Đã đổi tên nhóm "${oldGroupName}" thành "${newGroupName}"`,
            modifiedCount: result.modifiedCount,
        });
    }
    catch (error) {
        console.error("Error renaming group:", error);
        return res.status(500).json({
            success: false,
            message: "Đã có lỗi xảy ra khi đổi tên nhóm",
        });
    }
})));
/**
 * @route DELETE /exam/memory/:productId/groups/:groupName
 * @desc Xóa group (set tất cả câu hỏi trong group về ungrouped)
 * @access Private
 */
router.delete("/:productId/groups/:groupName", (0, asynHandler_1.default)(authMiddleware_1.authenticateToken), (0, asynHandler_1.default)((req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const { productId, groupName } = req.params;
    try {
        const result = yield MemoryQuestion_1.default.updateMany({
            userId: req.user._id,
            productId,
            group: groupName,
            isActive: true,
        }, {
            $set: { group: null },
        });
        if (result.modifiedCount === 0) {
            return res.status(404).json({
                success: false,
                message: "Không tìm thấy nhóm hoặc nhóm không có câu hỏi nào",
            });
        }
        return res.json({
            success: true,
            message: `Đã xóa nhóm "${groupName}" và chuyển ${result.modifiedCount} câu hỏi về trạng thái không nhóm`,
            modifiedCount: result.modifiedCount,
        });
    }
    catch (error) {
        console.error("Error deleting group:", error);
        return res.status(500).json({
            success: false,
            message: "Đã có lỗi xảy ra khi xóa nhóm",
        });
    }
})));
/**
 * @route POST /exam/memory/:productId/practice/quizizz-start
 * @desc Bắt đầu chế độ luyện nhanh với Quizizz interface và group selection
 * @access Private
 */
router.post("/:productId/practice/quizizz-start", (0, asynHandler_1.default)(authMiddleware_1.authenticateToken), (0, asynHandler_1.default)((req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const { productId } = req.params;
    // Parse form data (arrays come as JSON strings)
    let selectedGroups = [];
    try {
        selectedGroups = req.body.selectedGroups
            ? JSON.parse(req.body.selectedGroups)
            : [];
    }
    catch (_a) {
        selectedGroups = Array.isArray(req.body.selectedGroups)
            ? req.body.selectedGroups
            : [req.body.selectedGroups].filter(Boolean);
    }
    const questionCount = req.body.questionCount === "all"
        ? "all"
        : parseInt(req.body.questionCount) || 10;
    const timeLimit = parseInt(req.body.timeLimit) || 15;
    const shuffleQuestions = req.body.shuffleQuestions === "true";
    const shuffleAnswers = req.body.shuffleAnswers === "true";
    const focusWeakQuestions = req.body.focusWeakQuestions === "true";
    const reviewMode = req.body.reviewMode === "true";
    try {
        // Validate input
        if (!selectedGroups || selectedGroups.length === 0) {
            return res.status(400).json({
                success: false,
                message: "Vui lòng chọn ít nhất một nhóm câu hỏi",
            });
        }
        // Build query filter based on selected groups
        let memoryQuery = {
            userId: req.user._id,
            productId,
            isActive: true,
        };
        if (!selectedGroups.includes("all")) {
            // Filter by specific groups
            const groupFilter = { $or: [] };
            if (selectedGroups.includes("ungrouped")) {
                groupFilter.$or.push({ group: { $exists: false } });
                groupFilter.$or.push({ group: null });
                groupFilter.$or.push({ group: "" });
            }
            // Add specific group names
            const specificGroups = selectedGroups.filter((g) => g !== "ungrouped");
            if (specificGroups.length > 0) {
                groupFilter.$or.push({ group: { $in: specificGroups } });
            }
            memoryQuery = Object.assign(Object.assign({}, memoryQuery), groupFilter);
        }
        // Build sort criteria based on options
        let sortCriteria = {};
        if (focusWeakQuestions) {
            // Prioritize questions with low accuracy or few practice attempts
            sortCriteria = { practiceCount: 1, correctCount: 1, createdAt: -1 };
        }
        else {
            // Default sorting
            sortCriteria = { createdAt: -1 };
        }
        // Get memory questions based on filter
        const memoryQuestions = yield MemoryQuestion_1.default.find(memoryQuery).sort(sortCriteria);
        if (memoryQuestions.length === 0) {
            return res.status(404).json({
                success: false,
                message: "Không có câu hỏi nào trong nhóm đã chọn",
            });
        }
        // Determine final question count
        const finalQuestionCount = questionCount === "all"
            ? memoryQuestions.length
            : Math.min(questionCount, memoryQuestions.length);
        // Select questions
        let selectedMemoryQuestions = memoryQuestions;
        if (shuffleQuestions && questionCount !== "all") {
            selectedMemoryQuestions = memoryQuestions
                .sort(() => Math.random() - 0.5)
                .slice(0, finalQuestionCount);
        }
        else if (questionCount !== "all") {
            selectedMemoryQuestions = memoryQuestions.slice(0, finalQuestionCount);
        }
        // Get full question data
        const questionIds = selectedMemoryQuestions.map((q) => q.questionId);
        const questions = yield question_1.default.find({ _id: { $in: questionIds } });
        if (questions.length === 0) {
            return res.status(404).json({
                success: false,
                message: "Không tìm thấy dữ liệu câu hỏi",
            });
        }
        // Process questions similar to takeExamQuizizz
        const safeQuestions = questions.map((q) => {
            let processedOptions = [];
            if (q.answers && Array.isArray(q.answers) && q.answers.length > 0) {
                processedOptions = q.answers.map((opt) => {
                    if (typeof opt === "object") {
                        return {
                            text: opt.text || "",
                            isCorrect: !!opt.isCorrect,
                        };
                    }
                    return { text: String(opt), isCorrect: false };
                });
            }
            else {
                // Create default options if none exist
                processedOptions = [
                    { text: "Lựa chọn A", isCorrect: true },
                    { text: "Lựa chọn B", isCorrect: false },
                    { text: "Lựa chọn C", isCorrect: false },
                    { text: "Lựa chọn D", isCorrect: false },
                ];
            }
            // Shuffle answers if requested
            if (shuffleAnswers) {
                // Keep track of correct answer position
                const correctIndex = processedOptions.findIndex((opt) => opt.isCorrect);
                processedOptions = processedOptions.sort(() => Math.random() - 0.5);
            }
            return Object.assign(Object.assign({}, q.toObject()), { options: processedOptions });
        });
        // Create a virtual exam object for the practice session
        const virtualExam = {
            _id: `memory-practice-${Date.now()}`,
            name: `Luyện tập ghi nhớ - ${selectedGroups.includes("all")
                ? "Tất cả nhóm"
                : selectedGroups.join(", ")}`,
            description: `Luyện tập ${finalQuestionCount} câu hỏi trong ${timeLimit} phút`,
            duration: timeLimit,
            questionCount: finalQuestionCount,
            isActive: true,
            practiceMode: true,
            selectedGroups: selectedGroups,
            reviewMode: reviewMode,
        };
        // Prepare render data using the same structure as takeExamQuizizz
        let renderData = {
            user: res.locals.user,
            student: res.locals.user, // Use user as student for template compatibility
            exam: virtualExam,
            title: `Luyện tập ghi nhớ: ${virtualExam.name} - Quizizz`,
            questions: safeQuestions,
            queryParams: {
                shuffleQuestions: shuffleQuestions.toString(),
                shuffleAnswers: shuffleAnswers.toString(),
            },
        };
        // Encrypt questions data similar to takeExamQuizizz
        try {
            if (safeQuestions && safeQuestions.length > 0) {
                const examData = {
                    examId: virtualExam._id,
                    questions: safeQuestions,
                };
                const { encryptObjectOptimized } = yield Promise.resolve().then(() => __importStar(require("../util/encryption")));
                const encrypted = encryptObjectOptimized(examData);
                renderData.encryptedExamConfig = {
                    appConfigData: encrypted.appConfigData,
                    token: encrypted.token,
                    salt: encrypted.salt,
                    algorithm: encrypted.algorithm,
                };
            }
        }
        catch (encryptError) {
            console.warn("⚠️ Không thể mã hóa câu hỏi practice, sử dụng dữ liệu thường:", encryptError);
            // Questions are already in renderData
        }
        // Render using the same Quizizz template
        return res.render("exam/quizizz", renderData);
    }
    catch (error) {
        console.error("Lỗi khi bắt đầu luyện tập memory với Quizizz:", error);
        return res.status(500).json({
            success: false,
            message: "Đã xảy ra lỗi khi bắt đầu luyện tập",
            error: error.message,
        });
    }
})));
exports.default = router;
//# sourceMappingURL=memory.route.js.map