/**
 * Quizizz Practice UI Module
 * Handles advanced UI interactions, animations, and sound effects
 */
(function (window) {
  "use strict";

  // Sound configuration
  const SOUNDS = {
    correct: "/media/correct_answer.mp3",
    incorrect: "/media/wrong_answer.mp3",
    tick: "/media/tick.mp3",
    streak: "/media/streak.mp3",
    complete: "/media/complete.mp3",
  };

  // Animation configuration
  const ANIMATIONS = {
    questionTransition: 600,
    optionHover: 300,
    feedbackDuration: 2000,
    celebrationDuration: 3000,
  };

  let soundEnabled = true;
  let audioCache = {};

  /**
   * Initialize UI enhancements
   */
  function initUI() {
    console.log("🎨 Initializing Quizizz UI enhancements...");
    
    // Preload sounds
    preloadSounds();
    
    // Setup advanced interactions
    setupAdvancedInteractions();
    
    // Setup keyboard shortcuts
    setupKeyboardShortcuts();
    
    // Setup sound toggle
    setupSoundToggle();
    
    // Setup confetti system
    setupConfetti();
  }

  /**
   * Preload sound effects
   */
  function preloadSounds() {
    Object.entries(SOUNDS).forEach(([key, url]) => {
      const audio = new Audio(url);
      audio.preload = "auto";
      audio.volume = 0.6;
      audioCache[key] = audio;
    });
  }

  /**
   * Play sound effect
   */
  function playSound(soundKey) {
    if (!soundEnabled || !audioCache[soundKey]) return;
    
    try {
      const audio = audioCache[soundKey];
      audio.currentTime = 0;
      audio.play().catch(e => console.warn("Could not play sound:", e));
    } catch (error) {
      console.warn("Sound playback error:", error);
    }
  }

  /**
   * Setup advanced interactions
   */
  function setupAdvancedInteractions() {
    // Add hover effects to option cards
    document.addEventListener("mouseover", (e) => {
      if (e.target.closest(".option-card")) {
        const card = e.target.closest(".option-card");
        if (!card.classList.contains("disabled")) {
          addHoverEffect(card);
        }
      }
    });

    document.addEventListener("mouseout", (e) => {
      if (e.target.closest(".option-card")) {
        const card = e.target.closest(".option-card");
        removeHoverEffect(card);
      }
    });

    // Add click ripple effect
    document.addEventListener("click", (e) => {
      if (e.target.closest(".option-card, .quizizz-button")) {
        addRippleEffect(e.target.closest(".option-card, .quizizz-button"), e);
      }
    });
  }

  /**
   * Add hover effect to option cards
   */
  function addHoverEffect(card) {
    card.style.transform = "translateY(-4px) scale(1.02)";
    card.style.boxShadow = "0 8px 25px rgba(0, 0, 0, 0.25)";
  }

  /**
   * Remove hover effect from option cards
   */
  function removeHoverEffect(card) {
    if (!card.classList.contains("selected")) {
      card.style.transform = "";
      card.style.boxShadow = "";
    }
  }

  /**
   * Add ripple effect to clicked elements
   */
  function addRippleEffect(element, event) {
    const rect = element.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = event.clientX - rect.left - size / 2;
    const y = event.clientY - rect.top - size / 2;

    const ripple = document.createElement("div");
    ripple.style.cssText = `
      position: absolute;
      width: ${size}px;
      height: ${size}px;
      left: ${x}px;
      top: ${y}px;
      background: rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      transform: scale(0);
      animation: ripple 0.6s ease-out;
      pointer-events: none;
      z-index: 10;
    `;

    element.style.position = "relative";
    element.style.overflow = "hidden";
    element.appendChild(ripple);

    setTimeout(() => {
      ripple.remove();
    }, 600);
  }

  /**
   * Setup keyboard shortcuts
   */
  function setupKeyboardShortcuts() {
    document.addEventListener("keydown", (e) => {
      // Number keys 1-4 for options
      if (e.key >= "1" && e.key <= "4") {
        e.preventDefault();
        const optionIndex = parseInt(e.key) - 1;
        selectOptionByIndex(optionIndex);
      }
      
      // Space bar to select highlighted option
      if (e.key === " ") {
        e.preventDefault();
        const highlighted = document.querySelector(".option-card:hover");
        if (highlighted) {
          highlighted.click();
        }
      }
      
      // Escape to show exit modal
      if (e.key === "Escape") {
        e.preventDefault();
        if (window.QuizizzPractice) {
          document.getElementById("exitButton")?.click();
        }
      }
    });
  }

  /**
   * Select option by index
   */
  function selectOptionByIndex(index) {
    const options = document.querySelectorAll(".option-card");
    if (options[index] && !options[index].classList.contains("disabled")) {
      options[index].click();
    }
  }

  /**
   * Setup sound toggle functionality
   */
  function setupSoundToggle() {
    // Create sound toggle button
    const soundToggle = document.createElement("button");
    soundToggle.className = "sound-toggle";
    soundToggle.innerHTML = `<i class="fas fa-volume-up"></i>`;
    soundToggle.title = "Bật/tắt âm thanh";
    
    soundToggle.addEventListener("click", toggleSound);
    
    // Add to header
    const header = document.querySelector("header .flex");
    if (header) {
      header.appendChild(soundToggle);
    }
  }

  /**
   * Toggle sound on/off
   */
  function toggleSound() {
    soundEnabled = !soundEnabled;
    const icon = document.querySelector(".sound-toggle i");
    if (icon) {
      icon.className = soundEnabled ? "fas fa-volume-up" : "fas fa-volume-mute";
    }
    
    // Play test sound if enabling
    if (soundEnabled) {
      playSound("tick");
    }
  }

  /**
   * Setup confetti system
   */
  function setupConfetti() {
    // Create confetti container
    const confettiContainer = document.createElement("div");
    confettiContainer.id = "confettiContainer";
    confettiContainer.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: 999;
    `;
    document.body.appendChild(confettiContainer);
  }

  /**
   * Create confetti animation
   */
  function createConfetti() {
    const container = document.getElementById("confettiContainer");
    if (!container) return;

    const colors = ["#ff4081", "#8854d0", "#4caf50", "#ffeb3b", "#2196f3"];
    const confettiCount = 50;

    for (let i = 0; i < confettiCount; i++) {
      const confetti = document.createElement("div");
      confetti.style.cssText = `
        position: absolute;
        width: 10px;
        height: 10px;
        background: ${colors[Math.floor(Math.random() * colors.length)]};
        left: ${Math.random() * 100}%;
        top: -10px;
        border-radius: ${Math.random() > 0.5 ? "50%" : "0"};
        animation: confetti-fall ${2 + Math.random() * 3}s linear forwards;
        transform: rotate(${Math.random() * 360}deg);
      `;

      container.appendChild(confetti);

      // Remove after animation
      setTimeout(() => {
        confetti.remove();
      }, 5000);
    }
  }

  /**
   * Enhanced answer feedback with animations
   */
  function showEnhancedFeedback(isCorrect, streak = 0) {
    // Play appropriate sound
    playSound(isCorrect ? "correct" : "incorrect");
    
    // Show confetti for correct answers
    if (isCorrect) {
      createConfetti();
      
      // Play streak sound for streaks
      if (streak > 1) {
        setTimeout(() => playSound("streak"), 500);
      }
    }
    
    // Add screen shake for incorrect answers
    if (!isCorrect) {
      document.body.style.animation = "shake 0.5s ease-in-out";
      setTimeout(() => {
        document.body.style.animation = "";
      }, 500);
    }
  }

  /**
   * Animate score increase
   */
  function animateScoreIncrease(newScore, oldScore) {
    const scoreDisplay = document.getElementById("scoreDisplay");
    if (!scoreDisplay) return;

    const difference = newScore - oldScore;
    if (difference <= 0) return;

    // Create floating score animation
    const floatingScore = document.createElement("div");
    floatingScore.textContent = `+${difference}`;
    floatingScore.style.cssText = `
      position: absolute;
      color: #4caf50;
      font-weight: bold;
      font-size: 20px;
      pointer-events: none;
      z-index: 1000;
      animation: float-up 2s ease-out forwards;
    `;

    const rect = scoreDisplay.getBoundingClientRect();
    floatingScore.style.left = rect.right + "px";
    floatingScore.style.top = rect.top + "px";

    document.body.appendChild(floatingScore);

    // Animate score counter
    animateCounter(scoreDisplay, oldScore, newScore, 1000);

    // Remove floating score
    setTimeout(() => {
      floatingScore.remove();
    }, 2000);
  }

  /**
   * Animate counter from old value to new value
   */
  function animateCounter(element, start, end, duration) {
    const startTime = performance.now();
    
    function updateCounter(currentTime) {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);
      
      const current = Math.floor(start + (end - start) * progress);
      element.textContent = current;
      
      if (progress < 1) {
        requestAnimationFrame(updateCounter);
      }
    }
    
    requestAnimationFrame(updateCounter);
  }

  /**
   * Animate streak counter
   */
  function animateStreak(streakCount) {
    const streakElement = document.getElementById("streakCounter");
    if (!streakElement) return;

    if (streakCount > 0) {
      streakElement.classList.remove("hidden");
      streakElement.style.animation = "streak-pop 0.5s ease-out";
      
      setTimeout(() => {
        streakElement.style.animation = "";
      }, 500);
    } else {
      streakElement.classList.add("hidden");
    }
  }

  /**
   * Create particle effect for correct answers
   */
  function createParticleEffect(element) {
    const rect = element.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;

    for (let i = 0; i < 12; i++) {
      const particle = document.createElement("div");
      particle.style.cssText = `
        position: fixed;
        width: 6px;
        height: 6px;
        background: #4caf50;
        border-radius: 50%;
        left: ${centerX}px;
        top: ${centerY}px;
        pointer-events: none;
        z-index: 1000;
        animation: particle-burst 0.8s ease-out forwards;
        transform: rotate(${i * 30}deg);
      `;

      document.body.appendChild(particle);

      setTimeout(() => {
        particle.remove();
      }, 800);
    }
  }

  /**
   * Add CSS animations
   */
  function addAnimationStyles() {
    const style = document.createElement("style");
    style.textContent = `
      @keyframes ripple {
        to {
          transform: scale(2);
          opacity: 0;
        }
      }
      
      @keyframes confetti-fall {
        to {
          transform: translateY(100vh) rotate(720deg);
          opacity: 0;
        }
      }
      
      @keyframes float-up {
        0% {
          opacity: 1;
          transform: translateY(0);
        }
        100% {
          opacity: 0;
          transform: translateY(-50px);
        }
      }
      
      @keyframes streak-pop {
        0% {
          transform: scale(1);
        }
        50% {
          transform: scale(1.2);
        }
        100% {
          transform: scale(1);
        }
      }
      
      @keyframes particle-burst {
        0% {
          transform: rotate(var(--rotation)) translateX(0);
          opacity: 1;
        }
        100% {
          transform: rotate(var(--rotation)) translateX(50px);
          opacity: 0;
        }
      }
      
      .sound-toggle {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        padding: 8px 12px;
        color: white;
        cursor: pointer;
        transition: all 0.3s ease;
      }
      
      .sound-toggle:hover {
        background: rgba(255, 255, 255, 0.2);
      }
    `;
    document.head.appendChild(style);
  }

  // Initialize when DOM is ready
  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", () => {
      addAnimationStyles();
      initUI();
    });
  } else {
    addAnimationStyles();
    initUI();
  }

  // Expose public API
  window.QuizizzUI = {
    playSound,
    showEnhancedFeedback,
    animateScoreIncrease,
    animateStreak,
    createParticleEffect,
    createConfetti,
    toggleSound,
  };

})(window);
