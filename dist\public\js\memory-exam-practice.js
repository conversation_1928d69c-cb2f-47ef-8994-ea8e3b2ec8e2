/**
 * memory-exam-practice.js
 * Module để xử lý chức năng luyện tập ghi nhớ
 * Sử dụng pattern IIFE để tạo namespace và bảo vệ biến
 */
(function (window) {
    "use strict";
    // Cấu hình và state
    const _config = {
        apiBase: "/exam/memory",
        timeLimit: 15 * 60, // mặc định 15 phút
        shuffleQuestions: false,
        shuffleAnswers: false,
    };
    // State của bài luyện tập
    let _state = {
        productId: null,
        practiceId: null,
        questions: [],
        currentQuestionIndex: 0,
        answers: {},
        timeLeft: 0,
        timer: null,
        isSubmitting: false,
        totalQuestions: 0,
    };
    // Cache các phần tử DOM
    const _dom = {};
    /**
     * Khởi tạo module
     */
    function init() {
        var _a, _b;
        // Lấy thông tin từ meta tags
        _state.productId = (_a = document
            .querySelector('meta[name="productId"]')) === null || _a === void 0 ? void 0 : _a.getAttribute("content");
        _state.practiceId = (_b = document
            .querySelector('meta[name="practiceId"]')) === null || _b === void 0 ? void 0 : _b.getAttribute("content");
        // Lấy tham số từ URL
        const urlParams = new URLSearchParams(window.location.search);
        _config.shuffleQuestions = urlParams.get("shuffleQuestions") === "true";
        _config.shuffleAnswers = urlParams.get("shuffleAnswers") === "true";
        const count = parseInt(urlParams.get("count")) || 10;
        const time = parseInt(urlParams.get("time")) || 15;
        _config.timeLimit = time * 60;
        _state.timeLeft = _config.timeLimit;
        console.log("Memory Practice initialized with:", {
            productId: _state.productId,
            practiceId: _state.practiceId,
            count: count,
            time: time,
            shuffleQuestions: _config.shuffleQuestions,
            shuffleAnswers: _config.shuffleAnswers,
        });
        // Cache các phần tử DOM
        _cacheDomElements();
        // Load câu hỏi
        _loadQuestions(count);
        // Thiết lập các event listeners
        _setupEventListeners();
    }
    /**
     * Cache các phần tử DOM để tối ưu hiệu suất
     */
    function _cacheDomElements() {
        _dom.loadingContainer = document.getElementById("loadingContainer");
        _dom.practiceContainer = document.getElementById("practiceContainer");
        _dom.questionContainer = document.getElementById("questionContainer");
        _dom.optionsContainer = document.getElementById("optionsContainer");
        _dom.prevButton = document.getElementById("prevButton");
        _dom.nextButton = document.getElementById("nextButton");
        _dom.progressBar = document.getElementById("progressBar");
        _dom.progressPercentage = document.getElementById("progressPercentage");
        _dom.currentQuestionIndicator = document.getElementById("currentQuestionIndicator");
        _dom.resultContainer = document.getElementById("resultContainer");
        _dom.detailedResultsContainer = document.getElementById("detailedResultsContainer");
        _dom.timerDisplay = document.getElementById("timerDisplay");
        _dom.correctSound = document.getElementById("correctSound");
        _dom.wrongSound = document.getElementById("wrongSound");
    }
    /**
     * Thiết lập các event listeners
     */
    function _setupEventListeners() {
        // Nút điều hướng câu hỏi
        _dom.prevButton.addEventListener("click", _goToPreviousQuestion);
        _dom.nextButton.addEventListener("click", _goToNextQuestion);
        // Xử lý phím tắt
        document.addEventListener("keydown", (event) => {
            // Chỉ xử lý khi container câu hỏi đang hiển thị
            if (_dom.practiceContainer.classList.contains("hidden"))
                return;
            switch (event.key) {
                case "ArrowLeft":
                    _goToPreviousQuestion();
                    break;
                case "ArrowRight":
                    _goToNextQuestion();
                    break;
                case "1":
                case "2":
                case "3":
                case "4":
                    const index = parseInt(event.key) - 1;
                    _selectAnswer(index);
                    break;
            }
        });
    }
    /**
     * Load câu hỏi từ API
     */
    function _loadQuestions(count) {
        // Hiển thị loading
        _dom.loadingContainer.classList.remove("hidden");
        _dom.practiceContainer.classList.add("hidden");
        // Gọi API để lấy câu hỏi
        fetch(`${_config.apiBase}/${_state.productId}/practice/start`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify({
                count: count,
                timeLimit: _config.timeLimit / 60, // Chuyển từ giây sang phút
                shuffleQuestions: _config.shuffleQuestions,
                shuffleAnswers: _config.shuffleAnswers,
            }),
        })
            .then((response) => {
            if (!response.ok) {
                throw new Error("Không thể tải câu hỏi");
            }
            return response.json();
        })
            .then((data) => {
            if (data.success && data.questions) {
                // Lưu danh sách câu hỏi và thiết lập bài luyện tập
                _state.questions = data.questions;
                _state.totalQuestions = data.questions.length;
                // Cập nhật hiển thị
                _dom.currentQuestionIndicator.textContent = `Câu 1/${_state.totalQuestions}`;
                _dom.loadingContainer.classList.add("hidden");
                _dom.practiceContainer.classList.remove("hidden");
                // Hiển thị câu hỏi đầu tiên
                _displayCurrentQuestion();
                // Bắt đầu đồng hồ đếm ngược
                _startTimer();
            }
            else {
                throw new Error(data.message || "Không thể tải câu hỏi");
            }
        })
            .catch((error) => {
            console.error("Error loading questions:", error);
            _showError("Không thể tải câu hỏi: " + error.message);
        });
    }
    /**
     * Hiển thị lỗi
     */
    function _showError(message) {
        _dom.loadingContainer.innerHTML = `
      <div class="text-center">
        <div class="text-red-500 mb-4">
          <i class="fas fa-exclamation-circle text-4xl"></i>
        </div>
        <h3 class="text-lg font-medium text-gray-800 mb-2">Đã xảy ra lỗi</h3>
        <p class="text-gray-600 mb-4">${message}</p>
        <button onclick="window.location.reload()" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
          Thử lại
        </button>
      </div>
    `;
    }
    /**
     * Hiển thị câu hỏi hiện tại
     */
    function _displayCurrentQuestion() {
        const question = _state.questions[_state.currentQuestionIndex];
        if (!question)
            return;
        // Hiển thị nội dung câu hỏi
        _dom.questionContainer.innerHTML = `
      <div class="flex items-start">
        <span class="bg-indigo-100 text-indigo-800 font-medium rounded-full px-2.5 py-1 text-xs mr-2">
          ${_state.currentQuestionIndex + 1}/${_state.totalQuestions}
        </span>
        <div class="break-words">${question.text}</div>
      </div>
    `;
        // Hiển thị các phương án trả lời
        let optionsHTML = "";
        question.options.forEach((option, index) => {
            const isSelected = _state.answers[question._id] === index;
            optionsHTML += `
        <div class="option-item">
          <button 
            class="w-full text-left p-3 rounded-lg border ${isSelected
                ? "bg-indigo-50 border-indigo-300"
                : "border-gray-200 hover:bg-gray-50"} transition-colors"
            onclick="window.MemoryExamPractice.selectAnswer(${index})"
          >
            <div class="flex items-start">
              <span class="inline-flex items-center justify-center h-5 w-5 rounded-full ${isSelected
                ? "bg-indigo-500 text-white"
                : "bg-gray-200 text-gray-700"} mr-2 text-xs">
                ${String.fromCharCode(65 + index)}
              </span>
              <span class="option-text break-words">${option}</span>
            </div>
          </button>
        </div>
      `;
        });
        _dom.optionsContainer.innerHTML = optionsHTML;
        // Cập nhật thanh tiến trình
        const progress = ((_state.currentQuestionIndex + 1) / _state.totalQuestions) * 100;
        _dom.progressBar.style.width = `${progress}%`;
        _dom.progressPercentage.textContent = `${Math.round(progress)}%`;
        // Cập nhật chỉ báo câu hỏi hiện tại
        _dom.currentQuestionIndicator.textContent = `Câu ${_state.currentQuestionIndex + 1}/${_state.totalQuestions}`;
        // Cập nhật trạng thái nút
        _updateButtonsState();
    }
    /**
     * Cập nhật trạng thái của các nút điều hướng
     */
    function _updateButtonsState() {
        // Nút Previous bị vô hiệu hóa khi ở câu hỏi đầu tiên
        _dom.prevButton.disabled = _state.currentQuestionIndex === 0;
        // Nút Next thay đổi text khi ở câu hỏi cuối cùng
        if (_state.currentQuestionIndex === _state.totalQuestions - 1) {
            _dom.nextButton.textContent = "Nộp bài";
            _dom.nextButton.classList.remove("bg-indigo-600", "hover:bg-indigo-700");
            _dom.nextButton.classList.add("bg-green-600", "hover:bg-green-700");
        }
        else {
            _dom.nextButton.innerHTML =
                'Câu tiếp theo <i class="fas fa-chevron-right ml-2"></i>';
            _dom.nextButton.classList.add("bg-indigo-600", "hover:bg-indigo-700");
            _dom.nextButton.classList.remove("bg-green-600", "hover:bg-green-700");
        }
    }
    /**
     * Chọn phương án trả lời cho câu hỏi hiện tại
     */
    function _selectAnswer(optionIndex) {
        const question = _state.questions[_state.currentQuestionIndex];
        if (!question)
            return;
        // Lưu câu trả lời
        _state.answers[question._id] = optionIndex;
        // Cập nhật giao diện
        _displayCurrentQuestion();
    }
    /**
     * Chuyển đến câu hỏi trước
     */
    function _goToPreviousQuestion() {
        if (_state.currentQuestionIndex > 0) {
            _state.currentQuestionIndex--;
            _displayCurrentQuestion();
        }
    }
    /**
     * Chuyển đến câu hỏi tiếp theo hoặc nộp bài nếu đang ở câu cuối
     */
    function _goToNextQuestion() {
        if (_state.currentQuestionIndex < _state.totalQuestions - 1) {
            _state.currentQuestionIndex++;
            _displayCurrentQuestion();
        }
        else {
            _confirmSubmit();
        }
    }
    /**
     * Xác nhận nộp bài
     */
    function _confirmSubmit() {
        // Đếm số câu hỏi đã trả lời
        const answeredCount = Object.keys(_state.answers).length;
        // Hiển thị modal xác nhận
        if (confirm(`Bạn đã trả lời ${answeredCount}/${_state.totalQuestions} câu hỏi. Bạn có chắc muốn nộp bài?`)) {
            _submitPractice();
        }
    }
    /**
     * Nộp bài luyện tập
     */
    function _submitPractice() {
        if (_state.isSubmitting)
            return;
        _state.isSubmitting = true;
        // Dừng đồng hồ đếm ngược
        if (_state.timer) {
            clearInterval(_state.timer);
        }
        // Hiển thị loading
        _dom.practiceContainer.classList.add("hidden");
        _dom.loadingContainer.classList.remove("hidden");
        _dom.loadingContainer.innerHTML = `
      <div class="text-center">
        <div class="animate-spin rounded-full h-16 w-16 border-b-2 border-indigo-500 mb-4"></div>
        <p class="text-gray-600">Đang xử lý kết quả...</p>
      </div>
    `;
        // Gọi API để nộp bài
        fetch(`${_config.apiBase}/${_state.productId}/practice/submit`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify({
                practiceId: _state.practiceId,
                answers: _state.answers,
                timeSpent: _config.timeLimit - _state.timeLeft,
            }),
        })
            .then((response) => {
            if (!response.ok) {
                throw new Error("Không thể nộp bài");
            }
            return response.json();
        })
            .then((data) => {
            if (data.success) {
                // Hiển thị kết quả
                _displayResults(data.results);
            }
            else {
                throw new Error(data.message || "Không thể nộp bài");
            }
        })
            .catch((error) => {
            console.error("Error submitting practice:", error);
            _showError("Không thể nộp bài: " + error.message);
        })
            .finally(() => {
            _state.isSubmitting = false;
        });
    }
    /**
     * Hiển thị kết quả của bài luyện tập
     */
    function _displayResults(results) {
        // Ẩn các container khác
        _dom.loadingContainer.classList.add("hidden");
        _dom.practiceContainer.classList.add("hidden");
        // Tính toán số câu đúng và tỷ lệ đúng
        const correctCount = results.correctAnswers;
        const totalCount = _state.totalQuestions;
        const percentCorrect = Math.round((correctCount / totalCount) * 100);
        // Hiển thị kết quả tổng quan
        _dom.resultContainer.classList.remove("hidden");
        _dom.resultContainer.innerHTML = `
      <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <div class="text-center mb-6">
          <h2 class="text-2xl font-bold text-gray-800 mb-2">Kết quả luyện tập ghi nhớ</h2>
          <p class="text-gray-600">Bạn đã hoàn thành bài luyện tập!</p>
        </div>
        
        <div class="flex justify-center mb-6">
          <div class="w-48 h-48 relative">
            <svg class="w-full h-full" viewBox="0 0 36 36">
              <path class="stroke-current text-gray-200" stroke-width="3.8" fill="none" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831" />
              <path class="stroke-current ${percentCorrect >= 80
            ? "text-green-500"
            : percentCorrect >= 50
                ? "text-yellow-500"
                : "text-red-500"}" stroke-width="3.8" fill="none" stroke-dasharray="${percentCorrect}, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831" stroke-linecap="round" />
              <text x="18" y="20.35" class="text-3xl font-medium" text-anchor="middle">${percentCorrect}%</text>
            </svg>
          </div>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div class="bg-blue-50 p-4 rounded-lg text-center">
            <p class="text-blue-800 text-sm font-medium">Số câu hỏi</p>
            <p class="text-blue-600 text-2xl font-bold">${totalCount}</p>
          </div>
          
          <div class="bg-green-50 p-4 rounded-lg text-center">
            <p class="text-green-800 text-sm font-medium">Số câu đúng</p>
            <p class="text-green-600 text-2xl font-bold">${correctCount}</p>
          </div>
          
          <div class="bg-red-50 p-4 rounded-lg text-center">
            <p class="text-red-800 text-sm font-medium">Số câu sai</p>
            <p class="text-red-600 text-2xl font-bold">${totalCount - correctCount}</p>
          </div>
        </div>
        
        <div class="flex justify-center space-x-4">
          <button onclick="window.location.href='/course/${_state.productId}/exams?tab=memory'" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">
            Quay lại danh sách
          </button>
          <button onclick="window.location.reload()" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
            Luyện tập lại
          </button>
        </div>
      </div>
      
      <button id="showDetailedResultsBtn" class="w-full py-2 bg-white text-gray-700 rounded-md hover:bg-gray-100 border border-gray-200 mb-6">
        Xem chi tiết <i class="fas fa-chevron-down ml-1"></i>
      </button>
    `;
        // Hiển thị kết quả chi tiết
        _dom.detailedResultsContainer.classList.remove("hidden");
        // Tạo HTML cho các câu hỏi và đáp án
        let detailedHTML = '<div class="space-y-6">';
        results.questions.forEach((result, index) => {
            const question = _state.questions.find((q) => q._id === result.questionId);
            if (!question)
                return;
            const userAnswer = result.userAnswer !== undefined ? result.userAnswer : null;
            const isCorrect = result.isCorrect;
            detailedHTML += `
        <div class="bg-white rounded-lg shadow-sm p-4 border ${isCorrect ? "border-green-200" : "border-red-200"}">
          <div class="flex items-start justify-between mb-3">
            <div class="flex items-start">
              <span class="bg-gray-100 text-gray-800 font-medium rounded-full px-2.5 py-1 text-xs mr-2">
                ${index + 1}
              </span>
              <div class="text-gray-800">${question.text}</div>
            </div>
            <span class="inline-flex items-center justify-center h-6 w-6 rounded-full ${isCorrect
                ? "bg-green-100 text-green-700"
                : "bg-red-100 text-red-700"}">
              <i class="fas ${isCorrect ? "fa-check" : "fa-times"}"></i>
            </span>
          </div>
          
          <div class="pl-7 space-y-2 mb-3">
            ${question.options
                .map((option, i) => {
                const isUserAnswer = userAnswer === i;
                const isCorrectAnswer = i === question.answer;
                let optionClass = "border-gray-200 bg-white";
                if (isCorrectAnswer)
                    optionClass = "border-green-300 bg-green-50";
                if (isUserAnswer && !isCorrect)
                    optionClass = "border-red-300 bg-red-50";
                return `
                <div class="p-2 rounded-md border ${optionClass} flex items-start">
                  <span class="inline-flex items-center justify-center h-5 w-5 rounded-full ${isCorrectAnswer
                    ? "bg-green-500 text-white"
                    : isUserAnswer
                        ? "bg-red-500 text-white"
                        : "bg-gray-200 text-gray-700"} mr-2 text-xs">
                    ${String.fromCharCode(65 + i)}
                  </span>
                  <span>${option}</span>
                  ${isCorrectAnswer
                    ? '<span class="ml-auto text-green-600"><i class="fas fa-check"></i></span>'
                    : ""}
                  ${isUserAnswer && !isCorrect
                    ? '<span class="ml-auto text-red-600"><i class="fas fa-times"></i></span>'
                    : ""}
                </div>
              `;
            })
                .join("")}
          </div>
          
          ${question.explanation
                ? `
            <div class="pl-7 mt-3 p-3 bg-blue-50 rounded-md text-blue-800 text-sm">
              <div class="font-medium mb-1">Giải thích:</div>
              <div>${question.explanation}</div>
            </div>
          `
                : ""}
        </div>
      `;
        });
        detailedHTML += "</div>";
        _dom.detailedResultsContainer.innerHTML = detailedHTML;
        _dom.detailedResultsContainer.classList.add("hidden");
        // Thêm event listener cho nút xem chi tiết
        document
            .getElementById("showDetailedResultsBtn")
            .addEventListener("click", function () {
            const isHidden = _dom.detailedResultsContainer.classList.contains("hidden");
            _dom.detailedResultsContainer.classList.toggle("hidden");
            this.innerHTML = isHidden
                ? 'Ẩn chi tiết <i class="fas fa-chevron-up ml-1"></i>'
                : 'Xem chi tiết <i class="fas fa-chevron-down ml-1"></i>';
        });
    }
    /**
     * Bắt đầu đồng hồ đếm ngược
     */
    function _startTimer() {
        _state.timeLeft = _config.timeLimit;
        _updateTimerDisplay();
        _state.timer = setInterval(() => {
            _state.timeLeft--;
            if (_state.timeLeft <= 0) {
                clearInterval(_state.timer);
                alert("Hết thời gian! Bài luyện tập của bạn sẽ được nộp tự động.");
                _submitPractice();
                return;
            }
            _updateTimerDisplay();
            // Cảnh báo khi còn ít thời gian
            if (_state.timeLeft === 60) {
                // 1 phút
                alert("Còn 1 phút nữa hết giờ!");
            }
        }, 1000);
    }
    /**
     * Cập nhật hiển thị đồng hồ đếm ngược
     */
    function _updateTimerDisplay() {
        const minutes = Math.floor(_state.timeLeft / 60);
        const seconds = _state.timeLeft % 60;
        _dom.timerDisplay.textContent = `${minutes
            .toString()
            .padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
        // Thay đổi màu khi còn ít thời gian
        if (_state.timeLeft <= 60) {
            // Còn 1 phút hoặc ít hơn
            _dom.timerDisplay.classList.add("text-red-600");
            _dom.timerDisplay.classList.remove("text-blue-700");
        }
    }
    // API công khai cho module
    const publicAPI = {
        init: init,
        selectAnswer: _selectAnswer,
    };
    // Gán API cho window
    window.MemoryExamPractice = publicAPI;
})(window);
//# sourceMappingURL=memory-exam-practice.js.map