<div class="max-w-4xl mx-auto px-1 sm:px-0">
  <div class="bg-white rounded-lg shadow-md p-2 sm:p-8">
    <!-- Thông tin khóa học -->
    <div class="mb-4 sm:mb-6 flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-800 mb-1">
          <%= product.name %>
        </h1>
        <div class="flex items-center text-sm text-gray-500">
          <span class="inline-flex items-center mr-4">
            <i class="fas fa-file-lines mr-1"></i> Tài li<PERSON>u học tập
          </span>
          <span
            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= product.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' %>"
          >
            <%= product.status %>
          </span>
        </div>
      </div>
      <a
        href="/home"
        class="inline-flex items-center text-indigo-600 hover:text-indigo-800"
      >
        <i class="fas fa-arrow-left mr-1"></i> Quay lại
      </a>
    </div>

    <!-- Document Info -->
    <div class="mb-6 bg-gray-50 rounded-lg p-4">
      <div class="flex items-center justify-between">
        <div>
          <% if (document.modifiedTime) { %>
          <p class="text-sm text-gray-500">
            <i class="fas fa-clock mr-1"></i>
            Cập nhật lần cuối: <%= new
            Date(document.modifiedTime).toLocaleString('vi-VN') %>
          </p>
          <% } %>
        </div>
        <div class="flex items-center space-x-2">
          <button
            onclick="copyDocumentLink()"
            class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors"
          >
            <i class="fas fa-share mr-1"></i>
            <span class="hidden sm:inline">Chia sẻ</span>
            <span class="sm:hidden">Chia sẻ</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Document Content -->
    <div class="bg-white border border-gray-200 rounded-lg overflow-hidden">
      <div class="p-6">
        <div
          id="document-content"
          class="prose prose-sm sm:prose lg:prose-lg max-w-none prose-headings:text-gray-900 prose-h1:text-2xl prose-h1:font-bold prose-h1:mb-4 prose-h2:text-xl prose-h2:font-semibold prose-h2:mb-3 prose-h3:text-lg prose-h3:font-medium prose-h3:mb-2 prose-p:text-gray-700 prose-p:leading-relaxed prose-p:mb-4 prose-strong:text-gray-900 prose-strong:font-semibold prose-em:text-gray-800 prose-em:italic prose-ul:list-disc prose-ul:pl-6 prose-ul:mb-4 prose-ol:list-decimal prose-ol:pl-6 prose-ol:mb-4 prose-li:text-gray-700 prose-li:mb-1 prose-blockquote:border-l-4 prose-blockquote:border-indigo-200 prose-blockquote:pl-4 prose-blockquote:italic prose-blockquote:text-gray-600 prose-code:bg-gray-100 prose-code:px-1 prose-code:py-0.5 prose-code:rounded prose-code:text-sm prose-pre:bg-gray-100 prose-pre:p-4 prose-pre:rounded-lg prose-pre:overflow-x-auto prose-table:border-collapse prose-table:w-full prose-td:border prose-td:border-gray-300 prose-td:px-3 prose-td:py-2 prose-th:border prose-th:border-gray-300 prose-th:px-3 prose-th:py-2 prose-th:bg-gray-50 prose-th:font-semibold prose-img:max-w-full prose-img:h-auto prose-img:rounded-lg prose-img:shadow-md"
        >
          <%- document.content %>
        </div>
      </div>
    </div>

    <!-- Back to Course Button -->
    <div class="mt-6 flex justify-center">
      <a
        href="/course/<%= product._id %>/exams"
        class="inline-flex items-center px-6 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-indigo-600 hover:bg-indigo-700 transition-colors"
      >
        <i class="fas fa-arrow-left mr-2"></i>
        Quay lại danh sách bài thi
      </a>
    </div>
  </div>
</div>

<!-- Loading Modal -->
<div
  id="loading-modal"
  class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center"
>
  <div class="bg-white rounded-lg p-6 max-w-sm w-full mx-4">
    <div class="text-center">
      <div
        class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-indigo-100 mb-4"
      >
        <i class="fas fa-spinner fa-spin text-2xl text-indigo-600"></i>
      </div>
      <h3 class="text-lg font-medium text-gray-900 mb-2">
        Đang tải tài liệu...
      </h3>
      <p class="text-sm text-gray-500">Vui lòng đợi trong giây lát</p>
    </div>
  </div>
</div>

<script>
  // Copy document link function
  function copyDocumentLink() {
    const url = window.location.href;
    if (navigator.clipboard && navigator.clipboard.writeText) {
      navigator.clipboard
        .writeText(url)
        .then(() => {
          showNotification("Đã sao chép liên kết!", "success");
        })
        .catch(() => {
          fallbackCopyTextToClipboard(url);
        });
    } else {
      fallbackCopyTextToClipboard(url);
    }
  }

  // Fallback copy function
  function fallbackCopyTextToClipboard(text) {
    const textArea = document.createElement("textarea");
    textArea.value = text;
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.position = "fixed";
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
      const successful = document.execCommand("copy");
      if (successful) {
        showNotification("Đã sao chép liên kết!", "success");
      } else {
        showNotification("Không thể sao chép liên kết", "error");
      }
    } catch (err) {
      showNotification("Không thể sao chép liên kết", "error");
    }

    document.body.removeChild(textArea);
  }

  // Show notification function
  function showNotification(message, type = "success") {
    // Remove existing notifications
    const existingNotifications = document.querySelectorAll(
      ".notification-toast"
    );
    existingNotifications.forEach((notification) => notification.remove());

    // Create new notification
    const notification = document.createElement("div");
    notification.className = `notification-toast fixed top-4 right-4 z-50 px-4 py-3 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full ${
      type === "success" ? "bg-green-500 text-white" : "bg-red-500 text-white"
    }`;
    notification.innerHTML = `
      <div class="flex items-center">
        <i class="fas ${
          type === "success" ? "fa-check-circle" : "fa-exclamation-circle"
        } mr-2"></i>
        <span>${message}</span>
      </div>
    `;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
      notification.classList.remove("translate-x-full");
    }, 100);

    // Auto remove after 3 seconds
    setTimeout(() => {
      notification.classList.add("translate-x-full");
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    }, 3000);
  }

  // Print styles
  const printStyles = `
    <style>
      @media print {
        body * {
          visibility: hidden !important;
          display: none !important;
        }
        #document-content, #document-content * {
          visibility: visible !important;
          display: block !important;
        }
        #document-content {
          position: absolute !important;
          left: 0 !important;
          top: 0 !important;
          width: 100% !important;
          padding: 20px !important;
          font-size: 12pt !important;
          line-height: 1.5 !important;
        }
        #document-content h1, #document-content h2, #document-content h3 {
          page-break-after: avoid !important;
          margin-top: 20px !important;
          margin-bottom: 10px !important;
        }
        #document-content p {
          margin-bottom: 10px !important;
        }
        #document-content ul, #document-content ol {
          margin-bottom: 15px !important;
        }
        #document-content li {
          margin-bottom: 5px !important;
        }
      }
    </style>
  `;

  // Add print styles to head
  document.head.insertAdjacentHTML("beforeend", printStyles);

  // Smooth scroll for long documents
  document.addEventListener("DOMContentLoaded", function () {
    // Add smooth scrolling behavior
    document.documentElement.style.scrollBehavior = "smooth";

    // Add table of contents if document is long
    const content = document.getElementById("document-content");
    if (content && content.scrollHeight > 2000) {
      addTableOfContents();
    }
  });

  // Add table of contents for long documents
  function addTableOfContents() {
    const content = document.getElementById("document-content");
    const headings = content.querySelectorAll("h1, h2, h3");

    if (headings.length > 3) {
      const toc = document.createElement("div");
      toc.className = "bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6";
      toc.innerHTML =
        '<h3 class="text-lg font-semibold text-blue-800 mb-3"><i class="fas fa-list mr-2"></i>Mục lục</h3>';

      const tocList = document.createElement("ul");
      tocList.className = "space-y-1";

      headings.forEach((heading, index) => {
        const id = `heading-${index}`;
        heading.id = id;

        const li = document.createElement("li");
        const level = parseInt(heading.tagName.substring(1));
        li.className = `ml-${(level - 1) * 4}`;

        const link = document.createElement("a");
        link.href = `#${id}`;
        link.className = "text-blue-600 hover:text-blue-800 text-sm";
        link.textContent = heading.textContent;

        li.appendChild(link);
        tocList.appendChild(li);
      });

      toc.appendChild(tocList);
      content.parentNode.insertBefore(toc, content);
    }
  }
</script>

<% script = `
<script>
  console.log("Documents page loaded successfully");
</script>
` %>
