<div class="max-w-4xl mx-auto px-1 sm:px-0">
  <div class="bg-white rounded-lg shadow-md p-2 sm:p-8">
    <!-- Thông tin khóa học -->
    <div class="mb-4 sm:mb-6 flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-800 mb-1">
          <%= product.name %>
        </h1>
        <div class="flex items-center text-sm text-gray-500">
          <span class="inline-flex items-center mr-4">
            <i class="fas fa-file-lines mr-1"></i> <%= new
            Date(document.modifiedTime).toLocaleString('vi-VN') %>
          </span>
          <span
            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= product.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' %>"
          >
            <%= product.status %>
          </span>
        </div>
      </div>
      <a
        href="/home"
        class="inline-flex items-center text-indigo-600 hover:text-indigo-800"
      >
        <i class="fas fa-arrow-left mr-1"></i> Quay lại
      </a>
    </div>

    <!-- Document Content -->
    <div class="bg-white border border-gray-200 rounded-lg overflow-hidden">
      <!-- Search Bar -->
      <div class="border-b border-gray-200 bg-gray-50 p-4">
        <div class="flex items-center space-x-3">
          <div class="flex-1 relative">
            <div
              class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
            >
              <i class="fas fa-search text-gray-400"></i>
            </div>
            <input
              type="text"
              id="document-search"
              placeholder="Tìm kiếm trong tài liệu... (Ctrl+F hoặc /)"
              class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 text-sm"
              title="Sử dụng Ctrl+F hoặc phím / để focus vào ô tìm kiếm"
            />
          </div>
          <div class="flex items-center space-x-2">
            <span
              id="search-results"
              class="text-sm text-gray-500 min-w-0"
            ></span>
            <button
              id="search-prev"
              class="p-2 border border-gray-300 rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
              disabled
              title="Kết quả trước"
            >
              <i class="fas fa-chevron-up text-xs"></i>
            </button>
            <button
              id="search-next"
              class="p-2 border border-gray-300 rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
              disabled
              title="Kết quả tiếp theo"
            >
              <i class="fas fa-chevron-down text-xs"></i>
            </button>
            <button
              id="search-clear"
              class="p-2 border border-gray-300 rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100"
              title="Xóa tìm kiếm"
            >
              <i class="fas fa-times text-xs"></i>
            </button>
          </div>
        </div>
      </div>

      <!-- Scrollable Document Content Container -->
      <div class="relative">
        <div
          id="document-container"
          class="h-[70vh] overflow-y-auto p-6 scroll-smooth"
          style="max-height: 70vh"
        >
          <div
            id="document-content"
            class="prose prose-sm sm:prose lg:prose-lg max-w-none prose-headings:text-gray-900 prose-h1:text-2xl prose-h1:font-bold prose-h1:mb-4 prose-h2:text-xl prose-h2:font-semibold prose-h2:mb-3 prose-h3:text-lg prose-h3:font-medium prose-h3:mb-2 prose-p:text-gray-700 prose-p:leading-relaxed prose-p:mb-4 prose-strong:text-gray-900 prose-strong:font-semibold prose-em:text-gray-800 prose-em:italic prose-ul:list-disc prose-ul:pl-6 prose-ul:mb-4 prose-ol:list-decimal prose-ol:pl-6 prose-ol:mb-4 prose-li:text-gray-700 prose-li:mb-1 prose-blockquote:border-l-4 prose-blockquote:border-indigo-200 prose-blockquote:pl-4 prose-blockquote:italic prose-blockquote:text-gray-600 prose-code:bg-gray-100 prose-code:px-1 prose-code:py-0.5 prose-code:rounded prose-code:text-sm prose-pre:bg-gray-100 prose-pre:p-4 prose-pre:rounded-lg prose-pre:overflow-x-auto prose-table:border-collapse prose-table:w-full prose-td:border prose-td:border-gray-300 prose-td:px-3 prose-td:py-2 prose-th:border prose-th:border-gray-300 prose-th:px-3 prose-th:py-2 prose-th:bg-gray-50 prose-th:font-semibold prose-img:max-w-full prose-img:h-auto prose-img:rounded-lg prose-img:shadow-md"
          >
            <%- document.content %>
          </div>
        </div>

        <!-- Scroll indicator -->
        <div
          id="scroll-indicator"
          class="absolute bottom-4 right-4 bg-gray-800 text-white px-2 py-1 rounded text-xs opacity-0 transition-opacity duration-200"
        >
          <i class="fas fa-arrow-down mr-1"></i>Cuộn để xem thêm
        </div>
      </div>
    </div>

    <!-- Back to Course Button -->
    <div class="mt-6 flex justify-center">
      <a
        href="/course/<%= product._id %>/exams"
        class="inline-flex items-center px-6 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-indigo-600 hover:bg-indigo-700 transition-colors"
      >
        <i class="fas fa-arrow-left mr-2"></i>
        Quay lại danh sách bài thi
      </a>
    </div>
  </div>
</div>

<!-- Loading Modal -->
<div
  id="loading-modal"
  class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center"
>
  <div class="bg-white rounded-lg p-6 max-w-sm w-full mx-4">
    <div class="text-center">
      <div
        class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-indigo-100 mb-4"
      >
        <i class="fas fa-spinner fa-spin text-2xl text-indigo-600"></i>
      </div>
      <h3 class="text-lg font-medium text-gray-900 mb-2">
        Đang tải tài liệu...
      </h3>
      <p class="text-sm text-gray-500">Vui lòng đợi trong giây lát</p>
    </div>
  </div>
</div>

<style>
  /* Search highlight styles */
  .search-highlight {
    background-color: #fef3c7 !important;
    padding: 0 2px !important;
    border-radius: 2px !important;
    font-weight: 500 !important;
  }

  .search-highlight.current {
    background-color: #fbbf24 !important;
    color: #1f2937 !important;
    box-shadow: 0 0 0 2px #f59e0b !important;
  }

  /* Custom scrollbar for document container */
  #document-container::-webkit-scrollbar {
    width: 8px;
  }

  #document-container::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
  }

  #document-container::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
  }

  #document-container::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
  }

  /* Scroll indicator animation */
  @keyframes bounce-scroll {
    0%,
    20%,
    53%,
    80%,
    100% {
      transform: translateY(0);
    }
    40%,
    43% {
      transform: translateY(-4px);
    }
    70% {
      transform: translateY(-2px);
    }
    90% {
      transform: translateY(-1px);
    }
  }

  .bounce-scroll {
    animation: bounce-scroll 2s infinite;
  }

  /* Mobile responsive adjustments */
  @media (max-width: 768px) {
    #document-container {
      height: 60vh !important;
      max-height: 60vh !important;
    }

    .search-highlight {
      padding: 1px !important;
    }

    /* Adjust search bar for mobile */
    .flex.items-center.space-x-3 {
      flex-wrap: wrap;
      gap: 0.5rem;
    }

    .flex-1.relative {
      min-width: 100%;
      margin-bottom: 0.5rem;
    }

    .flex.items-center.space-x-2 {
      justify-content: center;
      width: 100%;
    }

    /* Mobile table of contents */
    .sticky.top-0.z-10 {
      position: relative !important;
    }

    .max-h-48 {
      max-height: 8rem !important;
    }
  }

  @media (max-width: 480px) {
    #document-container {
      height: 55vh !important;
      max-height: 55vh !important;
      padding: 1rem !important;
    }

    .prose {
      font-size: 0.875rem !important;
    }
  }
</style>

<script>
  // Document search functionality
  class DocumentSearch {
    constructor() {
      this.searchInput = document.getElementById("document-search");
      this.searchResults = document.getElementById("search-results");
      this.searchPrev = document.getElementById("search-prev");
      this.searchNext = document.getElementById("search-next");
      this.searchClear = document.getElementById("search-clear");
      this.documentContent = document.getElementById("document-content");
      this.documentContainer = document.getElementById("document-container");

      this.matches = [];
      this.currentMatch = -1;
      this.originalContent = this.documentContent.innerHTML;

      this.init();
    }

    init() {
      // Search input event
      this.searchInput.addEventListener("input", (e) => {
        this.performSearch(e.target.value.trim());
      });

      // Navigation events
      this.searchPrev.addEventListener("click", () => this.navigateMatch(-1));
      this.searchNext.addEventListener("click", () => this.navigateMatch(1));
      this.searchClear.addEventListener("click", () => this.clearSearch());

      // Keyboard shortcuts
      this.searchInput.addEventListener("keydown", (e) => {
        if (e.key === "Enter") {
          e.preventDefault();
          if (e.shiftKey) {
            this.navigateMatch(-1);
          } else {
            this.navigateMatch(1);
          }
        } else if (e.key === "Escape") {
          this.clearSearch();
        }
      });
    }

    performSearch(query) {
      if (!query) {
        this.clearHighlights();
        return;
      }

      // Clear previous highlights
      this.clearHighlights();

      // Create regex for case-insensitive search
      const regex = new RegExp(`(${this.escapeRegex(query)})`, "gi");

      // Get text content and find matches
      this.findMatches(query);

      if (this.matches.length > 0) {
        // Highlight all matches
        this.highlightMatches(regex);

        // Navigate to first match
        this.currentMatch = 0;
        this.updateCurrentMatch();
        this.updateSearchResults();
        this.enableNavigation();
      } else {
        this.updateSearchResults();
        this.disableNavigation();
      }
    }

    findMatches(query) {
      this.matches = [];
      const walker = document.createTreeWalker(
        this.documentContent,
        NodeFilter.SHOW_TEXT,
        null,
        false
      );

      let textNode;
      while ((textNode = walker.nextNode())) {
        const text = textNode.textContent;
        const regex = new RegExp(this.escapeRegex(query), "gi");
        let match;

        while ((match = regex.exec(text)) !== null) {
          this.matches.push({
            node: textNode,
            index: match.index,
            length: match[0].length,
            text: match[0],
          });
        }
      }
    }

    highlightMatches(regex) {
      const walker = document.createTreeWalker(
        this.documentContent,
        NodeFilter.SHOW_TEXT,
        null,
        false
      );

      const textNodes = [];
      let textNode;
      while ((textNode = walker.nextNode())) {
        if (regex.test(textNode.textContent)) {
          textNodes.push(textNode);
        }
      }

      // Reset regex
      regex.lastIndex = 0;

      textNodes.forEach((node) => {
        const text = node.textContent;
        if (regex.test(text)) {
          const highlightedText = text.replace(
            regex,
            '<span class="search-highlight">$1</span>'
          );
          const wrapper = document.createElement("div");
          wrapper.innerHTML = highlightedText;

          const fragment = document.createDocumentFragment();
          while (wrapper.firstChild) {
            fragment.appendChild(wrapper.firstChild);
          }

          node.parentNode.replaceChild(fragment, node);
        }
      });
    }

    updateCurrentMatch() {
      // Remove current class from all highlights
      document.querySelectorAll(".search-highlight.current").forEach((el) => {
        el.classList.remove("current");
      });

      // Add current class to current match
      const highlights = document.querySelectorAll(".search-highlight");
      if (highlights[this.currentMatch]) {
        highlights[this.currentMatch].classList.add("current");

        // Scroll to current match
        const rect = highlights[this.currentMatch].getBoundingClientRect();
        const containerRect = this.documentContainer.getBoundingClientRect();

        if (
          rect.top < containerRect.top ||
          rect.bottom > containerRect.bottom
        ) {
          highlights[this.currentMatch].scrollIntoView({
            behavior: "smooth",
            block: "center",
            inline: "nearest",
          });
        }
      }
    }

    navigateMatch(direction) {
      if (this.matches.length === 0) return;

      this.currentMatch += direction;

      if (this.currentMatch >= this.matches.length) {
        this.currentMatch = 0;
      } else if (this.currentMatch < 0) {
        this.currentMatch = this.matches.length - 1;
      }

      this.updateCurrentMatch();
      this.updateSearchResults();
    }

    updateSearchResults() {
      if (this.matches.length === 0) {
        this.searchResults.textContent = this.searchInput.value.trim()
          ? "Không tìm thấy"
          : "";
      } else {
        this.searchResults.textContent = `${this.currentMatch + 1}/${
          this.matches.length
        }`;
      }
    }

    enableNavigation() {
      this.searchPrev.disabled = false;
      this.searchNext.disabled = false;
    }

    disableNavigation() {
      this.searchPrev.disabled = true;
      this.searchNext.disabled = true;
    }

    clearSearch() {
      this.searchInput.value = "";
      this.clearHighlights();
    }

    clearHighlights() {
      this.documentContent.innerHTML = this.originalContent;
      this.matches = [];
      this.currentMatch = -1;
      this.updateSearchResults();
      this.disableNavigation();
    }

    escapeRegex(string) {
      return string.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
    }
  }

  // Scroll indicator functionality
  class ScrollIndicator {
    constructor() {
      this.container = document.getElementById("document-container");
      this.indicator = document.getElementById("scroll-indicator");
      this.init();
    }

    init() {
      if (!this.container || !this.indicator) return;

      // Check if content needs scrolling
      this.checkScrollNeed();

      // Add scroll event listener
      this.container.addEventListener("scroll", () => {
        this.updateIndicator();
      });

      // Add resize listener to recheck scroll need
      window.addEventListener("resize", () => {
        this.checkScrollNeed();
      });
    }

    checkScrollNeed() {
      const hasScroll =
        this.container.scrollHeight > this.container.clientHeight;

      if (hasScroll) {
        this.showIndicator();
        setTimeout(() => this.hideIndicator(), 3000); // Auto hide after 3s
      } else {
        this.hideIndicator();
      }
    }

    updateIndicator() {
      const { scrollTop, scrollHeight, clientHeight } = this.container;
      const scrollPercent = scrollTop / (scrollHeight - clientHeight);

      if (scrollPercent < 0.95) {
        // Not at bottom
        this.showIndicator();
      } else {
        this.hideIndicator();
      }
    }

    showIndicator() {
      this.indicator.classList.remove("opacity-0");
      this.indicator.classList.add("opacity-100", "bounce-scroll");
    }

    hideIndicator() {
      this.indicator.classList.add("opacity-0");
      this.indicator.classList.remove("opacity-100", "bounce-scroll");
    }
  }

  // Initialize on DOM content loaded
  document.addEventListener("DOMContentLoaded", function () {
    // Initialize search functionality
    new DocumentSearch();

    // Initialize scroll indicator
    new ScrollIndicator();

    // Add smooth scrolling behavior
    document.documentElement.style.scrollBehavior = "smooth";
  });

  // Global keyboard shortcuts
  document.addEventListener("keydown", function (e) {
    // Focus search with Ctrl+F or Cmd+F (prevent default browser search)
    if ((e.ctrlKey || e.metaKey) && e.key === "f") {
      e.preventDefault();
      const searchInput = document.getElementById("document-search");
      if (searchInput) {
        searchInput.focus();
        searchInput.select();
      }
    }

    // Focus search with / key (like vim)
    if (e.key === "/" && !e.target.matches("input, textarea")) {
      e.preventDefault();
      const searchInput = document.getElementById("document-search");
      if (searchInput) {
        searchInput.focus();
      }
    }
  });

  // Enhanced table of contents for scrollable container
  function addTableOfContents() {
    const content = document.getElementById("document-content");
    const container = document.getElementById("document-container");
    const headings = content.querySelectorAll("h1, h2, h3");

    if (headings.length > 3) {
      // Create collapsible TOC
      const toc = document.createElement("div");
      toc.className =
        "bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6 sticky top-0 z-10";

      const tocHeader = document.createElement("div");
      tocHeader.className = "flex items-center justify-between cursor-pointer";
      tocHeader.innerHTML = `
        <h3 class="text-lg font-semibold text-blue-800 mb-0">
          <i class="fas fa-list mr-2"></i>Mục lục
        </h3>
        <button class="text-blue-600 hover:text-blue-800 p-1" id="toc-toggle">
          <i class="fas fa-chevron-up text-sm"></i>
        </button>
      `;

      const tocContent = document.createElement("div");
      tocContent.id = "toc-content";
      tocContent.className = "mt-3";

      const tocList = document.createElement("ul");
      tocList.className = "space-y-1 max-h-48 overflow-y-auto";

      headings.forEach((heading, index) => {
        const id = `heading-${index}`;
        heading.id = id;

        const li = document.createElement("li");
        const level = parseInt(heading.tagName.substring(1));
        li.className = `ml-${(level - 1) * 4}`;

        const link = document.createElement("a");
        link.className =
          "text-blue-600 hover:text-blue-800 text-sm block py-1 px-2 rounded hover:bg-blue-100 transition-colors";
        link.textContent = heading.textContent;

        // Custom scroll behavior for scrollable container
        link.addEventListener("click", (e) => {
          e.preventDefault();
          const targetElement = document.getElementById(id);
          if (targetElement && container) {
            const containerRect = container.getBoundingClientRect();
            const targetRect = targetElement.getBoundingClientRect();
            const scrollTop =
              container.scrollTop + targetRect.top - containerRect.top - 20;

            container.scrollTo({
              top: scrollTop,
              behavior: "smooth",
            });
          }
        });

        li.appendChild(link);
        tocList.appendChild(li);
      });

      tocContent.appendChild(tocList);
      toc.appendChild(tocHeader);
      toc.appendChild(tocContent);

      // Toggle functionality
      const toggleBtn = tocHeader.querySelector("#toc-toggle");
      const toggleIcon = toggleBtn.querySelector("i");
      let isCollapsed = false;

      tocHeader.addEventListener("click", () => {
        isCollapsed = !isCollapsed;
        if (isCollapsed) {
          tocContent.style.display = "none";
          toggleIcon.className = "fas fa-chevron-down text-sm";
        } else {
          tocContent.style.display = "block";
          toggleIcon.className = "fas fa-chevron-up text-sm";
        }
      });

      content.parentNode.insertBefore(toc, content);
    }
  }

  // Initialize everything when DOM loads
  document.addEventListener("DOMContentLoaded", function () {
    // Add table of contents if document is long
    const content = document.getElementById("document-content");
    if (content && content.scrollHeight > 1500) {
      setTimeout(() => addTableOfContents(), 100); // Small delay to ensure content is rendered
    }
  });
</script>

<% script = `
<script>
  console.log("Documents page loaded successfully");
</script>
` %>
