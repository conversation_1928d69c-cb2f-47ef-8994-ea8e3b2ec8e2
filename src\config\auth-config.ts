/**
 * Authentication Configuration
 * Centralized configuration for all authentication-related timeouts and settings
 */

// Time constants in milliseconds
const MINUTE = 60 * 1000;
const HOUR = 60 * MINUTE;
const DAY = 24 * HOUR;

/**
 * Authentication timeout configurations
 */
export const AuthConfig = {
  // Default login session duration (90 days)
  DEFAULT_LOGIN_DURATION: 90 * DAY,

  // JWT Token configurations
  JWT: {
    // JWT token expiration (90 days)
    EXPIRATION: 90 * DAY,

    // JWT refresh threshold (auto refresh when token has less than 1 hour remaining)
    REFRESH_THRESHOLD: 1 * HOUR,
  },

  // Session configurations
  SESSION: {
    // Session expiration in database (90 days - matching JWT)
    EXPIRATION: 90 * DAY,

    // Session cleanup interval (daily)
    CLEANUP_INTERVAL: 1 * DAY,
  },

  // Cookie configurations
  COOKIE: {
    // Cookie max age (90 days - matching JWT and session)
    MAX_AGE: 90 * DAY,

    // Cookie security options
    HTTP_ONLY: true,
    SECURE: process.env.NODE_ENV === "production",
    SAME_SITE: "lax" as const,
  },

  // Password reset configurations
  RESET_TOKEN: {
    // Reset token expiration (1 hour)
    EXPIRATION: 1 * HOUR,
  },

  // Other authentication timeouts
  OTHER: {
    // Rate limiting window
    RATE_LIMIT_WINDOW: 15 * MINUTE,

    // Account lockout duration (if implemented)
    ACCOUNT_LOCKOUT: 30 * MINUTE,
  },
} as const;

/**
 * Helper functions for authentication configuration
 */
export const AuthUtils = {
  /**
   * Get JWT expiration in seconds (for jsonwebtoken library)
   */
  getJwtExpirationInSeconds(): number {
    return Math.floor(AuthConfig.JWT.EXPIRATION / 1000);
  },

  /**
   * Get JWT expiration as string (for jsonwebtoken library)
   */
  getJwtExpirationString(): string {
    return `${AuthConfig.JWT.EXPIRATION}ms`;
  },

  /**
   * Check if token should be refreshed
   */
  shouldRefreshToken(expirationTime: number): boolean {
    const timeUntilExpiration = expirationTime - Date.now();
    return timeUntilExpiration < AuthConfig.JWT.REFRESH_THRESHOLD;
  },

  /**
   * Calculate expiration date from now
   */
  getExpirationDate(
    duration: number = AuthConfig.DEFAULT_LOGIN_DURATION
  ): Date {
    return new Date(Date.now() + duration);
  },

  /**
   * Get cookie options with configured settings
   */
  getCookieOptions() {
    return {
      httpOnly: AuthConfig.COOKIE.HTTP_ONLY,
      secure: AuthConfig.COOKIE.SECURE,
      sameSite: AuthConfig.COOKIE.SAME_SITE,
      maxAge: AuthConfig.COOKIE.MAX_AGE,
    };
  },
};

/**
 * Development/debugging helper
 */
export const AuthConfigInfo = {
  // Human-readable duration descriptions
  durations: {
    defaultLogin: `${AuthConfig.DEFAULT_LOGIN_DURATION / DAY} days`,
    jwtExpiration: `${AuthConfig.JWT.EXPIRATION / DAY} days`,
    sessionExpiration: `${AuthConfig.SESSION.EXPIRATION / DAY} days`,
    cookieMaxAge: `${AuthConfig.COOKIE.MAX_AGE / DAY} days`,
    resetTokenExpiration: `${AuthConfig.RESET_TOKEN.EXPIRATION / HOUR} hour(s)`,
  },

  // Configuration summary for logging
  summary: {
    loginDuration: AuthConfig.DEFAULT_LOGIN_DURATION,
    jwtExpiration: AuthConfig.JWT.EXPIRATION,
    sessionExpiration: AuthConfig.SESSION.EXPIRATION,
    cookieMaxAge: AuthConfig.COOKIE.MAX_AGE,
    allConfiguredFor90Days:
      AuthConfig.DEFAULT_LOGIN_DURATION === AuthConfig.JWT.EXPIRATION &&
      AuthConfig.JWT.EXPIRATION === AuthConfig.SESSION.EXPIRATION &&
      AuthConfig.SESSION.EXPIRATION === AuthConfig.COOKIE.MAX_AGE,
  },
};

export default AuthConfig;
