// status-checker.js
// B<PERSON><PERSON> toàn bộ code trong một hàm (IIFE) để tránh làm ảnh hưởng đến các biến toàn cục.
(function () {
  // 1. <PERSON><PERSON><PERSON> nghĩa toàn bộ CSS dưới dạng một chuỗi (string)
  const cssStyles = `
    #offline-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.75);
      z-index: 9999;
      display: flex;
      justify-content: center;
      align-items: center;
      transition: opacity 0.3s ease;
      opacity: 0;
      pointer-events: none; /* Cho phép click xuyên qua khi ẩn */
    }

    #offline-overlay.visible {
      opacity: 1;
      pointer-events: all; /* Chặn click khi hiện */
    }

    #offline-overlay p {
      color: white;
      font-size: 24px;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      text-align: center;
      padding: 20px;
    }
  `;

  // 2. Ch<PERSON>n CSS vào thẻ <head> của trang
  const styleElement = document.createElement("style");
  styleElement.textContent = cssStyles;
  document.head.appendChild(styleElement);

  // 3. Tạo sẵn element overlay và message một lần duy nhất
  const overlayElement = document.createElement("div");
  overlayElement.id = "offline-overlay";

  const messageElement = document.createElement("p");
  messageElement.textContent =
    "Đang ở trạng thái offline. Vui lòng kết nối mạng!";

  overlayElement.appendChild(messageElement);
  document.body.appendChild(overlayElement);

  // 4. Định nghĩa hàm để hiển thị và ẩn overlay bằng cách thêm/xóa class
  const showOfflineStatus = () => {
    overlayElement.classList.add("visible");
  };

  const hideOfflineStatus = () => {
    overlayElement.classList.remove("visible");
  };

  // 5. Lắng nghe sự kiện và kiểm tra trạng thái ban đầu
  window.addEventListener("offline", showOfflineStatus);
  window.addEventListener("online", hideOfflineStatus);

  if (!navigator.onLine) {
    showOfflineStatus();
  }
})(); // Dấu () cuối cùng để thực thi hàm ngay lập tức
