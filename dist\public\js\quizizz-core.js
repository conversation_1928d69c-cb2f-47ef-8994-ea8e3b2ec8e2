// Quizizz - File JavaScript core - <PERSON><PERSON><PERSON> hàm cơ bản
// Khởi tạo biến và khởi tạo
let currentQuestionIndex = 0;
let score = 0;
let userAnswers = [];
let examDuration = 30 * 60;
let remainingTime = examDuration;
let startTime = Date.now();
let timerInterval;
let nextQuestionTimeout;
let wrongQuestions = [];
let isSubmitting = false;
let isExitPopupVisible = false;
let transitionTime = 3000;
let soundEnabled = true;
let questionsProcessed = 0;
let incorrectQuestions = [];
let retryMode = false;
let questionToRetry = null;
let originalQuestionIndex = null;
let justRetried = false;
// examData will be accessed via window.examData to avoid conflicts
let isDarkTheme = false; // Biến kiểm soát giao diện tối
// *** EXPOSE LOCAL VARIABLES TO WINDOW OBJECT ***
function exposeVariablesToWindow() {
    window.currentQuestionIndex = currentQuestionIndex;
    window.score = score;
    window.userAnswers = userAnswers;
    window.examDuration = examDuration;
    window.remainingTime = remainingTime;
    window.startTime = startTime;
    window.timerInterval = timerInterval;
    window.questionsProcessed = questionsProcessed;
    window.incorrectQuestions = incorrectQuestions;
    window.retryMode = retryMode;
    window.questionToRetry = questionToRetry;
    window.originalQuestionIndex = originalQuestionIndex;
    window.justRetried = justRetried;
}
// *** SYNC WINDOW VARIABLES TO LOCAL ***
function syncWindowToLocal() {
    if (typeof window.currentQuestionIndex !== "undefined") {
        currentQuestionIndex = window.currentQuestionIndex;
    }
    if (typeof window.score !== "undefined") {
        score = window.score;
    }
    if (typeof window.userAnswers !== "undefined") {
        userAnswers = window.userAnswers;
    }
    if (typeof window.examDuration !== "undefined") {
        examDuration = window.examDuration;
    }
    if (typeof window.remainingTime !== "undefined") {
        remainingTime = window.remainingTime;
    }
    if (typeof window.startTime !== "undefined") {
        startTime = window.startTime;
    }
    if (typeof window.questionsProcessed !== "undefined") {
        questionsProcessed = window.questionsProcessed;
    }
    if (typeof window.incorrectQuestions !== "undefined") {
        incorrectQuestions = window.incorrectQuestions;
    }
}
// Expose functions to window
window.exposeVariablesToWindow = exposeVariablesToWindow;
window.syncWindowToLocal = syncWindowToLocal;
// Khởi tạo ứng dụng khi DOM đã sẵn sàng
// Chỉ add event listener nếu DOM chưa ready
if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", initQuizizz);
}
else {
    // DOM đã ready, có thể gọi initQuizizz() trực tiếp từ bên ngoài
}
// Hàm khởi tạo ứng dụng
function initQuizizz() {
    // Lấy dữ liệu từ JSON script hoặc sử dụng window.examData đã có
    if (!window.examData) {
        const examDataElement = document.getElementById("exam-data");
        if (examDataElement) {
            window.examData = JSON.parse(examDataElement.textContent);
            console.log("📋 Loaded examData từ script element");
        }
    }
    else {
    }
    // *** SỬ DỤNG GLOBAL VARIABLES KHI RESTORE ***
    // Nếu đang restore từ localStorage, sử dụng global variables thay vì local
    if (window.isRestoringFromLocalStorage) {
        syncWindowToLocal();
    }
    // Lấy thời gian làm bài (chỉ khi không restore)
    if (!window.isRestoringFromLocalStorage) {
        const durationElement = document.getElementById("exam-duration");
        if (durationElement) {
            examDuration = parseInt(durationElement.textContent) * 60;
            remainingTime = examDuration;
        }
    }
    // Thiết lập điều khiển
    setupTransitionTimeControl();
    setupSoundToggle();
    setupThemeToggle(); // Thêm setup cho chuyển đổi giao diện
    setupPageLeaveHandlers();
    setupSecurityMeasures(); // Thêm biện pháp bảo mật
    // *** KHỞI TẠO WINDOW VARIABLES ***
    // Đảm bảo window variables được khởi tạo đúng
    if (!window.isRestoringFromLocalStorage) {
        console.log("🔧 Khởi tạo window variables cho bài thi mới");
        window.currentQuestionIndex = currentQuestionIndex;
        window.score = score;
        window.userAnswers = userAnswers;
        window.startTime = startTime;
        window.remainingTime = remainingTime;
        window.examDuration = examDuration;
        window.questionsProcessed = questionsProcessed;
        // Expose all variables to ensure consistency
        exposeVariablesToWindow();
    }
    else {
        // For restored sessions, expose variables after sync
        exposeVariablesToWindow();
    }
    // Chỉ hiển thị câu hỏi đầu tiên nếu KHÔNG phải đang restore từ localStorage
    if (!window.isRestoringFromLocalStorage) {
        showQuestion(currentQuestionIndex);
    }
    else {
    }
    // Khởi động bộ đếm thời gian chỉ khi không restore từ localStorage
    if (!window.isRestoringFromLocalStorage) {
        timerInterval = setInterval(updateTimer, 1000);
        updateTimer(); // Cập nhật lần đầu ngay lập tức
    }
    else {
    }
    // Thiết lập sự kiện nút nộp bài
    setupSubmitButton();
    // Thiết lập sự kiện làm lại câu sai
    setupRetryWrongButton();
    // Khởi tạo âm thanh
    setupAudio();
    // Thiết lập localStorage periodic save cho Quizizz (chỉ khi không restore)
    if (!window.isRestoringFromLocalStorage) {
        setupQuizizzPeriodicSave();
    }
    else {
        // Khi restore, vẫn cần setup periodic save để tiếp tục save
        setTimeout(() => {
            setupQuizizzPeriodicSave();
        }, 1000); // Delay để đảm bảo mọi thứ đã ready
    }
    console.log("Khởi tạo Quizizz thành công");
}
// Thiết lập điều khiển thời gian chuyển câu
function setupTransitionTimeControl() {
    const transitionSelect = document.getElementById("transitionTime");
    if (transitionSelect) {
        // Lấy giá trị từ localStorage (mã hóa)
        let savedTransitionTime = null;
        if (typeof SecureStorage !== "undefined") {
            savedTransitionTime = SecureStorage.getItem("quizizzTransitionTime");
        }
        else {
            savedTransitionTime = localStorage.getItem("quizizzTransitionTime");
        }
        if (savedTransitionTime) {
            transitionSelect.value = savedTransitionTime;
            transitionTime = parseInt(savedTransitionTime, 10);
        }
        // Xử lý sự kiện thay đổi
        transitionSelect.addEventListener("change", function () {
            transitionTime = parseInt(this.value, 10);
            if (typeof SecureStorage !== "undefined") {
                SecureStorage.setItem("quizizzTransitionTime", transitionTime);
            }
            else {
                localStorage.setItem("quizizzTransitionTime", transitionTime);
            }
        });
    }
}
// Thiết lập điều khiển âm thanh
function setupSoundToggle() {
    const soundToggleBtn = document.getElementById("soundToggleBtn");
    const soundIcon = document.getElementById("soundIcon");
    if (soundToggleBtn && soundIcon) {
        // Đặt giá trị từ localStorage (mã hóa)
        let savedSoundSetting = null;
        if (typeof SecureStorage !== "undefined") {
            savedSoundSetting = SecureStorage.getItem("quizizzSoundEnabled");
        }
        else {
            savedSoundSetting = localStorage.getItem("quizizzSoundEnabled");
        }
        if (savedSoundSetting !== null) {
            soundEnabled = savedSoundSetting === "true" || savedSoundSetting === true;
            updateSoundIcon();
        }
        // Xử lý sự kiện thay đổi
        soundToggleBtn.addEventListener("click", function () {
            soundEnabled = !soundEnabled;
            if (typeof SecureStorage !== "undefined") {
                SecureStorage.setItem("quizizzSoundEnabled", soundEnabled);
            }
            else {
                localStorage.setItem("quizizzSoundEnabled", soundEnabled);
            }
            updateSoundIcon();
            window.soundEnabled = soundEnabled;
        });
    }
    // Cập nhật icon âm thanh
    function updateSoundIcon() {
        if (soundEnabled) {
            soundIcon.className = "fas fa-volume-up text-lg";
            soundToggleBtn.setAttribute("title", "Tắt âm thanh");
        }
        else {
            soundIcon.className = "fas fa-volume-mute text-lg text-gray-400";
            soundToggleBtn.setAttribute("title", "Bật âm thanh");
        }
    }
}
// Thiết lập điều khiển chuyển đổi giao diện
function setupThemeToggle() {
    const themeToggleBtn = document.getElementById("themeToggleBtn");
    const themeIcon = document.getElementById("themeIcon");
    const quizizzContainer = document.getElementById("quizizzContainer");
    if (themeToggleBtn && themeIcon && quizizzContainer) {
        // Lấy trạng thái giao diện từ localStorage (mã hóa)
        let savedTheme = null;
        if (typeof SecureStorage !== "undefined") {
            savedTheme = SecureStorage.getItem("quizizzTheme");
        }
        else {
            savedTheme = localStorage.getItem("quizizzTheme");
        }
        if (savedTheme !== null) {
            isDarkTheme = savedTheme === "dark";
            updateTheme();
        }
        // Xử lý sự kiện khi nhấn nút đổi giao diện
        themeToggleBtn.addEventListener("click", function () {
            isDarkTheme = !isDarkTheme;
            updateTheme();
            if (typeof SecureStorage !== "undefined") {
                SecureStorage.setItem("quizizzTheme", isDarkTheme ? "dark" : "light");
            }
            else {
                localStorage.setItem("quizizzTheme", isDarkTheme ? "dark" : "light");
            }
        });
    }
    // Cập nhật giao diện theo trạng thái hiện tại
    function updateTheme() {
        if (isDarkTheme) {
            quizizzContainer.classList.add("theme-dark");
            document.body.classList.add("theme-dark");
            themeIcon.className = "fas fa-sun text-lg"; // Icon mặt trời khi ở chế độ tối
            themeToggleBtn.setAttribute("title", "Chuyển sang giao diện sáng");
            // Chỉ refresh câu hỏi nếu đang ở trang làm bài, không phải trang kết quả
            if (typeof showQuestion === "function" &&
                typeof currentQuestionIndex !== "undefined" &&
                !document.querySelector(".result-display")) {
                showQuestion(currentQuestionIndex);
            }
        }
        else {
            quizizzContainer.classList.remove("theme-dark");
            document.body.classList.remove("theme-dark");
            themeIcon.className = "fas fa-moon text-lg"; // Icon mặt trăng khi ở chế độ sáng
            themeToggleBtn.setAttribute("title", "Chuyển sang giao diện tối");
            // Chỉ refresh câu hỏi nếu đang ở trang làm bài, không phải trang kết quả
            if (typeof showQuestion === "function" &&
                typeof currentQuestionIndex !== "undefined" &&
                !document.querySelector(".result-display")) {
                showQuestion(currentQuestionIndex);
            }
        }
    }
}
// Cập nhật bộ đếm thời gian
function updateTimer() {
    remainingTime--;
    // Sync with window object
    window.remainingTime = remainingTime;
    if (remainingTime <= 0) {
        // Hết thời gian, tự động nộp bài
        clearInterval(timerInterval);
        // Hiển thị popup thông báo hết giờ
        showExitConfirmPopup("Đã hết thời gian làm bài!", "Thời gian làm bài đã kết thúc. Bài thi của bạn sẽ được nộp tự động.", function () {
            isSubmitting = true;
            showResult();
        });
        // Tự động nhấn nút nộp bài sau 5 giây
        setTimeout(function () {
            if (isExitPopupVisible) {
                document.getElementById("exitConfirmLeaveBtn").click();
            }
        }, 5000);
        return;
    }
    const hours = Math.floor(remainingTime / 3600);
    const minutes = Math.floor((remainingTime % 3600) / 60);
    const seconds = remainingTime % 60;
    const timerDisplay = document.getElementById("timerDisplay");
    timerDisplay.textContent = `${hours.toString().padStart(2, "0")}:${minutes
        .toString()
        .padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
    // *** THÊM ANIMATION WARNINGS CHO TIMER ***
    const timerElement = timerDisplay.parentElement;
    // Thay đổi màu và animation khi gần hết thời gian
    if (remainingTime <= 300) {
        // 5 phút cuối - warning state
        timerElement.classList.add("bg-red-600", "quiz-timer-warning");
        timerElement.classList.remove("bg-purple-800", "quiz-timer-critical");
        // Critical state khi còn dưới 1 phút
        if (remainingTime <= 60) {
            timerElement.classList.remove("quiz-timer-warning");
            timerElement.classList.add("quiz-timer-critical", "bg-red-800");
            // Thêm shake effect mỗi 10 giây
            // if (remainingTime % 10 === 0) {
            //   timerElement.classList.add("quiz-shake");
            //   setTimeout(() => {
            //     timerElement.classList.remove("quiz-shake");
            //   }, 600);
            // }
        }
        else {
            // Reset about normal state
            timerElement.classList.remove("bg-red-600", "bg-red-800", "quiz-timer-warning", "quiz-timer-critical");
            timerElement.classList.add("bg-purple-800");
        }
    }
}
// Thiết lập nút nộp bài
function setupSubmitButton() {
    const submitButton = document.getElementById("submitExamButton");
    if (submitButton) {
        submitButton.addEventListener("click", function () {
            // Hiển thị popup xác nhận nộp bài
            showExitConfirmPopup("Xác nhận nộp bài", "Bạn có chắc chắn muốn nộp bài? Thời gian làm bài còn lại sẽ bị hủy.", function () {
                isSubmitting = true;
                showResult();
            });
        });
    }
}
// Thiết lập nút làm lại câu sai
function setupRetryWrongButton() {
    document.addEventListener("click", function (e) {
        if (e.target && e.target.id === "retryWrongAnswers") {
            handleRetryWrongQuestions();
        }
    });
}
// Xử lý làm lại câu sai
function handleRetryWrongQuestions() {
    // Kiểm tra xem có câu hỏi sai nào không
    if (wrongQuestions.length === 0) {
        alert("Bạn đã làm đúng tất cả các câu hỏi!");
        return;
    }
    // Lưu thông tin câu hỏi sai vào localStorage
    const wrongQuestionsData = {
        examId: window.examData.examId,
        examType: "quizizz",
        questions: wrongQuestions,
    };
    // Lưu vào localStorage thường (wrongQuestions không cần mã hóa cao)
    localStorage.setItem("wrongQuestions", JSON.stringify(wrongQuestionsData));
    console.log("💾 Đã lưu wrongQuestions vào localStorage:", wrongQuestionsData);
    // Chuyển hướng đến trang làm lại
    window.location.href = `/exam/retry-wrong-questions?examId=${window.examData.examId}&examType=quizizz`;
}
// Hiển thị popup xác nhận rời khỏi
function showExitConfirmPopup(title, message, onLeave) {
    const popup = document.getElementById("exitConfirmPopup");
    const titleElement = document.getElementById("exitConfirmTitle");
    const messageElement = document.getElementById("exitConfirmMessage");
    const stayButton = document.getElementById("exitConfirmStayBtn");
    const leaveButton = document.getElementById("exitConfirmLeaveBtn");
    // Cập nhật nội dung
    titleElement.textContent = title || "Bạn có chắc chắn muốn rời khỏi?";
    messageElement.textContent =
        message ||
            "Rời khỏi trang này sẽ kết thúc bài thi của bạn và không thể hoàn tác.";
    // Hiển thị popup
    popup.classList.remove("hidden");
    isExitPopupVisible = true;
    // Xử lý sự kiện nút
    stayButton.onclick = function () {
        popup.classList.add("hidden");
        isExitPopupVisible = false;
        history.pushState(null, null, window.location.pathname);
    };
    leaveButton.onclick = function () {
        isSubmitting = true; // Đánh dấu là đang nộp bài
        popup.classList.add("hidden");
        isExitPopupVisible = false;
        // Luôn hiển thị kết quả bài thi khi người dùng chọn kết thúc
        if (document.querySelector(".result-display") === null) {
            showResult();
        }
        if (typeof onLeave === "function") {
            onLeave();
        }
    };
}
// Biến để kiểm soát việc gửi request lưu lịch sử
let isSavingHistory = false;
// Lưu kết quả bài thi vào lịch sử
function saveExamHistory(score, totalQuestions, duration) {
    // Kiểm tra nếu người dùng đã đăng nhập
    if (!document.querySelector("#profile-button")) {
        console.log("Người dùng chưa đăng nhập, không lưu lịch sử");
        return;
    }
    // Kiểm tra nếu đang trong quá trình lưu
    if (isSavingHistory) {
        return;
    }
    // Đánh dấu đang lưu
    isSavingHistory = true;
    // Chuẩn bị dữ liệu để gửi API
    const historyData = {
        examId: window.examData.examId,
        examName: document.querySelector("h1").textContent.replace("Bài thi: ", ""),
        score: score,
        totalQuestions: totalQuestions,
        duration: duration, // Thời gian làm bài (giây)
        examType: "quizizz",
    };
    if (!window.location.pathname.includes("practice/quizizz-start")) {
        // Gửi request API để lưu lịch sử
        fetch("/exam/save-history", {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(historyData),
        })
            .then((response) => response.json())
            .then((data) => {
            if (data.success) {
                console.log(`✅ Đã lưu lịch sử bài thi thành công}`);
            }
            else {
                console.error(`❌ Lỗi khi lưu lịch sử bài thi: ${data.message}`);
            }
        })
            .catch((error) => {
            console.error("❌ Lỗi khi gửi request lưu lịch sử:", error);
        })
            .finally(() => {
            // Đánh dấu đã hoàn thành quá trình lưu
            isSavingHistory = false;
        });
    }
}
// Thiết lập xử lý rời khỏi trang
function setupPageLeaveHandlers() {
    // Xử lý sự kiện khi người dùng sử dụng nút Back/Forward của trình duyệt
    window.addEventListener("popstate", function (event) {
        if (!isSubmitting && !isExitPopupVisible) {
            // Ngăn chặn hành vi mặc định của trình duyệt
            event.preventDefault();
            // Hiển thị popup xác nhận
            showExitConfirmPopup("Bạn đang cố gắng rời khỏi trang", "Sử dụng nút Quay lại của trình duyệt sẽ kết thúc bài thi của bạn. Bạn có muốn tiếp tục?");
            // Thêm một mục vào lịch sử để ngăn chặn việc quay lại
            history.pushState(null, null, window.location.pathname);
        }
    });
    // Xử lý sự kiện khi người dùng nhấn các phím tắt để tải lại trang
    document.addEventListener("keydown", function (event) {
        if (!isSubmitting &&
            !isExitPopupVisible &&
            ((event.ctrlKey && event.key === "r") ||
                event.key === "F5" ||
                (event.ctrlKey && event.key === "F5"))) {
            // Ngăn chặn hành vi mặc định
            event.preventDefault();
            // Hiển thị popup xác nhận
            showExitConfirmPopup("Bạn đang cố gắng tải lại trang", "Tải lại trang sẽ kết thúc bài thi của bạn. Bạn có muốn tiếp tục?");
            return false;
        }
    });
    // Ngăn chặn menu chuột phải
    document.addEventListener("contextmenu", function (event) {
        event.preventDefault();
        return false;
    });
    // Thêm một mục vào lịch sử ngay khi trang tải để ngăn chặn nút Back
    history.pushState(null, null, window.location.pathname);
    // Sự kiện khi người dùng tải lại trang hoặc đóng tab
    window.addEventListener("beforeunload", function (event) {
        if (!isSubmitting) {
            event.preventDefault();
            event.returnValue =
                "Bạn có chắc chắn muốn rời khỏi trang? Việc này sẽ kết thúc bài thi của bạn.";
            return event.returnValue;
        }
    });
}
// Thiết lập các biện pháp bảo mật
function setupSecurityMeasures() {
    // Gọi hàm bảo mật từ file security-measures.js
    // Hàm này sẽ tự động phát hiện ngữ cảnh và thiết lập bảo mật phù hợp
    if (typeof GlobalSecurityMeasures === "function") {
        GlobalSecurityMeasures();
        // console.log("Đã thiết lập biện pháp bảo mật từ file security-measures.js");
    }
    else {
        // console.log(
        //   "File security-measures.js chưa được tải hoặc hàm GlobalSecurityMeasures không tồn tại"
        // );
    }
}
// Thiết lập periodic save cho Quizizz localStorage
function setupQuizizzPeriodicSave() {
    // Periodic save mỗi 30 giây
    let periodicSaveInterval = setInterval(function () {
        if (!isSubmitting && typeof saveQuizizzDataToStorage === "function") {
            saveQuizizzDataToStorage();
        }
    }, 30000); // 30 seconds
    // Save ngay lập tức khi bắt đầu bài thi mới
    setTimeout(function () {
        if (typeof saveQuizizzDataToStorage === "function") {
            saveQuizizzDataToStorage();
        }
    }, 2000); // Save sau 2 giây để đảm bảo quiz đã init
    // Clear interval khi nộp bài
    const originalShowResult = window.showResult;
    window.showResult = function () {
        clearInterval(periodicSaveInterval);
        // Clear localStorage khi nộp bài
        if (typeof clearQuizizzDataFromStorage === "function") {
            clearQuizizzDataFromStorage();
        }
        if (originalShowResult) {
            originalShowResult.apply(this, arguments);
        }
    };
}
//# sourceMappingURL=quizizz-core.js.map