import express, { Request, Response } from "express";
import asyncHandler from "../util/asynHandler";

const router = express.Router();

// Route để xử lý tương thích cho URL cũ làm lại câu hỏi sai
router.get(
  "/retry-wrong-questions",
  asyncHandler(async (req: Request, res: Response) => {
    // Lấy tham số từ query string
    const { examId, examType } = req.query;

    // Kiểm tra tham số bắt buộc
    if (!examId || !examType) {
      return res.status(400).json({
        success: false,
        message: "<PERSON>hiế<PERSON> thông tin cần thiết: examId và examType là bắt buộc",
      });
    }

    // Chuyển hướng đến URL chính xác với cùng các tham số
    return res.redirect(
      `/exam/retry-wrong-questions?examId=${examId}&examType=${examType}`
    );
  })
);

// Trang đăng ký
router.get("/register", (req: Request, res: Response) => {
  res.render("register", {
    title: "Đăng ký tài khoản",
    layout: "layouts/layout",
    user: res.locals.user || null,
    error: req.query.error || null,
  });
});

// Trang quên mật khẩu
router.get("/forgot-password", (req: Request, res: Response) => {
  res.render("forgot-password", {
    title: "Quên mật khẩu",
    layout: "layouts/layout",
    user: res.locals.user || null,
    error: req.query.error || null,
  });
});

// Trang đặt lại mật khẩu
router.get("/reset-password", (req: Request, res: Response) => {
  const token = req.query.token as string;

  if (!token) {
    return res.redirect("/forgot-password?error=Token không hợp lệ");
  }

  res.render("reset-password", {
    title: "Đặt lại mật khẩu",
    layout: "layouts/layout",
    user: res.locals.user || null,
    token,
    error: req.query.error || null,
  });
});

export default router;
