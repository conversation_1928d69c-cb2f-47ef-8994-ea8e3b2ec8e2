"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.userCheckMiddleware = void 0;
const express_1 = __importDefault(require("express"));
const mongoose_1 = __importDefault(require("mongoose"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const student_service_1 = require("../services/student.service");
const modelService_1 = require("../services/modelService");
const course_controller_1 = require("../controllers/course.controller");
const student_middleware_1 = require("../middlewares/student.middleware");
const asynHandler_1 = __importDefault(require("../util/asynHandler"));
const userProgress_service_1 = __importDefault(require("../services/userProgress.service"));
const Announcement_1 = __importDefault(require("../models/Announcement"));
const router = express_1.default.Router();
// Middleware để kiểm tra user và thêm vào res.locals
const userCheckMiddleware = (req, res, next) => {
    // Lấy token từ cookie
    const token = req.cookies.jwt;
    if (!token) {
        res.locals.user = null;
        return next();
    }
    // Xác thực JWT và thiết lập user
    try {
        const { id } = jsonwebtoken_1.default.verify(token, process.env.JWT_SECRET);
        mongoose_1.default
            .model("User")
            .findById(id)
            .select("-password -createdAt -updatedAt -__v")
            .then((user) => {
            // Kiểm tra token với activeToken
            if (user && user.get("activeToken") === token) {
                res.locals.user = user;
            }
            else {
                res.locals.user = null;
            }
            next();
        })
            .catch(() => {
            res.locals.user = null;
            next();
        });
    }
    catch (error) {
        res.locals.user = null;
        next();
    }
};
exports.userCheckMiddleware = userCheckMiddleware;
// Handler cho trang chủ
const homeHandler = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    // Nếu user đã đăng nhập, chuyển hướng đến /home
    if (res.locals.user) {
        return res.redirect("/home");
    }
    const announcement = yield Announcement_1.default.findOne({
        location: "homepage_guest",
        isActive: true,
    }).sort({ priority: -1, createdAt: -1 }); // Sắp xếp theo mức độ ưu tiên và ngày tạo
    res.render("index", {
        user: res.locals.user,
        announcement: announcement || null,
    });
});
// Handler cho trang đăng nhập
const loginHandler = (req, res) => {
    // Nếu user đã đăng nhập, chuyển hướng đến /home
    if (res.locals.user) {
        return res.redirect("/home");
    }
    res.render("login", {
        user: res.locals.user,
        error: req.query.error || null,
    });
};
// Handler cho trang home
const homePageHandler = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    if (!res.locals.user) {
        return res.redirect("/login");
    }
    try {
        // Tìm sinh viên theo email của người dùng đã đăng nhập
        const student = yield (0, student_service_1.getStudentByEmail)(res.locals.user.email);
        let courses = [];
        if (student) {
            // Lấy danh sách khóa học của sinh viên và chuyển đổi thành plain objects
            let rawCourses = yield (0, student_service_1.getStudentCourses)(student._id.toString());
            courses = rawCourses.map((course) => course.toObject ? course.toObject() : JSON.parse(JSON.stringify(course)));
            if (courses && courses.length > 0) {
                // Lấy danh sách ID khóa học
                const productIds = courses.map((course) => course._id.toString());
                try {
                    // Lấy thông tin tiến độ cho các khóa học
                    const userProgressList = yield (0, userProgress_service_1.default)(res.locals.user._id.toString(), productIds);
                    // Kết hợp thông tin tiến độ vào danh sách khóa học
                    courses = courses.map((course) => {
                        const progress = userProgressList.find((p) => p.product_id.toString() === course._id.toString());
                        if (progress) {
                            return Object.assign(Object.assign({}, course), { progress_percentage: progress.progress_percentage, total_correct_answers: progress.total_correct_answers, total_questions: progress.total_questions, completed_exams: progress.completed_exams });
                        }
                        return Object.assign(Object.assign({}, course), { progress_percentage: 0, total_correct_answers: 0, total_questions: course.countQuestion || 0, completed_exams: 0 });
                    });
                }
                catch (serviceError) {
                    courses = [];
                    return res.render("home", {
                        user: res.locals.user,
                        student,
                        courses: [],
                        error: "Có lỗi xảy ra khi lấy thông tin khóa học",
                    });
                }
            }
        }
        const announcement = yield Announcement_1.default.findOne({
            location: `homepage_authenticated`,
            isActive: true,
        }).sort({ priority: -1 }); // Lấy thông báo có priority cao nhất
        // Đảm bảo dữ liệu trả về là plain objects không có các thuộc tính Mongoose
        const cleanCourses = JSON.parse(JSON.stringify(courses));
        res.render("home", {
            user: res.locals.user,
            student,
            courses: cleanCourses,
            announcement: announcement || null,
        });
    }
    catch (error) {
        console.error("Lỗi khi lấy thông tin khóa học:", error);
        res.render("home", {
            user: res.locals.user,
            student: null,
            courses: [],
            error: "Có lỗi xảy ra khi lấy thông tin khóa học",
        });
    }
});
// Handler cho trang profile
const profileHandler = (req, res) => {
    if (!res.locals.user) {
        return res.redirect("/login");
    }
    res.render("profile", { user: res.locals.user });
};
// Handler cho trang danh sách khóa học
const coursesHandler = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // Lấy danh sách khóa học từ API
        const products = yield modelService_1.Product.find({ status: "active" }).lean();
        res.render("courses/index", {
            user: res.locals.user,
            products,
        });
    }
    catch (error) {
        console.error("Lỗi khi lấy danh sách khóa học:", error);
        res.render("error", {
            user: res.locals.user,
            message: "Có lỗi xảy ra khi tải danh sách khóa học",
        });
    }
});
// Handler cho trang luyện tập ghi nhớ
const memoryPracticeHandler = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    if (!res.locals.user) {
        return res.redirect("/login");
    }
    try {
        const { productId, practiceId } = req.params;
        const { count = 10, time = 15, shuffleQuestions, shuffleAnswers, } = req.query;
        // Lấy thông tin khóa học
        const product = yield modelService_1.Product.findById(productId);
        if (!product) {
            return res.status(404).render("error", {
                message: "Không tìm thấy thông tin môn học",
                error: { status: 404 },
                user: res.locals.user,
            });
        }
        // Lấy danh sách câu hỏi ghi nhớ (sẽ được truyền qua API riêng)
        // Render trang memory-practice
        res.render("exam/memory-practice", {
            user: res.locals.user,
            product,
            practiceId,
            settings: {
                count,
                time,
                shuffleQuestions: shuffleQuestions === "true",
                shuffleAnswers: shuffleAnswers === "true",
            },
        });
    }
    catch (error) {
        console.error("Lỗi khi tải trang luyện tập ghi nhớ:", error);
        res.status(500).render("error", {
            message: "Có lỗi xảy ra khi tải trang luyện tập",
            error: { status: 500 },
            user: res.locals.user,
        });
    }
});
// Đăng ký các routes
router.get("/", homeHandler);
router.get("/login", loginHandler);
router.get("/home", homePageHandler);
router.get("/profile", profileHandler);
router.get("/courses", coursesHandler);
router.get("/course/:productId/exams", student_middleware_1.checkStudentCourseAccess, (0, asynHandler_1.default)(course_controller_1.getCourseExams));
router.get("/course/:productId/:practiceId/memory-practice", student_middleware_1.checkStudentCourseAccess, memoryPracticeHandler);
exports.default = router;
//# sourceMappingURL=pageRoutes.js.map