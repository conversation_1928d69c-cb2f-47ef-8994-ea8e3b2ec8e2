import dotenv from "dotenv";
import redisActiveDeviceService from "../services/redisService";

dotenv.config();

/**
 * Test script để kiểm tra Redis integration cho activeDevices
 * <PERSON><PERSON>m tra cả Redis operations và fallback mechanisms
 */

async function testRedisIntegration() {
  console.log("🔧 Starting Redis Integration Test...\n");

  try {
    // 1. Health check
    console.log("1. Testing Redis Health Check:");
    const health = await redisActiveDeviceService.healthCheck();
    console.log(
      `   Redis status: ${health.redis ? "✅ Connected" : "❌ Disconnected"}`
    );
    console.log(
      `   Fallback status: ${
        health.fallback ? "✅ Available" : "❌ Not Available"
      }`
    );

    // 2. Connection status
    console.log("\n2. Connection Status:");
    const status = redisActiveDeviceService.getConnectionStatus();
    console.log(`   Is connected: ${status.isConnected}`);
    console.log(`   Fallback cache size: ${status.fallbackSize}`);

    // 3. Test set/get operations
    console.log("\n3. Testing Set/Get Operations:");
    const testUserId = "test-user-123";
    const testClientId = "client-456";

    await redisActiveDeviceService.setActiveDevice(testUserId, testClientId);
    console.log(
      `   ✅ Set active device for user ${testUserId}: ${testClientId}`
    );

    const retrievedClientId = await redisActiveDeviceService.getActiveDevice(
      testUserId
    );
    console.log(`   Retrieved client ID: ${retrievedClientId}`);

    if (retrievedClientId === testClientId) {
      console.log("   ✅ Set/Get test PASSED");
    } else {
      console.log("   ❌ Set/Get test FAILED");
    }

    // 4. Test hasActiveDevice
    console.log("\n4. Testing hasActiveDevice:");
    const hasDevice = await redisActiveDeviceService.hasActiveDevice(
      testUserId
    );
    console.log(
      `   User ${testUserId} has active device: ${
        hasDevice ? "✅ Yes" : "❌ No"
      }`
    );

    // 5. Test isActiveDevice
    console.log("\n5. Testing isActiveDevice:");
    const isActiveDevice = await redisActiveDeviceService.isActiveDevice(
      testUserId,
      testClientId
    );
    console.log(
      `   Client ${testClientId} is active for user ${testUserId}: ${
        isActiveDevice ? "✅ Yes" : "❌ No"
      }`
    );

    const isNotActiveDevice = await redisActiveDeviceService.isActiveDevice(
      testUserId,
      "wrong-client"
    );
    console.log(
      `   Wrong client is active for user ${testUserId}: ${
        isNotActiveDevice ? "❌ Yes (ERROR)" : "✅ No (CORRECT)"
      }`
    );

    // 6. Test hasActiveDeviceButNotThis
    console.log("\n6. Testing hasActiveDeviceButNotThis:");
    const hasButNotThis =
      await redisActiveDeviceService.hasActiveDeviceButNotThis(
        testUserId,
        "other-client"
      );
    console.log(
      `   User has device but not 'other-client': ${
        hasButNotThis ? "✅ Yes" : "❌ No"
      }`
    );

    // 7. Test refresh TTL
    console.log("\n7. Testing TTL refresh:");
    await redisActiveDeviceService.refreshActiveDeviceTTL(testUserId);
    console.log("   ✅ TTL refreshed");

    // 8. Test getAllActiveDevices
    console.log("\n8. Testing getAllActiveDevices:");
    const allDevices = await redisActiveDeviceService.getAllActiveDevices();
    console.log(`   Found ${Object.keys(allDevices).length} active devices:`);
    Object.entries(allDevices).forEach(([userId, clientId]) => {
      console.log(`     User ${userId}: ${clientId}`);
    });

    // 9. Test remove device
    console.log("\n9. Testing removeActiveDevice:");
    await redisActiveDeviceService.removeActiveDevice(testUserId);
    console.log(`   ✅ Removed active device for user ${testUserId}`);

    const removedDevice = await redisActiveDeviceService.getActiveDevice(
      testUserId
    );
    console.log(`   Device after removal: ${removedDevice || "null"}`);

    if (!removedDevice) {
      console.log("   ✅ Remove test PASSED");
    } else {
      console.log("   ❌ Remove test FAILED");
    }

    // 10. Test restoreFromDatabase simulation
    console.log("\n10. Testing restoreFromDatabase:");
    const mockUsers = [
      { _id: "user1", lastActiveDevice: "client-a" },
      { _id: "user2", lastActiveDevice: "client-b" },
      { _id: "user3", lastActiveDevice: "client-c" },
    ];

    const restoredCount = await redisActiveDeviceService.restoreFromDatabase(
      mockUsers
    );
    console.log(`   ✅ Restored ${restoredCount} devices from mock database`);

    const allAfterRestore =
      await redisActiveDeviceService.getAllActiveDevices();
    console.log(
      `   Total devices after restore: ${Object.keys(allAfterRestore).length}`
    );

    console.log("\n🎉 Redis Integration Test Completed Successfully!");

    // Cleanup test data
    console.log("\n🧹 Cleaning up test data...");
    for (const userId of Object.keys(allAfterRestore)) {
      if (userId.startsWith("user") || userId.startsWith("test-")) {
        await redisActiveDeviceService.removeActiveDevice(userId);
      }
    }

    console.log("✅ Cleanup completed");
  } catch (error) {
    console.error("❌ Redis Integration Test Failed:", error);
  } finally {
    // Cleanup Redis connection
    await redisActiveDeviceService.cleanup();
    console.log("🔐 Redis connection closed");
    process.exit(0);
  }
}

// Run the test
if (require.main === module) {
  testRedisIntegration();
}

export default testRedisIntegration;
