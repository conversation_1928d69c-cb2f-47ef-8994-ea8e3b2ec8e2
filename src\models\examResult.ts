import mongoose, { Document, Schema } from "mongoose";

// Interface cho ExamResult
export interface IExamResult extends Document {
  examId: mongoose.Types.ObjectId;
  studentId: mongoose.Types.ObjectId;
  score: number;
  totalQuestions: number;
  correctAnswers: number;
  startTime: Date;
  endTime: Date;
  answers: {
    questionId: mongoose.Types.ObjectId;
    selectedAnswers: mongoose.Types.ObjectId[];
    isCorrect: boolean;
  }[];
  createdAt: Date;
  updatedAt: Date;
}

// Schema cho ExamResult
const ExamResultSchema: Schema = new Schema(
  {
    examId: { type: Schema.Types.ObjectId, ref: "Exam", required: true },
    studentId: { type: Schema.Types.ObjectId, ref: "Student", required: true },
    score: { type: Number, required: true },
    totalQuestions: { type: Number, required: true },
    correctAnswers: { type: Number, required: true },
    startTime: { type: Date, required: true },
    endTime: { type: Date, required: true },
    answers: [
      {
        questionId: {
          type: Schema.Types.ObjectId,
          ref: "Question",
          required: true,
        },
        selectedAnswers: [{ type: Schema.Types.ObjectId, ref: "Answer" }],
        isCorrect: { type: Boolean, required: true },
      },
    ],
  },
  { timestamps: true }
);

// Model cho ExamResult
export default mongoose.model<IExamResult>("ExamResult", ExamResultSchema);
