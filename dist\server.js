"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const mongoose_1 = __importDefault(require("mongoose"));
const passport_1 = __importDefault(require("passport"));
const cookie_parser_1 = __importDefault(require("cookie-parser"));
const cors_1 = __importDefault(require("cors"));
const path_1 = __importDefault(require("path"));
const express_ejs_layouts_1 = __importDefault(require("express-ejs-layouts"));
const dotenv_1 = __importDefault(require("dotenv"));
// Import routes
const authRoutes_1 = __importDefault(require("./routes/authRoutes"));
const userRoutes_1 = __importDefault(require("./routes/userRoutes"));
const dataRoutes_1 = __importDefault(require("./routes/dataRoutes"));
const exam_route_1 = __importDefault(require("./routes/exam.route"));
const sseRoutes_1 = __importDefault(require("./routes/sseRoutes"));
const pageRoutes_1 = __importStar(require("./routes/pageRoutes"));
const practiceRoutes_1 = __importDefault(require("./routes/practiceRoutes"));
const redirectRoutes_1 = __importDefault(require("./routes/redirectRoutes"));
// Import controllers
const redirectController_1 = require("./controllers/redirectController");
// Import passport config
const passport_2 = __importDefault(require("./config/passport"));
// Import middleware xử lý lỗi
const errorMiddleware_1 = require("./middlewares/errorMiddleware");
dotenv_1.default.config();
// Tạo ứng dụng Express
const app = (0, express_1.default)();
// Cấu hình EJS và layouts
app.set("view engine", "ejs");
app.set("views", path_1.default.join(__dirname, "views"));
app.use(express_ejs_layouts_1.default);
app.set("layout", "layouts/layout");
app.set("layout extractScripts", true);
// Static files
app.use(express_1.default.static(path_1.default.join(__dirname, "public")));
// Middleware
app.use(express_1.default.json());
app.use(express_1.default.urlencoded({ extended: true }));
app.use((0, cookie_parser_1.default)());
app.use((0, cors_1.default)({
    origin: "http://localhost:3000", // Frontend URL
    credentials: true, // Cho phép chia sẻ cookie giữa domains
}));
// Khởi tạo Passport
app.use(passport_1.default.initialize());
(0, passport_2.default)();
// Middleware để kiểm tra user và thêm vào res.locals
app.use("/", pageRoutes_1.userCheckMiddleware);
// Đăng ký các routes
app.use("/auth", authRoutes_1.default);
app.use("/", pageRoutes_1.default);
app.use("/", practiceRoutes_1.default);
app.use("/exam", exam_route_1.default);
app.use("/api", userRoutes_1.default);
app.use("/api/data", dataRoutes_1.default);
app.use("/api/sse", sseRoutes_1.default);
app.use("/", redirectRoutes_1.default);
// Chuyển hướng URL cũ
app.get("/retry-wrong-questions", (req, res, next) => {
    (0, redirectController_1.redirectLegacyExamRoute)(req, res, next);
});
// Đăng ký middleware xử lý lỗi 404 cho tất cả các routes không khớp
app.use("/", (req, res, next) => {
    (0, errorMiddleware_1.notFoundMiddleware)(req, res, next);
});
// Middleware xử lý lỗi không lường trước
app.use((err, req, res, next) => {
    (0, errorMiddleware_1.uncaughtErrorMiddleware)(err, req, res, next);
});
// Middleware xử lý lỗi chung
app.use((err, req, res, next) => {
    (0, errorMiddleware_1.globalErrorMiddleware)(err, req, res, next);
});
// Khởi động server
const PORT = process.env.PORT || 5000;
mongoose_1.default
    .connect(process.env.MONGODB_URI_DB)
    .then(() => {
    console.log("Đã kết nối với MongoDB");
    app.listen(PORT, () => {
        console.log(`Server đang chạy trên cổng ${PORT}`);
    });
})
    .catch((err) => {
    console.error("Lỗi kết nối MongoDB:", err);
    process.exit(1);
});
exports.default = app;
//# sourceMappingURL=server.js.map