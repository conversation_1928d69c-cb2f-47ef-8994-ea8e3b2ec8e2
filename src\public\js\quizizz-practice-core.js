/**
 * Quizizz Practice Core Module
 * Handles the core functionality for Quizizz-style practice sessions
 */
(function (window) {
  "use strict";

  // Configuration
  const CONFIG = {
    apiBase: "/exam/memory",
    defaultTimeLimit: 15 * 60, // 15 minutes in seconds
    questionTimeLimit: 20, // 20 seconds per question
    pointsPerCorrect: 100,
    streakBonus: 50,
    maxStreak: 10,
    feedbackDuration: 2000, // 2 seconds
    transitionDelay: 1000, // 1 second
  };

  // State management
  let state = {
    productId: null,
    practiceId: null,
    questions: [],
    currentQuestionIndex: 0,
    answers: [],
    score: 0,
    streak: 0,
    maxStreak: 0,
    timeLeft: CONFIG.defaultTimeLimit,
    questionTimeLeft: CONFIG.questionTimeLimit,
    timer: null,
    questionTimer: null,
    isAnswered: false,
    isSubmitting: false,
    startTime: null,
    settings: {
      count: 10,
      time: 15,
      shuffleQuestions: true,
      shuffleAnswers: true,
    },
  };

  // DOM elements cache
  let dom = {};

  /**
   * Initialize the Quizizz practice module
   */
  function init() {
    console.log("🎮 Initializing Quizizz Practice...");

    try {
      // Cache DOM elements
      cacheDOMElements();

      // Check if critical elements exist
      if (!dom.loadingContainer) {
        console.error("❌ Critical DOM elements missing");
        alert("Lỗi: Không thể tải giao diện. Vui lòng tải lại trang.");
        return;
      }

      // Get practice settings
      loadSettings();

      // Get product ID from meta tag
      state.productId = document.querySelector(
        'meta[name="productId"]'
      )?.content;

      if (!state.productId) {
        console.error("❌ Product ID not found");
        showError("Không thể xác định môn học");
        return;
      }

      // Setup event listeners
      setupEventListeners();

      // Load questions and start practice
      loadQuestions();
    } catch (error) {
      console.error("❌ Error during initialization:", error);
      alert("Có lỗi xảy ra khi khởi tạo. Vui lòng tải lại trang.");
    }
  }

  /**
   * Cache DOM elements for better performance
   */
  function cacheDOMElements() {
    dom = {
      loadingContainer: document.getElementById("loadingContainer"),
      questionContainer: document.getElementById("questionContainer"),
      resultsContainer: document.getElementById("resultsContainer"),
      questionText: document.getElementById("questionText"),
      questionImageContainer: document.getElementById("questionImageContainer"),
      questionImage: document.getElementById("questionImage"),
      optionsGrid: document.getElementById("optionsGrid"),
      questionCounter: document.getElementById("questionCounter"),
      scoreDisplay: document.getElementById("scoreDisplay"),
      streakCounter: document.getElementById("streakCounter"),
      streakCount: document.getElementById("streakCount"),
      timerDisplay: document.getElementById("timerDisplay"),
      progressBar: document.getElementById("progressBar"),
      answerFeedback: document.getElementById("answerFeedback"),
      feedbackIcon: document.getElementById("feedbackIcon"),
      feedbackText: document.getElementById("feedbackText"),
      exitButton: document.getElementById("exitButton"),
      exitModal: document.getElementById("exitModal"),
      cancelExitBtn: document.getElementById("cancelExitBtn"),
      confirmExitBtn: document.getElementById("confirmExitBtn"),
    };

    // Log missing critical elements for debugging
    const criticalElements = [
      "loadingContainer",
      "questionContainer",
      "resultsContainer",
    ];
    criticalElements.forEach((elementId) => {
      if (!dom[elementId]) {
        console.warn(`⚠️ Critical DOM element missing: ${elementId}`);
      }
    });
  }

  /**
   * Load practice settings from the page
   */
  function loadSettings() {
    try {
      const settingsElement = document.getElementById("practiceSettings");
      if (settingsElement) {
        state.settings = JSON.parse(settingsElement.textContent);
        state.timeLeft = state.settings.time * 60; // Convert minutes to seconds
      }
    } catch (error) {
      console.warn("⚠️ Could not load settings, using defaults");
    }
  }

  /**
   * Setup event listeners
   */
  function setupEventListeners() {
    // Exit button
    if (dom.exitButton) {
      dom.exitButton.addEventListener("click", showExitModal);
    }

    // Exit modal buttons
    if (dom.cancelExitBtn) {
      dom.cancelExitBtn.addEventListener("click", hideExitModal);
    }

    if (dom.confirmExitBtn) {
      dom.confirmExitBtn.addEventListener("click", exitPractice);
    }

    // Keyboard shortcuts
    document.addEventListener("keydown", handleKeyPress);

    // Prevent page unload during practice
    window.addEventListener("beforeunload", handleBeforeUnload);
  }

  /**
   * Load questions from the API
   */
  async function loadQuestions() {
    try {
      showLoading("Đang tải câu hỏi...");

      const response = await fetch(
        `${CONFIG.apiBase}/${state.productId}/quizizz-practice/start`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            count: state.settings.count,
            timeLimit: state.settings.time,
            shuffleQuestions: state.settings.shuffleQuestions,
            shuffleAnswers: state.settings.shuffleAnswers,
          }),
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success && data.questions) {
        state.questions = data.questions;
        state.practiceId = data.practiceId;
        state.startTime = Date.now();

        console.log(`✅ Loaded ${data.questions.length} questions`);

        // Start the practice
        startPractice();
      } else {
        throw new Error(data.message || "Không thể tải câu hỏi");
      }
    } catch (error) {
      console.error("❌ Error loading questions:", error);
      showError("Không thể tải câu hỏi: " + error.message);
    }
  }

  /**
   * Start the practice session
   */
  function startPractice() {
    console.log("🚀 Starting Quizizz practice...");

    hideLoading();
    showQuestion();
    startTimer();
    updateUI();
  }

  /**
   * Display the current question
   */
  function showQuestion() {
    const question = state.questions[state.currentQuestionIndex];
    if (!question) {
      console.error(
        "❌ No question found at index",
        state.currentQuestionIndex
      );
      return;
    }

    // Reset question state
    state.isAnswered = false;
    state.questionTimeLeft = CONFIG.questionTimeLimit;

    // Update question text
    dom.questionText.textContent = question.text;

    // Handle question image
    if (question.image) {
      dom.questionImage.src = question.image;
      dom.questionImageContainer.classList.remove("hidden");
    } else {
      dom.questionImageContainer.classList.add("hidden");
    }

    // Create option cards
    createOptionCards(question.answers);

    // Show question container
    dom.questionContainer.classList.remove("hidden");
    dom.resultsContainer.classList.add("hidden");

    // Start question timer
    startQuestionTimer();

    console.log(
      `📝 Showing question ${state.currentQuestionIndex + 1}/${
        state.questions.length
      }`
    );
  }

  /**
   * Create option cards for the current question
   */
  function createOptionCards(answers) {
    dom.optionsGrid.innerHTML = "";

    const optionLetters = ["A", "B", "C", "D"];

    answers.forEach((answer, index) => {
      const optionCard = document.createElement("div");
      optionCard.className = "option-card";
      optionCard.dataset.index = index;

      optionCard.innerHTML = `
        <div class="option-letter">${optionLetters[index]}</div>
        <div class="option-text">${answer.text}</div>
      `;

      optionCard.addEventListener("click", () => selectAnswer(index));

      // Add staggered animation
      optionCard.style.animationDelay = `${index * 0.1}s`;

      dom.optionsGrid.appendChild(optionCard);
    });
  }

  /**
   * Handle answer selection
   */
  function selectAnswer(selectedIndex) {
    if (state.isAnswered) return;

    state.isAnswered = true;
    clearTimeout(state.questionTimer);

    const question = state.questions[state.currentQuestionIndex];
    const isCorrect = selectedIndex === question.correctAnswerIndex;

    // Record the answer
    const answerData = {
      questionIndex: state.currentQuestionIndex,
      memoryQuestionId: question.memoryQuestionId,
      selectedIndex: selectedIndex,
      correctIndex: question.correctAnswerIndex,
      isCorrect: isCorrect,
      timeSpent: CONFIG.questionTimeLimit - state.questionTimeLeft,
    };

    state.answers.push(answerData);

    // Update score and streak
    const oldScore = state.score;
    if (isCorrect) {
      const points =
        CONFIG.pointsPerCorrect + state.streak * CONFIG.streakBonus;
      state.score += points;
      state.streak++;
      state.maxStreak = Math.max(state.maxStreak, state.streak);
    } else {
      state.streak = 0;
    }

    // Show feedback
    showAnswerFeedback(isCorrect, selectedIndex, question.correctAnswerIndex);

    // Enhanced UI feedback
    if (window.QuizizzUI) {
      window.QuizizzUI.showEnhancedFeedback(isCorrect, state.streak);
      if (isCorrect) {
        window.QuizizzUI.animateScoreIncrease(state.score, oldScore);
      }
      window.QuizizzUI.animateStreak(state.streak);
    }

    // Update UI
    updateUI();

    // Move to next question after delay
    setTimeout(() => {
      nextQuestion();
    }, CONFIG.feedbackDuration);
  }

  /**
   * Show answer feedback
   */
  function showAnswerFeedback(isCorrect, selectedIndex, correctIndex) {
    // Update option cards
    const optionCards = dom.optionsGrid.querySelectorAll(".option-card");
    optionCards.forEach((card, index) => {
      card.classList.add("disabled");

      if (index === correctIndex) {
        card.classList.add("correct");
      } else if (index === selectedIndex && !isCorrect) {
        card.classList.add("incorrect");
      }
    });

    // Show feedback overlay
    dom.feedbackIcon.className = `feedback-icon fas ${
      isCorrect ? "fa-check-circle correct" : "fa-times-circle incorrect"
    }`;
    dom.feedbackText.textContent = isCorrect ? "Chính xác!" : "Sai rồi!";
    dom.answerFeedback.classList.add("show");

    // Hide feedback after duration
    setTimeout(() => {
      dom.answerFeedback.classList.remove("show");
    }, CONFIG.feedbackDuration - 500);
  }

  /**
   * Move to the next question or finish practice
   */
  function nextQuestion() {
    state.currentQuestionIndex++;

    if (state.currentQuestionIndex >= state.questions.length) {
      finishPractice();
    } else {
      showQuestion();
    }
  }

  /**
   * Start the main timer
   */
  function startTimer() {
    state.timer = setInterval(() => {
      state.timeLeft--;
      updateTimerDisplay();

      if (state.timeLeft <= 0) {
        finishPractice();
      }
    }, 1000);
  }

  /**
   * Start the question timer
   */
  function startQuestionTimer() {
    state.questionTimer = setInterval(() => {
      state.questionTimeLeft--;

      if (state.questionTimeLeft <= 0) {
        // Auto-select wrong answer (timeout)
        selectAnswer(-1); // -1 indicates timeout
      }
    }, 1000);
  }

  /**
   * Update the UI elements
   */
  function updateUI() {
    // Update question counter
    dom.questionCounter.textContent = `Câu ${state.currentQuestionIndex + 1}/${
      state.questions.length
    }`;

    // Update score
    dom.scoreDisplay.textContent = state.score;

    // Update streak
    if (state.streak > 0) {
      dom.streakCount.textContent = state.streak;
      dom.streakCounter.classList.remove("hidden");
    } else {
      dom.streakCounter.classList.add("hidden");
    }

    // Update progress bar
    const progress =
      ((state.currentQuestionIndex + 1) / state.questions.length) * 100;
    dom.progressBar.style.width = `${progress}%`;

    // Update timer
    updateTimerDisplay();
  }

  /**
   * Update timer display
   */
  function updateTimerDisplay() {
    const minutes = Math.floor(state.timeLeft / 60);
    const seconds = state.timeLeft % 60;
    dom.timerDisplay.textContent = `${minutes}:${seconds
      .toString()
      .padStart(2, "0")}`;

    // Add warning class if time is running low
    if (state.timeLeft <= 60) {
      dom.timerDisplay.classList.add("warning");
    }
  }

  /**
   * Finish the practice session
   */
  async function finishPractice() {
    console.log("🏁 Finishing practice...");

    // Clear timers
    clearInterval(state.timer);
    clearTimeout(state.questionTimer);

    // Submit results
    await submitResults();

    // Show results
    showResults();
  }

  /**
   * Submit practice results to the server
   */
  async function submitResults() {
    if (state.isSubmitting) return;
    state.isSubmitting = true;

    try {
      const timeSpent = Math.floor((Date.now() - state.startTime) / 1000);

      const response = await fetch(
        `${CONFIG.apiBase}/${state.productId}/quizizz-practice/submit`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            practiceId: state.practiceId,
            answers: state.answers,
            timeSpent: timeSpent,
            score: state.score,
            streak: state.maxStreak,
            status: "completed",
          }),
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log("✅ Results submitted successfully:", data);
    } catch (error) {
      console.error("❌ Error submitting results:", error);
      // Continue to show results even if submission fails
    } finally {
      state.isSubmitting = false;
    }
  }

  /**
   * Show practice results
   */
  function showResults() {
    const correctCount = state.answers.filter((a) => a.isCorrect).length;
    const totalQuestions = state.questions.length;
    const accuracy =
      totalQuestions > 0
        ? Math.round((correctCount / totalQuestions) * 100)
        : 0;

    let grade = "Cần cải thiện";
    if (accuracy >= 90) grade = "Xuất sắc";
    else if (accuracy >= 80) grade = "Tốt";
    else if (accuracy >= 60) grade = "Khá";
    else if (accuracy >= 40) grade = "Trung bình";

    dom.resultsContainer.innerHTML = `
      <div class="results-container">
        <div class="results-score">${state.score}</div>
        <div class="results-grade">${grade}</div>
        
        <div class="results-stats">
          <div class="stat-card">
            <div class="stat-value">${correctCount}/${totalQuestions}</div>
            <div class="stat-label">Câu đúng</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">${accuracy}%</div>
            <div class="stat-label">Độ chính xác</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">${state.maxStreak}</div>
            <div class="stat-label">Streak tối đa</div>
          </div>
        </div>

        <div class="flex justify-center gap-4 mt-8">
          <button class="quizizz-button primary" onclick="window.QuizizzPractice.restart()">
            <i class="fas fa-redo mr-2"></i>Luyện tập lại
          </button>
          <button class="quizizz-button secondary" onclick="window.QuizizzPractice.exit()">
            <i class="fas fa-home mr-2"></i>Về trang chủ
          </button>
        </div>
      </div>
    `;

    dom.questionContainer.classList.add("hidden");
    dom.resultsContainer.classList.remove("hidden");
  }

  /**
   * Utility functions
   */
  function showLoading(message = "Đang tải...") {
    if (dom.loadingContainer) {
      const loadingText = dom.loadingContainer.querySelector(".loading-text");
      if (loadingText) {
        loadingText.textContent = message;
      }
      dom.loadingContainer.classList.remove("hidden");
    }

    if (dom.questionContainer) {
      dom.questionContainer.classList.add("hidden");
    }

    if (dom.resultsContainer) {
      dom.resultsContainer.classList.add("hidden");
    }
  }

  function hideLoading() {
    if (dom.loadingContainer) {
      dom.loadingContainer.classList.add("hidden");
    }
  }

  function showError(message) {
    if (dom.loadingContainer) {
      dom.loadingContainer.innerHTML = `
        <div class="text-center">
          <div class="text-6xl text-red-500 mb-6">
            <i class="fas fa-exclamation-triangle"></i>
          </div>
          <h3 class="text-2xl font-bold mb-4">Có lỗi xảy ra</h3>
          <p class="text-lg mb-6">${message}</p>
          <button class="quizizz-button primary" onclick="window.location.reload()">
            <i class="fas fa-refresh mr-2"></i>Thử lại
          </button>
        </div>
      `;
      dom.loadingContainer.classList.remove("hidden");
    } else {
      // Fallback if DOM elements are not available
      console.error("Error:", message);
      alert("Có lỗi xảy ra: " + message);
    }
  }

  function showExitModal() {
    dom.exitModal.classList.remove("hidden");
  }

  function hideExitModal() {
    dom.exitModal.classList.add("hidden");
  }

  function exitPractice() {
    window.location.href = `/course/${state.productId}/exams?tab=memory`;
  }

  function restart() {
    window.location.reload();
  }

  function handleKeyPress(event) {
    if (state.isAnswered) return;

    const key = event.key.toLowerCase();
    const keyMap = { a: 0, b: 1, c: 2, d: 3 };

    if (keyMap.hasOwnProperty(key)) {
      event.preventDefault();
      selectAnswer(keyMap[key]);
    }
  }

  function handleBeforeUnload(event) {
    if (
      state.currentQuestionIndex > 0 &&
      state.currentQuestionIndex < state.questions.length
    ) {
      event.preventDefault();
      event.returnValue = "Bạn có chắc muốn thoát? Tiến độ sẽ không được lưu.";
      return event.returnValue;
    }
  }

  // Public API
  window.QuizizzPractice = {
    init: init,
    restart: restart,
    exit: exitPractice,
    getState: () => ({ ...state }),
  };
})(window);
