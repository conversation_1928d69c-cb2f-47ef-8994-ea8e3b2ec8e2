version: "3.8"

services:
  app:
    build: .
    image: auth-app
    container_name: auth-app
    restart: always
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=production
      - PORT=5000
      - MONGODB_URI=${MONGODB_URI:-************************************:port/database}
      - JWT_SECRET=${JWT_SECRET:-your_secure_jwt_secret_key}
      - JWT_EXPIRATION=${JWT_EXPIRATION:-1d}
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
      - CALLBACK_URL=${CALLBACK_URL}
    networks:
      - app-network
    volumes:
      - ./logs:/usr/src/app/logs
    command: ["./run.sh"]

networks:
  app-network:
    driver: bridge
