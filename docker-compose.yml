version: "3.8"

services:
  app:
    build: .
    image: auth-app
    container_name: auth-app
    restart: always
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=production
      - PORT=5000
      - MONGODB_URI=${MONGODB_URI:-************************************:port/database}
      - JWT_SECRET=${JWT_SECRET:-your_secure_jwt_secret_key}
      - JWT_EXPIRATION=${JWT_EXPIRATION:-1d}
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
      - CALLBACK_URL=${CALLBACK_URL}
      - REDIS_HOST=${REDIS_HOST:-redis}
      - REDIS_PORT=${REDIS_PORT:-6379}
      - REDIS_PASSWORD=${REDIS_PASSWORD:-}
    networks:
      - app-network
    volumes:
      - ./logs:/usr/src/app/logs
    command: ["./run.sh"]
    depends_on:
      - redis

  redis:
    image: redis:7-alpine
    container_name: auth-app-redis
    restart: always
    ports:
      - "6379:6379"
    command:
      [
        "redis-server",
        "--appendonly",
        "yes",
        "--requirepass",
        "${REDIS_PASSWORD:-}",
      ]
    volumes:
      - redis_data:/data
    networks:
      - app-network
    environment:
      - REDIS_REPLICATION_MODE=master

volumes:
  redis_data:
    driver: local

networks:
  app-network:
    driver: bridge
