{"extends": "./tsconfig.json", "compilerOptions": {"noEmitOnError": false, "skipLibCheck": true, "skipDefaultLibCheck": true, "sourceMap": false, "removeComments": true, "noImplicitAny": false, "strictNullChecks": false, "strictFunctionTypes": false, "strictPropertyInitialization": false, "noImplicitThis": false, "alwaysStrict": false, "noUnusedLocals": false, "noUnusedParameters": false, "noImplicitReturns": false, "noFallthroughCasesInSwitch": false, "allowJs": true, "checkJs": false, "resolveJsonModule": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": false, "ignoreDeprecations": "5.0"}, "include": ["src/**/*"], "exclude": ["node_modules", "**/*.test.ts"]}