"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.savePracticeExamResult = exports.takePracticeExam = exports.startPracticeExam = exports.checkExistingPractice = exports.getWrongQuestions = exports.retryWrongQuestions = exports.takeExamQuizizz = exports.takeExamGoogleForm = exports.createExam = exports.getExamsByProductId = exports.getExamById = void 0;
const exam_service_1 = __importDefault(require("../services/exam.service"));
const errorhandler_1 = require("../util/errorhandler");
const question_1 = __importDefault(require("../models/question"));
const exam_1 = __importDefault(require("../models/exam"));
const mongoose_1 = __importDefault(require("mongoose"));
const encryption_1 = require("../util/encryption");
const PracticeExamHistory_1 = __importDefault(require("../models/PracticeExamHistory"));
const modelService_1 = require("../services/modelService");
const student_service_1 = require("../services/student.service");
// Helper để đảo thứ tự ngẫu nhiên một mảng
const shuffleArray = (array) => {
    let currentIndex = array.length;
    let randomIndex;
    // Trong khi còn phần tử chưa xử lý
    while (currentIndex !== 0) {
        // Chọn một phần tử còn lại ngẫu nhiên
        randomIndex = Math.floor(Math.random() * currentIndex);
        currentIndex--;
        // Hoán đổi với phần tử hiện tại
        [array[currentIndex], array[randomIndex]] = [
            array[randomIndex],
            array[currentIndex],
        ];
    }
    return array;
};
// Thuật toán tối ưu để chọn N câu hỏi ngẫu nhiên từ mảng lớn
// Sử dụng Fisher-Yates shuffle được tối ưu - O(N) thay vì O(total)
const selectRandomQuestions = (questions, count) => {
    const totalQuestions = questions.length;
    if (totalQuestions <= count) {
        // Nếu tổng số câu hỏi <= số cần lấy, trả về toàn bộ
        return shuffleArray([...questions]);
    }
    // Thuật toán reservoir sampling được tối ưu
    // Đây là thuật toán nhanh nhất cho việc chọn ngẫu nhiên
    const selected = [];
    // Lấy count câu hỏi đầu tiên
    for (let i = 0; i < count; i++) {
        selected[i] = questions[i];
    }
    // Với mỗi câu hỏi còn lại, có xác suất count/i để thay thế
    for (let i = count; i < totalQuestions; i++) {
        const randomIndex = Math.floor(Math.random() * (i + 1));
        if (randomIndex < count) {
            selected[randomIndex] = questions[i];
        }
    }
    // Shuffle kết quả để đảm bảo thứ tự ngẫu nhiên
    return shuffleArray(selected);
};
// Helper function to optimize questions for storage/client (remove unnecessary fields)
const optimizeQuestions = (questions) => {
    return questions.map((question) => {
        // Tạo mảng answerOrder để lưu thứ tự các câu trả lời
        const answerOrder = question.options
            ? question.options.map((_, index) => index + 1)
            : [1, 2, 3, 4];
        return {
            questionId: question._id, // Chỉ lưu ID của câu hỏi
            answerOrder: answerOrder, // Lưu thứ tự các câu trả lời [1,2,3,4] hoặc [4,2,1,3]
        };
    });
};
// Helper function để gửi dữ liệu câu hỏi đầy đủ đến client nhưng vẫn tối ưu dữ liệu
const prepareQuestionsForClient = (questions) => {
    return questions.map((question) => {
        // Đảm bảo chỉ lấy các trường cần thiết để giảm kích thước dữ liệu
        return {
            _id: question._id,
            text: question.text,
            options: question.options || [],
            image: question.image || null,
            questionNumber: question.questionNumber || 0,
        };
    });
};
// Lấy đề thi và câu hỏi với các tùy chọn đã bị xáo trộn
const getExamWithShuffledQuestions = (examId_1, ...args_1) => __awaiter(void 0, [examId_1, ...args_1], void 0, function* (examId, shuffleQuestions = false, shuffleAnswers = false) {
    try {
        const exam = yield exam_1.default.findById(examId);
        if (!exam) {
            throw new Error("Không tìm thấy đề thi");
        }
        // Lấy tất cả câu hỏi của đề thi
        let questions = yield question_1.default.find({
            examId: new mongoose_1.default.Types.ObjectId(examId),
        });
        // Đảo thứ tự câu hỏi nếu được yêu cầu
        if (shuffleQuestions) {
            questions = shuffleArray([...questions]);
        }
        // Xử lý các lựa chọn trong mỗi câu hỏi
        const processedQuestions = questions.map((question) => {
            const questionObj = question.toObject();
            // Chuyển đổi trường answers thành options để phù hợp với frontend
            if (questionObj.answers && Array.isArray(questionObj.answers)) {
                // Đảo thứ tự các lựa chọn nếu được yêu cầu
                questionObj.options = shuffleAnswers
                    ? shuffleArray([...questionObj.answers])
                    : [...questionObj.answers];
                // Xóa trường answers để tránh trùng lặp dữ liệu
                delete questionObj.answers;
            }
            else {
                // Tạo options mặc định nếu không có answers
                const defaultOptions = [
                    { text: "Lựa chọn A", isCorrect: true },
                    { text: "Lựa chọn B", isCorrect: false },
                    { text: "Lựa chọn C", isCorrect: false },
                    { text: "Lựa chọn D", isCorrect: false },
                ];
                questionObj.options = shuffleAnswers
                    ? shuffleArray([...defaultOptions])
                    : defaultOptions;
            }
            return questionObj;
        });
        return {
            exam,
            questions: processedQuestions,
        };
    }
    catch (error) {
        throw error;
    }
});
// Helper function để khôi phục dữ liệu câu hỏi đầy đủ từ database
const fetchFullQuestionsFromOptimized = (optimizedQuestions) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // Lấy tất cả questionIds từ mảng optimizedQuestions
        const questionIds = optimizedQuestions.map((q) => q.questionId || q);
        const fullQuestions = yield question_1.default.find({ _id: { $in: questionIds } });
        if (!fullQuestions || fullQuestions.length === 0) {
            console.error("❌ Không tìm thấy câu hỏi nào từ database!");
            return [];
        }
        // Map câu hỏi đã tối ưu với dữ liệu đầy đủ và sắp xếp theo answerOrder
        const processedQuestions = [];
        for (let index = 0; index < optimizedQuestions.length; index++) {
            const optimized = optimizedQuestions[index];
            const fullQuestion = fullQuestions.find((q) => q._id.toString() === (optimized.questionId || optimized).toString());
            if (!fullQuestion) {
                console.warn(`⚠️ Không tìm thấy câu hỏi với ID: ${optimized.questionId || optimized}`);
                continue;
            }
            // Chuyển từ document sang plain object
            const rawQuestionObj = fullQuestion.toObject
                ? fullQuestion.toObject()
                : Object.assign({}, fullQuestion);
            // Tạo đối tượng mới với các trường theo interface
            const processedQuestion = {
                _id: rawQuestionObj._id,
                text: rawQuestionObj.text,
                options: [],
                image: rawQuestionObj.image || null,
                questionNumber: index + 1,
            };
            // Xử lý options theo answerOrder đã lưu
            if (rawQuestionObj.answers && Array.isArray(rawQuestionObj.answers)) {
                // Lấy các options từ answers
                const originalOptions = [...rawQuestionObj.answers];
                // Nếu có answerOrder, sắp xếp lại options theo thứ tự đã lưu
                if (optimized.answerOrder && Array.isArray(optimized.answerOrder)) {
                    const orderedOptions = [];
                    for (let i = 0; i < optimized.answerOrder.length; i++) {
                        const originalIndex = optimized.answerOrder[i] - 1;
                        if (originalIndex >= 0 && originalIndex < originalOptions.length) {
                            orderedOptions.push(originalOptions[originalIndex]);
                        }
                    }
                    processedQuestion.options =
                        orderedOptions.length > 0 ? orderedOptions : originalOptions;
                }
                else {
                    // Nếu không có answerOrder, sử dụng thứ tự gốc
                    processedQuestion.options = originalOptions;
                }
            }
            processedQuestions.push(processedQuestion);
        }
        return processedQuestions;
    }
    catch (error) {
        console.error("❌ Lỗi khi khôi phục dữ liệu câu hỏi:", error);
        return [];
    }
});
class ExamController {
    /**
     * Lấy danh sách bài kiểm tra theo sản phẩm
     */
    getExamsByProduct(req, res) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { productId } = req.params;
                const { page = 1, limit = 10 } = req.query;
                const result = yield exam_service_1.default.getExamsByProduct(productId, Number(page), Number(limit));
                return res.status(200).json({
                    message: "Success",
                    data: result,
                });
            }
            catch (error) {
                return (0, errorhandler_1.responseError)(res, error.message, 400);
            }
        });
    }
    /**
     * Lấy thông tin bài kiểm tra theo ID
     */
    getExamById(req, res) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { examId } = req.params;
                const exam = yield exam_service_1.default.getExamById(examId);
                return res.status(200).json({
                    message: "Success",
                    data: exam,
                });
            }
            catch (error) {
                return (0, errorhandler_1.responseError)(res, error.message, 400);
            }
        });
    }
    /**
     * Tạo bài kiểm tra mới
     */
    createExam(req, res) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { productId } = req.params;
                const examData = req.body;
                const newExam = yield exam_service_1.default.createExam(productId, examData);
                return res.status(201).json({
                    message: "Success",
                    data: newExam,
                });
            }
            catch (error) {
                return (0, errorhandler_1.responseError)(res, error.message, 400);
            }
        });
    }
    /**
     * Cập nhật bài kiểm tra
     */
    updateExam(req, res) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { examId } = req.params;
                const examData = req.body;
                const updatedExam = yield exam_service_1.default.updateExam(examId, examData);
                return res.status(200).json({
                    message: "Success",
                    data: updatedExam,
                });
            }
            catch (error) {
                return (0, errorhandler_1.responseError)(res, error.message, 400);
            }
        });
    }
    /**
     * Xóa bài kiểm tra
     */
    deleteExam(req, res) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { examId } = req.params;
                const result = yield exam_service_1.default.deleteExam(examId);
                return res.status(200).json({
                    message: "Success",
                    data: result,
                });
            }
            catch (error) {
                return (0, errorhandler_1.responseError)(res, error.message, 400);
            }
        });
    }
    /**
     * Lấy bài kiểm tra kèm tất cả câu hỏi
     */
    getExamWithQuestions(req, res) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { examId } = req.params;
                const result = yield exam_service_1.default.getExamWithQuestions(examId);
                return res.status(200).json({
                    message: "Success",
                    data: result,
                });
            }
            catch (error) {
                return (0, errorhandler_1.responseError)(res, error.message, 400);
            }
        });
    }
    /**
     * Hiển thị trang làm bài thi bằng Google Form
     */
    takeExamGoogleForm(req, res) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { examId } = req.params;
                // Lấy các tham số đảo thứ tự từ query string
                const shuffleQuestions = req.query.shuffleQuestions === "true";
                const shuffleAnswers = req.query.shuffleAnswers === "true";
                const { exam, questions } = yield getExamWithShuffledQuestions(examId, shuffleQuestions, shuffleAnswers);
                // Mã hóa dữ liệu câu hỏi
                let encryptedQuestionsConfig = null;
                let renderData = {
                    user: res.locals.user,
                    student: req.student,
                    exam,
                    title: `Làm bài thi: ${exam.name} - Google Form`,
                    questions: questions, // Always pass questions for template fallback
                };
                try {
                    if (questions && questions.length > 0) {
                        const encrypted = (0, encryption_1.encryptObjectOptimized)(questions);
                        encryptedQuestionsConfig = {
                            appConfigData: encrypted.appConfigData,
                            token: encrypted.token,
                            salt: encrypted.salt,
                            algorithm: encrypted.algorithm,
                        };
                        // Pass encrypted data AND keep questions for template fallback
                        renderData.encryptedQuestionsConfig = encryptedQuestionsConfig;
                    }
                }
                catch (encryptError) {
                    console.warn("⚠️ Không thể mã hóa câu hỏi Google Form, sử dụng dữ liệu thường:", encryptError);
                    // Questions are already in renderData
                }
                return res.render("exam/google-form", renderData);
            }
            catch (error) {
                console.error("Lỗi khi lấy bài thi Google Form:", error);
                return res.status(500).render("error", {
                    message: "Đã xảy ra lỗi khi tải bài thi",
                    error: { status: 500, stack: error.message },
                    user: res.locals.user,
                });
            }
        });
    }
    /**
     * Hiển thị trang làm bài thi bằng Quizizz
     */
    takeExamQuizizz(req, res) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { examId } = req.params;
                // Lấy các tham số đảo thứ tự từ query string
                const shuffleQuestions = req.query.shuffleQuestions === "true";
                const shuffleAnswers = req.query.shuffleAnswers === "true";
                const { exam, questions } = yield getExamWithShuffledQuestions(examId, shuffleQuestions, shuffleAnswers);
                // Chuyển đổi dữ liệu câu hỏi thành chuỗi JSON an toàn
                const safeQuestions = questions.map((q) => {
                    // Đảm bảo options được xử lý đúng
                    let processedOptions = [];
                    if (q.options && Array.isArray(q.options) && q.options.length > 0) {
                        processedOptions = q.options.map((opt) => {
                            if (typeof opt === "object") {
                                return {
                                    text: opt.text || "",
                                    isCorrect: !!opt.isCorrect,
                                };
                            }
                            return { text: String(opt), isCorrect: false };
                        });
                    }
                    else {
                        // Nếu không có options, tạo ra các options mặc định
                        console.warn(`Câu hỏi không có options: ${q._id}. Tạo options mặc định.`);
                        processedOptions = [
                            { text: "Lựa chọn A", isCorrect: true },
                            { text: "Lựa chọn B", isCorrect: false },
                            { text: "Lựa chọn C", isCorrect: false },
                            { text: "Lựa chọn D", isCorrect: false },
                        ];
                    }
                    return Object.assign(Object.assign({}, q), { options: processedOptions });
                });
                // Mã hóa dữ liệu câu hỏi Quizizz
                let encryptedQuestionsConfig = null;
                let renderData = {
                    user: res.locals.user,
                    student: req.student,
                    exam,
                    title: `Làm bài thi: ${exam.name} - Quizizz`,
                    questions: safeQuestions, // Always pass questions for template fallback
                };
                try {
                    if (safeQuestions && safeQuestions.length > 0) {
                        // Mã hóa exam data với questions
                        const examData = {
                            examId: exam._id,
                            questions: safeQuestions,
                        };
                        const encrypted = (0, encryption_1.encryptObjectOptimized)(examData);
                        encryptedQuestionsConfig = {
                            appConfigData: encrypted.appConfigData,
                            token: encrypted.token,
                            salt: encrypted.salt,
                            algorithm: encrypted.algorithm,
                        };
                        // Pass encrypted data AND keep questions for template fallback
                        renderData.encryptedExamConfig = encryptedQuestionsConfig;
                    }
                }
                catch (encryptError) {
                    console.warn("⚠️ Không thể mã hóa câu hỏi Quizizz, sử dụng dữ liệu thường:", encryptError);
                    // Questions are already in renderData
                }
                return res.render("exam/quizizz", renderData);
            }
            catch (error) {
                console.error("Lỗi khi lấy bài thi Quizizz:", error);
                return res.status(500).render("error", {
                    message: "Đã xảy ra lỗi khi tải bài thi",
                    error: { status: 500, stack: error.message },
                    user: res.locals.user,
                });
            }
        });
    }
    /**
     * Hiển thị trang làm lại câu hỏi sai
     */
    retryWrongQuestions(req, res) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { examId, examType } = req.query;
                if (!examId || !examType) {
                    throw new Error("Thiếu thông tin cần thiết");
                }
                // Lấy thông tin bài thi
                const exam = yield exam_service_1.default.getExamById(examId);
                if (!exam) {
                    throw new Error("Không tìm thấy bài thi");
                }
                // Kiểm tra loại đề thi được yêu cầu
                const templateName = examType === "google-form"
                    ? "exam/wrong-questions-google-form"
                    : "exam/wrong-questions-quizizz";
                return res.render(templateName, {
                    user: res.locals.user,
                    student: req.student,
                    exam,
                    title: `Làm lại câu hỏi sai: ${exam.name}`,
                });
            }
            catch (error) {
                console.error("Lỗi khi xử lý làm lại câu hỏi sai:", error);
                return res.status(500).render("error", {
                    message: "Đã xảy ra lỗi khi tải trang làm lại câu hỏi",
                    error: { status: 500, stack: error.message },
                    user: res.locals.user,
                });
            }
        });
    }
    /**
     * API để lấy danh sách câu hỏi sai từ một bài thi
     */
    getWrongQuestions(req, res) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a;
            try {
                const { examId } = req.params;
                const studentId = (_a = req.student) === null || _a === void 0 ? void 0 : _a._id;
                if (!examId) {
                    return res.status(400).json({
                        success: false,
                        message: "Thiếu ID bài thi",
                    });
                }
                // Lấy thông tin bài thi
                const exam = yield exam_service_1.default.getExamById(examId);
                if (!exam) {
                    return res.status(404).json({
                        success: false,
                        message: "Không tìm thấy bài thi",
                    });
                }
                // Lấy tất cả câu hỏi của bài thi
                const questions = yield question_1.default.find({
                    examId: new mongoose_1.default.Types.ObjectId(examId),
                });
                // Lấy kết quả bài thi gần nhất của học sinh (giả định đã có API lấy kết quả)
                // Đây là mẫu, bạn cần điều chỉnh theo cấu trúc dữ liệu thực tế
                const examResult = yield Promise.resolve().then(() => __importStar(require("../models/examResult"))).then((module) => module.default);
                const latestResult = yield examResult
                    .findOne({
                    examId,
                    studentId,
                })
                    .sort({ createdAt: -1 });
                // Nếu không có kết quả hoặc chưa làm bài, trả về mảng rỗng
                if (!latestResult || !latestResult.answers) {
                    return res.status(404).json({
                        success: false,
                        message: "Không tìm thấy kết quả bài thi",
                    });
                }
                // Lọc các câu hỏi sai
                const wrongQuestions = [];
                for (const answer of latestResult.answers) {
                    if (!answer.isCorrect) {
                        const question = questions.find((q) => q._id.toString() === answer.questionId.toString());
                        if (question) {
                            // Chuyển đổi từ document sang object và xáo trộn các lựa chọn
                            const questionObj = question.toObject();
                            // Chuyển đổi trường answers thành options nếu cần
                            if (questionObj.answers && Array.isArray(questionObj.answers)) {
                                questionObj.options = [...questionObj.answers];
                                // Xóa trường answers để tránh trùng lặp dữ liệu
                                delete questionObj.answers;
                            }
                            wrongQuestions.push(questionObj);
                        }
                    }
                }
                // Trả về danh sách câu hỏi sai
                return res.status(200).json({
                    success: true,
                    examId,
                    questions: wrongQuestions,
                });
            }
            catch (error) {
                console.error("Lỗi khi lấy danh sách câu hỏi sai:", error);
                return res.status(500).json({
                    success: false,
                    message: "Đã xảy ra lỗi khi xử lý yêu cầu",
                    error: error.message,
                });
            }
        });
    }
}
// Export các hàm controller riêng lẻ
const getExamById = (req, res) => new ExamController().getExamById(req, res);
exports.getExamById = getExamById;
const getExamsByProductId = (req, res) => new ExamController().getExamsByProduct(req, res);
exports.getExamsByProductId = getExamsByProductId;
const createExam = (req, res) => new ExamController().createExam(req, res);
exports.createExam = createExam;
const takeExamGoogleForm = (req, res) => new ExamController().takeExamGoogleForm(req, res);
exports.takeExamGoogleForm = takeExamGoogleForm;
const takeExamQuizizz = (req, res) => new ExamController().takeExamQuizizz(req, res);
exports.takeExamQuizizz = takeExamQuizizz;
const retryWrongQuestions = (req, res) => new ExamController().retryWrongQuestions(req, res);
exports.retryWrongQuestions = retryWrongQuestions;
const getWrongQuestions = (req, res) => new ExamController().getWrongQuestions(req, res);
exports.getWrongQuestions = getWrongQuestions;
// ====================
// PRACTICE EXAM FUNCTIONS
// ====================
/**
 * Kiểm tra bài thi thử đang làm dở
 */
const checkExistingPractice = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const { productId } = req.params;
        const userId = (_a = res.locals.user) === null || _a === void 0 ? void 0 : _a._id;
        if (!userId) {
            return res.status(401).json({
                success: false,
                message: "Không tìm thấy thông tin người dùng",
            });
        }
        // Tìm bài thi thử đang làm dở (status = in_progress và trong vòng 60 phút)
        const existingPractice = yield PracticeExamHistory_1.default.findOne({
            userId,
            courseId: productId,
            status: "in_progress",
            startedAt: {
                $gte: new Date(Date.now() - 60 * 60 * 1000), // 60 phút trước
            },
        }).sort({ startedAt: -1 });
        if (existingPractice) {
            const timeElapsed = Math.floor((Date.now() - existingPractice.startedAt.getTime()) / 1000);
            const timeRemaining = 60 * 60 - timeElapsed; // 60 phút - thời gian đã trôi qua
            if (timeRemaining > 0) {
                // Còn thời gian và status = in_progress, có thể làm tiếp
                res.json({
                    success: true,
                    hasExistingPractice: true,
                    hasIncompletePractice: true,
                    practiceId: existingPractice._id.toString(),
                    timeRemaining,
                    timeElapsed,
                    startTime: existingPractice.startedAt,
                    status: existingPractice.status,
                    totalQuestions: existingPractice.totalQuestions || 100,
                    // Backward compatibility
                    hasExisting: true,
                    existingExam: {
                        id: existingPractice._id,
                        practiceId: existingPractice._id.toString(),
                        startTime: existingPractice.startedAt,
                        selectedQuestions: existingPractice.selectedQuestions,
                        userAnswers: existingPractice.userAnswers,
                        timeElapsed,
                        timeRemaining,
                        status: existingPractice.status,
                    },
                });
            }
            else {
                // Hết thời gian, bài thi cũ không còn hiệu lực
                res.json({
                    success: true,
                    hasExistingPractice: false,
                    hasIncompletePractice: false,
                    // Backward compatibility
                    hasExisting: false,
                });
            }
        }
        else {
            // Không có bài thi nào đang làm dở
            res.json({
                success: true,
                hasExistingPractice: false,
                hasIncompletePractice: false,
                // Backward compatibility
                hasExisting: false,
            });
        }
    }
    catch (error) {
        console.error("Error checking existing practice:", error);
        res.status(500).json({
            success: false,
            message: "Lỗi kiểm tra bài thi hiện tại",
        });
    }
});
exports.checkExistingPractice = checkExistingPractice;
/**
 * Bắt đầu thi thử - hiển thị trang chuẩn bị và tạo session thi thử
 */
const startPracticeExam = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, _b, _c;
    const { productId } = req.params;
    const action = (_a = req.body) === null || _a === void 0 ? void 0 : _a.action; // 'new' hoặc 'continue' - chỉ có trong POST request
    const userId = (_b = res.locals.user) === null || _b === void 0 ? void 0 : _b._id;
    // Kiểm tra nếu là API call (từ frontend)
    const isApiCall = ((_c = req.headers["content-type"]) === null || _c === void 0 ? void 0 : _c.includes("application/json")) &&
        req.method === "POST";
    try {
        if (!userId) {
            if (isApiCall) {
                return res.status(401).json({
                    success: false,
                    message: "Người dùng chưa đăng nhập",
                });
            }
            return res.redirect("/login");
        }
        // Lấy thông tin khóa học
        const product = yield modelService_1.Product.findById(productId);
        if (!product) {
            if (isApiCall) {
                return res.status(404).json({
                    success: false,
                    message: "Không tìm thấy khóa học",
                });
            }
            return res.status(404).render("error", {
                message: "Không tìm thấy khóa học",
                error: { status: 404 },
                user: res.locals.user,
            });
        }
        // Lấy thông tin sinh viên
        const student = yield (0, student_service_1.getStudentByEmail)(res.locals.user.email);
        if (!student) {
            if (isApiCall) {
                return res.status(403).json({
                    success: false,
                    message: "Bạn chưa được đăng ký vào khóa học này",
                });
            }
            return res.status(403).render("error", {
                message: "Bạn chưa được đăng ký vào khóa học này",
                error: { status: 403 },
                user: res.locals.user,
            });
        }
        // Nếu là GET request, check existing practice để quyết định hiển thị gì
        if (!action) {
            // GET request - check existing practice
            const existingPractice = yield PracticeExamHistory_1.default.findOne({
                userId,
                courseId: productId,
                status: "in_progress",
                startedAt: {
                    $gte: new Date(Date.now() - 60 * 60 * 1000), // 60 phút trước
                },
            }).sort({ startedAt: -1 });
            if (existingPractice) {
                const timeElapsed = Math.floor((Date.now() - existingPractice.createdAt.getTime()) / 1000);
                const timeRemaining = 60 * 60 - timeElapsed;
                if (timeRemaining > 0) {
                    // Có bài thi đang làm dở, render với thông tin existing
                    return res.render("exam/practice-exam", {
                        user: res.locals.user,
                        student,
                        product,
                        practiceInfo: {
                            courseId: productId,
                            courseName: product.name,
                            hasExisting: true,
                            existingExam: {
                                timeRemaining,
                                timeElapsed,
                                startTime: existingPractice.createdAt,
                                practiceId: existingPractice._id.toString(), // Thêm practiceId
                            },
                        },
                    });
                }
            }
            // Không có bài thi đang làm dở, proceed tạo bài mới
        }
        // Kiểm tra nếu user muốn làm tiếp bài cũ (POST request hoặc route với practiceId)
        if (action === "continue") {
            let existingPractice;
            // Nếu có practiceId trong body (từ route mới), tìm theo ID cụ thể
            if (req.body.practiceId) {
                existingPractice = yield PracticeExamHistory_1.default.findOne({
                    _id: req.body.practiceId,
                    userId,
                    courseId: productId,
                    status: "in_progress",
                });
            }
            else {
                // Fallback - tìm practice gần nhất
                existingPractice = yield PracticeExamHistory_1.default.findOne({
                    userId,
                    courseId: productId,
                    status: "in_progress",
                    startedAt: {
                        $gte: new Date(Date.now() - 60 * 60 * 1000), // 60 phút trước
                    },
                }).sort({ startedAt: -1 });
            }
            if (existingPractice) {
                const timeElapsed = Math.floor((Date.now() - existingPractice.startedAt.getTime()) / 1000);
                const timeRemaining = 60 * 60 - timeElapsed;
                if (timeRemaining <= 0) {
                    // Bài thi đã hết giờ, tự động cập nhật trạng thái thành "time_up"
                    yield PracticeExamHistory_1.default.findByIdAndUpdate(existingPractice._id, {
                        status: "time_up",
                        completedAt: new Date(),
                    });
                    // Tiếp tục tạo bài thi mới
                }
                else if (timeRemaining > 0) {
                    // Use saved questions to maintain consistent order
                    let questionsToUse = [];
                    if (existingPractice.selectedQuestions &&
                        existingPractice.selectedQuestions.length > 0) {
                        // Sử dụng helper function mới để khôi phục dữ liệu câu hỏi đầy đủ
                        questionsToUse = yield fetchFullQuestionsFromOptimized(existingPractice.selectedQuestions);
                    }
                    if (questionsToUse.length === 0) {
                        // Nếu không có câu hỏi, fallback tạo bài mới
                        // Continue to create new exam logic
                    }
                    else {
                        // Mã hóa dữ liệu bài thi cũ
                        const encryptionResult = (0, encryption_1.encryptObjectOptimized)({
                            questions: prepareQuestionsForClient(questionsToUse),
                            practiceInfo: {
                                courseId: productId,
                                courseName: product.name,
                                totalQuestions: questionsToUse.length,
                                duration: 60,
                                type: "course-review",
                                timeRemaining,
                                existingAnswers: existingPractice.userAnswers,
                                startTime: existingPractice.startedAt,
                            },
                        });
                        return res.render("exam/practice-exam", {
                            user: res.locals.user,
                            student,
                            product,
                            encryptedPracticeData: encryptionResult.appConfigData,
                            encryptionToken: encryptionResult.token,
                            encryptionSalt: encryptionResult.salt,
                            practiceInfo: {
                                courseId: productId,
                                courseName: product.name,
                                totalQuestions: questionsToUse.length,
                                duration: 60,
                                timeRemaining,
                                existingAnswers: existingPractice.userAnswers,
                                isContinue: true,
                                practiceId: existingPractice._id.toString(), // Thêm practiceId của bài cũ
                            },
                        });
                    }
                }
            }
        }
        // Nếu user chọn "làm bài mới" và có bài thi cũ đang làm dở
        if (action === "new") {
            const existingPractice = yield PracticeExamHistory_1.default.findOne({
                userId,
                courseId: productId,
                status: "in_progress",
                startedAt: {
                    $gte: new Date(Date.now() - 60 * 60 * 1000), // 60 phút trước
                },
            }).sort({ startedAt: -1 });
            if (existingPractice) {
                // Update bài thi cũ sang trạng thái "abandoned" khi user chọn làm bài mới
                yield PracticeExamHistory_1.default.findByIdAndUpdate(existingPractice._id, {
                    status: "abandoned",
                    completedAt: new Date(),
                });
            }
        }
        // Lấy tất cả đề thi của khóa học
        const exams = yield exam_1.default.find({
            productId: new mongoose_1.default.Types.ObjectId(productId),
            status: "active",
        });
        if (exams.length === 0) {
            return res.render("exam/practice-start", {
                user: res.locals.user,
                student,
                product,
                error: "Không có đề thi nào trong khóa học này để tạo bài thi thử",
            });
        }
        // Lấy tất cả câu hỏi từ các đề thi
        const examIds = exams.map((exam) => exam._id);
        const allQuestions = yield question_1.default.find({
            examId: { $in: examIds },
        });
        console.log(`📊 Tìm thấy ${allQuestions.length} câu hỏi từ ${exams.length} đề thi`);
        if (allQuestions.length < 10) {
            return res.render("exam/practice-start", {
                user: res.locals.user,
                student,
                product,
                error: `Cần ít nhất 10 câu hỏi để tạo bài thi thử. Hiện tại chỉ có ${allQuestions.length} câu hỏi.`,
            });
        }
        // Chọn 100 câu hỏi ngẫu nhiên (hoặc ít hơn nếu không đủ)
        const questionCount = Math.min(100, allQuestions.length);
        const selectedQuestions = selectRandomQuestions(allQuestions, questionCount);
        // Xáo trộn các lựa chọn trong mỗi câu hỏi (chỉ 1 lần khi tạo bài)
        const processedQuestions = selectedQuestions.map((question, index) => {
            const questionObj = question.toObject ? question.toObject() : question;
            if (questionObj.answers && Array.isArray(questionObj.answers)) {
                questionObj.options = shuffleArray([...questionObj.answers]);
                delete questionObj.answers;
            }
            // Thêm số thứ tự câu hỏi
            questionObj.questionNumber = index + 1;
            // Ensure required fields exist
            if (!questionObj.text || !questionObj.options) {
                console.warn(`⚠️ Question ${index + 1} missing required fields:`, {
                    hasText: !!questionObj.text,
                    hasOptions: !!questionObj.options,
                });
            }
            return questionObj;
        });
        // Create optimized questions for storage (only essential fields)
        const optimizedQuestions = optimizeQuestions(processedQuestions);
        // Tạo record practice exam với status "in_progress"
        let practiceId = null;
        try {
            const newPracticeExam = new PracticeExamHistory_1.default({
                courseId: productId,
                courseName: product.name,
                userId,
                totalQuestions: processedQuestions.length,
                practiceType: "course-review",
                status: "in_progress",
                selectedQuestions: optimizedQuestions, // Đã được tối ưu theo định dạng mới (questionId và answerOrder)
                userAnswers: [],
                startedAt: new Date(),
            });
            yield newPracticeExam.save();
            practiceId = newPracticeExam._id.toString();
        }
        catch (error) {
            console.error("❌ Lỗi tạo record practice exam:", error);
            // Không return error ở đây, vẫn cho phép user làm bài
        }
        // Nếu là API call, trả về JSON response với practiceId
        if (isApiCall) {
            return res.status(201).json({
                success: true,
                message: "Đã tạo bài thi thử mới thành công",
                practiceId: practiceId,
                courseId: productId,
                courseName: product.name,
                totalQuestions: processedQuestions.length,
            });
        }
        // Mã hóa dữ liệu câu hỏi đầy đủ để hiển thị trên client
        const encryptionResult = (0, encryption_1.encryptObjectOptimized)({
            questions: prepareQuestionsForClient(processedQuestions), // Sử dụng hàm mới để chuẩn bị dữ liệu
            practiceInfo: {
                courseId: productId,
                courseName: product.name,
                totalQuestions: processedQuestions.length,
                duration: 60, // 60 phút
                type: "course-review",
            },
        });
        // Render trang làm bài thi thử
        res.render("exam/practice-exam", {
            user: res.locals.user,
            student,
            product,
            encryptedPracticeData: encryptionResult.appConfigData,
            encryptionToken: encryptionResult.token,
            encryptionSalt: encryptionResult.salt,
            practiceInfo: {
                courseId: productId,
                courseName: product.name,
                totalQuestions: processedQuestions.length,
                duration: 60,
                practiceId: practiceId, // Thêm practiceId
            },
        });
    }
    catch (error) {
        console.error("Lỗi khi bắt đầu thi thử:", error);
        if (isApiCall) {
            return res.status(500).json({
                success: false,
                message: "Đã xảy ra lỗi khi chuẩn bị bài thi thử",
                error: error.message,
            });
        }
        res.status(500).render("error", {
            message: "Đã xảy ra lỗi khi chuẩn bị bài thi thử",
            error: { status: 500 },
            user: res.locals.user,
        });
    }
});
exports.startPracticeExam = startPracticeExam;
/**
 * Hiển thị trang làm bài thi thử
 */
const takePracticeExam = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    // Function này có thể để trống vì chúng ta redirect trực tiếp từ startPracticeExam
    // hoặc sử dụng để hiển thị trang làm bài cụ thể nếu cần
    res.redirect("/home");
});
exports.takePracticeExam = takePracticeExam;
/**
 * Lưu kết quả thi thử
 */
const savePracticeExamResult = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const { courseId, courseName, score, totalQuestions, correctAnswers, duration, selectedQuestions, userAnswers, } = req.body;
        const userId = (_a = res.locals.user) === null || _a === void 0 ? void 0 : _a._id;
        if (!userId) {
            return res.status(401).json({
                success: false,
                message: "Người dùng chưa đăng nhập",
            });
        }
        // Validate dữ liệu
        if (!courseId ||
            !courseName ||
            score === undefined ||
            !totalQuestions ||
            !duration) {
            return res.status(400).json({
                success: false,
                message: "Thiếu thông tin cần thiết để lưu kết quả",
            });
        }
        // Kiểm tra xem có bài thi thử đang làm dở không (status = in_progress và trong vòng 60 phút)
        const existingPractice = yield PracticeExamHistory_1.default.findOne({
            userId,
            courseId,
            status: "in_progress",
            startedAt: {
                $gte: new Date(Date.now() - 60 * 60 * 1000), // 60 phút trước
            },
        }).sort({ startedAt: -1 });
        let practiceHistory;
        if (existingPractice) {
            // Chuyển đổi định dạng userAnswers để phù hợp với schema mới
            const formattedUserAnswers = Array.isArray(userAnswers)
                ? userAnswers.map((answer) => {
                    // Đảm bảo có selectedAnswerId
                    const selectedAnswerId = answer.selectedAnswerId || null;
                    if (!selectedAnswerId) {
                        console.warn("⚠️ Không có selectedAnswerId cho câu hỏi:", answer.questionId);
                    }
                    return {
                        questionId: answer.questionId,
                        selectedAnswerId: selectedAnswerId,
                        isCorrect: answer.isCorrect,
                    };
                })
                : [];
            // Update existing practice to completed
            practiceHistory = yield PracticeExamHistory_1.default.findByIdAndUpdate(existingPractice._id, {
                score: Math.round(score),
                totalQuestions,
                correctAnswers: correctAnswers || Math.round((score * totalQuestions) / 100),
                duration,
                userAnswers: formattedUserAnswers,
                status: "completed",
                completedAt: new Date(),
            }, { new: true });
        }
        else {
            // Chuyển đổi định dạng userAnswers và selectedQuestions để phù hợp với schema mới
            const formattedUserAnswers = Array.isArray(userAnswers)
                ? userAnswers.map((answer) => {
                    // Đảm bảo có selectedAnswerId
                    const selectedAnswerId = answer.selectedAnswerId || null;
                    if (!selectedAnswerId) {
                        console.warn("⚠️ Không có selectedAnswerId cho câu hỏi:", answer.questionId);
                    }
                    return {
                        questionId: answer.questionId,
                        selectedAnswerId: selectedAnswerId,
                        isCorrect: answer.isCorrect,
                    };
                })
                : [];
            const formattedSelectedQuestions = Array.isArray(selectedQuestions)
                ? selectedQuestions.map((question) => {
                    if (typeof question === "object" && question.questionId) {
                        return {
                            questionId: question.questionId,
                            answerOrder: question.answerOrder || [1, 2, 3, 4],
                        };
                    }
                    else if (typeof question === "object" && question._id) {
                        return {
                            questionId: question._id,
                            answerOrder: [1, 2, 3, 4], // Default order
                        };
                    }
                    else {
                        return {
                            questionId: question,
                            answerOrder: [1, 2, 3, 4], // Default order
                        };
                    }
                })
                : [];
            // Tạo bản ghi lịch sử thi thử mới (đã hoàn thành)
            practiceHistory = new PracticeExamHistory_1.default({
                courseId,
                courseName,
                userId,
                score: Math.round(score),
                totalQuestions,
                correctAnswers: correctAnswers || Math.round((score * totalQuestions) / 100),
                duration,
                practiceType: "course-review",
                status: "completed",
                selectedQuestions: formattedSelectedQuestions,
                userAnswers: formattedUserAnswers,
                startedAt: new Date(Date.now() - duration * 1000), // Tính ngược từ thời gian hoàn thành
                completedAt: new Date(),
            });
            yield practiceHistory.save();
        }
        return res.status(201).json({
            success: true,
            message: "Đã lưu kết quả thi thử thành công",
            practiceHistory: {
                id: practiceHistory._id,
                score: practiceHistory.score,
                totalQuestions: practiceHistory.totalQuestions,
                correctAnswers: practiceHistory.correctAnswers,
                duration: practiceHistory.duration,
                completedAt: practiceHistory.completedAt,
            },
        });
    }
    catch (error) {
        console.error("Lỗi khi lưu kết quả thi thử:", error);
        return res.status(500).json({
            success: false,
            message: "Đã xảy ra lỗi khi lưu kết quả thi thử",
            error: error.message,
        });
    }
});
exports.savePracticeExamResult = savePracticeExamResult;
exports.default = new ExamController();
//# sourceMappingURL=exam.controller.js.map