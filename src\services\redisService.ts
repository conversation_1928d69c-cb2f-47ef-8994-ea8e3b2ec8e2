import Redis from "ioredis";
import dotenv from "dotenv";
dotenv.config();
/**
 * Redis Service cho việc quản lý activeDevices cache
 * Thay thế in-memory Map với Redis persistence
 */

interface RedisConfig {
  host: string;
  port: number;
  password?: string;
  retryDelayOnFailover: number;
  maxRetriesPerRequest: number;
  lazyConnect: boolean;
  connectTimeout: number;
  commandTimeout: number;
}

class RedisActiveDeviceService {
  private redis: Redis | null = null;
  private isConnected: boolean = false;
  private fallbackMap: Map<string, string> = new Map(); // Fallback cho khi Redis fail
  private readonly ACTIVE_DEVICES_PREFIX = "active_device:";
  private readonly DEFAULT_TTL = 60 * 24 * 60 * 60; // 60 ngày (seconds)

  constructor() {
    this.initializeRedis();
  }

  private async initializeRedis(): Promise<void> {
    try {
      const config: RedisConfig = {
        host: process.env.REDIS_HOST || "localhost",
        port: parseInt(process.env.REDIS_PORT || "6379"),
        password: process.env.REDIS_PASSWORD || undefined,
        retryDelayOnFailover: 100,
        maxRetriesPerRequest: 3,
        lazyConnect: true,
        connectTimeout: 10000,
        commandTimeout: 5000,
      };

      this.redis = new Redis(config);

      // Event handlers
      this.redis.on("connect", () => {
        console.log("✅ Redis connected successfully");
        this.isConnected = true;
      });

      this.redis.on("error", (error) => {
        console.error("❌ Redis connection error:", error.message);
        this.isConnected = false;
      });

      this.redis.on("close", () => {
        console.log("⚠️  Redis connection closed");
        this.isConnected = false;
      });

      this.redis.on("reconnecting", () => {
        console.log("🔄 Redis reconnecting...");
      });

      // Thử kết nối
      await this.redis.connect();
    } catch (error) {
      console.error("❌ Failed to initialize Redis:", error);
      this.isConnected = false;
    }
  }

  private getKey(userId: string): string {
    return `${this.ACTIVE_DEVICES_PREFIX}${userId}`;
  }

  /**
   * Set active device cho user với TTL
   */
  async setActiveDevice(userId: string, clientId: string): Promise<void> {
    try {
      if (this.isConnected && this.redis) {
        const key = this.getKey(userId);
        await this.redis.setex(key, this.DEFAULT_TTL, clientId);

        // Đồng bộ với fallback map
        this.fallbackMap.set(userId, clientId);
      } else {
        // Fallback khi Redis không khả dụng
        console.warn("⚠️  Redis not available, using fallback memory storage");
        this.fallbackMap.set(userId, clientId);
      }
    } catch (error) {
      console.error("❌ Error setting active device in Redis:", error);
      // Fallback nếu Redis operation fail
      this.fallbackMap.set(userId, clientId);
    }
  }

  /**
   * Get active device cho user
   */
  async getActiveDevice(userId: string): Promise<string | null> {
    try {
      if (this.isConnected && this.redis) {
        const key = this.getKey(userId);
        const clientId = await this.redis.get(key);

        if (clientId) {
          // Đồng bộ với fallback map
          this.fallbackMap.set(userId, clientId);
          return clientId;
        }

        // Không tìm thấy trong Redis, check fallback
        return this.fallbackMap.get(userId) || null;
      } else {
        // Redis không khả dụng, dùng fallback
        return this.fallbackMap.get(userId) || null;
      }
    } catch (error) {
      console.error("❌ Error getting active device from Redis:", error);
      // Fallback nếu Redis operation fail
      return this.fallbackMap.get(userId) || null;
    }
  }

  /**
   * Check có active device hay không
   */
  async hasActiveDevice(userId: string): Promise<boolean> {
    const device = await this.getActiveDevice(userId);
    return device !== null;
  }

  /**
   * Check active device có phải là clientId cụ thể hay không
   */
  async isActiveDevice(userId: string, clientId: string): Promise<boolean> {
    const activeDevice = await this.getActiveDevice(userId);
    return activeDevice === clientId;
  }

  /**
   * Check nếu user có active device NHƯNG không phải clientId này
   */
  async hasActiveDeviceButNotThis(
    userId: string,
    clientId: string
  ): Promise<boolean> {
    const activeDevice = await this.getActiveDevice(userId);
    return activeDevice !== null && activeDevice !== clientId;
  }

  /**
   * Remove active device cho user
   */
  async removeActiveDevice(userId: string): Promise<void> {
    try {
      if (this.isConnected && this.redis) {
        const key = this.getKey(userId);
        await this.redis.del(key);
      }

      // Remove từ fallback map cũng
      this.fallbackMap.delete(userId);
    } catch (error) {
      console.error("❌ Error removing active device from Redis:", error);
      // Vẫn remove từ fallback map
      this.fallbackMap.delete(userId);
    }
  }

  /**
   * Refresh TTL cho active device
   */
  async refreshActiveDeviceTTL(userId: string): Promise<void> {
    try {
      if (this.isConnected && this.redis) {
        const key = this.getKey(userId);
        await this.redis.expire(key, this.DEFAULT_TTL);
      }
    } catch (error) {
      console.error("❌ Error refreshing TTL for active device:", error);
    }
  }

  /**
   * Khôi phục từ database (tương tự restoreActiveDevices cũ)
   */
  async restoreFromDatabase(
    users: Array<{ _id: string; lastActiveDevice: string }>
  ): Promise<number> {
    let count = 0;

    for (const user of users) {
      if (user.lastActiveDevice) {
        await this.setActiveDevice(user._id.toString(), user.lastActiveDevice);
        count++;
      }
    }

    console.log(`🔄 Restored ${count} active devices to Redis`);
    return count;
  }

  /**
   * Get tất cả active devices (cho debug hoặc monitoring)
   */
  async getAllActiveDevices(): Promise<{ [userId: string]: string }> {
    const result: { [userId: string]: string } = {};

    try {
      if (this.isConnected && this.redis) {
        const pattern = `${this.ACTIVE_DEVICES_PREFIX}*`;
        const keys = await this.redis.keys(pattern);

        if (keys.length > 0) {
          const values = await this.redis.mget(...keys);

          keys.forEach((key, index) => {
            if (values[index]) {
              const userId = key.replace(this.ACTIVE_DEVICES_PREFIX, "");
              result[userId] = values[index]!;
            }
          });
        }
      }

      // Merge với fallback map
      this.fallbackMap.forEach((clientId, userId) => {
        if (!result[userId]) {
          result[userId] = clientId;
        }
      });
    } catch (error) {
      console.error("❌ Error getting all active devices:", error);
      // Return fallback map nếu Redis fail
      this.fallbackMap.forEach((clientId, userId) => {
        result[userId] = clientId;
      });
    }

    return result;
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<{ redis: boolean; fallback: boolean }> {
    let redisHealth = false;

    try {
      if (this.redis && this.isConnected) {
        await this.redis.ping();
        redisHealth = true;
      }
    } catch (error) {
      redisHealth = false;
    }

    return {
      redis: redisHealth,
      fallback: this.fallbackMap.size >= 0, // Fallback luôn available
    };
  }

  /**
   * Cleanup - đóng Redis connection
   */
  async cleanup(): Promise<void> {
    try {
      if (this.redis) {
        await this.redis.quit();
        this.redis = null;
        this.isConnected = false;
        console.log("✅ Redis connection closed cleanly");
      }
    } catch (error) {
      console.error("❌ Error closing Redis connection:", error);
    }
  }

  /**
   * Get connection status
   */
  getConnectionStatus(): { isConnected: boolean; fallbackSize: number } {
    return {
      isConnected: this.isConnected,
      fallbackSize: this.fallbackMap.size,
    };
  }
}

// Singleton instance
const redisActiveDeviceService = new RedisActiveDeviceService();

export default redisActiveDeviceService;
export { RedisActiveDeviceService };
