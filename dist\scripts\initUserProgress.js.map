{"version": 3, "file": "initUserProgress.js", "sourceRoot": "", "sources": ["../../src/scripts/initUserProgress.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA,wDAAgC;AAChC,oDAA4B;AAC5B,2EAA0E;AAE1E,6BAA6B;AAC7B,gBAAM,CAAC,MAAM,EAAE,CAAC;AAEhB,qBAAqB;AACrB,kBAAQ;KACL,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,uCAAuC,CAAC;KAC3E,IAAI,CAAC,GAAS,EAAE;IACf,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;IAE7C,OAAO,CAAC,GAAG,CAAC,gDAAgD,CAAC,CAAC;IAE9D,IAAI,CAAC;QACH,6BAA6B;QAC7B,MAAM,IAAI,GAAG,OAAO,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC;QAC/C,MAAM,OAAO,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC,OAAO,CAAC;QACtD,MAAM,IAAI,GAAG,OAAO,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC;QAC/C,MAAM,WAAW,GAAG,OAAO,CAAC,uBAAuB,CAAC,CAAC,OAAO,CAAC;QAC7D,MAAM,YAAY,GAAG,OAAO,CAAC,wBAAwB,CAAC,CAAC,OAAO,CAAC;QAE/D,2BAA2B;QAC3B,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAClC,OAAO,CAAC,GAAG,CAAC,YAAY,KAAK,CAAC,MAAM,aAAa,CAAC,CAAC;QAEnD,mCAAmC;QACnC,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACxC,OAAO,CAAC,GAAG,CAAC,YAAY,QAAQ,CAAC,MAAM,UAAU,CAAC,CAAC;QAEnD,2CAA2C;QAC3C,MAAM,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;QAClC,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;QAE3D,sDAAsD;QACtD,MAAM,MAAM,GAAG,MAAM,IAAA,6CAAsB,GAAE,CAAC;QAE9C,IAAI,MAAM,EAAE,CAAC;YACX,OAAO,CAAC,GAAG,CAAC,gDAAgD,CAAC,CAAC;QAChE,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,KAAK,CAAC,8CAA8C,CAAC,CAAC;QAChE,CAAC;QAED,mBAAmB;QACnB,MAAM,aAAa,GAAG,MAAM,YAAY,CAAC,cAAc,EAAE,CAAC;QAC1D,OAAO,CAAC,GAAG,CAAC,6CAA6C,aAAa,EAAE,CAAC,CAAC;QAE1E,8BAA8B;QAC9B,MAAM,cAAc,GAAG,MAAM,YAAY,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;QACjE,OAAO,CAAC,GAAG,CACT,2BAA2B,EAC3B,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,IAAI,EAAE,CAAC,CAAC,CACxC,CAAC;IACJ,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,6CAA6C,EAAE,KAAK,CAAC,CAAC;IACtE,CAAC;IAED,eAAe;IACf,MAAM,kBAAQ,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;IAClC,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;IAC1C,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC,CAAA,CAAC;KACD,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE;IACb,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,GAAG,CAAC,CAAC;IAC7C,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC,CAAC"}