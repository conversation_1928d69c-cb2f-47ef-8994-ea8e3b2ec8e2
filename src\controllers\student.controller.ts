import { Request, Response } from "express";
import studentService from "../service/student.service";
import { responseError } from "../util/errorhandler";

class StudentController {
  /**
   * <PERSON><PERSON><PERSON> danh sách sinh viên theo khóa học
   */
  async getStudentsByCourse(req: Request, res: Response) {
    try {
      const { productId } = req.params;
      const { page } = req.query as unknown as { page: number };

      const result = await studentService.getListStudentByProductId(
        productId,
        page
      );

      return res.status(200).json({
        message: "Success",
        data: result,
      });
    } catch (error: any) {
      return responseError(res, error.message, 400);
    }
  }

  /**
   * Tạo sinh viên mới
   */
  async createStudent(req: Request, res: Response) {
    try {
      const studentData = req.body;
      const newStudent = await studentService.createStudent(studentData);

      return res.status(201).json({
        message: "Success",
        data: newStudent,
      });
    } catch (error: any) {
      return responseError(res, error.message, 400);
    }
  }

  /**
   * Xóa sinh viên
   */
  async deleteStudent(req: Request, res: Response) {
    try {
      const { studentId } = req.params;

      const result = await studentService.deleteStudent(studentId);

      return res.status(200).json({
        message: "Success",
        data: result,
      });
    } catch (error: any) {
      return responseError(res, error.message, 400);
    }
  }

  async searchStudentByProductId(req: Request, res: Response) {
    try {
      const { productId } = req.params;
      const { keyword } = req.query as unknown as { keyword: string };
      const { page } = req.query as unknown as { page: number };

      const result = await studentService.searchStudentByProductId(
        keyword,
        productId,
        page
      );

      return res.status(200).json({
        message: "Success",
        data: result,
      });
    } catch (error: any) {
      return responseError(res, error.message, 400);
    }
  }

  async getCountStudentByProductId(req: Request, res: Response) {
    try {
      const { productId } = req.params;
      const result = await studentService.getCountStudentByProductId(productId);

      return res.status(200).json({
        message: "Success",
        data: result,
      });
    } catch (error: any) {
      return responseError(res, error.message, 400);
    }
  }

  /**
   * Lấy danh sách đề thi của sinh viên theo khóa học
   */
  async getStudentExams(req: Request, res: Response) {
    try {
      const { studentId, productId } = req.params;

      // Sử dụng middleware validateStudentCourse trước đó để đảm bảo sinh viên có quyền truy cập
      // Sau đó chuyển đến service để lấy danh sách đề thi
      const result = await studentService.getStudentExams(studentId, productId);

      return res.status(200).json({
        message: "Success",
        data: result,
      });
    } catch (error: any) {
      return responseError(res, error.message, 400);
    }
  }

  /**
   * Lấy danh sách bài kiểm tra của sinh viên theo khóa học
   */
  async getStudentTests(req: Request, res: Response) {
    try {
      const { studentId, productId } = req.params;

      // Sử dụng middleware validateStudentCourse trước đó để đảm bảo sinh viên có quyền truy cập
      // Sau đó chuyển đến service để lấy danh sách bài kiểm tra
      const result = await studentService.getStudentTests(studentId, productId);

      return res.status(200).json({
        message: "Success",
        data: result,
      });
    } catch (error: any) {
      return responseError(res, error.message, 400);
    }
  }
}

export default new StudentController();
