<!-- <PERSON><PERSON> cương -->
<div class="bg-gray-50 rounded-lg p-3 sm:p-6">
  <div class="flex items-center justify-between mb-6">
    <h2 class="text-xl font-semibold text-gray-700 flex items-center">
      <i class="fas fa-list-ul mr-2 text-indigo-500"></i> <PERSON><PERSON> cương môn học
    </h2>
    <div class="text-sm text-gray-500">
      Tổng:
      <span class="font-medium" id="totalQuestionsCount">
        <% if (questionsByExam && questionsByExam.length > 0) { %> <%=
        questionsByExam.reduce((total, exam) => total + exam.questions.length,
        0) %> <% } else { %> -- <% } %>
      </span>
    </div>
  </div>

  <% if ((!questionsByExam || questionsByExam.length === 0) &&
  !encryptedQuestions) { %>
  <div class="bg-blue-50 border-l-4 border-blue-400 p-4">
    <div class="flex">
      <div class="flex-shrink-0">
        <i class="fas fa-info-circle text-blue-400"></i>
      </div>
      <div class="ml-3">
        <p class="text-sm text-blue-700">
          Chưa có câu hỏi nào trong các đề thi của môn học này.
        </p>
      </div>
    </div>
  </div>
  <% } else { %>
  <!-- Controls: Search, Filter dropdown và Button hiển thị đáp án -->
  <div class="mb-4 sm:mb-6 space-y-3 sm:space-y-4">
    <!-- Row 1: Search box -->
    <div class="flex items-center">
      <div class="relative flex-1">
        <div
          class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
        >
          <i class="fas fa-search text-gray-400"></i>
        </div>
        <input
          type="text"
          id="searchInput"
          placeholder="Tìm kiếm câu hỏi..."
          class="block w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm"
        />
        <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
          <button
            onclick="clearSearch()"
            class="text-gray-400 hover:text-gray-600 focus:outline-none p-1"
            id="clearSearchBtn"
            style="display: none"
          >
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- Row 2: Filter -->
    <div class="flex items-center gap-2">
      <!-- Icon filter -->
      <div class="flex-shrink-0">
        <i class="fas fa-filter text-indigo-500 text-sm"></i>
      </div>

      <!-- Dropdown lọc theo đề -->
      <select
        id="examFilter"
        onchange="filterByExam()"
        class="px-2 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 bg-white text-xs sm:text-sm flex-1 min-w-0"
      >
        <option value="all">Hiển thị tất cả</option>
        <!-- Options sẽ được thêm bằng JavaScript -->
      </select>
    </div>

    <!-- Search results info -->
    <div
      id="searchInfo"
      class="text-sm text-gray-600 bg-blue-50 px-3 py-2 rounded-lg"
      style="display: none"
    >
      <!-- <i class="fas fa-info-circle mr-1 text-blue-500"></i> -->
      <span id="searchResultCount" class="hidden">0</span>
    </div>
  </div>

  <!-- Scrollable container cho infinite scrolling -->
  <div
    id="questionsScrollContainer"
    class="bg-gray-50 rounded-lg border border-gray-200 shadow-inner"
    style="height: 70vh; overflow-y: auto"
  >
    <div id="questionsContainer" class="p-1 sm:p-4">
      <!-- Loading state cho encrypted data -->
      <% if (encryptedQuestions) { %>
      <div class="text-center py-8" id="decryptionLoading">
        <div class="text-indigo-600 mb-2">
          <i class="fas fa-cog text-2xl animate-spin"></i>
        </div>
        <p class="text-gray-600">Đang tải cấu hình ứng dụng...</p>
        <div class="mt-2 w-8 h-8 mx-auto">
          <svg
            class="animate-spin"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              class="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              stroke-width="4"
            ></circle>
            <path
              class="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
        </div>
      </div>
      <% } else { %>
      <!-- Placeholder cho dữ liệu thường -->
      <div class="text-center py-8 text-gray-500">
        <i class="fas fa-list text-2xl"></i>
        <p>Đang tải câu hỏi...</p>
      </div>
      <% } %>
    </div>
  </div>

  <!-- Hidden data chứa tất cả câu hỏi (có thể được mã hóa) -->
  <% if (encryptedQuestions) { %>
  <script type="application/json" id="appConfigData">
    {
      "payload": "<%- encryptedQuestions %>",
      "token": "<%- encryptionKey %>",
      "salt": "<%- encryptionIV %>"
    }
  </script>
  <% } else { %>
  <script type="application/json" id="allQuestionsData">
    <%- JSON.stringify(questionsByExam) %>
  </script>
  <% } %> <% } %>
</div>

<!-- Sửa lại hàm renderQuestionHTML trong script -->
<script>
  // Hàm mới để render câu hỏi HTML
  function renderQuestionHTML(question, index) {
    const examName = question.examName || "";
    const questionId = question._id || "";
    const examId = question.examId || "";

    let html = `
      <div class="question-item bg-white rounded-lg border border-gray-200 p-4 mb-4 hover:shadow-sm transition-all" 
           data-question-index="${index}" 
           data-question-id="${questionId}" 
           id="question-${index}" 
           data-exam-name="${examName}">
        <div class="flex items-start justify-between mb-3">
          <h4 class="font-medium text-gray-800 text-sm">
            Câu ${index + 1}
            <span class="text-xs text-gray-500">(${examName})</span>
          </h4>
          <button class="add-to-memory-btn text-gray-400 hover:text-indigo-500 px-2 focus:outline-none transition-colors" 
                  onclick="addToMemory('${questionId}', '${examId}')" title="Thêm vào ghi nhớ">
            <i class="far fa-bookmark"></i>
          </button>
        </div>
        
        <div class="text-gray-700 mb-4 text-sm leading-relaxed break-words question-text">
          ${question.text || "Nội dung câu hỏi không có sẵn"}
        </div>
        
        <div class="answers-container space-y-2" data-question="${index}">
    `;

    // Thêm các đáp án
    (question.answers || question.options || []).forEach(
      (answer, answerIndex) => {
        const optionLabel = String.fromCharCode(65 + answerIndex);
        const isCorrect = answer.isCorrect || false;
        let answerText = answer.text || answer || "Đáp án không có sẵn";

        // Loại bỏ tiền tố A., B., C., D. nếu có
        if (typeof answerText === "string") {
          const prefixRegex = /^\s*(?:[A-D]|[1-9])[\.\)][\s\u00A0]*/i;
          if (prefixRegex.test(answerText)) {
            answerText = answerText.replace(prefixRegex, "");
          }
        }

        // Thiết lập style cho đáp án đúng
        let borderColor = "border-gray-200";
        let bgColor = "";
        if (isCorrect) {
          borderColor = "border-green-200";
          bgColor = "bg-green-50";
        }

        html += `
        <div class="answer-option flex items-center p-3 border ${borderColor} ${bgColor} rounded-lg text-sm answer-text" 
             data-correct="${isCorrect}" data-answer-index="${answerIndex}">
          <span class="font-medium mr-3 text-xs bg-gray-100 px-2 py-1 rounded-full">${optionLabel}</span>
          <span class="flex-1 break-words">${answerText}</span>
          ${
            isCorrect
              ? '<span class="correct-indicator ml-2"><i class="fas fa-check text-green-600"></i></span>'
              : ""
          }
        </div>
      `;
      }
    );

    html += `
        </div>
      </div>
    `;

    return html;
  }
</script>
