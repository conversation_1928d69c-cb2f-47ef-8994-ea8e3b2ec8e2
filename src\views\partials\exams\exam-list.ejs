<!-- <PERSON><PERSON> sách đề thi -->
<% if (typeof announcement !== "undefined" && announcement) { %> <%-
include('../announcement-modal') %> <% } %>
<div class="bg-gray-50 rounded-lg p-2 sm:p-6">
  <div class="flex items-center justify-between mb-6">
    <h2 class="text-xl font-semibold text-gray-700 flex items-center">
      <i class="fas fa-file-alt mr-2 text-indigo-500"></i> Danh sách đề thi
    </h2>
    <div class="text-sm text-gray-500">
      Tổng số: <span class="font-medium"><%= exams.length %></span>
    </div>
  </div>

  <% if (!exams || exams.length === 0) { %>
  <div class="bg-blue-50 border-l-4 border-blue-400 p-4">
    <div class="flex">
      <div class="flex-shrink-0">
        <i class="fas fa-info-circle text-blue-400"></i>
      </div>
      <div class="ml-3">
        <p class="text-sm text-blue-700">Ch<PERSON>a có đề thi nào cho môn học này.</p>
      </div>
    </div>
  </div>
  <% } else { %>
  <div class="overflow-x-auto -mx-2 sm:mx-0">
    <table class="min-w-full bg-white rounded-lg overflow-hidden">
      <thead class="bg-gray-100">
        <tr>
          <th
            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
          >
            Tên đề thi
          </th>
          <th
            class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider"
          >
            Thời gian
          </th>
          <th
            class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider"
          >
            Trạng thái
          </th>
          <th
            class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider"
          >
            Thao tác
          </th>
        </tr>
      </thead>
      <tbody class="divide-y divide-gray-200">
        <% exams.forEach(exam => { %>
        <tr class="hover:bg-gray-50">
          <% if (exam.status === 'active') { %>
          <td
            class="px-6 py-4 whitespace-nowrap cursor-pointer hover:bg-indigo-50 transition-colors duration-200"
            onclick="openExamModal('<%= exam._id %>', '<%= exam.name %>')"
            title="Click để làm bài"
          >
            <div
              class="font-medium text-gray-900 hover:text-indigo-600 transition-colors duration-200"
            >
              <%= exam.name %>
            </div>
            <div class="text-sm text-gray-500">
              <%= exam.description || 'Không có mô tả' %>
            </div>
          </td>
          <% } else { %>
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="font-medium text-gray-900"><%= exam.name %></div>
            <div class="text-sm text-gray-500">
              <%= exam.description || 'Không có mô tả' %>
            </div>
          </td>
          <% } %>
          <td class="px-6 py-4 whitespace-nowrap text-center">
            <div class="text-sm text-gray-900"><%= exam.duration %> phút</div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-center">
            <span
              class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <%= exam.status === 'active' ? 'bg-green-100 text-green-800' : exam.status === 'inactive' ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800' %>"
            >
              <%= exam.status %>
            </span>
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-center">
            <% if (exam.status === 'active') { %>
            <button
              onclick="openExamModal('<%= exam._id %>', '<%= exam.name %>')"
              class="text-indigo-600 hover:text-indigo-900 px-2 focus:outline-none"
            >
              <i class="fas fa-play-circle mr-1"></i> Làm bài
            </button>
            <% } else { %>
            <span class="text-gray-400 px-2">Chưa mở</span>
            <% } %>
          </td>
        </tr>
        <% }) %>
      </tbody>
    </table>
  </div>
  <% } %>
</div>
