<div class="max-w-3xl mx-auto px-2 sm:px-0">
  <div class="bg-white sm:rounded-lg sm:shadow-md p-4 sm:p-8">
    <div class="text-center mb-6 sm:mb-8">
      <div
        class="bg-red-100 text-red-700 p-3 sm:p-4 rounded-lg inline-block mb-3 sm:mb-4"
      >
        <i class="fas fa-exclamation-circle text-3xl sm:text-4xl"></i>
      </div>
      <h1 class="text-lg sm:text-2xl font-bold text-gray-800 mb-2 px-2">
        <%= message %>
      </h1>
      <p class="text-gray-600 text-sm sm:text-base">
        <%= error.status %> <% if (error.description) { %> - <%=
        error.description %> <% } %>
      </p>
    </div>

    <div class="mt-6 sm:mt-8 text-center px-2 sm:px-0">
      <div
        class="flex flex-col sm:flex-row justify-center items-center space-y-3 sm:space-y-0 sm:space-x-4"
      >
        <% if (typeof showRetryButton !== 'undefined' && showRetryButton &&
        typeof productId !== 'undefined') { %>
        <!-- Button Thi lại -->
        <button
          onclick="startNewPracticeFromError()"
          class="w-full sm:w-auto bg-orange-600 text-white rounded-lg px-6 py-3 hover:bg-orange-700 transition font-medium text-sm sm:text-base"
        >
          <i class="fas fa-redo mr-2"></i> Thi lại
        </button>
        <% } %>

        <a
          href="/home"
          class="w-full sm:w-auto bg-indigo-600 text-white rounded-lg px-6 py-3 hover:bg-indigo-700 transition font-medium text-center text-sm sm:text-base block"
        >
          <i class="fas fa-home mr-2"></i> Quay lại trang chủ
        </a>
      </div>
    </div>

    <% if (typeof showRetryButton !== 'undefined' && showRetryButton && typeof
    productId !== 'undefined') { %>
    <script>
      async function startNewPracticeFromError() {
        const productId = "<%= productId %>";

        console.log("🔄 Thi lại từ error page...");

        try {
          // Tạo bài thi mới
          const response = await fetch(`/exam/practice/${productId}/start`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            credentials: "include",
            body: JSON.stringify({
              action: "new",
            }),
          });

          const result = await response.json();

          if (result.success && result.practiceId) {
            console.log("✅ Đã tạo bài thi mới:", result.practiceId);

            // Chuyển hướng đến bài thi mới
            const newPracticeUrl = `/course/${productId}/${result.practiceId}/practice-exam`;
            window.location.href = newPracticeUrl;
          } else {
            throw new Error(result.message || "Không thể tạo bài thi mới");
          }
        } catch (error) {
          console.error("❌ Lỗi thi lại:", error);
          alert("Có lỗi khi tạo bài thi mới. Vui lòng thử lại.");
        }
      }
    </script>
    <% } %>
  </div>
</div>
