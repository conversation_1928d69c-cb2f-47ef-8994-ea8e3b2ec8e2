import { Request, Response, NextFunction, RequestHandler } from "express";
import express from "express";
import { checkStudentCourseAccess } from "../middlewares/student.middleware";
import asyncHandler from "../util/asynHandler";

const router = express.Router();

// Route thi thử mới với practiceId
router.get(
  "/course/:productId/:practiceId/practice-exam",
  checkStudentCourseAccess,
  asyncHandler(async (req: Request, res: Response) => {
    const { productId, practiceId } = req.params;
    const userId = res.locals.user?._id;

    if (!userId) {
      return res.redirect("/login");
    }

    try {
      // Import models
      const PracticeExamHistory = (
        await import("../models/PracticeExamHistory")
      ).default;
      const Product = (await import("../models/products")).default;

      // Kiểm tra practice exam có tồn tại và thuộc về user không
      const practiceExam = await PracticeExamHistory.findOne({
        _id: practiceId,
        userId: userId,
        courseId: productId,
      });

      if (!practiceExam) {
        return res.status(404).render("error", {
          message: "Không tìm thấy bài thi hoặc bạn không có quyền truy cập",
          error: { status: 404 },
          user: res.locals.user,
        });
      }

      // Kiểm tra trạng thái của practice exam
      const now = new Date();
      const timeElapsed = Math.floor(
        (now.getTime() - practiceExam.startedAt.getTime()) / 1000
      );
      const timeRemaining = 60 * 60 - timeElapsed; // 60 phút

      if (practiceExam.status === "completed") {
        // Bài thi đã hoàn thành
        return res.render("error", {
          message:
            "Bài thi này đã hoàn thành. Bạn có thể thi lại bằng cách tạo bài thi mới.",
          error: { status: 410 }, // Gone
          user: res.locals.user,
          showRetryButton: true,
          productId: productId,
        });
      }

      if (practiceExam.status === "time_up") {
        // Bài thi đã hết giờ
        return res.render("error", {
          message:
            "Bài thi này đã hết thời gian. Bạn có thể thi lại bằng cách tạo bài thi mới.",
          error: { status: 410 }, // Gone
          user: res.locals.user,
          showRetryButton: true,
          productId: productId,
        });
      }

      if (practiceExam.status === "abandoned") {
        // Bài thi đã bị hủy
        return res.render("error", {
          message:
            "Bài thi này đã bị hủy. Bạn có thể thi lại bằng cách tạo bài thi mới.",
          error: { status: 410 }, // Gone
          user: res.locals.user,
          showRetryButton: true,
          productId: productId,
        });
      }

      if (timeRemaining <= 0) {
        // Quá thời gian - tự động cập nhật thành time_up
        await PracticeExamHistory.findByIdAndUpdate(practiceId, {
          status: "time_up",
          completedAt: new Date(),
        });

        return res.render("error", {
          message:
            "Bài thi này đã quá thời gian cho phép (60 phút). Bạn có thể thi lại bằng cách tạo bài thi mới.",
          error: { status: 410 }, // Gone
          user: res.locals.user,
          showRetryButton: true,
          productId: productId,
        });
      }

      // Bài thi đang trong trạng thái "in_progress" và còn thời gian

      // Import startPracticeExam từ exam controller
      const { startPracticeExam } = await import(
        "../controllers/exam.controller"
      );

      // Gọi startPracticeExam với action = "continue" để load existing practice
      req.body = { action: "continue", practiceId };
      await startPracticeExam(req, res);
    } catch (error) {
      console.error("❌ Error checking practice exam:", error);
      return res.status(500).render("error", {
        message: "Có lỗi xảy ra khi kiểm tra bài thi",
        error: { status: 500 },
        user: res.locals.user,
      });
    }
  })
);

export default router;
