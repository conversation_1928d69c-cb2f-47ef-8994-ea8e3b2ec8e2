"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getActiveDevice = exports.getUserConnections = exports.checkPendingForceLogout = exports.logoutOtherDevices = exports.restoreActiveDevices = exports.setCurrentDeviceAsActive = exports.removeConnection = exports.setupSSEConnection = exports.addConnection = void 0;
const User_1 = __importDefault(require("../models/User"));
/**
 * Server-Sent Events Service
 * Quản lý các kết nối SSE và gửi thông báo đến client
 */
// Lưu trữ tất cả các kết nối theo userId
const connections = new Map();
// Lưu trữ các interval ping theo connectionId (userId:clientId)
const pingIntervals = new Map();
// Lưu trữ thiết bị đăng nhập gần đây nhất của mỗi user
const activeDevices = new Map();
// Lưu trữ thông tin forced logout để xử lý khi thiết bị kết nối lại
const pendingForceLogouts = new Map();
// Tạo connectionId
function getConnectionId(userId, clientId) {
    return `${userId}:${clientId}`;
}
// Thêm kết nối SSE mới
const addConnection = (userId, clientId, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, _b;
    try {
        // Kiểm tra xem thiết bị này có phải là thiết bị hoạt động mới nhất không
        const currentActiveDevice = yield User_1.default.findById(userId).select("lastActiveDevice");
        const dbActiveDevice = currentActiveDevice === null || currentActiveDevice === void 0 ? void 0 : currentActiveDevice.lastActiveDevice;
        // Đồng bộ thông tin active device từ DB với bộ nhớ
        if (dbActiveDevice &&
            (!activeDevices.has(userId) ||
                activeDevices.get(userId) !== dbActiveDevice)) {
            // console.log(
            //   `Syncing active device for user ${userId} from DB: ${dbActiveDevice}`
            // );
            activeDevices.set(userId, dbActiveDevice);
        }
        // Kiểm tra xem kết nối đã tồn tại chưa
        if (connections.has(userId) && ((_a = connections.get(userId)) === null || _a === void 0 ? void 0 : _a.has(clientId))) {
            const connectionId = getConnectionId(userId, clientId);
            // console.log(
            //   `Found existing connection for ${connectionId}, cleaning up...`
            // );
            // Xóa interval ping cũ nếu có
            if (pingIntervals.has(connectionId)) {
                clearInterval(pingIntervals.get(connectionId));
                pingIntervals.delete(connectionId);
            }
        }
        if (!connections.has(userId)) {
            connections.set(userId, new Map());
        }
        // Lưu kết nối vào Map với key là clientId
        (_b = connections.get(userId)) === null || _b === void 0 ? void 0 : _b.set(clientId, res);
        // console.log(
        //   `New SSE connection added for user ${userId}, clientId: ${clientId}`
        // );
        // Cấu hình kết nối SSE
        (0, exports.setupSSEConnection)(res, userId, clientId);
        // Kiểm tra xem thiết bị này có phải là thiết bị active mới nhất không
        // Nếu không phải, kiểm tra và xử lý forced logout
        if (activeDevices.has(userId) && activeDevices.get(userId) !== clientId) {
            // console.log(
            //   `Client ${clientId} is not the newest device (${activeDevices.get(
            //     userId
            //   )}) for user ${userId}`
            // );
            const shouldForceLogout = yield (0, exports.checkPendingForceLogout)(userId, clientId, res);
            if (shouldForceLogout) {
                // console.log(
                //   `Forcing logout of old device ${clientId} for user ${userId}`
                // );
            }
        }
        // Xử lý khi client ngắt kết nối
        res.on("close", () => {
            // console.log(
            //   `Connection closed for user ${userId}, clientId: ${clientId}`
            // );
            (0, exports.removeConnection)(userId, clientId);
        });
    }
    catch (error) {
        // console.error(
        //   `Error in addConnection for user ${userId}, clientId: ${clientId}:`,
        //   error
        // );
    }
});
exports.addConnection = addConnection;
// Thiết lập kết nối SSE
const setupSSEConnection = (res, userId, clientId) => {
    try {
        // Gửi event kết nối thành công
        res.write(`event: connected\ndata: ${JSON.stringify({
            connected: true,
            timestamp: new Date().toISOString(),
        })}\n\n`);
        // Nếu có userId và clientId, quản lý interval với Map
        let pingInterval;
        if (userId && clientId) {
            const connectionId = getConnectionId(userId, clientId);
            // Xóa interval cũ nếu có
            if (pingIntervals.has(connectionId)) {
                clearInterval(pingIntervals.get(connectionId));
                pingIntervals.delete(connectionId);
            }
            // Tạo interval mới
            pingInterval = setInterval(() => {
                try {
                    res.write(`event: ping\ndata: ${JSON.stringify({
                        timestamp: new Date().toISOString(),
                    })}\n\n`);
                }
                catch (error) {
                    console.error(`Error sending ping to ${connectionId}:`, error);
                    clearInterval(pingInterval);
                    pingIntervals.delete(connectionId);
                }
            }, 30000);
            // Lưu interval vào Map
            pingIntervals.set(connectionId, pingInterval);
            // Xóa interval khi kết nối đóng
            res.on("close", () => {
                if (pingIntervals.has(connectionId)) {
                    clearInterval(pingIntervals.get(connectionId));
                    pingIntervals.delete(connectionId);
                }
            });
        }
        else {
            // Backwards compatibility - cách cũ
            pingInterval = setInterval(() => {
                try {
                    res.write(`event: ping\ndata: ${JSON.stringify({
                        timestamp: new Date().toISOString(),
                    })}\n\n`);
                }
                catch (error) {
                    console.error("Error sending ping:", error);
                    clearInterval(pingInterval);
                }
            }, 30000);
            // Xóa interval khi kết nối đóng
            res.on("close", () => {
                clearInterval(pingInterval);
            });
        }
    }
    catch (error) {
        console.error("Error in setupSSEConnection:", error);
    }
};
exports.setupSSEConnection = setupSSEConnection;
// Xóa kết nối SSE
const removeConnection = (userId, clientId) => {
    var _a, _b;
    try {
        const connectionId = getConnectionId(userId, clientId);
        // Xóa interval ping nếu có
        if (pingIntervals.has(connectionId)) {
            console.log(`Cleaning up ping interval for ${connectionId}`);
            clearInterval(pingIntervals.get(connectionId));
            pingIntervals.delete(connectionId);
        }
        if (connections.has(userId)) {
            (_a = connections.get(userId)) === null || _a === void 0 ? void 0 : _a.delete(clientId);
            // console.log(
            //   `SSE connection removed for user ${userId}, clientId: ${clientId}`
            // );
            // Nếu không còn kết nối nào cho userId, xóa entry
            if (((_b = connections.get(userId)) === null || _b === void 0 ? void 0 : _b.size) === 0) {
                connections.delete(userId);
                // console.log(`Removed all connections for user ${userId}`);
            }
        }
    }
    catch (error) {
        // console.error(
        //   `Error in removeConnection for user ${userId}, clientId: ${clientId}:`,
        //   error
        // );
    }
};
exports.removeConnection = removeConnection;
// Thiết lập thiết bị hiện tại là thiết bị đăng nhập mới nhất
const setCurrentDeviceAsActive = (userId, clientId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // Lưu vào bộ nhớ cache
        activeDevices.set(userId, clientId);
        // Lưu vào cơ sở dữ liệu để duy trì giữa các lần khởi động server
        yield User_1.default.findByIdAndUpdate(userId, { lastActiveDevice: clientId });
        // console.log(
        //   `Set device ${clientId} as newest active device for user ${userId} (in memory and DB)`
        // );
    }
    catch (error) {
        // console.error(`Error updating lastActiveDevice in database:`, error);
        // Vẫn lưu vào bộ nhớ cache trong trường hợp lỗi DB
        activeDevices.set(userId, clientId);
        // console.log(
        //   `Set device ${clientId} as newest active device for user ${userId} (memory only)`
        // );
    }
});
exports.setCurrentDeviceAsActive = setCurrentDeviceAsActive;
// Khôi phục activeDevices từ cơ sở dữ liệu khi khởi động server
const restoreActiveDevices = () => __awaiter(void 0, void 0, void 0, function* () {
    try {
        console.log("Restoring active devices from database...");
        // Tìm tất cả người dùng có lastActiveDevice
        const users = yield User_1.default.find({ lastActiveDevice: { $ne: null } });
        let count = 0;
        // Khôi phục vào bộ nhớ cache
        for (const user of users) {
            if (user.lastActiveDevice) {
                activeDevices.set(user._id.toString(), user.lastActiveDevice);
                count++;
            }
        }
        console.log(`Restored ${count} active devices from database`);
    }
    catch (error) {
        console.error("Error restoring active devices from database:", error);
    }
});
exports.restoreActiveDevices = restoreActiveDevices;
// Đăng xuất tất cả các thiết bị của người dùng, trừ thiết bị hiện tại
const logoutOtherDevices = (userId_1, ...args_1) => __awaiter(void 0, [userId_1, ...args_1], void 0, function* (userId, currentClientId = null) {
    // Không cần gọi lại setCurrentDeviceAsActive ở đây vì nó thường được gọi trước khi gọi hàm này
    // Nếu cần, chỉ đặt thiết bị hiện tại làm thiết bị hoạt động mới nhất khi chưa được đặt trước đó
    if (currentClientId && !activeDevices.has(userId)) {
        yield (0, exports.setCurrentDeviceAsActive)(userId, currentClientId);
    }
    // Kiểm tra kết nối tồn tại
    if (!connections.has(userId)) {
        // console.log(
        //   `User ${userId} has no active SSE connections - storing forced logout flag for future connections`
        // );
        // Lưu trạng thái vào bộ nhớ tạm để xử lý khi thiết bị kết nối lại
        storeForceLogoutFlag(userId, currentClientId);
        return;
    }
    const userConnections = connections.get(userId);
    const connectionsCount = userConnections.size;
    // Nếu chỉ có một kết nối và đó chính là thiết bị hiện tại, không cần làm gì cả
    if (connectionsCount === 1 &&
        currentClientId &&
        userConnections.has(currentClientId)) {
        // console.log(
        //   `Only one connection for user ${userId} and it's the current device. No need to force logout.`
        // );
        return;
    }
    // console.log(
    //   `Attempting to logout ${connectionsCount} device(s) for user ${userId} (excluding clientId: ${currentClientId})`
    // );
    let successCount = 0;
    // Gửi trực tiếp đến từng thiết bị
    userConnections.forEach((res, clientId) => {
        if (currentClientId === null || clientId !== currentClientId) {
            try {
                // Tạo dữ liệu sự kiện với timestamp để tránh cache
                const eventData = JSON.stringify({
                    message: "Tài khoản của bạn vừa được đăng nhập từ thiết bị khác. Bạn đã bị đăng xuất.",
                    timestamp: new Date().toISOString(),
                    forced: true,
                });
                // Tạo event string đầy đủ
                const event = `event: forced-logout\ndata: ${eventData}\n\n`;
                // console.log(`Sending forced-logout directly to clientId ${clientId}`);
                // Gửi sự kiện
                res.write(event);
                // Thêm ping ngay sau để đảm bảo event được gửi
                res.write(`event: ping\ndata: ${JSON.stringify({
                    timestamp: new Date().toISOString(),
                })}\n\n`);
                successCount++;
            }
            catch (error) {
                console.error(`Error sending logout notification to client ${clientId}:`, error);
            }
        }
        else {
            console.log(`Skipping current device with clientId: ${clientId}`);
        }
    });
    // console.log(
    //   `Successfully sent logout notifications to ${successCount}/${
    //     currentClientId ? connectionsCount - 1 : connectionsCount
    //   } devices for user ${userId}`
    // );
    // Lưu trạng thái vào bộ nhớ tạm để xử lý các thiết bị kết nối sau này
    storeForceLogoutFlag(userId, currentClientId);
});
exports.logoutOtherDevices = logoutOtherDevices;
// Lưu flag để xử lý khi thiết bị kết nối lại
function storeForceLogoutFlag(userId, excludeClientId) {
    pendingForceLogouts.set(userId, {
        excludeClientId,
        timestamp: Date.now(),
    });
    // console.log(
    //   `Stored forced logout flag for user ${userId}, excluding clientId: ${excludeClientId}`
    // );
}
// Kiểm tra và xử lý forced logout bảo lưu khi thiết bị kết nối mới
const checkPendingForceLogout = (userId, clientId, res) => __awaiter(void 0, void 0, void 0, function* () {
    // Xóa các flag quá hạn (lưu tối đa 1 giờ)
    const ONE_HOUR = 60 * 60 * 1000;
    const now = Date.now();
    // Dọn dẹp các flag cũ
    pendingForceLogouts.forEach((data, uid) => {
        if (now - data.timestamp > ONE_HOUR) {
            pendingForceLogouts.delete(uid);
        }
    });
    // Kiểm tra xem thiết bị này có phải là thiết bị hoạt động mới nhất không từ cả DB và bộ nhớ
    const isActiveInMemory = activeDevices.has(userId) && activeDevices.get(userId) === clientId;
    // Kiểm tra từ DB nếu không tìm thấy trong bộ nhớ cache hoặc cần xác nhận
    if (!isActiveInMemory) {
        try {
            const user = yield User_1.default.findById(userId).select("lastActiveDevice");
            if (user && user.lastActiveDevice === clientId) {
                console.log(`Device ${clientId} is the newest active device for user ${userId} (from DB), no need to logout`);
                // Cập nhật lại bộ nhớ cache nếu DB có thông tin mới hơn
                activeDevices.set(userId, clientId);
                return false;
            }
        }
        catch (error) {
            console.error(`Error checking active device in DB for user ${userId}:`, error);
            // Trong trường hợp lỗi, chỉ dựa vào bộ nhớ cache
        }
    }
    else {
        console.log(`Device ${clientId} is the newest active device for user ${userId} (from memory), no need to logout`);
        return false;
    }
    // Kiểm tra xem có forced logout nào chờ cho user này không
    if (pendingForceLogouts.has(userId)) {
        const data = pendingForceLogouts.get(userId);
        // Nếu clientId hiện tại không phải là thiết bị đăng nhập mới
        if (data.excludeClientId !== clientId) {
            // console.log(
            //   `Processing pending forced logout for user ${userId}, client ${clientId}`
            // );
            // Gửi thông báo forced logout
            try {
                const eventData = JSON.stringify({
                    message: "Tài khoản của bạn đã được đăng nhập từ thiết bị khác. Bạn đã bị đăng xuất.",
                    timestamp: new Date().toISOString(),
                    forced: true,
                    delayed: true,
                });
                res.write(`event: forced-logout\ndata: ${eventData}\n\n`);
                // console.log(`Sent delayed forced-logout to client ${clientId}`);
                return true;
            }
            catch (error) {
                console.error(`Error sending delayed forced-logout to client ${clientId}:`, error);
            }
        }
        else {
            console.log(`Client ${clientId} is the new login device, skipping forced logout`);
        }
    }
    return false;
});
exports.checkPendingForceLogout = checkPendingForceLogout;
// Lấy tất cả kết nối của một người dùng
const getUserConnections = (userId) => {
    return connections.get(userId);
};
exports.getUserConnections = getUserConnections;
// Trả về clientId của thiết bị mới nhất đang hoạt động
const getActiveDevice = (userId) => {
    return activeDevices.get(userId);
};
exports.getActiveDevice = getActiveDevice;
exports.default = {
    addConnection: exports.addConnection,
    setupSSEConnection: exports.setupSSEConnection,
    removeConnection: exports.removeConnection,
    logoutOtherDevices: exports.logoutOtherDevices,
    getUserConnections: exports.getUserConnections,
    setCurrentDeviceAsActive: exports.setCurrentDeviceAsActive,
    getActiveDevice: exports.getActiveDevice,
    restoreActiveDevices: exports.restoreActiveDevices,
};
//# sourceMappingURL=sseService.js.map