"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const exam_controller_1 = __importDefault(require("../controllers/exam.controller"));
const question_controller_1 = __importDefault(require("../controllers/question.controller"));
const examHistory_controller_1 = require("../controllers/examHistory.controller");
const asynHandler_1 = __importDefault(require("../util/asynHandler"));
const exam_controller_2 = require("../controllers/exam.controller");
const student_middleware_1 = require("../middlewares/student.middleware");
const router = express_1.default.Router();
// Route để xem lịch sử làm bài thi
router.get("/history", (0, asynHandler_1.default)(examHistory_controller_1.getUserExamHistory));
// Route để lưu kết quả bài thi (API)
router.post("/save-history", (0, asynHandler_1.default)(examHistory_controller_1.saveExamHistory));
// Route cho trang làm lại câu hỏi sai - đặt trước các route có ":examId" để tránh xung đột
router.get("/retry-wrong-questions", student_middleware_1.checkStudentCourseAccess, (0, asynHandler_1.default)(exam_controller_2.retryWrongQuestions));
// Route lấy tất cả đề thi của một khóa học
router.get("/product/:productId", (0, asynHandler_1.default)(exam_controller_2.getExamsByProductId));
// Lấy bài kiểm tra theo ID
router.get("/:examId", (0, asynHandler_1.default)(exam_controller_1.default.getExamById));
// API route để lấy danh sách câu hỏi sai của một bài thi
router.get("/:examId/wrong-questions", (0, asynHandler_1.default)(exam_controller_2.getWrongQuestions));
// Cập nhật bài kiểm tra
// router.patch("/:examId", asyncHandler(examController.updateExam));
// Xóa bài kiểm tra
// router.delete("/:examId", asyncHandler(examController.deleteExam));
// Lấy bài kiểm tra kèm tất cả câu hỏi
router.get("/:examId/full", (0, asynHandler_1.default)(exam_controller_1.default.getExamWithQuestions));
// Routes cho questions liên quan đến exam
router.get("/:examId/questions", (0, asynHandler_1.default)(question_controller_1.default.getQuestionsByExam));
// router.post(
//   "/:examId/questions",
//   asyncHandler(questionController.createQuestion)
// );
// Route để tạo nhiều câu hỏi cùng lúc
// router.post(
//   "/:examId/questions/batch",
//   asyncHandler(questionController.createMultipleQuestions)
// );
// Routes cho các hình thức làm bài thi
router.get("/:examId/google-form", student_middleware_1.checkStudentCourseAccess, exam_controller_2.takeExamGoogleForm);
router.get("/:examId/quizizz", student_middleware_1.checkStudentCourseAccess, exam_controller_2.takeExamQuizizz);
// Routes cho thi thử
router.get("/practice/:productId/check", (0, asynHandler_1.default)(exam_controller_2.checkExistingPractice));
router.get("/practice/:productId/history", (0, asynHandler_1.default)((req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const { productId } = req.params;
        const userId = (_a = res.locals.user) === null || _a === void 0 ? void 0 : _a._id;
        if (!userId) {
            return res.status(401).json({
                success: false,
                message: "Người dùng chưa đăng nhập",
            });
        }
        // Import PracticeExamHistory model
        const PracticeExamHistory = require("../models/PracticeExamHistory").default;
        // Lấy lịch sử practice exam của user cho course cụ thể
        const history = yield PracticeExamHistory.find({
            userId: userId,
            courseId: productId,
            status: "completed",
        })
            .sort({ completedAt: -1 })
            .limit(10)
            .select("score totalQuestions correctAnswers duration completedAt createdAt status");
        return res.json({
            success: true,
            data: history,
        });
    }
    catch (error) {
        console.error("❌ Lỗi lấy lịch sử practice:", error);
        return res.status(500).json({
            success: false,
            message: "Lỗi server khi lấy lịch sử",
            error: error.message,
        });
    }
})));
// Route để lấy chi tiết một lần thi cụ thể
router.get("/practice/detail/:practiceId", (0, asynHandler_1.default)((req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const { practiceId } = req.params;
        const userId = (_a = res.locals.user) === null || _a === void 0 ? void 0 : _a._id;
        if (!userId) {
            return res.status(401).json({
                success: false,
                message: "Người dùng chưa đăng nhập",
            });
        }
        // Import PracticeExamHistory model
        const PracticeExamHistory = require("../models/PracticeExamHistory").default;
        const Question = require("../models/question").default;
        // Lấy chi tiết practice exam của user
        const detail = yield PracticeExamHistory.findOne({
            _id: practiceId,
            userId: userId,
        }).select("score totalQuestions correctAnswers duration completedAt createdAt status courseName userAnswers selectedQuestions");
        if (!detail) {
            return res.status(404).json({
                success: false,
                message: "Không tìm thấy lịch sử thi",
            });
        }
        // Lấy thông tin đầy đủ của câu hỏi từ database
        const enhancedDetail = Object.assign({}, detail.toObject());
        // Lấy danh sách questionIds từ selectedQuestions
        const questionIds = detail.selectedQuestions.map((q) => q.questionId);
        // Tạo map từ questionId -> userAnswer object để truy xuất nhanh
        const userAnswerMap = {};
        if (detail.userAnswers && Array.isArray(detail.userAnswers)) {
            detail.userAnswers.forEach((answer) => {
                if (answer && answer.questionId) {
                    userAnswerMap[answer.questionId.toString()] = answer;
                }
            });
        }
        // Lấy thông tin đầy đủ của câu hỏi từ database
        const fullQuestions = yield Question.find({
            _id: { $in: questionIds },
        });
        // Map câu hỏi đã tối ưu với dữ liệu đầy đủ và sắp xếp theo thứ tự ban đầu
        enhancedDetail.fullQuestions = detail.selectedQuestions
            .map((savedQuestion, index) => {
            // Tìm câu hỏi đầy đủ tương ứng với questionId
            const fullQuestion = fullQuestions.find((q) => q._id.toString() === savedQuestion.questionId.toString());
            if (!fullQuestion) {
                console.warn(`⚠️ Không tìm thấy câu hỏi với ID: ${savedQuestion.questionId}`);
                return null;
            }
            // Lấy thông tin userAnswer cho câu hỏi này
            const userAnswer = userAnswerMap[savedQuestion.questionId.toString()];
            const isCorrect = userAnswer ? userAnswer.isCorrect : false;
            const selectedAnswerId = userAnswer
                ? userAnswer.selectedAnswerId
                : null;
            // Chuyển từ document sang plain object
            const questionObj = fullQuestion.toObject
                ? fullQuestion.toObject()
                : Object.assign({}, fullQuestion);
            // Xử lý options theo answerOrder đã lưu
            if (questionObj.answers && Array.isArray(questionObj.answers)) {
                // Sắp xếp lại options theo answerOrder đã lưu
                const originalOptions = [...questionObj.answers];
                const orderedOptions = [];
                // Đảm bảo answerOrder hợp lệ
                const answerOrder = Array.isArray(savedQuestion.answerOrder) &&
                    savedQuestion.answerOrder.length > 0
                    ? savedQuestion.answerOrder
                    : [1, 2, 3, 4];
                // Sắp xếp theo answerOrder
                for (let i = 0; i < answerOrder.length; i++) {
                    const originalIndex = answerOrder[i] - 1;
                    if (originalIndex >= 0 &&
                        originalIndex < originalOptions.length) {
                        orderedOptions.push(originalOptions[originalIndex]);
                    }
                }
                questionObj.options =
                    orderedOptions.length > 0 ? orderedOptions : originalOptions;
            }
            return {
                _id: questionObj._id,
                text: questionObj.text,
                options: questionObj.options || questionObj.answers || [],
                image: questionObj.image || null,
                questionNumber: index + 1,
                isCorrect: isCorrect, // Thêm thông tin đúng/sai
                selectedAnswerId: selectedAnswerId, // Thêm ID đáp án đã chọn
            };
        })
            .filter(Boolean); // Lọc bỏ các null values
        return res.json({
            success: true,
            data: enhancedDetail,
        });
    }
    catch (error) {
        console.error("❌ Lỗi lấy chi tiết practice:", error);
        return res.status(500).json({
            success: false,
            message: "Lỗi server khi lấy chi tiết",
            error: error.message,
        });
    }
})));
router.post("/practice/:productId/start", (0, asynHandler_1.default)(exam_controller_2.startPracticeExam)); // API để tạo practice exam mới
router.post("/practice/save-result", (0, asynHandler_1.default)(exam_controller_2.savePracticeExamResult));
// API route để sync answers cho cross-device support
router.post("/practice/sync-answers", (0, asynHandler_1.default)((req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const { practiceId, userAnswers, currentQuestionIndex } = req.body;
        const userId = (_a = res.locals.user) === null || _a === void 0 ? void 0 : _a._id;
        if (!userId) {
            return res.status(401).json({
                success: false,
                message: "Người dùng chưa đăng nhập",
            });
        }
        if (!practiceId || !userAnswers) {
            return res.status(400).json({
                success: false,
                message: "Thiếu practiceId hoặc userAnswers",
            });
        }
        // Import PracticeExamHistory model
        const PracticeExamHistory = require("../models/PracticeExamHistory").default;
        // Update practice exam with latest answers
        const updated = yield PracticeExamHistory.findOneAndUpdate({
            _id: practiceId,
            userId: userId,
            status: "in_progress",
        }, {
            userAnswers: userAnswers,
            currentQuestionIndex: currentQuestionIndex || 0,
            lastSyncAt: new Date(),
        }, { new: true });
        if (!updated) {
            return res.status(404).json({
                success: false,
                message: "Không tìm thấy bài thi hoặc bài thi đã hoàn thành",
            });
        }
        return res.json({
            success: true,
            message: "Đã sync answers thành công",
            syncedAt: updated.lastSyncAt,
        });
    }
    catch (error) {
        console.error("❌ Lỗi sync answers:", error);
        return res.status(500).json({
            success: false,
            message: "Lỗi server khi sync answers",
            error: error.message,
        });
    }
})));
exports.default = router;
//# sourceMappingURL=exam.route.js.map