import express, { Request, Response } from "express";
import passport from "passport";
import jwt from "jsonwebtoken";
import crypto from "crypto";
import User, { IUser } from "../models/User";
import ResetToken from "../models/ResetToken";
import {
  authenticateToken,
  singleDeviceLogin,
  authenticateForSSE,
} from "../middlewares/authMiddleware";
import { forgotPasswordRateLimit } from "../middlewares/rateLimitMiddleware";
import * as sseService from "../services/sseService";
import * as sessionService from "../services/sessionService";
import * as emailService from "../services/emailService";
import Session from "../models/Session";
import asyncHandler from "../util/asynHandler";
import {
  jwtSecret,
  jwtExpirationString,
  cookieMaxAge,
  cookieSecure,
  cookieHttpOnly,
} from "../config/auth.config";

const router = express.Router();

// Tạo JWT token - Using centralized configuration
const generateToken = (user: IUser): string => {
  return jwt.sign({ id: user._id, email: user.email }, jwtSecret, {
    expiresIn: jwtExpirationString,
  } as any);
};

// Kiểm tra trạng thái xác thực (cho client gọi mỗi 3 phút)
router.get(
  "/check",
  authenticateToken as any,
  asyncHandler(async (req: Request, res: Response) => {
    try {
      // Nếu đã đi qua middleware authenticateToken, người dùng đã được xác thực
      // Token mới (nếu cần) đã được cập nhật trong middleware

      return res.status(200).json({
        isAuthenticated: true,
        message: "Phiên đăng nhập hợp lệ",
        user: {
          id: (req.user as IUser)._id,
          email: (req.user as IUser).email,
        },
      });
    } catch (error: any) {
      console.error("Auth check error:", error);
      return res.status(500).json({
        isAuthenticated: false,
        message: "Lỗi server khi kiểm tra xác thực",
        error: error.message,
      });
    }
  })
);

// Khởi tạo đăng nhập Google
router.get(
  "/google",
  passport.authenticate("google", { scope: ["profile", "email"] })
);

// Callback URL sau khi Google xác thực
router.get(
  "/google/callback",
  passport.authenticate("google", {
    session: false,
    failureRedirect: "/login?error=Đăng nhập thất bại",
  }),
  async (req: Request, res: Response) => {
    try {
      const user = req.user as IUser;
      // console.log(`Google auth callback for user: ${user.email}`);

      // Lấy clientId từ query hoặc tạo mới với định dạng rõ ràng
      const clientId =
        (req.query.clientId as string) ||
        `device_${Math.random().toString(36).substring(2, 10)}_${Date.now()}`;
      // console.log(`Using clientId: ${clientId} for login callback`);

      // Kiểm tra các phiên đăng nhập hiện tại
      const activeSessions = await Session.find({
        userId: user._id,
        isActive: true,
      });

      if (activeSessions.length > 0) {
        // console.log(
        //   `User ${user._id} (${user.email}) logged in from a new device. Found ${activeSessions.length} active sessions.`
        // );

        // Đánh dấu thiết bị hiện tại là thiết bị hoạt động mới nhất
        await sseService.setCurrentDeviceAsActive(
          user._id.toString(),
          clientId
        );
        // console.log(
        //   `Set clientId ${clientId} as active device for user ${user._id}`
        // );

        // QUAN TRỌNG: Đăng xuất tất cả thiết bị khác TRƯỚC KHI CẬP NHẬT TOKEN
        await sseService.logoutOtherDevices(user._id.toString(), clientId);

        // Đợi một chút để đảm bảo thông báo được gửi
        await new Promise((resolve) => setTimeout(resolve, 500));
      } else {
        // console.log(
        //   `First login or no active sessions for user ${user._id} (${user.email})`
        // );

        // Vẫn cần đánh dấu thiết bị này là thiết bị hoạt động mới nhất cho lần đăng nhập đầu tiên
        await sseService.setCurrentDeviceAsActive(
          user._id.toString(),
          clientId
        );
        // console.log(
        //   `Set clientId ${clientId} as active device for user ${user._id} (first login)`
        // );
      }

      // Tạo phiên đăng nhập mới
      const { token, session } = await sessionService.createSession(
        user,
        clientId,
        req
      );
      console.log(
        `Created new session with ID: ${session._id} for user ${user._id}`
      );

      // Lưu token vào cookie - Using centralized configuration
      res.cookie("jwt", token, {
        httpOnly: cookieHttpOnly,
        secure: cookieSecure,
        maxAge: cookieMaxAge,
      });
      // console.log(`JWT token set in cookie for user ${user._id}`);

      // Thêm script để lưu JWT vào localStorage (không lưu token thực, chỉ lưu trạng thái có token)
      const authScript = `
        <script>
          // Lưu trạng thái đăng nhập và thông tin người dùng vào localStorage
          localStorage.setItem('auth_status', 'logged_in');
          localStorage.setItem('user_id', '${user._id}');
          localStorage.setItem('clientId', '${clientId}');
          localStorage.setItem('session_id', '${session._id}');
          console.log('Auth data stored in localStorage');
          
          // Chuyển hướng đến trang home
          window.location.href = '/home?clientId=${clientId}';
        </script>
      `;

      // Trả về trang HTML với script thay vì chuyển hướng trực tiếp
      res.send(`
        <!DOCTYPE html>
        <html>
          <head>
            <title>Đang chuyển hướng...</title>
          </head>
          <body>
            <div style="text-align: center; margin-top: 100px;">
              <h2>Đăng nhập thành công</h2>
              <p>Đang chuyển hướng đến trang chính...</p>
            </div>
            ${authScript}
          </body>
        </html>
      `);
    } catch (error: any) {
      // console.error("Google auth callback error:", error);
      res.redirect("/login?error=Lỗi đăng nhập: " + error.message);
    }
  }
);

// Đăng xuất
router.get("/logout", async (req: Request, res: Response) => {
  try {
    // Lấy token từ cookie
    const token = req.cookies.jwt;

    // Lấy clientId từ query hoặc localStorage (thông qua script)
    const clientId = req.query.clientId as string;

    if (token) {
      // Đăng xuất phiên hiện tại
      await sessionService.logoutSession(token);
      // console.log(
      //   `Logged out session with token: ${token.substring(0, 10)}...`
      // );
    }

    // Xóa cookie
    res.clearCookie("jwt");

    // Trả về script để xóa localStorage trước khi chuyển hướng
    const logoutScript = `
      <script>
        // Xóa dữ liệu xác thực khỏi localStorage
        localStorage.removeItem('auth_status');
        localStorage.removeItem('user_id');
        localStorage.removeItem('session_id');
        
        // Không xóa clientId để có thể sử dụng lại
        console.log('Auth data cleared from localStorage');
        
        // Chuyển hướng về trang đăng nhập
        window.location.href = '/login';
      </script>
    `;

    // Trả về trang HTML với script để đảm bảo localStorage được xóa
    res.send(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Đăng xuất...</title>
        </head>
        <body>
          <div style="text-align: center; margin-top: 100px;">
            <h2>Đăng xuất thành công</h2>
            <p>Đang chuyển hướng về trang đăng nhập...</p>
          </div>
          ${logoutScript}
        </body>
      </html>
    `);
  } catch (error: any) {
    res.redirect("/login?error=Lỗi đăng xuất: " + error.message);
  }
});

// Kiểm tra trạng thái đăng nhập - API
router.get(
  "/status",
  authenticateToken as any,
  asyncHandler(async (req: Request, res: Response) => {
    console.log("Kiểm tra trạng thái đăng nhập");
    const user = req.user as IUser;
    const session = req.session;

    res.status(200).json({
      success: true,
      // user: {
      //   id: user._id,
      //   email: user.email,
      //   displayName: user.displayName,
      //   profilePhoto: user.profilePhoto,
      // },
      // session: {
      //   id: session._id,
      //   clientId: session.clientId,
      //   deviceInfo: session.deviceInfo,
      //   lastActive: session.lastActive,
      //   createdAt: session.createdAt,
      // },
    });
  })
);

// Lấy danh sách các phiên đăng nhập
router.get(
  "/sessions",
  authenticateToken as any,
  asyncHandler(async (req: Request, res: Response) => {
    try {
      const user = req.user as IUser;

      // Lấy danh sách phiên
      const sessions = await sessionService.getUserSessions(
        user._id.toString()
      );

      // Định dạng lại dữ liệu để hiển thị
      const formattedSessions = sessions.map((session) => ({
        id: session._id,
        deviceInfo: {
          browser: session.deviceInfo.browser || "Unknown",
          os: session.deviceInfo.os || "Unknown",
          deviceName: session.deviceInfo.deviceName || "Unknown device",
        },
        isCurrentDevice: session.isCurrentDevice,
        lastActive: session.lastActive,
        createdAt: session.createdAt,
      }));

      return res.status(200).json({
        success: true,
        sessions: formattedSessions,
      });
    } catch (error: any) {
      // console.error("Error fetching sessions:", error);
      return res.status(500).json({
        success: false,
        message: "Lỗi khi lấy danh sách phiên đăng nhập",
        error: error.message,
      });
    }
  })
);

// Đăng xuất một phiên cụ thể
router.post(
  "/logout-session/:sessionId",
  asyncHandler(authenticateToken as any),
  asyncHandler(async (req: Request, res: Response) => {
    try {
      const user = req.user as IUser;
      const { sessionId } = req.params;

      // Tìm phiên
      const session = await Session.findById(sessionId);

      if (!session) {
        return res.status(404).json({
          success: false,
          message: "Không tìm thấy phiên đăng nhập",
        });
      }

      // Kiểm tra phiên có thuộc về user này không
      if (session.userId.toString() !== user._id.toString()) {
        return res.status(403).json({
          success: false,
          message: "Bạn không có quyền đăng xuất phiên này",
        });
      }

      // Kiểm tra xem có phải phiên hiện tại không
      if (session.token === req.cookies.jwt) {
        return res.status(400).json({
          success: false,
          message:
            "Không thể đăng xuất phiên hiện tại qua API này. Vui lòng sử dụng /auth/logout",
        });
      }

      // Xóa hoàn toàn phiên thay vì chỉ vô hiệu hóa
      await Session.deleteOne({ _id: sessionId });

      // Nếu cần, gửi thông báo đến thiết bị đó qua SSE
      await sseService.logoutOtherDevices(
        user._id.toString(),
        req.session.clientId
      );

      return res.status(200).json({
        success: true,
        message: "Đã xóa phiên đăng nhập thành công",
      });
    } catch (error: any) {
      // console.error("Error logging out session:", error);
      return res.status(500).json({
        success: false,
        message: "Lỗi khi đăng xuất phiên",
        error: error.message,
      });
    }
  })
);

// ==== ROUTES CHO EMAIL/PASSWORD LOGIN ====

// Đăng ký tài khoản mới với email/password
router.post(
  "/register",
  asyncHandler(async (req: Request, res: Response) => {
    try {
      const { email, password, displayName } = req.body;

      // Validate input
      if (!email || !password || !displayName) {
        return res.status(400).json({
          success: false,
          message: "Email, mật khẩu và tên hiển thị là bắt buộc",
        });
      }

      if (password.length < 6) {
        return res.status(400).json({
          success: false,
          message: "Mật khẩu phải có ít nhất 6 ký tự",
        });
      }

      // Kiểm tra email đã tồn tại
      const existingUser = await User.findOne({ email });
      if (existingUser) {
        return res.status(409).json({
          success: false,
          message: "Tài khoản đã tồn tại",
        });
      }

      // Tạo avatar từ ui-avatars
      const profilePhoto = `https://ui-avatars.com/api/?name=${encodeURIComponent(
        email
      )}&background=random`;

      // Tạo user mới
      const newUser = new User({
        email,
        password,
        displayName,
        profilePhoto,
      });

      await newUser.save();

      // Gửi email chào mừng (không chặn nếu lỗi)
      try {
        await emailService.sendWelcomeEmail(email, displayName);
      } catch (emailError) {
        console.error("Failed to send welcome email:", emailError);
      }

      res.status(201).json({
        success: true,
        message: "Đăng ký thành công! Vui lòng đăng nhập.",
      });
    } catch (error: any) {
      console.error("Register error:", error);
      res.status(500).json({
        success: false,
        message: "Lỗi server khi đăng ký",
        error: error.message,
      });
    }
  })
);

// Đăng nhập với email/password
router.post(
  "/login",
  asyncHandler(async (req: Request, res: Response) => {
    try {
      const { email, password } = req.body;

      // Validate input
      if (!email || !password) {
        return res.status(400).json({
          success: false,
          message: "Email và mật khẩu là bắt buộc",
        });
      }

      // Tìm user
      const user = await User.findOne({ email });
      if (!user || !user.password) {
        return res.status(401).json({
          success: false,
          message: "Email hoặc mật khẩu không đúng",
        });
      }

      // Kiểm tra password
      const isPasswordValid = await user.comparePassword(password);
      if (!isPasswordValid) {
        return res.status(401).json({
          success: false,
          message: "Email hoặc mật khẩu không đúng",
        });
      }

      // Tạo clientId
      const clientId = `device_${Math.random()
        .toString(36)
        .substring(2, 10)}_${Date.now()}`;

      // Kiểm tra các phiên đăng nhập hiện tại (giống logic Google OAuth)
      const activeSessions = await Session.find({
        userId: user._id,
        isActive: true,
      });

      if (activeSessions.length > 0) {
        // Đánh dấu thiết bị hiện tại là thiết bị hoạt động mới nhất
        await sseService.setCurrentDeviceAsActive(
          user._id.toString(),
          clientId
        );

        // Đăng xuất tất cả thiết bị khác
        await sseService.logoutOtherDevices(user._id.toString(), clientId);

        // Đợi một chút để đảm bảo thông báo được gửi
        await new Promise((resolve) => setTimeout(resolve, 500));
      } else {
        // Đánh dấu thiết bị này là thiết bị hoạt động mới nhất cho lần đăng nhập đầu tiên
        await sseService.setCurrentDeviceAsActive(
          user._id.toString(),
          clientId
        );
      }

      // Tạo phiên đăng nhập mới
      const { token, session } = await sessionService.createSession(
        user,
        clientId,
        req
      );

      // Lưu token vào cookie - Using centralized configuration
      res.cookie("jwt", token, {
        httpOnly: cookieHttpOnly,
        secure: cookieSecure,
        maxAge: cookieMaxAge,
      });

      res.status(200).json({
        success: true,
        message: "Đăng nhập thành công",
        user: {
          id: user._id,
          email: user.email,
          displayName: user.displayName,
          profilePhoto: user.profilePhoto,
        },
        clientId,
        sessionId: session._id,
      });
    } catch (error: any) {
      console.error("Login error:", error);
      res.status(500).json({
        success: false,
        message: "Lỗi server khi đăng nhập",
        error: error.message,
      });
    }
  })
);

// Quên mật khẩu
router.post(
  "/forgot-password",
  asyncHandler(forgotPasswordRateLimit as any),
  asyncHandler(async (req: Request, res: Response) => {
    try {
      const { email } = req.body;

      if (!email) {
        return res.status(400).json({
          success: false,
          message: "Email là bắt buộc",
        });
      }

      // Kiểm tra email có tồn tại không
      const user = await User.findOne({ email });
      if (!user) {
        // Không tiết lộ thông tin về việc email có tồn tại hay không
        return res.status(200).json({
          success: true,
          message: "Nếu email tồn tại, liên kết đặt lại mật khẩu đã được gửi.",
        });
      }

      // Tạo reset token
      const resetToken = crypto.randomBytes(32).toString("hex");
      const expiresAt = new Date(Date.now() + 60 * 60 * 1000); // 1 giờ

      // Xóa token cũ nếu có
      await ResetToken.deleteMany({ email });

      // Lưu token mới
      const newResetToken = new ResetToken({
        email,
        token: resetToken,
        expiresAt,
      });
      await newResetToken.save();

      // Gửi email
      await emailService.sendForgotPasswordEmail(email, resetToken);

      res.status(200).json({
        success: true,
        message:
          "Liên kết đặt lại mật khẩu đã được gửi đến email của bạn. Nếu bạn không nhận được email, vui lòng kiểm tra thư rác.",
      });
    } catch (error: any) {
      console.error("Forgot password error:", error);
      res.status(500).json({
        success: false,
        message: "Không thể gửi email đặt lại mật khẩu. Vui lòng thử lại sau.",
        error: error.message,
      });
    }
  })
);

// Đặt lại mật khẩu
router.post(
  "/reset-password",
  asyncHandler(async (req: Request, res: Response) => {
    try {
      const { token, newPassword } = req.body;

      if (!token || !newPassword) {
        return res.status(400).json({
          success: false,
          message: "Token và mật khẩu mới là bắt buộc",
        });
      }

      if (newPassword.length < 6) {
        return res.status(400).json({
          success: false,
          message: "Mật khẩu phải có ít nhất 6 ký tự",
        });
      }

      // Tìm và kiểm tra token
      const resetToken = await ResetToken.findOne({
        token,
        used: false,
        expiresAt: { $gt: new Date() },
      });

      if (!resetToken) {
        return res.status(400).json({
          success: false,
          message: "Token không hợp lệ hoặc đã hết hạn",
        });
      }

      // Tìm user
      const user = await User.findOne({ email: resetToken.email });
      if (!user) {
        return res.status(404).json({
          success: false,
          message: "Không tìm thấy người dùng",
        });
      }

      // Cập nhật mật khẩu
      user.password = newPassword;
      await user.save();

      // Đánh dấu token đã sử dụng
      resetToken.used = true;
      await resetToken.save();

      // Đăng xuất tất cả phiên hiện tại của user này
      await Session.updateMany({ userId: user._id }, { isActive: false });

      res.status(200).json({
        success: true,
        message: "Đặt lại mật khẩu thành công. Vui lòng đăng nhập lại.",
      });
    } catch (error: any) {
      console.error("Reset password error:", error);
      res.status(500).json({
        success: false,
        message: "Lỗi server khi đặt lại mật khẩu",
        error: error.message,
      });
    }
  })
);

export default router;
