{"version": 3, "file": "Session.js", "sourceRoot": "", "sources": ["../../src/models/Session.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,qDAAsD;AAoBtD,MAAM,aAAa,GAAG,IAAI,iBAAM,CAC9B;IACE,MAAM,EAAE;QACN,IAAI,EAAE,iBAAM,CAAC,KAAK,CAAC,QAAQ;QAC3B,GAAG,EAAE,MAAM;QACX,QAAQ,EAAE,IAAI;QACd,KAAK,EAAE,IAAI;KACZ;IACD,KAAK,EAAE;QACL,IAAI,EAAE,MAAM;QACZ,QAAQ,EAAE,IAAI;QACd,MAAM,EAAE,IAAI;KACb;IACD,QAAQ,EAAE;QACR,IAAI,EAAE,MAAM;QACZ,QAAQ,EAAE,IAAI;QACd,KAAK,EAAE,IAAI;KACZ;IACD,UAAU,EAAE;QACV,SAAS,EAAE,MAAM;QACj<PERSON>,EAAE,EAAE,MAAM;QACV,UAAU,EAAE,MAAM;QAClB,OAAO,EAAE,MAAM;QACf,EAAE,EAAE,MAAM;KACX;IACD,QAAQ,EAAE;QACR,IAAI,EAAE,OAAO;QACb,OAAO,EAAE,IAAI;QACb,KAAK,EAAE,IAAI;KACZ;IACD,eAAe,EAAE;QACf,IAAI,EAAE,OAAO;QACb,OAAO,EAAE,KAAK;KACf;IACD,UAAU,EAAE;QACV,IAAI,EAAE,IAAI;QACV,OAAO,EAAE,IAAI,CAAC,GAAG;KAClB;IACD,SAAS,EAAE;QACT,IAAI,EAAE,IAAI;QACV,QAAQ,EAAE,IAAI;QACd,KAAK,EAAE,IAAI;KACZ;CACF,EACD;IACE,UAAU,EAAE,IAAI;CACjB,CACF,CAAC;AAEF,8CAA8C;AAC9C,aAAa,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC;AAChD,aAAa,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC;AAEhD,oCAAoC;AACpC,aAAa,CAAC,OAAO,CAAC,aAAa,GAAG;yDACpC,MAAwC,EACxC,KAAa,EACb,QAAgB,EAChB,UAAe,EACf,YAAoB,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,iBAAiB;;QAE/D,wBAAwB;QACxB,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAC;QAEnD,mDAAmD;QACnD,MAAM,IAAI,CAAC,UAAU,CACnB,EAAE,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,EACjC,EAAE,IAAI,EAAE,EAAE,eAAe,EAAE,KAAK,EAAE,EAAE,CACrC,CAAC;QAEF,gBAAgB;QAChB,OAAO,IAAI,CAAC,MAAM,CAAC;YACjB,MAAM;YACN,KAAK;YACL,QAAQ;YACR,UAAU;YACV,QAAQ,EAAE,IAAI;YACd,eAAe,EAAE,IAAI;YACrB,SAAS;SACV,CAAC,CAAC;IACL,CAAC;CAAA,CAAC;AAEF,kDAAkD;AAClD,aAAa,CAAC,OAAO,CAAC,uBAAuB,GAAG,UAAU,KAAa;IACrE,OAAO,IAAI,CAAC,OAAO,CAAC;QAClB,KAAK;QACL,QAAQ,EAAE,IAAI;QACd,SAAS,EAAE,EAAE,GAAG,EAAE,IAAI,IAAI,EAAE,EAAE;KAC/B,CAAC,CAAC;AACL,CAAC,CAAC;AAEF,wEAAwE;AACxE,aAAa,CAAC,OAAO,CAAC,uBAAuB,GAAG,UAC9C,MAAwC,EACxC,eAAuB;IAEvB,OAAO,IAAI,CAAC,UAAU,CACpB;QACE,MAAM;QACN,QAAQ,EAAE,EAAE,GAAG,EAAE,eAAe,EAAE;QAClC,QAAQ,EAAE,IAAI;KACf,EACD;QACE,IAAI,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE;KAC1B,CACF,CAAC;AACJ,CAAC,CAAC;AAEF,gEAAgE;AAChE,aAAa,CAAC,OAAO,CAAC,gBAAgB,GAAG,UACvC,MAAwC,EACxC,QAAgB;IAEhB,OAAO,IAAI,CAAC,UAAU,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,eAAe,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,IAAI,CAC3E,GAAG,EAAE;QACH,OAAO,IAAI,CAAC,SAAS,CACnB,EAAE,MAAM,EAAE,QAAQ,EAAE,EACpB,EAAE,IAAI,EAAE,EAAE,eAAe,EAAE,IAAI,EAAE,EAAE,CACpC,CAAC;IACJ,CAAC,CACF,CAAC;AACJ,CAAC,CAAC;AAEF,mDAAmD;AACnD,aAAa,CAAC,OAAO,CAAC,qBAAqB,GAAG;IAC5C,OAAO,IAAI,CAAC,UAAU,CAAC;QACrB,GAAG,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,IAAI,EAAE,IAAI,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;KAChE,CAAC,CAAC;AACL,CAAC,CAAC;AAuBF,kBAAe,kBAAQ,CAAC,KAAK,CAAyB,SAAS,EAAE,aAAa,CAAC,CAAC"}