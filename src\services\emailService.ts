import nodemailer from "nodemailer";

// Tạo transporter với cấu hình Gmail
const createTransporter = () => {
  return nodemailer.createTransport({
    service: "gmail",
    auth: {
      user: process.env.MAIL,
      pass: process.env.PASSWORD,
    },
  });
};

// Gửi email quên mật khẩu
export const sendForgotPasswordEmail = async (
  email: string,
  resetToken: string
) => {
  try {
    const transporter = createTransporter();

    const resetUrl = `${
      process.env.NODE_ENV === "production"
        ? "https://testhmc.site"
        : "http://localhost:5000"
    }/reset-password?token=${resetToken}`;

    const mailOptions = {
      from: process.env.MAIL,
      to: email,
      subject: "Đặt lại mật khẩu - HMC",
      html: `
        <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">
          <h2 style="color: #333; text-align: center;">Đặt lại mật khẩu</h2>
          <p><PERSON><PERSON> chào,</p>
          <p>Bạn đã yêu cầu đặt lại mật khẩu cho tài khoản của mình.</p>
          <p>Vui lòng nhấn vào nút bên dưới để đặt lại mật khẩu:</p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${resetUrl}" 
               style="background-color: #007bff; color: white; padding: 12px 24px; 
                      text-decoration: none; border-radius: 5px; display: inline-block;">
              Đặt lại mật khẩu
            </a>
          </div>
          <p>Hoặc sao chép và dán liên kết sau vào trình duyệt:</p>
          <p style="word-break: break-all; color: #666;">${resetUrl}</p>
          <p><strong>Lưu ý:</strong> Liên kết này sẽ hết hạn sau 1 giờ.</p>
          <p>Nếu bạn không yêu cầu đặt lại mật khẩu, vui lòng bỏ qua email này.</p>
          <hr style="margin: 20px 0; border: none; border-top: 1px solid #eee;">
          <p style="color: #666; font-size: 12px;">
            Email này được gửi từ hệ thống HMC. Vui lòng không trả lời email này.
          </p>
        </div>
      `,
    };

    await transporter.sendMail(mailOptions);
    console.log(`Password reset email sent to ${email}`);
    return true;
  } catch (error: any) {
    console.error("Error sending email:", error);
    throw new Error("Không thể gửi email. Vui lòng thử lại sau.");
  }
};

// Gửi email chào mừng khi đăng ký thành công
export const sendWelcomeEmail = async (email: string, displayName: string) => {
  try {
    const transporter = createTransporter();

    const mailOptions = {
      from: process.env.MAIL,
      to: email,
      subject: "Chào mừng đến với HMC!",
      html: `
        <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">
          <h2 style="color: #333; text-align: center;">Chào mừng đến với HMC!</h2>
          <p>Xin chào ${displayName},</p>
          <p>Cảm ơn bạn đã đăng ký tài khoản tại HMC. Tài khoản của bạn đã được tạo thành công!</p>
          <p>Bạn có thể bắt đầu sử dụng hệ thống bằng cách đăng nhập với email và mật khẩu đã đăng ký.</p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${
              process.env.NODE_ENV === "production"
                ? "https://testhmc.site"
                : "http://localhost:5000"
            }/login" 
               style="background-color: #28a745; color: white; padding: 12px 24px; 
                      text-decoration: none; border-radius: 5px; display: inline-block;">
              Đăng nhập ngay
            </a>
          </div>
          <p>Chúc bạn có trải nghiệm tuyệt vời!</p>
          <hr style="margin: 20px 0; border: none; border-top: 1px solid #eee;">
          <p style="color: #666; font-size: 12px;">
            Email này được gửi từ hệ thống HMC.
          </p>
        </div>
      `,
    };

    await transporter.sendMail(mailOptions);
    console.log(`Welcome email sent to ${email}`);
    return true;
  } catch (error: any) {
    console.error("Error sending welcome email:", error);
    // Không throw error vì email chào mừng không quan trọng lắm
    return false;
  }
};
