# Redis-Based Session Persistence Implementation

## Overview

Đã thành công implement Redis-based session persistence để thay thế in-memory Map `activeDevices` trong hệ thống authentication. Solution này đảm bảo rằng user sessions sẽ persists qua application restarts và deployments.

## Các Files Đã Thay Đổi

### 1. **package.json**

- Thêm dependency: `ioredis: ^5.4.1`
- Thêm script test: `npm run test-redis`

### 2. **src/services/redisService.ts** (NEW)

Redis service class với các features:

#### Core Features:

- **Redis Connection Management**: Automatic connection với retry logic
- **Fallback Mechanism**: Fallback to in-memory Map khi Redis unavailable
- **TTL Support**: 7-day default TTL cho device entries
- **Thread-Safe Operations**: Async operations với proper error handling
- **Health Monitoring**: Built-in health check và connection status

#### Key Methods:

```typescript
setActiveDevice(userId: string, clientId: string): Promise<void>
getActiveDevice(userId: string): Promise<string | null>
hasActiveDevice(userId: string): Promise<boolean>
isActiveDevice(userId: string, clientId: string): Promise<boolean>
hasActiveDeviceButNotThis(userId: string, clientId: string): Promise<boolean>
removeActiveDevice(userId: string): Promise<void>
refreshActiveDeviceTTL(userId: string): Promise<void>
restoreFromDatabase(users: Array): Promise<number>
getAllActiveDevices(): Promise<{ [userId: string]: string }>
healthCheck(): Promise<{ redis: boolean; fallback: boolean }>
```

### 3. **src/services/sseService.ts** (UPDATED)

Completely migrated từ Map-based sang Redis-based:

#### Major Changes:

- ❌ Removed: `const activeDevices: Map<string, string> = new Map();`
- ✅ Added: `import redisActiveDeviceService from "./redisService";`
- 🔄 Updated: All Map operations converted to Redis service calls
- 📈 Improved: `restoreActiveDevices()` sử dụng Redis bulk operations

### 4. **docker-compose.yml** (UPDATED)

Added Redis service:

```yaml
redis:
  image: redis:7-alpine
  container_name: auth-app-redis
  restart: always
  ports:
    - "6379:6379"
  command:
    [
      "redis-server",
      "--appendonly",
      "yes",
      "--requirepass",
      "${REDIS_PASSWORD:-}",
    ]
  volumes:
    - redis_data:/data
  networks:
    - app-network
```

### 5. **env.example** (UPDATED)

Added Redis configuration:

```env
# Redis Configuration for Session Persistence
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
```

### 6. **src/scripts/testRedisIntegration.ts** (NEW)

Comprehensive test script để verify Redis integration.

## Configuration

### Environment Variables

```env
REDIS_HOST=localhost          # Redis server host
REDIS_PORT=6379              # Redis server port
REDIS_PASSWORD=              # Redis password (optional)
```

### Redis Settings

- **Default TTL**: 7 days (604800 seconds)
- **Prefix Pattern**: `active_device:{userId}`
- **Retry Logic**: 3 max retries per request
- **Connect Timeout**: 10 seconds
- **Command Timeout**: 5 seconds

## Deployment

### Docker Compose Deployment

```bash
# Start với Redis service
docker-compose up -d

# Check Redis status
docker logs auth-app-redis
```

### Manual Deployment

```bash
# Install dependencies
npm install ioredis

# Start Redis server
redis-server

# Test Redis connection
npm run test-redis

# Start application
npm start
```

## Features Implemented

### ✅ Redis Persistence

- All `activeDevices` data stored in Redis
- Automatic TTL management (7 days)
- Bulk operations for performance

### ✅ Fallback Mechanism

- Graceful degradation when Redis unavailable
- Automatic failover to in-memory Map
- Seamless recovery when Redis comes back online

### ✅ High Availability

- Connection retry logic
- Health monitoring
- Error handling với fallback

### ✅ Thread Safety

- All operations are async and thread-safe
- Concurrent access handling
- Atomic operations

### ✅ Performance Optimization

- Connection pooling via ioredis
- Efficient key patterns
- Bulk restore operations

## Testing

### Running Tests

```bash
# Test Redis integration
npm run test-redis

# Expected output:
🔧 Starting Redis Integration Test...
1. Testing Redis Health Check:
   Redis status: ✅ Connected
   ...
🎉 Redis Integration Test Completed Successfully!
```

### Test Coverage

- Redis connection và health check
- Set/Get/Delete operations
- Fallback mechanism testing
- TTL functionality
- Bulk operations (restoreFromDatabase)
- Error handling scenarios

## Migration từ Map-based

### Before (In-Memory Map):

```typescript
const activeDevices: Map<string, string> = new Map();

// Operations
activeDevices.set(userId, clientId);
activeDevices.get(userId);
activeDevices.has(userId);
activeDevices.delete(userId);
```

### After (Redis-based):

```typescript
import redisActiveDeviceService from "./redisService";

// Operations (all async)
await redisActiveDeviceService.setActiveDevice(userId, clientId);
await redisActiveDeviceService.getActiveDevice(userId);
await redisActiveDeviceService.hasActiveDevice(userId);
await redisActiveDeviceService.removeActiveDevice(userId);
```

## Benefits

### 🔒 **Persistence**

- Sessions survive application restarts
- Data persists through deployments
- No session loss during scaling

### 📈 **Scalability**

- Supports multiple application instances
- Shared session state across containers
- Horizontal scaling ready

### 🛡️ **Reliability**

- Automatic fallback mechanisms
- Health monitoring
- Graceful error handling

### 🚀 **Performance**

- TTL-based automatic cleanup
- Efficient Redis operations
- Connection pooling

## Monitoring & Maintenance

### Health Checks

```typescript
const health = await redisActiveDeviceService.healthCheck();
console.log(`Redis: ${health.redis}, Fallback: ${health.fallback}`);
```

### Connection Status

```typescript
const status = redisActiveDeviceService.getConnectionStatus();
console.log(
  `Connected: ${status.isConnected}, Fallback Size: ${status.fallbackSize}`
);
```

### Cleanup

```typescript
await redisActiveDeviceService.cleanup(); // Close connections gracefully
```

## Troubleshooting

### Common Issues

1. **Redis Connection Failure**

   - Check REDIS_HOST và REDIS_PORT
   - Verify Redis server is running
   - Check network connectivity

2. **Authentication Issues**

   - Verify REDIS_PASSWORD if set
   - Check Redis server auth configuration

3. **Memory Usage**
   - Monitor Redis memory usage
   - Adjust TTL if needed
   - Check for memory leaks trong fallback Map

### Logs to Monitor

```
✅ Redis connected successfully          # Connection success
❌ Redis connection error: <error>       # Connection issues
⚠️  Redis not available, using fallback  # Fallback activation
🔄 Restored X active devices to Redis   # Startup restoration
```

## Future Enhancements

### Potential Improvements

- [ ] Redis Cluster support cho high availability
- [ ] Metrics và monitoring integration
- [ ] Configurable TTL per user/device type
- [ ] Redis Sentinel support
- [ ] Compression cho large datasets
- [ ] Background cleanup jobs
- [ ] Redis pub/sub cho real-time notifications

---

**Implementation Status: ✅ COMPLETE**

Hoàn toàn thay thế in-memory Map với Redis-based persistence solution while maintaining all existing functionality và thêm robust fallback mechanisms.
