import express from "express";
import { Request, Response } from "express";
import mongoose from "mongoose";
import asyncHand<PERSON> from "../util/asynHandler";
import MemoryQuestion from "../models/MemoryQuestion";
import Question from "../models/question";
import { authenticateToken } from "../middlewares/authMiddleware";

const router = express.Router();

/**
 * @route GET /exam/memory/:productId
 * @desc Lấy danh sách câu hỏi ghi nhớ của user
 * @access Private
 */
router.get(
  "/:productId",
  asyncHandler(authenticateToken),
  asyncHandler(async (req: Request, res: Response) => {
    const { productId } = req.params;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const skip = (page - 1) * limit;

    try {
      // L<PERSON>y danh sách câu hỏi ghi nhớ của user
      const memoryQuestions = await MemoryQuestion.find({
        userId: req.user._id,
        productId,
        isActive: true,
      })
        .skip(skip)
        .limit(limit)
        .sort({ createdAt: -1 });

      // Lấy thông tin chi tiết về câu hỏi
      const questionIds = memoryQuestions.map((q) => q.questionId);
      const questions = await Question.find({ _id: { $in: questionIds } });

      // Kết hợp thông tin câu hỏi với thông tin ghi nhớ
      const questionMap = new Map();
      questions.forEach((q) => questionMap.set(q._id.toString(), q));

      const enhancedMemoryQuestions = memoryQuestions.map((mq) => {
        const question = questionMap.get(mq.questionId.toString());
        return {
          _id: mq._id,
          userId: mq.userId,
          productId: mq.productId,
          questionId: mq.questionId,
          examId: mq.examId,
          source: mq.source,
          practiceCount: mq.practiceCount,
          correctCount: mq.correctCount,
          lastPracticed: mq.lastPracticed,
          createdAt: mq.createdAt,
          isActive: mq.isActive,
          questionData: question,
        };
      });

      // Tính toán thống kê
      const totalQuestions = await MemoryQuestion.countDocuments({
        userId: req.user._id,
        productId,
        isActive: true,
      });

      const stats = await MemoryQuestion.aggregate([
        {
          $match: {
            userId: new mongoose.Types.ObjectId(req.user._id as string),
            productId: new mongoose.Types.ObjectId(productId),
            isActive: true,
          },
        },
        {
          $group: {
            _id: null,
            totalQuestions: { $sum: 1 },
            totalPracticeCount: { $sum: "$practiceCount" },
            totalCorrectCount: { $sum: "$correctCount" },
          },
        },
      ]);

      // Tính toán độ chính xác trung bình
      let averageAccuracy = 0;
      let practiceCount = 0;

      if (stats.length > 0 && stats[0].totalPracticeCount > 0) {
        averageAccuracy =
          (stats[0].totalCorrectCount / stats[0].totalPracticeCount) * 100;
        practiceCount = stats[0].totalPracticeCount;
      }

      // Tính tổng số trang
      const totalPages = Math.ceil(totalQuestions / limit);

      return res.json({
        success: true,
        data: {
          questions: enhancedMemoryQuestions,
          stats: {
            totalQuestions,
            averageAccuracy,
            practiceCount,
          },
          pagination: {
            currentPage: page,
            totalPages,
            limit,
          },
        },
      });
    } catch (error) {
      console.error("Error retrieving memory questions:", error);
      return res.status(500).json({
        success: false,
        message: "Đã có lỗi xảy ra khi lấy dữ liệu câu hỏi ghi nhớ",
      });
    }
  })
);

/**
 * @route POST /exam/memory/:productId/add
 * @desc Thêm câu hỏi vào ghi nhớ
 * @access Private
 */
router.post(
  "/:productId/add",
  asyncHandler(authenticateToken),
  asyncHandler(async (req: Request, res: Response) => {
    const { productId } = req.params;
    const { questionId, examId, source = "manual" } = req.body;

    try {
      // Kiểm tra câu hỏi có tồn tại không
      const question = await Question.findById(questionId);
      if (!question) {
        return res.status(404).json({
          success: false,
          message: "Không tìm thấy câu hỏi",
        });
      }

      // Kiểm tra câu hỏi đã được thêm vào ghi nhớ chưa
      const existingQuestion = await MemoryQuestion.findOne({
        userId: req.user._id,
        questionId,
      });

      if (existingQuestion) {
        // Nếu câu hỏi đã bị xóa trước đó (isActive = false), kích hoạt lại
        if (!existingQuestion.isActive) {
          existingQuestion.isActive = true;
          existingQuestion.source = source;
          await existingQuestion.save();

          return res.json({
            success: true,
            data: existingQuestion,
            message: "Đã thêm lại câu hỏi vào danh sách ghi nhớ",
          });
        }

        // Nếu câu hỏi đã tồn tại và đang active
        return res.status(400).json({
          success: false,
          message: "Câu hỏi đã có trong danh sách ghi nhớ",
        });
      }

      // Tạo mới câu hỏi ghi nhớ
      const newMemoryQuestion = new MemoryQuestion({
        userId: req.user._id,
        productId,
        questionId,
        examId: examId || undefined,
        source,
        practiceCount: 0,
        correctCount: 0,
        createdAt: new Date(),
        isActive: true,
      });

      await newMemoryQuestion.save();

      return res.json({
        success: true,
        data: newMemoryQuestion,
        message: "Đã thêm câu hỏi vào danh sách ghi nhớ",
      });
    } catch (error) {
      console.error("Error adding memory question:", error);
      return res.status(500).json({
        success: false,
        message: "Đã có lỗi xảy ra khi thêm câu hỏi vào ghi nhớ",
      });
    }
  })
);

/**
 * @route DELETE /exam/memory/:productId/:questionId
 * @desc Xóa câu hỏi khỏi ghi nhớ
 * @access Private
 */
router.delete(
  "/:productId/:questionId",
  asyncHandler(authenticateToken),
  asyncHandler(async (req: Request, res: Response) => {
    const { productId, questionId } = req.params;

    try {
      // Tìm câu hỏi ghi nhớ
      const memoryQuestion = await MemoryQuestion.findOne({
        _id: questionId,
        userId: req.user._id,
        productId,
      });

      if (!memoryQuestion) {
        return res.status(404).json({
          success: false,
          message: "Không tìm thấy câu hỏi ghi nhớ",
        });
      }

      // Xóa mềm (soft delete)
      memoryQuestion.isActive = false;
      await memoryQuestion.save();

      return res.json({
        success: true,
        message: "Đã xóa câu hỏi khỏi danh sách ghi nhớ",
      });
    } catch (error) {
      console.error("Error deleting memory question:", error);
      return res.status(500).json({
        success: false,
        message: "Đã có lỗi xảy ra khi xóa câu hỏi",
      });
    }
  })
);

/**
 * @route DELETE /exam/memory/:productId/bulk-delete
 * @desc Xóa nhiều câu hỏi khỏi ghi nhớ
 * @access Private
 */
router.delete(
  "/:productId/bulk-delete",
  asyncHandler(authenticateToken),
  asyncHandler(async (req: Request, res: Response) => {
    const { productId } = req.params;
    const { questionIds } = req.body;

    if (!Array.isArray(questionIds) || questionIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: "Danh sách câu hỏi không hợp lệ",
      });
    }

    try {
      // Xóa mềm (soft delete) nhiều câu hỏi
      const result = await MemoryQuestion.updateMany(
        {
          _id: { $in: questionIds },
          userId: req.user._id,
          productId,
        },
        {
          $set: { isActive: false },
        }
      );

      return res.json({
        success: true,
        deletedCount: result.modifiedCount,
        message: `Đã xóa ${result.modifiedCount} câu hỏi khỏi danh sách ghi nhớ`,
      });
    } catch (error) {
      console.error("Error bulk deleting memory questions:", error);
      return res.status(500).json({
        success: false,
        message: "Đã có lỗi xảy ra khi xóa câu hỏi",
      });
    }
  })
);

/**
 * @route POST /exam/memory/:productId/practice/start
 * @desc Bắt đầu chế độ luyện nhanh
 * @access Private
 */
router.post(
  "/:productId/practice/start",
  asyncHandler(authenticateToken),
  asyncHandler(async (req: Request, res: Response) => {
    const { productId } = req.params;
    const {
      count = 10,
      timeLimit = 15,
      shuffleQuestions = true,
      shuffleAnswers = true,
    } = req.body;

    try {
      // Lấy danh sách câu hỏi ghi nhớ của user
      const memoryQuestions = await MemoryQuestion.find({
        userId: req.user._id,
        productId,
        isActive: true,
      }).sort({ practiceCount: 1, createdAt: -1 }); // Ưu tiên câu ít luyện tập

      if (memoryQuestions.length === 0) {
        return res.status(404).json({
          success: false,
          message: "Không có câu hỏi ghi nhớ nào",
        });
      }

      // Lấy số câu hỏi tối đa theo yêu cầu hoặc theo số câu có sẵn
      const questionCount = Math.min(count, memoryQuestions.length);

      // Chọn ngẫu nhiên câu hỏi nếu cần
      const selectedMemoryQuestions = shuffleQuestions
        ? memoryQuestions
            .sort(() => Math.random() - 0.5)
            .slice(0, questionCount)
        : memoryQuestions.slice(0, questionCount);

      // Lấy thông tin chi tiết về câu hỏi
      const questionIds = selectedMemoryQuestions.map((q) => q.questionId);
      const questions = await Question.find({ _id: { $in: questionIds } });

      // Kết hợp thông tin câu hỏi với thông tin ghi nhớ
      const questionMap = new Map();
      questions.forEach((q) => questionMap.set(q._id.toString(), q));

      const selectedQuestions = selectedMemoryQuestions.map((mq) => {
        const question = questionMap.get(mq.questionId.toString());
        return {
          memoryQuestionId: mq._id,
          questionId: mq.questionId,
          text: question?.text,
          options:
            shuffleAnswers && question?.answers
              ? [...question.answers].sort(() => Math.random() - 0.5)
              : question?.answers,
          image: question?.image,
        };
      });

      // Tạo ID practice session (có thể sử dụng để lưu kết quả sau này)
      const practiceId = new mongoose.Types.ObjectId();

      // Lưu session vào cache hoặc database (tùy vào implementation)
      // ...

      return res.json({
        success: true,
        practiceId: practiceId,
        questions: selectedQuestions,
        timeLimit,
        message: "Đã tạo bài luyện tập",
      });
    } catch (error) {
      console.error("Error starting memory practice:", error);
      return res.status(500).json({
        success: false,
        message: "Đã có lỗi xảy ra khi tạo bài luyện tập",
      });
    }
  })
);

/**
 * @route POST /exam/memory/:productId/practice/submit
 * @desc Nộp kết quả luyện nhanh
 * @access Private
 */
router.post(
  "/:productId/practice/submit",
  asyncHandler(authenticateToken),
  asyncHandler(async (req: Request, res: Response) => {
    const { productId } = req.params;
    const { answers, timeSpent, status = "completed" } = req.body;

    try {
      if (!answers || !Array.isArray(answers)) {
        return res.status(400).json({
          success: false,
          message: "Dữ liệu không hợp lệ",
        });
      }

      // Cập nhật thống kê luyện tập cho từng câu hỏi
      for (const [index, answer] of answers.entries()) {
        if (answer === null || answer === undefined) continue;

        const memoryQuestionId = req.body.memoryQuestionIds?.[index];
        if (!memoryQuestionId) continue;

        // Tìm câu hỏi ghi nhớ
        const memoryQuestion = await MemoryQuestion.findOne({
          _id: memoryQuestionId,
          userId: req.user._id,
          productId,
          isActive: true,
        });

        if (!memoryQuestion) continue;

        // Tìm câu hỏi gốc để biết đáp án đúng
        const question = await Question.findById(memoryQuestion.questionId);
        if (!question) continue;

        // Xác định câu trả lời có đúng không
        const correctAnswerIndex = question.answers.findIndex(
          (a) => a.isCorrect
        );
        const isCorrect = answer === correctAnswerIndex;

        // Cập nhật thống kê
        memoryQuestion.practiceCount += 1;
        if (isCorrect) {
          memoryQuestion.correctCount += 1;
        }
        memoryQuestion.lastPracticed = new Date();

        await memoryQuestion.save();
      }

      return res.json({
        success: true,
        message: "Đã lưu kết quả luyện tập",
      });
    } catch (error) {
      console.error("Error submitting memory practice results:", error);
      return res.status(500).json({
        success: false,
        message: "Đã có lỗi xảy ra khi lưu kết quả luyện tập",
      });
    }
  })
);

/**
 * @route GET /exam/memory/:productId/stats
 * @desc Thống kê luyện tập
 * @access Private
 */
router.get(
  "/:productId/stats",
  asyncHandler(authenticateToken),
  asyncHandler(async (req: Request, res: Response) => {
    const { productId } = req.params;

    try {
      const stats = await MemoryQuestion.aggregate([
        {
          $match: {
            userId: new mongoose.Types.ObjectId(req.user._id as string),
            productId: new mongoose.Types.ObjectId(productId),
            isActive: true,
          },
        },
        {
          $group: {
            _id: null,
            totalQuestions: { $sum: 1 },
            totalPracticeCount: { $sum: "$practiceCount" },
            totalCorrectCount: { $sum: "$correctCount" },
          },
        },
      ]);

      // Tính toán độ chính xác trung bình
      let averageAccuracy = 0;
      let practiceCount = 0;
      let totalQuestions = 0;

      if (stats.length > 0) {
        totalQuestions = stats[0].totalQuestions;
        practiceCount = stats[0].totalPracticeCount;

        if (practiceCount > 0) {
          averageAccuracy = (stats[0].totalCorrectCount / practiceCount) * 100;
        }
      }

      return res.json({
        success: true,
        data: {
          totalQuestions,
          practiceCount,
          averageAccuracy,
        },
      });
    } catch (error) {
      console.error("Error fetching memory stats:", error);
      return res.status(500).json({
        success: false,
        message: "Đã có lỗi xảy ra khi lấy thống kê luyện tập",
      });
    }
  })
);

export default router;
