"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const mongoose_1 = __importDefault(require("mongoose"));
const passport_1 = __importDefault(require("passport"));
const cookie_parser_1 = __importDefault(require("cookie-parser"));
const cors_1 = __importDefault(require("cors"));
const path_1 = __importDefault(require("path"));
const express_ejs_layouts_1 = __importDefault(require("express-ejs-layouts"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
// Import routes
const authRoutes_1 = __importDefault(require("./routes/authRoutes"));
const userRoutes_1 = __importDefault(require("./routes/userRoutes"));
const dataRoutes_1 = __importDefault(require("./routes/dataRoutes"));
const exam_route_1 = __importDefault(require("./routes/exam.route"));
const sseRoutes_1 = __importDefault(require("./routes/sseRoutes"));
// Import controllers và services
const student_service_1 = require("./services/student.service");
const modelService_1 = require("./services/modelService");
const course_controller_1 = require("./controllers/course.controller");
const student_middleware_1 = require("./middlewares/student.middleware");
const asynHandler_1 = __importDefault(require("./util/asynHandler"));
const redirectController_1 = require("./controllers/redirectController");
// Import passport config
const passport_2 = __importDefault(require("./config/passport"));
// Import middleware xử lý lỗi
const errorMiddleware_1 = require("./middlewares/errorMiddleware");
// Tạo ứng dụng Express
const app = (0, express_1.default)();
// Cấu hình EJS và layouts
app.set("view engine", "ejs");
app.set("views", path_1.default.join(__dirname, "views"));
app.use(express_ejs_layouts_1.default);
app.set("layout", "layouts/layout");
app.set("layout extractScripts", true);
// Static files
app.use(express_1.default.static(path_1.default.join(__dirname, "public")));
// Middleware
app.use(express_1.default.json());
app.use(express_1.default.urlencoded({ extended: true }));
app.use((0, cookie_parser_1.default)());
app.use((0, cors_1.default)({
    origin: "http://localhost:3000", // Frontend URL
    credentials: true, // Cho phép chia sẻ cookie giữa domains
}));
// Khởi tạo Passport
app.use(passport_1.default.initialize());
(0, passport_2.default)();
// Routes
app.use("/auth", authRoutes_1.default);
// Middleware để kiểm tra user và thêm vào res.locals
const userCheckMiddleware = (req, res, next) => {
    // Lấy token từ cookie
    const token = req.cookies.jwt;
    if (!token) {
        res.locals.user = null;
        return next();
    }
    // Xác thực JWT và thiết lập user
    try {
        const { id } = jsonwebtoken_1.default.verify(token, process.env.JWT_SECRET);
        mongoose_1.default
            .model("User")
            .findById(id)
            .then((user) => {
            // Kiểm tra token với activeToken
            if (user && user.get("activeToken") === token) {
                res.locals.user = user;
            }
            else {
                res.locals.user = null;
            }
            next();
        })
            .catch(() => {
            res.locals.user = null;
            next();
        });
    }
    catch (error) {
        res.locals.user = null;
        next();
    }
};
app.use("/", userCheckMiddleware);
// Handler cho trang chủ
const homeHandler = (req, res) => {
    // Nếu user đã đăng nhập, chuyển hướng đến /home
    if (res.locals.user) {
        return res.redirect("/home");
    }
    res.render("index", { user: res.locals.user });
};
// Handler cho trang đăng nhập
const loginHandler = (req, res) => {
    // Nếu user đã đăng nhập, chuyển hướng đến /home
    if (res.locals.user) {
        return res.redirect("/home");
    }
    res.render("login", {
        user: res.locals.user,
        error: req.query.error || null,
    });
};
// Handler cho trang home
const homePageHandler = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    if (!res.locals.user) {
        return res.redirect("/login");
    }
    try {
        // Tìm sinh viên theo email của người dùng đã đăng nhập
        const student = yield (0, student_service_1.getStudentByEmail)(res.locals.user.email);
        let courses = [];
        if (student) {
            // Lấy danh sách khóa học của sinh viên
            courses = yield (0, student_service_1.getStudentCourses)(student._id.toString());
        }
        res.render("home", {
            user: res.locals.user,
            student,
            courses,
        });
    }
    catch (error) {
        console.error("Lỗi khi lấy thông tin khóa học:", error);
        res.render("home", {
            user: res.locals.user,
            student: null,
            courses: [],
            error: "Có lỗi xảy ra khi lấy thông tin khóa học",
        });
    }
});
// Handler cho trang profile
const profileHandler = (req, res) => {
    if (!res.locals.user) {
        return res.redirect("/login");
    }
    res.render("profile", { user: res.locals.user });
};
// Handler cho trang danh sách khóa học
const coursesHandler = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // Lấy danh sách khóa học từ API
        const products = yield modelService_1.Product.find({ status: "active" }).lean();
        res.render("courses/index", {
            user: res.locals.user,
            products,
        });
    }
    catch (error) {
        console.error("Lỗi khi lấy danh sách khóa học:", error);
        res.render("error", {
            user: res.locals.user,
            message: "Có lỗi xảy ra khi tải danh sách khóa học",
        });
    }
});
// Đăng ký các routes
app.get("/", homeHandler);
app.get("/login", loginHandler);
app.get("/home", homePageHandler);
app.get("/profile", profileHandler);
app.get("/courses", coursesHandler);
app.get("/course/:productId/exams", student_middleware_1.checkStudentCourseAccess, (0, asynHandler_1.default)(course_controller_1.getCourseExams));
// Đăng ký route cho bài thi
app.use("/exam", exam_route_1.default);
// Route API gốc
app.use("/api", userRoutes_1.default);
app.use("/api/data", dataRoutes_1.default);
app.use("/api/sse", sseRoutes_1.default);
// Xử lý chuyển hướng URL cũ - sử dụng function handler thay vì truyền controller trực tiếp
app.get("/retry-wrong-questions", (req, res, next) => {
    (0, redirectController_1.redirectLegacyExamRoute)(req, res, next);
});
// Đăng ký middleware xử lý lỗi 404 cho tất cả các routes không khớp
app.use("/", (req, res, next) => {
    (0, errorMiddleware_1.notFoundMiddleware)(req, res, next);
});
// Middleware xử lý lỗi không lường trước
app.use((err, req, res, next) => {
    (0, errorMiddleware_1.uncaughtErrorMiddleware)(err, req, res, next);
});
// Middleware xử lý lỗi chung
app.use((err, req, res, next) => {
    (0, errorMiddleware_1.globalErrorMiddleware)(err, req, res, next);
});
exports.default = app;
//# sourceMappingURL=app.js.map