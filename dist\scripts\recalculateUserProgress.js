"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importDefault(require("mongoose"));
const dotenv_1 = __importDefault(require("dotenv"));
const userProgress_service_1 = require("../services/userProgress.service");
// Load environment variables
dotenv_1.default.config();
// Lấy userId từ tham số dòng lệnh
const userId = process.argv[2];
if (!userId) {
    console.error("❌ Vui lòng cung cấp userId. Ví dụ: npm run recalculate-user-progress 60f1234567890abcdef12345");
    process.exit(1);
}
// Connect to MongoDB
mongoose_1.default
    .connect(process.env.MONGODB_URI || "mongodb://localhost:27017/google-auth")
    .then(() => __awaiter(void 0, void 0, void 0, function* () {
    console.log("📚 Kết nối MongoDB thành công");
    console.log(`🔄 Bắt đầu cập nhật lại tiến độ học tập cho người dùng ${userId}...`);
    try {
        const result = yield (0, userProgress_service_1.recalculateUserProgress)(userId);
        if (result) {
            console.log("✅ Cập nhật tiến độ học tập thành công!");
        }
        else {
            console.error("❌ Cập nhật tiến độ học tập thất bại!");
        }
    }
    catch (error) {
        console.error("❌ Lỗi khi cập nhật tiến độ học tập:", error);
    }
    // Đóng kết nối
    yield mongoose_1.default.connection.close();
    console.log("📚 Đã đóng kết nối MongoDB");
    process.exit(0);
}))
    .catch((err) => {
    console.error("❌ Lỗi kết nối MongoDB:", err);
    process.exit(1);
});
//# sourceMappingURL=recalculateUserProgress.js.map