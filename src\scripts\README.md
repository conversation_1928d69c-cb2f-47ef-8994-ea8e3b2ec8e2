# Scripts cập nhật số lượng câu hỏi

Th<PERSON> mục này chứa các script để cập nhật số lượng câu hỏi trong đề thi và môn học.

## Các script có sẵn

### 1. C<PERSON><PERSON> nhật số câu hỏi trong đề thi (`updateExamQuestionCount.ts`)

Script này sẽ cập nhật trường `count` trong mỗi đề thi dựa trên số lượng câu hỏi thực tế thuộc về đề thi đó.

```bash
npm run update-exam-counts
```

### 2. Cập nhật tổng số câu hỏi trong môn học (`updateProductQuestionCount.ts`)

Script này sẽ cập nhật trường `countQuestion` trong mỗi môn học dựa trên tổng số câu hỏi từ tất cả các đề thi thuộc môn học đó.

```bash
npm run update-product-counts
```

### 3. <PERSON><PERSON>p nhật tất cả (`updateAllCounts.ts`)

Script này kết hợp cả hai chức năng trên, đầu tiên cập nhật số câu hỏi cho từng đề thi, sau đó cập nhật tổng số câu hỏi cho từng môn học.

```bash
npm run update-all-counts
```

## Cách thức hoạt động

### Cập nhật số câu hỏi trong đề thi

1. Lấy tất cả các đề thi từ collection `Exam`
2. Với mỗi đề thi, đếm số câu hỏi có `examId` trỏ đến đề thi đó trong collection `Question`
3. Cập nhật trường `count` trong đề thi với số lượng câu hỏi đếm được

### Cập nhật tổng số câu hỏi trong môn học

1. Lấy tất cả các môn học từ collection `Product`
2. Với mỗi môn học, lấy tất cả các đề thi có `productId` trỏ đến môn học đó
3. Tính tổng số câu hỏi từ trường `count` của tất cả đề thi thuộc môn học
4. Cập nhật trường `countQuestion` trong môn học với tổng số câu hỏi tính được

## Yêu cầu

- Biến môi trường `MONGODB_URI` cần được cấu hình trong file `.env` hoặc sẽ sử dụng giá trị mặc định `mongodb://localhost:27017/google-auth`
- Các model `Product`, `Exam` và `Question` phải đã được định nghĩa đúng cách

## Lưu ý

- Các script này được thiết kế để chạy độc lập và không ảnh hưởng đến code đã có sẵn trong dự án
- Nên chạy script `update-all-counts` để đảm bảo dữ liệu được đồng bộ hoàn toàn
- Có thể chạy các script này theo lịch trình định kỳ hoặc sau khi nhập dữ liệu mới
