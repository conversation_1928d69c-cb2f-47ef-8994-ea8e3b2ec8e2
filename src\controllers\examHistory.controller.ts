import { Request, Response } from "express";
import ExamHistory from "../models/ExamHistory";
import mongoose from "mongoose";

class ExamHistoryController {
  /**
   * L<PERSON><PERSON> kết quả bài thi vào lịch sử
   */
  async saveExamHistory(req: Request, res: Response) {
    try {
      const { examId, examName, score, totalQuestions, duration, examType } =
        req.body;

      if (
        !examId ||
        !examName ||
        score === undefined ||
        !totalQuestions ||
        !duration ||
        !examType
      ) {
        return res.status(400).json({
          success: false,
          message: "Thiếu thông tin cần thiết để lưu lịch sử bài thi",
        });
      }

      // Lấy userId từ user đã đăng nhập
      const userId = res.locals.user?._id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: "Người dùng chưa đăng nhập",
        });
      }

      // T<PERSON><PERSON> bản ghi lịch sử mới
      const examHistory = new ExamHistory({
        examId,
        examName,
        userId,
        score,
        totalQuestions,
        duration,
        examType,
        completedAt: new Date(),
      });

      await examHistory.save();

      return res.status(201).json({
        success: true,
        message: "Đã lưu lịch sử bài thi thành công",
        examHistory,
      });
    } catch (error: any) {
      console.error("Lỗi khi lưu lịch sử bài thi:", error);
      return res.status(500).json({
        success: false,
        message: "Đã xảy ra lỗi khi lưu lịch sử bài thi",
        error: error.message,
      });
    }
  }

  /**
   * Lấy lịch sử làm bài thi của người dùng
   */
  async getUserExamHistory(req: Request, res: Response) {
    try {
      // Lấy userId từ user đã đăng nhập
      const userId = res.locals.user?._id;

      if (!userId) {
        return res.redirect("/auth/login");
      }

      // Lấy tham số phân trang từ query
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const skip = (page - 1) * limit;

      // Đếm tổng số bản ghi để tính số trang
      const totalRecords = await ExamHistory.countDocuments({ userId });
      const totalPages = Math.ceil(totalRecords / limit);

      // Lấy lịch sử thi với phân trang, sắp xếp theo thời gian làm mới nhất
      const examHistories = await ExamHistory.find({ userId })
        .select("-__v -createdAt -updatedAt")
        .sort({ completedAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean();

      return res.render("exam/history", {
        user: res.locals.user,
        examHistories,
        pagination: {
          page,
          limit,
          totalPages,
          totalRecords,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
        title: "Lịch sử làm bài thi",
      });
    } catch (error: any) {
      console.error("Lỗi khi lấy lịch sử bài thi:", error);
      return res.status(500).render("error", {
        message: "Đã xảy ra lỗi khi lấy lịch sử bài thi",
        error: { status: 500, stack: error.message },
        user: res.locals.user,
      });
    }
  }
}

// Export các hàm xử lý để sử dụng trong route
export const saveExamHistory = (req: Request, res: Response) =>
  new ExamHistoryController().saveExamHistory(req, res);

export const getUserExamHistory = (req: Request, res: Response) =>
  new ExamHistoryController().getUserExamHistory(req, res);
