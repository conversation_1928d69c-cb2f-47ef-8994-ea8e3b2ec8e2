/**
 * memory-exam-practice.js
 * Module xử lý chế độ luyện tập nhanh với timer và quizizz-style interface
 *
 * Pattern: IIFE với namespace isolation
 */
(function (window) {
  "use strict";

  // Private variables và state
  let _config = {
    productId: null,
    practiceId: null,
    apiBase: "/exam/memory",
    domSelectors: {
      container: "#memoryPracticeContainer",
      questionContainer: "#questionContainer",
      optionsContainer: "#optionsContainer",
      timerDisplay: "#timerDisplay",
      progressBar: "#progressBar",
      currentQuestionIndicator: "#currentQuestionIndicator",
      resultContainer: "#resultContainer",
      correctSound: "#correctSound",
      wrongSound: "#wrongSound",
    },
    // Âm thanh
    sounds: {
      correct: "/media/correct_answer.mp3",
      wrong: "/media/wrong_answer.mp3",
    },
    // Security measures
    security: {
      contextMessage: "luyện tập ghi nhớ",
      enableDevToolsDetection: true,
      enableScreenshotBlocking: true,
      enableRightClickBlocking: true,
      enableCopyBlocking: true,
      enablePrintBlocking: true,
      enableViewSourceBlocking: true,
      enableSavePageBlocking: true,
      enableDragDropBlocking: true,
      devToolsThreshold: 160,
      redirectOnDevTools: false,
      redirectUrl: "/home",
    },
  };

  let _state = {
    questions: [],
    currentQuestionIndex: 0,
    answers: [],
    timeRemaining: 0,
    totalTime: 0,
    timerInterval: null,
    isSubmitting: false,
    stats: {
      correctAnswers: 0,
      incorrectAnswers: 0,
      score: 0,
      timeSpent: 0,
    },
  };

  // DOM cache để lưu trữ tham chiếu đến DOM elements
  let _dom = {};

  // Khởi tạo module
  function _init() {
    // Lấy practiceId từ URL
    const pathParts = window.location.pathname.split("/");
    _config.productId = pathParts[2];
    _config.practiceId = pathParts[3];

    console.log(
      "Memory Exam Practice module initialized with practiceId:",
      _config.practiceId
    );

    // Khởi tạo DOM cache
    _initDomCache();

    // Kích hoạt security measures
    _activateSecurityMeasures();

    // Tải dữ liệu bài luyện tập
    _loadPracticeExam();

    // Gán các event listeners
    _setupEventListeners();

    // Ngăn chặn việc back trên trình duyệt
    window.history.pushState(null, null, window.location.href);
    window.onpopstate = function () {
      window.history.pushState(null, null, window.location.href);
      _showExitWarning();
    };
  }

  // Khởi tạo DOM cache
  function _initDomCache() {
    Object.keys(_config.domSelectors).forEach((key) => {
      const selector = _config.domSelectors[key];
      _dom[key] = document.querySelector(selector);
    });
  }

  // Kích hoạt các security measures
  function _activateSecurityMeasures() {
    if (typeof window.GlobalSecurityMeasures === "function") {
      window.GlobalSecurityMeasures(_config.security);
    } else {
      console.warn(
        "GlobalSecurityMeasures not found, security features will not be enabled"
      );
    }
  }

  // Thiết lập các event listeners
  function _setupEventListeners() {
    // Listen for exit confirmation
    window.addEventListener("beforeunload", function (e) {
      if (!_state.isSubmitting) {
        // Standard
        e.preventDefault();
        // Chrome
        e.returnValue = "";

        // Thông báo
        return "Bạn có chắc muốn rời khỏi trang? Tiến độ luyện tập của bạn sẽ bị mất!";
      }
    });
  }

  // Tải dữ liệu bài luyện tập
  function _loadPracticeExam() {
    _showLoading();

    fetch(
      `${_config.apiBase}/${_config.productId}/${_config.practiceId}/practice/data`
    )
      .then((response) => response.json())
      .then((data) => {
        if (data.success) {
          _state.questions = data.questions || [];
          _state.timeRemaining = data.timeLimit * 60; // Chuyển đổi từ phút sang giây
          _state.totalTime = data.timeLimit * 60;

          // Chuẩn bị mảng answers
          _state.answers = Array(_state.questions.length).fill(null);

          // Bắt đầu timer
          _startTimer();

          // Hiển thị câu hỏi đầu tiên
          _renderCurrentQuestion();

          // Cập nhật progress bar
          _updateProgress();
        } else {
          console.error("Failed to load practice exam:", data.message);
          _showErrorMessage("Không thể tải dữ liệu bài luyện tập");
        }
      })
      .catch((error) => {
        console.error("Error loading practice exam:", error);
        _showErrorMessage("Đã có lỗi xảy ra khi tải dữ liệu");
      })
      .finally(() => {
        _hideLoading();
      });
  }

  // Hiển thị câu hỏi hiện tại
  function _renderCurrentQuestion() {
    if (
      _state.currentQuestionIndex < 0 ||
      _state.currentQuestionIndex >= _state.questions.length ||
      !_dom.questionContainer ||
      !_dom.optionsContainer
    ) {
      return;
    }

    const question = _state.questions[_state.currentQuestionIndex];

    // Hiển thị text câu hỏi
    _dom.questionContainer.innerHTML = `
      <h3 class="text-xl font-semibold text-gray-800 mb-4">${question.text}</h3>
      ${
        question.image
          ? `<img src="${question.image}" alt="Question image" class="mb-4 max-w-full h-auto rounded">`
          : ""
      }
    `;

    // Hiển thị các đáp án
    let optionsHTML = "";
    (question.options || []).forEach((option, index) => {
      const optionLabel = String.fromCharCode(65 + index); // A, B, C, D, ...
      const selectedClass =
        _state.answers[_state.currentQuestionIndex] === index
          ? "border-indigo-500 bg-indigo-50"
          : "";

      optionsHTML += `
        <div class="option-button ${selectedClass} p-4 border rounded-lg mb-3 cursor-pointer hover:bg-gray-50 transition-all" 
             data-index="${index}" onclick="MemoryExamPractice.selectAnswer(${index})">
          <div class="flex items-start">
            <div class="option-label flex-shrink-0 w-8 h-8 flex items-center justify-center bg-indigo-100 text-indigo-800 font-medium rounded-full mr-3">
              ${optionLabel}
            </div>
            <div class="option-text text-gray-700">${
              option.text || option
            }</div>
          </div>
        </div>
      `;
    });

    _dom.optionsContainer.innerHTML = optionsHTML;

    // Cập nhật số câu hiện tại
    if (_dom.currentQuestionIndicator) {
      _dom.currentQuestionIndicator.textContent = `Câu ${
        _state.currentQuestionIndex + 1
      }/${_state.questions.length}`;
    }
  }

  // Bắt đầu timer
  function _startTimer() {
    _state.timerInterval = setInterval(() => {
      _state.timeRemaining--;
      _updateTimerDisplay();

      if (_state.timeRemaining <= 0) {
        _endPractice("time_up");
      }
    }, 1000);

    _updateTimerDisplay();
  }

  // Cập nhật hiển thị timer
  function _updateTimerDisplay() {
    if (!_dom.timerDisplay) return;

    const minutes = Math.floor(_state.timeRemaining / 60);
    const seconds = _state.timeRemaining % 60;

    _dom.timerDisplay.innerHTML = `${minutes
      .toString()
      .padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;

    // Thêm class cảnh báo khi thời gian dưới 1 phút
    if (_state.timeRemaining <= 60) {
      _dom.timerDisplay.classList.add("text-red-600", "font-bold");
    }
  }

  // Cập nhật progress bar
  function _updateProgress() {
    if (!_dom.progressBar) return;

    const progress =
      ((_state.currentQuestionIndex + 1) / _state.questions.length) * 100;
    _dom.progressBar.style.width = `${progress}%`;
  }

  // Chọn câu trả lời
  function selectAnswer(answerIndex) {
    // Lưu câu trả lời
    _state.answers[_state.currentQuestionIndex] = answerIndex;

    // Cập nhật UI
    const optionButtons = document.querySelectorAll(".option-button");
    optionButtons.forEach((button, index) => {
      if (index === answerIndex) {
        button.classList.add("border-indigo-500", "bg-indigo-50");
      } else {
        button.classList.remove("border-indigo-500", "bg-indigo-50");
      }
    });

    // Check đáp án đúng/sai ngay lập tức
    const currentQuestion = _state.questions[_state.currentQuestionIndex];
    const correctAnswerIndex = currentQuestion.options.findIndex(
      (option) => option.isCorrect
    );
    const isCorrect = answerIndex === correctAnswerIndex;

    // Hiển thị kết quả
    setTimeout(() => {
      _showAnswerResult(isCorrect, correctAnswerIndex);

      // Tự động chuyển câu sau 1.5s
      setTimeout(() => {
        if (_state.currentQuestionIndex < _state.questions.length - 1) {
          _nextQuestion();
        } else {
          _endPractice("completed");
        }
      }, 1500);
    }, 300);
  }

  // Hiển thị kết quả đáp án
  function _showAnswerResult(isCorrect, correctAnswerIndex) {
    // Play sound
    if (isCorrect) {
      _playSound("correct");
      _state.stats.correctAnswers++;
    } else {
      _playSound("wrong");
      _state.stats.incorrectAnswers++;
    }

    // Highlight đáp án đúng/sai
    const optionButtons = document.querySelectorAll(".option-button");
    optionButtons.forEach((button, index) => {
      if (index === correctAnswerIndex) {
        button.classList.add("border-green-500", "bg-green-50");
        button.classList.remove("border-indigo-500", "bg-indigo-50");

        // Thêm icon check
        const optionLabel = button.querySelector(".option-label");
        if (optionLabel) {
          optionLabel.classList.add("bg-green-500", "text-white");
          optionLabel.innerHTML = '<i class="fas fa-check"></i>';
        }
      } else if (_state.answers[_state.currentQuestionIndex] === index) {
        if (!isCorrect) {
          button.classList.add("border-red-500", "bg-red-50");
          button.classList.remove("border-indigo-500", "bg-indigo-50");

          // Thêm icon cross
          const optionLabel = button.querySelector(".option-label");
          if (optionLabel) {
            optionLabel.classList.add("bg-red-500", "text-white");
            optionLabel.innerHTML = '<i class="fas fa-times"></i>';
          }
        }
      }
    });
  }

  // Chuyển đến câu hỏi tiếp theo
  function _nextQuestion() {
    if (_state.currentQuestionIndex < _state.questions.length - 1) {
      _state.currentQuestionIndex++;
      _renderCurrentQuestion();
      _updateProgress();
    }
  }

  // Chuyển đến câu hỏi trước
  function _prevQuestion() {
    if (_state.currentQuestionIndex > 0) {
      _state.currentQuestionIndex--;
      _renderCurrentQuestion();
      _updateProgress();
    }
  }

  // Kết thúc bài luyện tập
  function _endPractice(status = "completed") {
    // Dừng timer
    if (_state.timerInterval) {
      clearInterval(_state.timerInterval);
    }

    // Tính toán các số liệu thống kê
    _state.stats.score = Math.round(
      (_state.stats.correctAnswers / _state.questions.length) * 100
    );
    _state.stats.timeSpent = _state.totalTime - _state.timeRemaining;

    // Gửi kết quả lên server
    _submitResults(status);
  }

  // Gửi kết quả lên server
  function _submitResults(status) {
    _state.isSubmitting = true;

    const payload = {
      answers: _state.answers,
      timeSpent: _state.stats.timeSpent,
      status: status,
    };

    fetch(`${_config.apiBase}/${_config.productId}/practice/submit`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
    })
      .then((response) => response.json())
      .then((data) => {
        if (data.success) {
          // Hiển thị kết quả
          _renderResults();
        } else {
          console.error("Failed to submit results:", data.message);
          alert("Có lỗi xảy ra khi lưu kết quả. Vui lòng thử lại.");
        }
      })
      .catch((error) => {
        console.error("Error submitting results:", error);
        alert("Có lỗi xảy ra khi lưu kết quả. Vui lòng thử lại.");
      });
  }

  // Hiển thị kết quả
  function _renderResults() {
    if (!_dom.resultContainer) return;

    const { correctAnswers, incorrectAnswers, score, timeSpent } = _state.stats;
    const minutes = Math.floor(timeSpent / 60);
    const seconds = timeSpent % 60;
    const timeDisplay = `${minutes}:${seconds.toString().padStart(2, "0")}`;

    _dom.resultContainer.innerHTML = `
      <div class="bg-white rounded-lg shadow-lg p-6 max-w-md mx-auto">
        <div class="text-center mb-6">
          <div class="mb-4">
            <i class="fas fa-trophy text-yellow-500 text-4xl"></i>
          </div>
          <h2 class="text-2xl font-bold text-gray-800">Kết quả luyện tập</h2>
          <p class="text-gray-600">Bạn đã hoàn thành bài luyện tập!</p>
        </div>
        
        <div class="grid grid-cols-2 gap-4 mb-6">
          <div class="bg-indigo-50 p-4 rounded-lg text-center">
            <div class="text-2xl font-bold text-indigo-600">${score}%</div>
            <div class="text-sm text-gray-600">Điểm số</div>
          </div>
          <div class="bg-green-50 p-4 rounded-lg text-center">
            <div class="text-2xl font-bold text-green-600">${timeDisplay}</div>
            <div class="text-sm text-gray-600">Thời gian</div>
          </div>
          <div class="bg-green-50 p-4 rounded-lg text-center">
            <div class="text-2xl font-bold text-green-600">${correctAnswers}</div>
            <div class="text-sm text-gray-600">Câu đúng</div>
          </div>
          <div class="bg-red-50 p-4 rounded-lg text-center">
            <div class="text-2xl font-bold text-red-600">${incorrectAnswers}</div>
            <div class="text-sm text-gray-600">Câu sai</div>
          </div>
        </div>
        
        <div class="flex flex-wrap gap-3 justify-center">
          <a href="/course/${_config.productId}/exams?tab=memory" class="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700">
            <i class="fas fa-arrow-left mr-2"></i>Quay lại
          </a>
          <button onclick="MemoryExamPractice.retryPractice()" class="px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700">
            <i class="fas fa-redo mr-2"></i>Luyện lại
          </button>
          <button onclick="MemoryExamPractice.showDetailedResults()" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
            <i class="fas fa-search mr-2"></i>Xem chi tiết
          </button>
        </div>
      </div>
    `;

    // Hiển thị container kết quả
    document.getElementById("practiceContainer").style.display = "none";
    _dom.resultContainer.style.display = "block";
  }

  // Luyện tập lại
  function retryPractice() {
    window.location.href = `/course/${_config.productId}/exams?tab=memory`;
  }

  // Xem kết quả chi tiết
  function showDetailedResults() {
    // Chuyển đổi container
    document.getElementById("practiceContainer").style.display = "none";
    document.getElementById("resultContainer").style.display = "none";
    document.getElementById("detailedResultsContainer").style.display = "block";

    // Hiển thị chi tiết từng câu
    _renderDetailedResults();
  }

  // Hiển thị chi tiết kết quả
  function _renderDetailedResults() {
    const detailedContainer = document.getElementById(
      "detailedResultsContainer"
    );
    if (!detailedContainer) return;

    let html = `
      <div class="bg-white rounded-lg shadow-lg p-6">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-xl font-bold text-gray-800">Chi tiết kết quả</h2>
          <div>
            <span class="text-sm text-gray-500">Điểm số: </span>
            <span class="text-lg font-semibold text-indigo-600">${_state.stats.score}%</span>
          </div>
        </div>
        <div class="space-y-6">
    `;

    _state.questions.forEach((question, index) => {
      const userAnswerIndex = _state.answers[index];
      const correctAnswerIndex = question.options.findIndex(
        (option) => option.isCorrect
      );
      const isCorrect = userAnswerIndex === correctAnswerIndex;

      const statusClass = isCorrect
        ? "bg-green-100 border-green-200"
        : "bg-red-100 border-red-200";
      const statusText = isCorrect ? "Đúng" : "Sai";
      const statusIconClass = isCorrect
        ? "text-green-500 fas fa-check-circle"
        : "text-red-500 fas fa-times-circle";

      html += `
        <div class="border ${statusClass} rounded-lg p-4">
          <div class="flex items-start justify-between mb-3">
            <h3 class="font-medium text-gray-800">Câu ${index + 1}</h3>
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
              isCorrect
                ? "bg-green-100 text-green-800"
                : "bg-red-100 text-red-800"
            }">
              <i class="${statusIconClass} mr-1"></i> ${statusText}
            </span>
          </div>
          <div class="text-gray-700 mb-4">${question.text}</div>
          <div class="space-y-2">
      `;

      question.options.forEach((option, optionIndex) => {
        let optionClass = "border-gray-200 bg-white";
        let labelClass = "bg-gray-100 text-gray-700";
        let iconHTML = "";

        if (optionIndex === correctAnswerIndex) {
          optionClass = "border-green-500 bg-green-50";
          labelClass = "bg-green-500 text-white";
          iconHTML = '<i class="fas fa-check ml-2"></i>';
        } else if (userAnswerIndex === optionIndex) {
          optionClass = "border-red-500 bg-red-50";
          labelClass = "bg-red-500 text-white";
          iconHTML = '<i class="fas fa-times ml-2"></i>';
        }

        const optionLabel = String.fromCharCode(65 + optionIndex); // A, B, C, D, ...

        html += `
          <div class="p-3 border rounded-lg ${optionClass}">
            <div class="flex items-center">
              <div class="w-7 h-7 flex items-center justify-center ${labelClass} rounded-full mr-3 text-sm font-medium">
                ${optionLabel}
              </div>
              <div class="flex-1">${option.text || option}</div>
              ${iconHTML}
            </div>
          </div>
        `;
      });

      html += `
          </div>
        </div>
      `;
    });

    html += `
        </div>
        <div class="mt-6 flex justify-between">
          <a href="/course/${_config.productId}/exams?tab=memory" class="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700">
            <i class="fas fa-arrow-left mr-2"></i>Quay lại
          </a>
          <button onclick="MemoryExamPractice.retryPractice()" class="px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700">
            <i class="fas fa-redo mr-2"></i>Luyện lại
          </button>
        </div>
      </div>
    `;

    detailedContainer.innerHTML = html;
  }

  // Phát âm thanh
  function _playSound(type) {
    if (!_config.sounds[type]) return;

    const audio = new Audio(_config.sounds[type]);
    audio.play().catch((e) => console.log("Audio play failed:", e));
  }

  // Hiển thị loading
  function _showLoading() {
    document.getElementById("loadingContainer")?.classList.remove("hidden");
    document.getElementById("practiceContainer")?.classList.add("hidden");
  }

  // Ẩn loading
  function _hideLoading() {
    document.getElementById("loadingContainer")?.classList.add("hidden");
    document.getElementById("practiceContainer")?.classList.remove("hidden");
  }

  // Hiển thị thông báo lỗi
  function _showErrorMessage(message) {
    const container = document.getElementById("errorContainer");
    if (!container) return;

    container.innerHTML = `
      <div class="bg-red-50 border-l-4 border-red-500 p-4">
        <div class="flex">
          <div class="flex-shrink-0">
            <i class="fas fa-exclamation-triangle text-red-500"></i>
          </div>
          <div class="ml-3">
            <p class="text-sm text-red-700">${message}</p>
          </div>
        </div>
      </div>
    `;

    container.classList.remove("hidden");
  }

  // Hiển thị cảnh báo khi người dùng cố gắng back
  function _showExitWarning() {
    const confirmExit = confirm(
      "Bạn có chắc muốn rời khỏi bài luyện tập? Tiến độ của bạn sẽ bị mất!"
    );
    if (confirmExit) {
      window.location.href = `/course/${_config.productId}/exams?tab=memory`;
    }
  }

  // Public API
  const publicAPI = {
    init: _init,
    selectAnswer: selectAnswer,
    nextQuestion: _nextQuestion,
    prevQuestion: _prevQuestion,
    retryPractice: retryPractice,
    showDetailedResults: showDetailedResults,
  };

  // Expose public API to window
  window.MemoryExamPractice = publicAPI;

  // Auto-initialize on DOMContentLoaded if we're on practice page
  document.addEventListener("DOMContentLoaded", function () {
    const isPracticePage =
      window.location.pathname.includes("/memory-practice");
    if (isPracticePage) {
      publicAPI.init();
    }
  });
})(window);
