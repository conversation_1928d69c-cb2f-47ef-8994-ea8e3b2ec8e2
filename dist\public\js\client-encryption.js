/**
 * Client-side data processing utilities
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
/**
 * Process encoded data using XOR algorithm with UTF-8 support
 */
function decryptData(dataHex, tokenHex, saltHex) {
    try {
        // Combine token and salt for processing
        const combinedKey = tokenHex + saltHex;
        const bytes = [];
        // Process hex to bytes (XOR processing)
        for (let i = 0; i < dataHex.length; i += 2) {
            const dataByte = parseInt(dataHex.substr(i, 2), 16);
            const keyByte = combinedKey.charCodeAt((i / 2) % combinedKey.length);
            const procByte = dataByte ^ keyByte;
            bytes.push(procByte);
        }
        // Convert bytes to UTF-8 string (browser compatibility)
        let decrypted = "";
        const uint8Array = new Uint8Array(bytes);
        // Use TextDecoder for proper UTF-8 handling
        if (typeof TextDecoder !== "undefined") {
            const decoder = new TextDecoder("utf-8");
            decrypted = decoder.decode(uint8Array);
        }
        else {
            // Fallback for older browsers
            decrypted = String.fromCharCode.apply(null, bytes);
        }
        return JSON.parse(decrypted);
    }
    catch (error) {
        console.error("Lỗi khi xử lý dữ liệu:", error);
        throw new Error("Không thể xử lý dữ liệu");
    }
}
/**
 * Process application configuration and load content
 */
function decryptAndLoadQuestions(payload, token, salt) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            console.log("Đang xử lý cấu hình ứng dụng...");
            console.log("Payload size:", payload.length);
            console.log("Token length:", token.length);
            console.log("Salt length:", salt.length);
            const processedData = decryptData(payload, token, salt);
            console.log("Xử lý thành công, tải được", processedData.length, "mục dữ liệu");
            // Return dữ liệu đã xử lý
            return processedData;
        }
        catch (error) {
            console.error("Lỗi trong quá trình xử lý:", error);
            console.error("Chi tiết lỗi:", error.message);
            // Hiển thị thông báo lỗi cho người dùng
            const container = document.getElementById("questionsScrollContainer");
            if (container) {
                container.innerHTML = `
        <div class="text-center py-8">
          <div class="text-red-600 mb-2">
            <i class="fas fa-exclamation-triangle text-2xl"></i>
          </div>
          <p class="text-gray-600">Không thể tải dữ liệu câu hỏi. Vui lòng thử lại.</p>
          <p class="text-sm text-gray-500 mt-2">Chi tiết lỗi: ${error.message}</p>
          <button onclick="location.reload()" class="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
            Tải lại trang
          </button>
        </div>
      `;
            }
            throw error;
        }
    });
}
// Test function để kiểm tra encryption/decryption
function testDecryption() {
    const testData = "Hello World";
    const testKey = "abcdef1234567890";
    const testIV = "0987654321fedcba";
    console.log("Testing XOR encryption/decryption...");
    console.log("Original:", testData);
    // Simulate encryption (for testing)
    const combinedKey = testKey + testIV;
    let encrypted = "";
    for (let i = 0; i < testData.length; i++) {
        const dataChar = testData.charCodeAt(i);
        const keyChar = combinedKey.charCodeAt(i % combinedKey.length);
        const encryptedChar = dataChar ^ keyChar;
        encrypted += encryptedChar.toString(16).padStart(2, "0");
    }
    console.log("Encrypted:", encrypted);
    // Test decryption
    let decrypted = "";
    for (let i = 0; i < encrypted.length; i += 2) {
        const encByte = parseInt(encrypted.substr(i, 2), 16);
        const keyByte = combinedKey.charCodeAt((i / 2) % combinedKey.length);
        const decByte = encByte ^ keyByte;
        decrypted += String.fromCharCode(decByte);
    }
    console.log("Decrypted:", decrypted);
    console.log("Test passed:", testData === decrypted);
}
/**
 * String encryption for localStorage
 */
function encryptStringOptimized(plaintext) {
    try {
        // Generate random token and salt
        const token = Math.random().toString(36).substring(2, 18);
        const salt = Math.random().toString(36).substring(2, 18);
        const combinedKey = token + salt;
        const keyLength = combinedKey.length;
        // Convert string to bytes
        const encoder = new TextEncoder();
        const bytes = encoder.encode(plaintext);
        // XOR encryption
        let encrypted = "";
        for (let i = 0; i < bytes.length; i++) {
            const encryptedByte = bytes[i] ^ combinedKey.charCodeAt(i % keyLength);
            encrypted += encryptedByte.toString(16).padStart(2, "0");
        }
        return {
            encryptedData: encrypted,
            token: token,
            salt: salt,
        };
    }
    catch (error) {
        console.error("Lỗi mã hóa string:", error);
        throw new Error("Không thể mã hóa dữ liệu");
    }
}
/**
 * String decryption for localStorage
 */
function decryptStringOptimized(encryptedData, token, salt) {
    try {
        const combinedKey = token + salt;
        const keyLength = combinedKey.length;
        // Convert hex to bytes
        const bytes = [];
        for (let i = 0; i < encryptedData.length; i += 2) {
            bytes.push(parseInt(encryptedData.substr(i, 2), 16));
        }
        // XOR decryption
        const decrypted = new Uint8Array(bytes.length);
        for (let i = 0; i < bytes.length; i++) {
            decrypted[i] = bytes[i] ^ combinedKey.charCodeAt(i % keyLength);
        }
        // Convert back to string
        const decoder = new TextDecoder();
        return decoder.decode(decrypted);
    }
    catch (error) {
        console.error("Lỗi giải mã string:", error);
        throw new Error("Không thể giải mã dữ liệu");
    }
}
// Expose functions globally
window.decryptAndLoadQuestions = decryptAndLoadQuestions;
window.decryptData = decryptData;
window.decryptDataOptimized = decryptDataOptimized;
window.encryptStringOptimized = encryptStringOptimized;
window.decryptStringOptimized = decryptStringOptimized;
window.testDecryption = testDecryption;
// Run test on load (for debugging)
if (window.location.search.includes("debug=1")) {
    document.addEventListener("DOMContentLoaded", testDecryption);
}
// Optimized client-side decryption utilities
/**
 * Ultra-optimized XOR decryption matching server-side Buffer performance
 */
function decryptDataOptimized(encryptedData, token, salt) {
    try {
        const combinedKey = token + salt;
        const keyLength = combinedKey.length;
        // Convert hex string to bytes more efficiently
        const bytes = [];
        for (let i = 0; i < encryptedData.length; i += 2) {
            bytes.push(parseInt(encryptedData.substr(i, 2), 16));
        }
        // Ultra-fast XOR decryption
        const decrypted = new Uint8Array(bytes.length);
        for (let i = 0; i < bytes.length; i++) {
            decrypted[i] = bytes[i] ^ combinedKey.charCodeAt(i % keyLength);
        }
        // Fast UTF-8 conversion
        const decoder = new TextDecoder();
        return decoder.decode(decrypted);
    }
    catch (error) {
        console.error("Lỗi khi giải mã tối ưu:", error);
        throw new Error("Không thể xử lý cấu hình ứng dụng");
    }
}
/**
 * Stream cipher decryption (ChaCha20-like)
 */
function streamDecrypt(encryptedData, token, salt) {
    try {
        const encoder = new TextEncoder();
        const keyBytes = encoder.encode(token.substring(0, 32));
        const ivBytes = encoder.encode(salt.substring(0, 16));
        const dataLength = encryptedData.length / 2;
        // Parse encrypted data
        const encrypted = new Uint8Array(dataLength);
        for (let i = 0; i < dataLength; i++) {
            encrypted[i] = parseInt(encryptedData.substr(i * 2, 2), 16);
        }
        // Generate same stream
        const stream = new Uint8Array(dataLength);
        let counter = 0;
        for (let i = 0; i < dataLength; i += 64) {
            const block = generateStreamBlock(keyBytes, ivBytes, counter++);
            const blockSize = Math.min(64, dataLength - i);
            for (let j = 0; j < blockSize; j++) {
                stream[i + j] = block[j];
            }
        }
        // XOR to decrypt
        const decrypted = new Uint8Array(dataLength);
        for (let i = 0; i < dataLength; i++) {
            decrypted[i] = encrypted[i] ^ stream[i];
        }
        const decoder = new TextDecoder();
        return decoder.decode(decrypted);
    }
    catch (error) {
        console.error("Lỗi khi xử lý stream:", error);
        throw new Error("Không thể xử lý cấu hình ứng dụng");
    }
}
/**
 * Generate stream block for stream cipher
 */
function generateStreamBlock(key, iv, counter) {
    const block = new Uint8Array(64);
    for (let i = 0; i < 64; i++) {
        const keyIndex = i % key.length;
        const ivIndex = i % iv.length;
        const counterByte = (counter + i) & 0xff;
        block[i] = (key[keyIndex] ^ iv[ivIndex] ^ counterByte ^ (i * 123)) & 0xff;
    }
    return block;
}
/**
 * Legacy XOR decryption (backward compatibility)
 */
function decryptDataLegacy(payload, token, salt) {
    try {
        const combinedKey = token + salt;
        const bytes = [];
        for (let i = 0; i < payload.length; i += 2) {
            const encByte = parseInt(payload.substr(i, 2), 16);
            const keyByte = combinedKey.charCodeAt((i / 2) % combinedKey.length);
            const decByte = encByte ^ keyByte;
            bytes.push(decByte);
        }
        const decoder = new TextDecoder();
        return decoder.decode(new Uint8Array(bytes));
    }
    catch (error) {
        console.error("Lỗi khi xử lý cấu hình legacy:", error);
        throw new Error("Không thể xử lý cấu hình ứng dụng");
    }
}
/**
 * Smart decryption with automatic algorithm detection
 */
function decryptAppConfig(appConfigData, token, salt, algorithm) {
    // Auto-detect algorithm if not provided
    if (!algorithm) {
        algorithm = "xor-optimized"; // Default fallback
    }
    let decryptedJson;
    switch (algorithm) {
        case "stream":
            decryptedJson = streamDecrypt(appConfigData, token, salt);
            break;
        case "xor-optimized":
            decryptedJson = decryptDataOptimized(appConfigData, token, salt);
            break;
        default:
            // Fallback to legacy
            decryptedJson = decryptDataLegacy(appConfigData, token, salt);
    }
    return JSON.parse(decryptedJson);
}
/**
 * Process application configuration with performance monitoring
 */
function processAppConfig(encryptedConfig) {
    return __awaiter(this, void 0, void 0, function* () {
        return new Promise((resolve, reject) => {
            try {
                const startTime = performance.now();
                if (!encryptedConfig || typeof encryptedConfig !== "object") {
                    reject(new Error("Cấu hình ứng dụng không hợp lệ"));
                    return;
                }
                const { appConfigData, token, salt, algorithm } = encryptedConfig;
                if (!appConfigData || !token || !salt) {
                    reject(new Error("Thiếu thông tin cấu hình cần thiết"));
                    return;
                }
                // Use requestIdleCallback for better performance
                if (window.requestIdleCallback) {
                    window.requestIdleCallback(() => {
                        try {
                            const config = decryptAppConfig(appConfigData, token, salt, algorithm);
                            const endTime = performance.now();
                            resolve(config);
                        }
                        catch (error) {
                            reject(error);
                        }
                    });
                }
                else {
                    // Fallback for older browsers
                    setTimeout(() => {
                        try {
                            const config = decryptAppConfig(appConfigData, token, salt, algorithm);
                            const endTime = performance.now();
                            resolve(config);
                        }
                        catch (error) {
                            reject(error);
                        }
                    }, 0);
                }
            }
            catch (error) {
                reject(error);
            }
        });
    });
}
/**
 * Load and process application configuration with UI feedback
 */
function loadAppConfiguration(encryptedConfig, onProgress) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            if (onProgress)
                onProgress("Khởi tạo cấu hình...", 10);
            if (!encryptedConfig) {
                throw new Error("Không có cấu hình để xử lý");
            }
            if (onProgress)
                onProgress("Đang xử lý cấu hình ứng dụng...", 50);
            const config = yield processAppConfig(encryptedConfig);
            if (onProgress)
                onProgress("Hoàn tất!", 100);
            return config;
        }
        catch (error) {
            console.error("❌ Lỗi khi tải cấu hình ứng dụng:", error);
            if (onProgress)
                onProgress("Lỗi xử lý cấu hình", 0);
            throw error;
        }
    });
}
/**
 * Batch process multiple configurations (for multiple exam types)
 */
function batchProcessConfigurations(configurations) {
    return __awaiter(this, void 0, void 0, function* () {
        const results = {};
        const promises = [];
        for (const [key, config] of Object.entries(configurations)) {
            if (config && config.appConfigData) {
                promises.push(processAppConfig(config)
                    .then((result) => {
                    results[key] = result;
                })
                    .catch((error) => {
                    console.error(`Lỗi xử lý cấu hình ${key}:`, error);
                    results[key] = null;
                }));
            }
        }
        yield Promise.all(promises);
        return results;
    });
}
// Legacy function for backward compatibility
function decryptAndLoadQuestions(encryptedQuestions, key, iv, onProgress) {
    return __awaiter(this, void 0, void 0, function* () {
        console.warn("⚠️ Sử dụng function cũ, khuyến nghị chuyển sang loadAppConfiguration");
        const legacyConfig = {
            appConfigData: encryptedQuestions,
            token: key,
            salt: iv,
        };
        return loadAppConfiguration(legacyConfig, onProgress);
    });
}
/**
 * Generate a session-based encryption key for localStorage
 */
function generateSessionKey() {
    // Use session ID + timestamp for unique key per session
    const sessionData = {
        timestamp: Date.now(),
        random: Math.random().toString(36).substring(2),
        userAgent: navigator.userAgent.substring(0, 20),
    };
    return btoa(JSON.stringify(sessionData)).substring(0, 32);
}
/**
 * Encrypt data for localStorage storage
 */
function encryptForStorage(data, customKey = null) {
    try {
        const jsonData = JSON.stringify(data);
        const key = customKey || generateSessionKey();
        const iv = Math.random().toString(36).substring(2, 18); // 16 chars
        // Simple XOR encryption for localStorage
        let encrypted = "";
        const combinedKey = key + iv;
        for (let i = 0; i < jsonData.length; i++) {
            const charCode = jsonData.charCodeAt(i);
            const keyCode = combinedKey.charCodeAt(i % combinedKey.length);
            const encryptedChar = (charCode ^ keyCode).toString(16).padStart(2, "0");
            encrypted += encryptedChar;
        }
        return {
            payload: encrypted,
            salt: iv,
            algorithm: "xor-storage",
        };
    }
    catch (error) {
        console.error("❌ Lỗi khi mã hóa dữ liệu cho storage:", error);
        return null;
    }
}
/**
 * Decrypt data from localStorage
 */
function decryptFromStorage(encryptedData, key) {
    try {
        if (!encryptedData || !encryptedData.payload || !encryptedData.salt) {
            throw new Error("Dữ liệu mã hóa không hợp lệ");
        }
        const { payload, salt } = encryptedData;
        const combinedKey = key + salt;
        // XOR decryption
        let decrypted = "";
        for (let i = 0; i < payload.length; i += 2) {
            const encryptedByte = parseInt(payload.substr(i, 2), 16);
            const keyByte = combinedKey.charCodeAt((i / 2) % combinedKey.length);
            const decryptedChar = String.fromCharCode(encryptedByte ^ keyByte);
            decrypted += decryptedChar;
        }
        return JSON.parse(decrypted);
    }
    catch (error) {
        console.error("❌ Lỗi khi giải mã dữ liệu từ storage:", error);
        return null;
    }
}
/**
 * Secure localStorage wrapper with encryption
 */
const SecureStorage = {
    // Get the encryption key for current session
    getSessionKey() {
        let key = sessionStorage.getItem("_sk");
        if (!key) {
            key = generateSessionKey();
            sessionStorage.setItem("_sk", key);
        }
        return key;
    },
    // Set encrypted item in localStorage
    setItem(key, value) {
        try {
            const sessionKey = this.getSessionKey();
            const encrypted = encryptForStorage(value, sessionKey);
            if (encrypted) {
                localStorage.setItem(key, JSON.stringify(encrypted));
                return true;
            }
            return false;
        }
        catch (error) {
            console.error(`❌ Lỗi khi lưu "${key}" vào localStorage:`, error);
            return false;
        }
    },
    // Get and decrypt item from localStorage
    getItem(key) {
        try {
            const stored = localStorage.getItem(key);
            if (!stored)
                return null;
            const encryptedData = JSON.parse(stored);
            const sessionKey = this.getSessionKey();
            const decrypted = decryptFromStorage(encryptedData, sessionKey);
            if (decrypted !== null) {
            }
            return decrypted;
        }
        catch (error) {
            console.error(`❌ Lỗi khi lấy "${key}" từ localStorage:`, error);
            return null;
        }
    },
    // Remove item from localStorage
    removeItem(key) {
        localStorage.removeItem(key);
    },
    // Clear all encrypted data
    clear() {
        localStorage.clear();
        sessionStorage.removeItem("_sk");
        console.log("🧹 Đã xóa toàn bộ dữ liệu localStorage đã mã hóa");
    },
};
/**
 * Clear all exam data from localStorage when starting a new exam
 * Keeps settings like theme, sound, transition time, and auth data
 */
function clearAllExamDataFromStorage(currentExamInfo = null) {
    // Settings to preserve (không xóa)
    const settingsToKeep = [
        "quizizzTransitionTime",
        "quizizzSoundEnabled",
        "quizizzTheme",
        "auth_status",
        "user_id",
        "clientId",
        "session_id",
        "_sk", // Session encryption key
    ];
    // Current exam patterns to preserve if specified
    const currentExamPatterns = [];
    if (currentExamInfo) {
        if (currentExamInfo.type === "quizizz" && currentExamInfo.examId) {
            currentExamPatterns.push(`quizizzExam_${currentExamInfo.examId}`);
        }
        if (currentExamInfo.type === "googleForm" && currentExamInfo.examId) {
            currentExamPatterns.push(`googleFormExam_${currentExamInfo.examId}`);
        }
        if (currentExamInfo.type === "practice" && currentExamInfo.practiceId) {
            currentExamPatterns.push(`practice_exam_data_${currentExamInfo.practiceId}`);
        }
    }
    let removedCount = 0;
    let keptCount = 0;
    const removedKeys = [];
    const keptKeys = [];
    // Scan all localStorage keys
    const allKeys = [];
    for (let i = 0; i < localStorage.length; i++) {
        allKeys.push(localStorage.key(i));
    }
    allKeys.forEach((key) => {
        if (!key)
            return;
        // Check if it's a setting to keep
        if (settingsToKeep.includes(key)) {
            keptKeys.push(key);
            keptCount++;
            return;
        }
        // Check if it's current exam data to keep
        const isCurrentExam = currentExamPatterns.some((pattern) => key === pattern);
        if (isCurrentExam) {
            keptKeys.push(key);
            keptCount++;
            return;
        }
        // Check if it's exam data that should be removed
        const isExamData = key.startsWith("quizizzExam_") ||
            key.startsWith("googleFormExam_") ||
            key.startsWith("practice_exam_data") ||
            key === "wrongQuestions";
        if (isExamData) {
            try {
                localStorage.removeItem(key);
                removedKeys.push(key);
                removedCount++;
            }
            catch (error) {
                console.error(`❌ Lỗi khi xóa ${key}:`, error);
            }
        }
        else {
            // Keep other data
            keptKeys.push(key);
            keptCount++;
        }
    });
    // Summary report
    if (removedKeys.length > 0) {
    }
    if (currentExamInfo) {
    }
    return {
        removed: removedCount,
        kept: keptCount,
        removedKeys,
        keptKeys,
    };
}
// Export functions for global use
window.loadAppConfiguration = loadAppConfiguration;
window.processAppConfig = processAppConfig;
window.batchProcessConfigurations = batchProcessConfigurations;
window.decryptAndLoadQuestions = decryptAndLoadQuestions; // Legacy support
window.SecureStorage = SecureStorage;
window.encryptForStorage = encryptForStorage;
window.decryptFromStorage = decryptFromStorage;
window.clearAllExamDataFromStorage = clearAllExamDataFromStorage;
//# sourceMappingURL=client-encryption.js.map