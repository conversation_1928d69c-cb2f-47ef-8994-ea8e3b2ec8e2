"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const student_middleware_1 = require("../middlewares/student.middleware");
const asynHandler_1 = __importDefault(require("../util/asynHandler"));
const router = express_1.default.Router();
// Route thi thử mới với practiceId
router.get("/course/:productId/:practiceId/practice-exam", student_middleware_1.checkStudentCourseAccess, (0, asynHandler_1.default)((req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    const { productId, practiceId } = req.params;
    const userId = (_a = res.locals.user) === null || _a === void 0 ? void 0 : _a._id;
    if (!userId) {
        return res.redirect("/login");
    }
    try {
        // Import models
        const PracticeExamHistory = (yield Promise.resolve().then(() => __importStar(require("../models/PracticeExamHistory")))).default;
        const Product = (yield Promise.resolve().then(() => __importStar(require("../models/products")))).default;
        // Kiểm tra practice exam có tồn tại và thuộc về user không
        const practiceExam = yield PracticeExamHistory.findOne({
            _id: practiceId,
            userId: userId,
            courseId: productId,
        });
        if (!practiceExam) {
            return res.status(404).render("error", {
                message: "Không tìm thấy bài thi hoặc bạn không có quyền truy cập",
                error: { status: 404 },
                user: res.locals.user,
            });
        }
        // Kiểm tra trạng thái của practice exam
        const now = new Date();
        const timeElapsed = Math.floor((now.getTime() - practiceExam.startedAt.getTime()) / 1000);
        const timeRemaining = 60 * 60 - timeElapsed; // 60 phút
        if (practiceExam.status === "completed") {
            // Bài thi đã hoàn thành
            return res.render("error", {
                message: "Bài thi này đã hoàn thành. Bạn có thể thi lại bằng cách tạo bài thi mới.",
                error: { status: 410 }, // Gone
                user: res.locals.user,
                showRetryButton: true,
                productId: productId,
            });
        }
        if (practiceExam.status === "time_up") {
            // Bài thi đã hết giờ
            return res.render("error", {
                message: "Bài thi này đã hết thời gian. Bạn có thể thi lại bằng cách tạo bài thi mới.",
                error: { status: 410 }, // Gone
                user: res.locals.user,
                showRetryButton: true,
                productId: productId,
            });
        }
        if (practiceExam.status === "abandoned") {
            // Bài thi đã bị hủy
            return res.render("error", {
                message: "Bài thi này đã bị hủy. Bạn có thể thi lại bằng cách tạo bài thi mới.",
                error: { status: 410 }, // Gone
                user: res.locals.user,
                showRetryButton: true,
                productId: productId,
            });
        }
        if (timeRemaining <= 0) {
            // Quá thời gian - tự động cập nhật thành time_up
            yield PracticeExamHistory.findByIdAndUpdate(practiceId, {
                status: "time_up",
                completedAt: new Date(),
            });
            return res.render("error", {
                message: "Bài thi này đã quá thời gian cho phép (60 phút). Bạn có thể thi lại bằng cách tạo bài thi mới.",
                error: { status: 410 }, // Gone
                user: res.locals.user,
                showRetryButton: true,
                productId: productId,
            });
        }
        // Bài thi đang trong trạng thái "in_progress" và còn thời gian
        // Import startPracticeExam từ exam controller
        const { startPracticeExam } = yield Promise.resolve().then(() => __importStar(require("../controllers/exam.controller")));
        // Gọi startPracticeExam với action = "continue" để load existing practice
        req.body = { action: "continue", practiceId };
        yield startPracticeExam(req, res);
    }
    catch (error) {
        console.error("❌ Error checking practice exam:", error);
        return res.status(500).render("error", {
            message: "Có lỗi xảy ra khi kiểm tra bài thi",
            error: { status: 500 },
            user: res.locals.user,
        });
    }
})));
exports.default = router;
//# sourceMappingURL=practiceRoutes.js.map