<!-- Thêm HTML cho popup cảnh báo -->
<div
  id="exitConfirmPopup"
  class="fixed inset-0 flex items-center justify-center z-50 hidden"
>
  <div class="fixed inset-0 bg-black opacity-50"></div>
  <div
    class="bg-white rounded-lg shadow-xl p-4 sm:p-6 max-w-md w-full mx-2 sm:mx-auto z-10 relative dark:bg-gray-800"
  >
    <div class="mb-4 text-center">
      <div
        class="mx-auto flex items-center justify-center h-12 sm:h-16 w-12 sm:w-16 rounded-full bg-yellow-100 mb-4 dark:bg-yellow-900"
      >
        <i
          class="fas fa-exclamation-triangle text-yellow-600 text-xl sm:text-2xl dark:text-yellow-300"
        ></i>
      </div>
      <h3
        class="text-base sm:text-lg font-medium text-gray-900 dark:text-gray-200"
        id="exitConfirmTitle"
      >
        B<PERSON><PERSON> c<PERSON> chắc chắn muốn rời khỏi?
      </h3>
      <p
        class="text-sm sm:text-base text-gray-500 mt-2 dark:text-gray-400"
        id="exitConfirmMessage"
      >
        Rời khỏi trang này sẽ kết thúc bài thi của bạn và không thể hoàn tác.
      </p>
    </div>
    <div class="flex justify-center space-x-3">
      <button
        id="exitConfirmStayBtn"
        class="bg-gray-200 hover:bg-gray-300 text-gray-800 py-2 px-3 sm:px-4 rounded-lg transition-colors text-sm sm:text-base dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-gray-200"
      >
        Ở lại làm bài
      </button>
      <button
        id="exitConfirmLeaveBtn"
        class="bg-red-600 hover:bg-red-700 text-white py-2 px-3 sm:px-4 rounded-lg transition-colors text-sm sm:text-base"
      >
        Kết thúc bài thi
      </button>
    </div>
  </div>
</div>
