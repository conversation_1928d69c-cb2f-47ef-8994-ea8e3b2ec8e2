"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const asynHandler_1 = __importDefault(require("../util/asynHandler"));
const router = express_1.default.Router();
// Route để xử lý tương thích cho URL cũ làm lại câu hỏi sai
router.get("/retry-wrong-questions", (0, asynHandler_1.default)((req, res) => __awaiter(void 0, void 0, void 0, function* () {
    // Lấy tham số từ query string
    const { examId, examType } = req.query;
    // Kiểm tra tham số bắt buộc
    if (!examId || !examType) {
        return res.status(400).json({
            success: false,
            message: "Thiếu thông tin cần thiết: examId và examType là bắt buộc",
        });
    }
    // Chuyển hướng đến URL chính xác với cùng các tham số
    return res.redirect(`/exam/retry-wrong-questions?examId=${examId}&examType=${examType}`);
})));
// Trang đăng ký
router.get("/register", (req, res) => {
    res.render("register", {
        title: "Đăng ký tài khoản",
        layout: "layouts/layout",
        user: res.locals.user || null,
        error: req.query.error || null,
    });
});
// Trang quên mật khẩu
router.get("/forgot-password", (req, res) => {
    res.render("forgot-password", {
        title: "Quên mật khẩu",
        layout: "layouts/layout",
        user: res.locals.user || null,
        error: req.query.error || null,
    });
});
// Trang đặt lại mật khẩu
router.get("/reset-password", (req, res) => {
    const token = req.query.token;
    if (!token) {
        return res.redirect("/forgot-password?error=Token không hợp lệ");
    }
    res.render("reset-password", {
        title: "Đặt lại mật khẩu",
        layout: "layouts/layout",
        user: res.locals.user || null,
        token,
        error: req.query.error || null,
    });
});
exports.default = router;
//# sourceMappingURL=redirectRoutes.js.map