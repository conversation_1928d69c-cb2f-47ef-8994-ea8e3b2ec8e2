"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.globalErrorMiddleware = exports.uncaughtErrorMiddleware = exports.notFoundMiddleware = void 0;
const errorhandler_1 = require("../util/errorhandler");
// Middleware xử lý lỗi 404 (Not Found)
const notFoundMiddleware = (req, res, next) => {
    // Log thông tin đường dẫn cho việc debug
    // console.log(
    //   `[404] Route không tìm thấy: ${req.originalUrl}, Method: ${req.method}`
    // );
    var _a;
    // Render trang 404 với URL gốc
    res.status(404).render("404", {
        user: ((_a = res.locals) === null || _a === void 0 ? void 0 : _a.user) || null,
        originalUrl: req.originalUrl,
        layout: "layouts/layout", // Đảm bảo sử dụng layout chung
    });
};
exports.notFoundMiddleware = notFoundMiddleware;
// Middleware bắt các lỗi không lường trước (uncaught)
const uncaughtErrorMiddleware = (err, req, res, next) => {
    console.error(`UNCAUGHT ERROR on ${req.method} ${req.path}:`, err);
    // Nếu lỗi không phải là instance của ErrorHandler, chuyển đổi nó
    if (!(err instanceof errorhandler_1.ErrorHandler)) {
        const error = new errorhandler_1.ErrorHandler(process.env.NODE_ENV === "production"
            ? "Đã xảy ra lỗi hệ thống"
            : err.message || "Lỗi hệ thống không xác định", 500);
        return next(error);
    }
    // Tiếp tục với middleware xử lý lỗi tiếp theo
    next(err);
};
exports.uncaughtErrorMiddleware = uncaughtErrorMiddleware;
// Middleware xử lý lỗi 500 và các lỗi khác
const globalErrorMiddleware = (err, req, res, next) => {
    var _a, _b;
    const statusCode = err.statusCode || 500;
    const message = err.message || "Lỗi máy chủ nội bộ";
    // Log chi tiết lỗi
    console.error(`[${statusCode}] Error for ${req.method} ${req.path}:`, {
        message: message,
        stack: err.stack,
    });
    // Kiểm tra nếu yêu cầu là API (XHR hoặc chờ đợi JSON)
    const isApiRequest = req.xhr ||
        ((_a = req.headers.accept) === null || _a === void 0 ? void 0 : _a.includes("application/json")) ||
        req.path.startsWith("/api/");
    if (isApiRequest) {
        // Trả về JSON cho các yêu cầu API
        res.status(statusCode).json({
            success: false,
            message: message,
            stack: process.env.NODE_ENV === "development" ? err.stack : undefined,
        });
    }
    else {
        try {
            // Render trang lỗi 500 cho các yêu cầu thông thường
            res.status(statusCode).render("500", {
                user: ((_b = res.locals) === null || _b === void 0 ? void 0 : _b.user) || null,
                statusCode: statusCode,
                message: message,
                stack: process.env.NODE_ENV === "development" ? err.stack : null,
                layout: "layouts/layout", // Đảm bảo sử dụng layout chung
            });
        }
        catch (renderError) {
            // Nếu có lỗi khi render, trả về lỗi dạng plain text
            console.error("Error rendering error page:", renderError);
            res.status(statusCode).send(`Lỗi máy chủ: ${message}`);
        }
    }
};
exports.globalErrorMiddleware = globalErrorMiddleware;
//# sourceMappingURL=errorMiddleware.js.map