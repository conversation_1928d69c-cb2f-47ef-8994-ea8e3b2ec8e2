<!-- Thi thử -->
<div class="bg-gray-50 sm:rounded-lg p-2 sm:p-6">
  <div
    class="flex flex-col sm:flex-row sm:items-center justify-between mb-4 sm:mb-6"
  >
    <h2
      class="text-lg sm:text-xl font-semibold text-gray-700 flex items-center mb-2 sm:mb-0"
    >
      <i class="fas fa-dumbbell mr-2 text-indigo-500"></i> Thi thử
    </h2>
    <div class="text-sm text-gray-500">
      <span
        class="bg-orange-100 text-orange-800 text-xs font-medium px-2.5 py-0.5 rounded"
      >
        <span id="totalQuestionsInPractice">
          <% if (questionsByExam && questionsByExam.length > 0) { %> <%=
          questionsByExam.reduce((total, exam) => total + exam.questions.length,
          0) %> <% } else { %> -- <% } %>
        </span>
        câu hỏi - 60 phút
      </span>
    </div>
  </div>

  <!-- Thông tin thi thử -->
  <div class="bg-white sm:rounded-lg p-3 sm:p-6 mb-4 sm:mb-6">
    <div
      class="flex flex-col sm:flex-row sm:items-start space-y-3 sm:space-y-0 sm:space-x-4"
    >
      <div class="flex-shrink-0 self-center sm:self-start">
        <div
          class="w-12 h-12 sm:w-16 sm:h-16 bg-orange-100 rounded-full flex items-center justify-center"
        >
          <i class="fas fa-flask text-orange-600 text-lg sm:text-2xl"></i>
        </div>
      </div>
      <div class="flex-1">
        <h3
          class="text-base sm:text-lg font-semibold text-gray-800 mb-2 text-center sm:text-left"
        >
          Thi thử môn học
        </h3>
        <p
          class="text-gray-600 mb-3 sm:mb-4 text-sm sm:text-base text-center sm:text-left"
        >
          Luyện tập với
          <span class="font-medium text-orange-600" id="practiceQuestionCount">
            <% if (questionsByExam && questionsByExam.length > 0) { %> <%=
            Math.min(100, questionsByExam.reduce((total, exam) => total +
            exam.questions.length, 0)) %> <% } else { %> -- <% } %>
          </span>
          câu hỏi được chọn ngẫu nhiên từ toàn bộ đề cương môn học. Đây là cơ
          hội tuyệt vời để kiểm tra kiến thức và chuẩn bị cho kỳ thi chính thức.
        </p>

        <!-- Thống kê -->
        <div class="grid grid-cols-3 gap-2 sm:gap-4 mb-3 sm:mb-4">
          <div class="bg-blue-50 rounded-lg p-2 sm:p-3">
            <div
              class="flex flex-col sm:flex-row sm:items-center text-center sm:text-left"
            >
              <i
                class="fas fa-question-circle text-blue-500 mb-1 sm:mb-0 sm:mr-2 text-sm sm:text-base"
              ></i>
              <div>
                <div class="text-xs sm:text-sm text-gray-600">Số câu hỏi</div>
                <div
                  class="font-semibold text-gray-800 text-sm sm:text-base"
                  id="practiceQuestionCountStat"
                >
                  <% if (questionsByExam && questionsByExam.length > 0) { %> <%=
                  Math.min(100, questionsByExam.reduce((total, exam) => total +
                  exam.questions.length, 0)) %> <% } else { %> -- <% } %>
                </div>
              </div>
            </div>
          </div>
          <div class="bg-green-50 rounded-lg p-2 sm:p-3">
            <div
              class="flex flex-col sm:flex-row sm:items-center text-center sm:text-left"
            >
              <i
                class="fas fa-clock text-green-500 mb-1 sm:mb-0 sm:mr-2 text-sm sm:text-base"
              ></i>
              <div>
                <div class="text-xs sm:text-sm text-gray-600">Thời gian</div>
                <div class="font-semibold text-gray-800 text-sm sm:text-base">
                  60 phút
                </div>
              </div>
            </div>
          </div>
          <div class="bg-purple-50 rounded-lg p-2 sm:p-3">
            <div
              class="flex flex-col sm:flex-row sm:items-center text-center sm:text-left"
            >
              <i
                class="fas fa-random text-purple-500 mb-1 sm:mb-0 sm:mr-2 text-sm sm:text-base"
              ></i>
              <div>
                <div class="text-xs sm:text-sm text-gray-600">Câu hỏi</div>
                <div class="font-semibold text-gray-800 text-xs sm:text-base">
                  Ngẫu nhiên
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Lưu ý quan trọng -->
  <div
    class="bg-yellow-50 border-l-4 border-yellow-400 p-3 sm:p-4 mb-4 sm:mb-6"
  >
    <div class="flex">
      <div class="flex-shrink-0">
        <i class="fas fa-exclamation-triangle text-yellow-400"></i>
      </div>
      <div class="ml-3">
        <h4 class="text-sm font-medium text-yellow-800">Lưu ý quan trọng</h4>
        <div class="mt-2 text-xs sm:text-sm text-yellow-700">
          <ul class="list-disc list-inside space-y-1">
            <li>Bạn cần trả lời hết tất cả câu hỏi mới có thể nộp bài</li>
            <li>Thời gian làm bài là 60 phút và sẽ tự động nộp khi hết giờ</li>
            <li>Kết quả sẽ được lưu lại để bạn có thể xem lại sau này</li>
            <li>Câu hỏi được chọn ngẫu nhiên từ toàn bộ đề cương</li>
          </ul>
        </div>
      </div>
    </div>
  </div>

  <!-- Nút bắt đầu -->
  <div class="text-center px-2 sm:px-0">
    <% if ((!questionsByExam || questionsByExam.length === 0) &&
    !encryptedQuestions) { %>
    <!-- Không có câu hỏi -->
    <div class="bg-gray-100 sm:rounded-lg p-6 sm:p-8">
      <i
        class="fas fa-info-circle text-gray-400 text-2xl sm:text-3xl mb-3 sm:mb-4"
      ></i>
      <h3 class="text-base sm:text-lg font-medium text-gray-700 mb-2">
        Chưa có câu hỏi
      </h3>
      <p class="text-gray-500 text-sm sm:text-base">
        Chưa có câu hỏi nào trong đề cương để tạo bài thi thử.
      </p>
    </div>
    <% } else { %>
    <!-- Có câu hỏi - hiển thị button -->
    <button
      onclick="startPracticeExam()"
      class="w-full sm:w-auto inline-flex items-center justify-center px-6 sm:px-8 py-3 sm:py-4 bg-gradient-to-r from-orange-500 to-red-600 text-white text-base sm:text-lg font-semibold rounded-lg sm:rounded-xl hover:from-orange-600 hover:to-red-700 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl"
    >
      <i class="fas fa-play mr-2 sm:mr-3"></i>
      Bắt đầu thi thử
    </button>
    <% } %>
  </div>

  <!-- Lịch sử thi thử -->
  <div class="bg-white sm:rounded-lg p-3 sm:p-6 mt-6">
    <div class="mb-3 sm:mb-4">
      <h3
        class="text-base sm:text-lg font-semibold text-gray-800 flex items-center"
      >
        <i class="fas fa-history mr-2 text-indigo-500"></i>
        <span class="hidden sm:inline">Lịch sử thi thử</span>
        <span class="sm:hidden">Lịch sử</span>
      </h3>
    </div>

    <div id="practiceHistoryContainer">
      <!-- Loading state -->
      <div class="text-center py-4" id="historyLoading">
        <i class="fas fa-spinner fa-spin text-gray-400 mb-2"></i>
        <p class="text-sm text-gray-500">Đang tải lịch sử...</p>
      </div>
    </div>
  </div>
</div>
