import Student from "../models/student";
import { Product } from "./modelService";

class StudentsService {
  async createStudent({
    email,
    productId,
  }: {
    email: string;
    productId: string;
  }) {
    const normalizedEmail = email.toLowerCase();

    try {
      const student = await Student.findOneAndUpdate(
        { email: normalizedEmail, productId },
        { email: normalizedEmail, productId },
        { new: true, upsert: true }
      ).lean();
      console.log("student", student);

      if (
        student &&
        student.createdAt.getTime() !== student.updatedAt.getTime()
      ) {
        return {
          message: "User already in the class",
        };
      }

      return {
        student,
      };
    } catch (error) {
      console.error("Error creating/finding student:", error);
      throw error;
    }
  }

  async getListStudentByProductId(productId: string, page: number = 1) {
    const limit = 15;
    const skip = (page - 1) * limit;
    const [students, total] = await Promise.all([
      Student.find({ productId })
        .sort({ createdAt: -1 })
        .select("-__v -createdAt -updatedAt")
        .skip(skip)
        .limit(limit)
        .lean(),
      Student.countDocuments(),
    ]);
    return {
      students,
      pagination: {
        total,
        page,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async deleteStudent(studentId: string) {
    return await Student.findByIdAndDelete(studentId);
  }

  async searchStudentByProductId(
    keyword: string,
    productId: string,
    page: number = 1
  ) {
    const limit = 15;
    const skip = (page - 1) * limit;
    const [students, total] = await Promise.all([
      Student.find({
        $or: [{ email: { $regex: keyword, $options: "i" } }],
        productId,
      })
        .select("-__v -createdAt -updatedAt")
        .skip(skip)
        .limit(limit)
        .lean(),
      Student.countDocuments({
        $or: [{ email: { $regex: keyword, $options: "i" } }, { productId }],
      }),
    ]);
    return {
      students,
      pagination: {
        total,
        page,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async getCountStudentByProductId(productId: string) {
    return await Student.countDocuments({ productId });
  }

  /**
   * Lấy danh sách đề thi của sinh viên theo khóa học
   * @param studentId ID của sinh viên
   * @param productId ID của khóa học
   * @returns Danh sách đề thi
   */
  async getStudentExams(studentId: string, productId: string) {
    try {
      // Xác nhận sinh viên tồn tại và có quyền truy cập khóa học
      const student = await Student.findOne({ _id: studentId, productId });

      if (!student) {
        throw new Error("Sinh viên không có quyền truy cập khóa học này");
      }

      // Giả định rằng có một model Exam với cấu trúc phù hợp
      // Bạn cần thay thế đoạn này bằng truy vấn thực tế đến model Exam của bạn
      // Ví dụ:
      // const exams = await Exam.find({ productId }).lean();

      // Hiện tại trả về dữ liệu mẫu
      return {
        exams: [],
        message: "Cần triển khai kết nối với model Exam thực tế",
      };
    } catch (error) {
      console.error("Error fetching student exams:", error);
      throw error;
    }
  }

  /**
   * Lấy danh sách bài kiểm tra của sinh viên theo khóa học
   * @param studentId ID của sinh viên
   * @param productId ID của khóa học
   * @returns Danh sách bài kiểm tra
   */
  async getStudentTests(studentId: string, productId: string) {
    try {
      // Xác nhận sinh viên tồn tại và có quyền truy cập khóa học
      const student = await Student.findOne({ _id: studentId, productId });

      if (!student) {
        throw new Error("Sinh viên không có quyền truy cập khóa học này");
      }

      // Giả định rằng có một model Test với cấu trúc phù hợp
      // Bạn cần thay thế đoạn này bằng truy vấn thực tế đến model Test của bạn
      // Ví dụ:
      // const tests = await Test.find({ productId }).lean();

      // Hiện tại trả về dữ liệu mẫu
      return {
        tests: [],
        message: "Cần triển khai kết nối với model Test thực tế",
      };
    } catch (error) {
      console.error("Error fetching student tests:", error);
      throw error;
    }
  }
}

// Hàm tìm sinh viên theo email
export const getStudentByEmail = async (email: string) => {
  try {
    return await Student.findOne({ email });
  } catch (error) {
    console.error("Lỗi khi tìm sinh viên theo email:", error);
    throw error;
  }
};

// Hàm lấy khóa học của sinh viên
export const getStudentCourses = async (studentId: string) => {
  try {
    // Lấy sinh viên đã chọn để lấy email
    const student = await Student.findById(studentId);
    if (!student) {
      throw new Error("Không tìm thấy sinh viên");
    }

    // Tìm tất cả bản ghi sinh viên có cùng email
    const studentRecords = await Student.find({
      email: student.email,
    }).populate("productId");

    if (!studentRecords || studentRecords.length === 0) {
      return [];
    }

    // Lọc các bản ghi có productId và loại bỏ các bản ghi bị trùng lặp
    const uniqueProductIds = new Map();

    studentRecords.forEach((record) => {
      if (record.productId) {
        const productId = record.productId._id.toString();
        if (!uniqueProductIds.has(productId)) {
          uniqueProductIds.set(productId, record.productId);
        }
      }
    });

    console.log(uniqueProductIds);
    // Chuyển Map thành mảng các khóa học và sắp xếp ngược lại
    return Array.from(uniqueProductIds.values()).reverse();
  } catch (error) {
    console.error("Lỗi khi lấy khóa học của sinh viên:", error);
    throw error;
  }
};

export default new StudentsService();
