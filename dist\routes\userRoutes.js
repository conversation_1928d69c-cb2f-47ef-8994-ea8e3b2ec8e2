"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const authMiddleware_1 = require("../middlewares/authMiddleware");
const asynHandler_1 = __importDefault(require("../util/asynHandler"));
const router = express_1.default.Router();
// Lấy thông tin người dùng hiện tại
router.get("/me", (0, asynHandler_1.default)(authMiddleware_1.authenticateToken), (0, asynHandler_1.default)((req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = req.user;
        if (!user) {
            return res.status(401).json({ message: "Không tìm thấy người dùng" });
        }
        // Trả về thông tin người dùng (loại bỏ các trường nhạy cảm)
        res.status(200).json({
            id: user._id,
            email: user.email,
            displayName: user.displayName,
            firstName: user.firstName,
            lastName: user.lastName,
            profilePhoto: user.profilePhoto,
        });
    }
    catch (error) {
        res.status(500).json({ message: error.message });
    }
})));
exports.default = router;
//# sourceMappingURL=userRoutes.js.map