/* CSS cho trang Quizizz */

/* <PERSON><PERSON><PERSON> diện mặc định (light mode) */
:root {
  --header-bg: #9333ea; /* bg-purple-600 */
  --header-hover: #7e22ce; /* bg-purple-700 */
  --panel-bg: #f5f3ff; /* bg-purple-50 */
  --panel-border: #ede9fe; /* border-purple-100 */
  --progress-bg: #f3f4f6; /* bg-gray-100 */
  --progress-bar: #8b5cf6; /* bg-purple-500 */
  --container-bg: #ffffff; /* bg-white */
  --text-color: #1f2937; /* text-gray-800 */
  --option-border: #e5e7eb; /* border-gray-200 */
  --option-bg-hover: #f9fafb; /* bg-gray-50 */
  --option-letter-bg: #f3f4f6; /* bg-gray-100 */
  --selected-border: #8b5cf6; /* border-purple-500 */
  --selected-bg: #f5f3ff; /* bg-purple-50 */
  --submit-bg: #059669; /* bg-green-600 */
  --submit-hover: #047857; /* bg-green-700 */
  --timer-bg: #5b21b6; /* bg-purple-800 */
  --dropdown-bg: #7e22ce; /* bg-purple-700 */
  --dropdown-border: #a855f7; /* border-purple-400 */
  --next-button-bg: #9333ea; /* bg-purple-600 */
  --next-button-hover: #7e22ce; /* bg-purple-700 */
  --finish-button-bg: #059669; /* bg-green-600 */
  --finish-button-hover: #047857; /* bg-green-700 */

  /* Màu cho đáp án đúng và sai */
  --correct-answer-bg: hsl(114, 100%, 87%); /* bg-green-100 */
  --correct-answer-border: #22c55e; /* border-green-500 */
  --correct-answer-text: #000000; /* text-green-800 */
  --wrong-answer-bg: hsl(0, 77%, 88%); /* bg-red-100 */
  --wrong-answer-border: #f18383; /* border-red-500 */
  --wrong-answer-text: #000000; /* text-red-800 */
  --correct-icon-color: #22c55e; /* text-green-600 */
  --wrong-icon-color: #ef4444; /* text-red-600 */
}

/* Giao diện tối (dark mode) */
.theme-dark {
  --header-bg: #66207a; /* bg-purple-950 - Màu tím đậm hơn cho header */
  --header-hover: #4c1d95; /* bg-purple-900 */
  --panel-bg: #863c9b; /* bg-purple-950 */
  --panel-border: #4c1d95; /* border-purple-900 */
  --progress-bg: #fcfcfc; /* bg-gray-800 */
  --progress-bar: #8b5cf6; /* bg-purple-500 */
  --container-bg: #111827; /* bg-gray-900 */
  --text-color: #f9fafb; /* text-gray-50 */
  --option-border: #374151; /* border-gray-700 */
  --option-bg-hover: #1f2937; /* bg-gray-800 */
  --option-letter-bg: #374151; /* bg-gray-700 */
  --selected-border: #8b5cf6; /* border-purple-500 */
  --selected-bg: #4c1d95; /* bg-purple-900 */
  --submit-bg: #047857; /* bg-green-700 */
  --submit-hover: #065f46; /* bg-green-800 */
  --timer-bg: #312e81; /* bg-indigo-900 */
  --dropdown-bg: #4c1d95; /* bg-purple-900 */
  --dropdown-border: #6d28d9; /* border-purple-600 */
  --next-button-bg: #4c1d95; /* bg-purple-900 */
  --next-button-hover: #5b21b6; /* bg-purple-800 */
  --finish-button-bg: #047857; /* bg-green-700 */
  --finish-button-hover: #065f46; /* bg-green-800 */

  /* Màu cho đáp án đúng và sai trong chế độ tối */
  --correct-answer-bg: #4cc003; /* bg-green-600 */
  --correct-answer-border: #44af02; /* border-green-500 */
  --correct-answer-text: #ffffff; /* text-white */
  --wrong-answer-bg: #e20909; /* bg-red-600 */
  --wrong-answer-border: #e20000; /* border-red-500 */
  --wrong-answer-text: #ffffff; /* text-white */
  --correct-icon-color: #ffffff; /* text-white */
  --wrong-icon-color: #ffffff; /* text-white */

  /* Màu sắc cho giao diện Quizizz tối */
  --color-option-1-bg: #0966bc; /* Màu xanh dương cho option 1 */
  --color-option-2-bg: #00a8b5; /* Màu xanh lục cho option 2 */
  --color-option-3-bg: #f79603; /* Màu cam cho option 3 */
  --color-option-4-bg: #f24877; /* Màu hồng cho option 4 */

  --color-option-1-text: #ffffff;
  --color-option-2-text: #ffffff;
  --color-option-3-text: #ffffff;
  --color-option-4-text: #ffffff;

  --quiz-background: #5a0773; /* Màu nền chính */
  /* --quiz-dark-background: #3b0764; Màu nền đậm hơn */
}

body.theme-dark {
  background-color: var(--quiz-background);
}

#quizizzContainer {
  transition: all 0.3s ease;
}

#quizizzContainer > div {
  background-color: var(--container-bg);
}

/* Header */
#quizizzContainer > div > div:first-child {
  background-color: var(--header-bg);
}

/* Dropdown trong header */
#transitionTime {
  background-color: var(--dropdown-bg);
  border-color: var(--dropdown-border);
}

/* Thông tin bài thi */
#quizizzContainer > div > div:nth-child(2) {
  background-color: var(--panel-bg);
  border-color: var(--panel-border);
  color: var(--text-color);
}

/* Thanh tiến độ */
#quizizzContainer > div > div:nth-child(3) {
  background-color: var(--progress-bg);
}

#progressBar {
  background-color: var(--progress-bar);
}

/* Nút nộp bài */
#submitExamButton {
  background-color: var(--submit-bg);
}

#submitExamButton:hover {
  background-color: var(--submit-hover);
}

/* Nút trong câu hỏi template */
#nextButton {
  background-color: var(--next-button-bg);
}

#nextButton:hover {
  background-color: var(--next-button-hover);
}

#finishButton {
  background-color: var(--finish-button-bg);
}

#finishButton:hover {
  background-color: var(--finish-button-hover);
}

/* Timer */
#examTimer {
  background-color: var(--timer-bg);
}

/* Nút công tắc giao diện */
#themeToggleBtn {
  cursor: pointer;
  transition: all 0.2s;
}

#themeToggleBtn:hover {
  opacity: 0.8;
}

.option-card {
  position: relative;
  border: 2px solid var(--option-border);
  border-radius: 0.5rem;
  padding: 0.75rem 1rem;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
}

.option-card:hover {
  background-color: var(--option-bg-hover);
}

.option-card.selected {
  border-color: var(--selected-border);
  background-color: var(--selected-bg);
}

/* Giao diện đáp án đúng sai */
/* Chế độ mặc định (light mode) */
.option-card.border-red-500,
.option-card.answer-wrong {
  background-color: var(--wrong-answer-bg) !important;
  border-color: var(--wrong-answer-border) !important;
  color: var(--wrong-answer-text) !important;
}

.option-card.selected,
.option-card.answer-correct {
  background-color: var(--correct-answer-bg) !important;
  border-color: var(--correct-answer-border) !important;
  color: var(--correct-answer-text) !important;
}

/* Chế độ tối (dark mode) */
.theme-dark .option-card.border-red-500,
.theme-dark .option-card.answer-wrong {
  background-color: var(--wrong-answer-bg) !important;
  border-color: var(--wrong-answer-border) !important;
  color: var(--wrong-answer-text) !important;
}

.theme-dark .option-card.selected,
.theme-dark .option-card.answer-correct {
  background-color: var(--correct-answer-bg) !important;
  border-color: var(--correct-answer-border) !important;
  color: var(--correct-answer-text) !important;
}

.theme-dark .option-card .text-green-600 {
  color: var(--correct-icon-color) !important;
}

.theme-dark .option-card .text-red-600 {
  color: var(--wrong-icon-color) !important;
}

.option-card .option-letter {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 9999px;
  background-color: var(--option-letter-bg);
  margin-right: 0.5rem;
  font-weight: 500;
  flex-shrink: 0;
}

.option-card.selected .option-letter {
  background-color: var(--selected-border);
  color: white;
}

@media (max-width: 640px) {
  .option-card {
    padding: 0.625rem 0.75rem;
  }

  .option-card .option-letter {
    width: 1.25rem;
    height: 1.25rem;
    font-size: 0.875rem;
  }
}

/* Style cho nút âm thanh */
#soundToggleBtn {
  cursor: pointer;
  transition: all 0.2s;
}

#soundToggleBtn:hover {
  opacity: 0.8;
}

/* Style cho trang làm lại câu hỏi */
.question-card {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* Tối ưu khoảng cách trong container câu hỏi */
#question-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 0; /* Quan trọng để flex-grow hoạt động đúng */
}

/* Giảm khoảng cách giữa câu hỏi và các tùy chọn */
#options-container {
  margin-bottom: 0;
}

/* Tối ưu khoảng cách trong option button */
.option-button {
  display: flex;
  align-items: center;
  padding: 0.75rem 0.75rem;
  word-break: break-word;
  position: relative;
}

/* Đảm bảo container chính không bị kéo giãn quá mức */
#exam-container {
  max-height: calc(100vh - 200px); /* Giới hạn chiều cao tối đa */
  min-height: 400px; /* Đảm bảo chiều cao tối thiểu */
  display: flex;
  flex-direction: column;
}

/* Cải thiện cách hiển thị các option */
.option-red,
.option-blue,
.option-green,
.option-yellow {
  transition: all 0.2s;
}

/* Giao diện tối - các màu cho các option */
.theme-dark .option-red {
  background-color: var(--color-option-1-bg);
  border-color: var(--color-option-1-bg);
  color: var(--color-option-1-text);
}

.theme-dark .option-blue {
  background-color: var(--color-option-2-bg);
  border-color: var(--color-option-2-bg);
  color: var(--color-option-2-text);
}

.theme-dark .option-green {
  background-color: var(--color-option-3-bg);
  border-color: var(--color-option-3-bg);
  color: var(--color-option-3-text);
}

.theme-dark .option-yellow {
  background-color: var(--color-option-4-bg);
  border-color: var(--color-option-4-bg);
  color: var(--color-option-4-text);
}

.theme-dark .option-red:hover,
.theme-dark .option-blue:hover,
.theme-dark .option-green:hover,
.theme-dark .option-yellow:hover {
  filter: brightness(1.1);
}

.theme-dark .option-letter {
  color: white;
  background-color: rgba(255, 255, 255, 0.2);
}

.theme-dark .option-number {
  position: absolute;
  top: 8px;
  right: 8px;
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
}

/* Cải thiện cách hiển thị câu hỏi */
#question-container h2 {
  word-break: break-word;
  hyphens: auto;
  color: var(--text-color);
}

/* Giao diện tối - đặt màu chung cho toàn bộ trang */
.theme-dark #quizizzContainer {
  background-color: var(--quiz-background);
}

.theme-dark #quizizzContainer > div {
  background-color: var(--quiz-dark-background);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3);
}

.theme-dark #questionContainer {
  background-color: var(--quiz-dark-background);
  color: white;
}

.theme-dark #questionText {
  color: white;
}

/* CSS cho trang kết quả */
.result-display {
  transition: all 0.3s ease;
  color: var(--text-color);
}

.result-display h2,
.result-display h3,
.result-display h4 {
  color: var(--text-color);
  transition: color 0.3s ease;
}

.theme-dark .result-display .text-purple-600 {
  color: #ffffff !important; /* text-purple-400 - màu tím sáng hơn */
}

.theme-dark .result-display .bg-purple-100 {
  background-color: #4c1d95 !important; /* bg-purple-900 */
}

.theme-dark .result-display .text-gray-600,
.theme-dark .result-display .text-gray-700,
.theme-dark .result-display .text-gray-800 {
  color: #000000 !important; /* text-gray-200 */
}

.theme-dark .result-display .bg-gray-50 {
  background-color: #ffffff !important; /* bg-gray-800 */
}

.theme-dark .result-display .border-gray-200 {
  border-color: #ffffff !important; /* border-gray-700 */
}

.theme-dark .result-display h3 {
  color: #000000 !important;
}
/* Kết quả câu đúng/sai trong dark mode */
.theme-dark .result-display .bg-green-100 {
  background-color: #065f46 !important; /* bg-green-800 */
  color: white !important;
  border-color: #059669 !important; /* border-green-600 */
}

.theme-dark .result-display .bg-red-100 {
  background-color: #b91c1c !important; /* bg-red-700 */
  color: white !important;
  border-color: #dc2626 !important; /* border-red-600 */
}

.theme-dark .result-display .text-red-800 {
  color: white !important;
}

.theme-dark .result-display .text-green-800 {
  color: white !important;
}

/* Nút trong trang kết quả */
.theme-dark .result-display .bg-purple-600 {
  background-color: #7e22ce !important; /* bg-purple-700 */
}

.theme-dark .result-display .hover\:bg-purple-700:hover {
  background-color: #6b21a8 !important; /* bg-purple-800 */
}

.theme-dark .result-display .bg-blue-600 {
  background-color: #3b82f6 !important; /* bg-blue-500 */
}

.theme-dark .result-display .hover\:bg-blue-700:hover {
  background-color: #2563eb !important; /* bg-blue-600 */
}

/* Review option colors for dark mode */
.theme-dark .result-display .bg-purple-50 {
  background-color: #4c1d95 !important; /* bg-purple-900 */
  color: white !important;
}

/* CSS cho cảnh báo bảo mật */
.theme-dark .security-warning {
  background-color: var(--container-bg);
}

.theme-dark .security-warning-title {
  color: #ef4444; /* text-red-500 */
}

.theme-dark .security-warning-message {
  color: var(--text-color);
}

.theme-dark .security-warning-btn {
  background-color: #dc2626; /* bg-red-600 */
}

.theme-dark .security-warning-btn:hover {
  background-color: #b91c1c; /* bg-red-700 */
}

/* ========================================
   QUIZIZZ ANIMATIONS & EFFECTS
   ======================================== */

/* Keyframe Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-100px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideOutLeft {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(-100px);
  }
}

@keyframes slideOutRight {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(100px);
  }
}
/* Bounce In: tiến vào */
@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3) translateY(-20px);
  }
  50% {
    opacity: 1;
    transform: scale(1.05) translateY(0);
  }
  70% {
    transform: scale(0.95);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}
/* Pulse: nhấp nháy */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}
/* Gentle Scale: tăng tốc độ */
@keyframes gentleScale {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.03);
  }
  100% {
    transform: scale(1.02);
  }
}
/* Gentle Shake Scale: rung nhẹ */
@keyframes gentleShakeScale {
  0% {
    transform: scale(1) translateX(0);
  }
  25% {
    transform: scale(1.02) translateX(-2px);
  }
  50% {
    transform: scale(1.03) translateX(2px);
  }
  75% {
    transform: scale(1.02) translateX(-1px);
  }
  100% {
    transform: scale(1.02) translateX(0);
  }
}
/* Zoom In: phóng to */
@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
/* Zoom Out: thu nhỏ */
@keyframes zoomOut {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.8);
  }
}
/* Pop In: nhảy vào */
@keyframes popIn {
  0% {
    opacity: 0;
    transform: scale(0.5) rotate(-10deg);
  }
  50% {
    opacity: 1;
    transform: scale(1.1) rotate(5deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
}
/* Slide Up: đi lên */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
/* Slide Down: đi xuống */
@keyframes slideDown {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(50px);
  }
}
/* Correct Answer: câu trả lời đúng */
@keyframes correctAnswer {
  0% {
    background-color: var(--option-bg-hover);
    transform: scale(1);
  }
  25% {
    background-color: var(--correct-answer-bg);
    transform: scale(1.02);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    background-color: var(--correct-answer-bg);
    transform: scale(1.02);
  }
}
/* Wrong Answer: câu trả lời sai */
@keyframes wrongAnswer {
  0% {
    background-color: var(--option-bg-hover);
    transform: scale(1);
  }
  25% {
    background-color: var(--wrong-answer-bg);
    transform: scale(1.02) translateX(-8px);
  }
  50% {
    transform: scale(1.02) translateX(8px);
  }
  75% {
    transform: scale(1.02) translateX(-4px);
  }
  100% {
    background-color: var(--wrong-answer-bg);
    transform: scale(1.02);
  }
}
/* Celebration: vui mừng */
@keyframes celebration {
  0%,
  100% {
    transform: scale(1) rotate(0deg);
  }
  25% {
    transform: scale(1.1) rotate(-5deg);
  }
  50% {
    transform: scale(1.2) rotate(5deg);
  }
  75% {
    transform: scale(1.1) rotate(-2deg);
  }
}
/* Loading: tải */
@keyframes loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
/* Countdown Pulse: đếm ngược */
@keyframes countdownPulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
/* Progress Shimmer: hiệu ứng sáng */
@keyframes progressShimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}
/* Flip In: lật vào */
@keyframes flipIn {
  0% {
    opacity: 0;
    transform: rotateY(-90deg);
  }
  100% {
    opacity: 1;
    transform: rotateY(0);
  }
}
/* Flip Out: lật ra */
@keyframes flipOut {
  0% {
    opacity: 1;
    transform: rotateY(0);
  }
  100% {
    opacity: 0;
    transform: rotateY(90deg);
  }
}

/* Animation Classes */
.quiz-fade-in {
  animation: fadeIn 0.84s ease-out forwards;
}
/* Slide In Left: đi vào từ trái */
.quiz-slide-in-left {
  animation: slideInLeft 0.84s ease-out forwards;
}

.quiz-slide-in-right {
  animation: slideInRight 0.84s ease-out forwards;
}

.quiz-slide-out-left {
  animation: slideOutLeft 0.6s ease-in forwards;
}

.quiz-slide-out-right {
  animation: slideOutRight 0.6s ease-in forwards;
}

.quiz-bounce-in {
  animation: bounceIn 0.96s ease-out forwards;
}

.quiz-pulse {
  animation: pulse 1s ease-in-out infinite;
}

.quiz-gentle-scale {
  animation: gentleScale 0.6s ease-out forwards;
}

.quiz-gentle-shake-scale {
  animation: gentleShakeScale 0.72s ease-out forwards;
}

.quiz-zoom-in {
  animation: zoomIn 0.24s ease-out forwards;
}

.quiz-zoom-out {
  animation: zoomOut 0.48s ease-in forwards;
}

.quiz-pop-in {
  animation: popIn 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55) forwards;
}

.quiz-slide-up {
  animation: slideUp 0.6s ease-out forwards;
}

.quiz-slide-down {
  animation: slideDown 0.48s ease-in forwards;
}

.quiz-correct-answer {
  animation: correctAnswer 0.96s ease-out forwards;
}

.quiz-wrong-answer {
  animation: wrongAnswer 0.96s ease-out forwards;
}

.quiz-celebration {
  animation: celebration 1.2s ease-in-out;
}

.quiz-loading {
  animation: loading 1.2s linear infinite;
}

.quiz-countdown-pulse {
  animation: countdownPulse 1.2s ease-in-out infinite;
}

.quiz-flip-in {
  animation: flipIn 0.72s ease-out forwards;
}

.quiz-flip-out {
  animation: flipOut 0.48s ease-in forwards;
}

.quiz-fade-out {
  /* animation: zoomOut 0.1s ease-in forwards; */
}

/* Transition Classes */
.quiz-transition {
  transition: all 0.36s cubic-bezier(0.4, 0, 0.2, 1);
}

.quiz-transition-fast {
  transition: all 0.24s cubic-bezier(0.4, 0, 0.2, 1);
}

.quiz-transition-slow {
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.quiz-transform-gpu {
  transform: translateZ(0);
  will-change: transform, opacity;
}

/* Enhanced Option Cards with Animations */
.option-card {
  transition: all 0.36s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(0);
  will-change: transform, background-color, border-color;
}

.option-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(147, 51, 234, 0.15);
}

.theme-dark .option-card:hover {
  box-shadow: 0 8px 20px rgba(139, 92, 246, 0.3);
}

.option-card:active {
  transform: translateY(0) scale(0.98);
}

.option-card.quiz-selected {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 10px 25px rgba(139, 92, 246, 0.25);
}

.theme-dark .option-card.quiz-selected {
  box-shadow: 0 10px 25px rgba(139, 92, 246, 0.4);
}

/* Question Container Animations */
#questionContainer {
  position: relative;
  overflow: hidden;
}

.question-slide-transition {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
}

/* Enhanced Progress Bar Animation */
#progressBar {
  transition: width 0.96s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

#progressBar::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: progressShimmer 2.4s infinite;
}

/* Enhanced Button Animations */
.quiz-button {
  transition: all 0.24s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.quiz-button:before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.72s, height 0.72s;
}

.quiz-button:active:before {
  width: 200px;
  height: 200px;
}

.quiz-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

.quiz-button:active {
  transform: translateY(0);
}

/* Timer Animation */
#examTimer {
  transition: all 0.3s ease;
}

#examTimer.quiz-timer-warning {
  animation: countdownPulse 0.5s ease-in-out infinite;
  background-color: #dc2626 !important;
}

#examTimer.quiz-timer-critical {
  animation: shake 0.3s ease-in-out infinite;
  background-color: #b91c1c !important;
}

/* Loading States */
.quiz-loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

.quiz-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--option-border);
  border-top: 4px solid var(--progress-bar);
  border-radius: 50%;
  animation: loading 1s linear infinite;
}

/* Success/Completion Animations */
.quiz-success-icon {
  color: var(--correct-icon-color);
  animation: bounceIn 0.6s ease-out;
}

.quiz-error-icon {
  color: var(--wrong-icon-color);
  animation: shake 0.6s ease-out;
}

/* Stagger animations for multiple elements */
.quiz-stagger-1 {
  animation-delay: 0.18s;
}
.quiz-stagger-2 {
  animation-delay: 0.36s;
}
.quiz-stagger-3 {
  animation-delay: 0.54s;
}
.quiz-stagger-4 {
  animation-delay: 0.72s;
}
.quiz-stagger-5 {
  animation-delay: 0.9s;
}

/* Answer reveal animations */
.quiz-answer-reveal {
  opacity: 0;
  animation: fadeIn 0.5s ease-out forwards;
}

/* Question transition container */
.quiz-question-container {
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Responsive Animations */
@media (max-width: 768px) {
  .option-card:hover {
    transform: none;
    box-shadow: none;
  }

  .quiz-button:hover {
    transform: none;
  }

  /* Reduce animations on mobile for performance */
  .quiz-transition {
    transition: all 0.24s ease;
  }

  .quiz-fade-in,
  .quiz-slide-in-left,
  .quiz-slide-in-right {
    animation-duration: 0.48s;
  }

  /* Simpler hover effects on mobile */
  .option-card:hover {
    background-color: var(--option-bg-hover);
  }
}

/* Reduced motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .quiz-pulse,
  .quiz-loading,
  .quiz-countdown-pulse {
    animation: none;
  }

  .option-card:hover {
    transform: none;
    box-shadow: none;
  }
}

/* Ngăn chặn in ấn bằng cách ẩn toàn bộ nội dung khi in */
@media print {
  body * {
    display: none !important;
    visibility: hidden !important;
  }
}
