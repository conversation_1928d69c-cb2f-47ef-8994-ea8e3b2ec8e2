import passport from "passport";
import {
  Strategy as GoogleStrategy,
  Profile,
  StrategyOptions,
} from "passport-google-oauth20";
import User, { IUser } from "../models/User";

interface GoogleProfile {
  id: string;
  emails: { value: string }[];
  displayName: string;
  name: {
    givenName: string;
    familyName: string;
  };
  photos: { value: string }[];
}

export default (): void => {
  passport.serializeUser((user: any, done) => {
    done(null, user.id);
  });

  passport.deserializeUser(async (id: string, done) => {
    try {
      const user = await User.findById(id);
      done(null, user);
    } catch (error) {
      done(error, null);
    }
  });

  passport.use(
    new GoogleStrategy(
      {
        clientID: process.env.GOOGLE_CLIENT_ID as string,
        clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,
        callbackURL: process.env.CALLBACK_URL as string,
        scope: ["profile", "email"],
      } as StrategyOptions,
      async (
        accessToken: string,
        refreshToken: string,
        profile: Profile,
        done: any
      ) => {
        try {
          const email = profile.emails ? profile.emails[0].value : "";

          // Đầu tiên kiểm tra xem người dùng đã tồn tại với googleId
          let user = await User.findOne({ googleId: profile.id });

          if (!user && email) {
            // Nếu chưa có googleId, kiểm tra xem có user nào với email này không
            user = await User.findOne({ email: email });

            if (user) {
              // Nếu đã có user với email này (có thể đăng ký bằng email/password trước),
              // cập nhật thông tin Google vào user hiện tại
              user.googleId = profile.id;
              user.displayName = user.displayName || profile.displayName;
              user.firstName = user.firstName || profile.name?.givenName;
              user.lastName = user.lastName || profile.name?.familyName;
              user.profilePhoto =
                user.profilePhoto ||
                (profile.photos ? profile.photos[0].value : undefined);

              await user.save();
            }
          }

          if (!user) {
            // Nếu hoàn toàn chưa tồn tại user nào, tạo mới
            user = await User.create({
              googleId: profile.id,
              email: email,
              displayName: profile.displayName,
              firstName: profile.name?.givenName,
              lastName: profile.name?.familyName,
              profilePhoto: profile.photos
                ? profile.photos[0].value
                : undefined,
            });
          }

          return done(null, user);
        } catch (error) {
          return done(error, null);
        }
      }
    )
  );
};
