/**
 * Image Error Debug Script
 * Temporary script để test và debug image loading functionality
 */

(function () {
  "use strict";

  // Log all image events for debugging
  function setupImageDebugging() {
    console.log("🖼️ Setting up image debugging...");

    // Monitor all image loading events
    document.addEventListener(
      "load",
      function (e) {
        if (
          e.target.tagName === "IMG" &&
          e.target.classList.contains("question-image")
        ) {
          console.log("✅ Image loaded successfully:", e.target.src);
        }
      },
      true
    );

    document.addEventListener(
      "error",
      function (e) {
        if (
          e.target.tagName === "IMG" &&
          e.target.classList.contains("question-image")
        ) {
          console.warn("❌ Image failed to load:", e.target.src);
        }
      },
      true
    );

    // Monitor error fallbacks becoming visible
    const observer = new MutationObserver(function (mutations) {
      mutations.forEach(function (mutation) {
        if (
          mutation.type === "attributes" &&
          mutation.attributeName === "class"
        ) {
          const target = mutation.target;
          if (
            target.classList.contains("image-error-fallback") &&
            !target.classList.contains("hidden")
          ) {
            console.warn(
              "⚠️ Error fallback became visible for:",
              target.closest(".question-image-container")
            );
          }
        }
      });
    });

    // Start observing
    observer.observe(document.body, {
      attributes: true,
      subtree: true,
      attributeFilter: ["class"],
    });

    console.log("🖼️ Image debugging setup complete");
  }

  // Test image handlers
  function testImageHandlers() {
    console.log("🧪 Testing image handlers...");

    // Create a test image element
    const testContainer = document.createElement("div");
    testContainer.className = "question-image-container";
    testContainer.innerHTML = `
      <img class="question-image" src="https://httpstat.us/200" alt="Test image">
      <div class="image-error-fallback hidden">Error fallback</div>
    `;

    const testImg = testContainer.querySelector("img");
    const errorFallback = testContainer.querySelector(".image-error-fallback");

    // Test successful load
    if (window.handleImageLoad) {
      window.handleImageLoad(testImg);
      if (
        testImg.style.display === "block" &&
        errorFallback.classList.contains("hidden")
      ) {
        console.log("✅ handleImageLoad works correctly");
      } else {
        console.error("❌ handleImageLoad not working properly");
      }
    }

    // Test error handling
    if (window.handleImageError) {
      window.handleImageError(testImg);
      if (
        testImg.style.display === "none" &&
        !errorFallback.classList.contains("hidden")
      ) {
        console.log("✅ handleImageError works correctly");
      } else {
        console.error("❌ handleImageError not working properly");
      }
    }

    console.log("🧪 Image handler testing complete");
  }

  // Count current images and their states
  function analyzeCurrentImages() {
    const images = document.querySelectorAll(".question-image");
    const errorFallbacks = document.querySelectorAll(
      ".image-error-fallback:not(.hidden)"
    );

    console.log("📊 Image Analysis:");
    console.log(`- Total question images: ${images.length}`);
    console.log(`- Visible error fallbacks: ${errorFallbacks.length}`);

    images.forEach((img, index) => {
      const container = img.closest(".question-image-container");
      const fallback = container?.querySelector(".image-error-fallback");
      const isImageVisible = img.style.display !== "none";
      const isFallbackVisible =
        fallback && !fallback.classList.contains("hidden");

      if (isFallbackVisible && isImageVisible) {
        console.warn(
          `⚠️ Both image and fallback visible for image ${index + 1}:`,
          img.src
        );
      }
    });
  }

  // Initialize debugging when DOM is ready
  function init() {
    if (document.readyState === "loading") {
      document.addEventListener("DOMContentLoaded", function () {
        setTimeout(() => {
          setupImageDebugging();
          testImageHandlers();
          analyzeCurrentImages();
        }, 1000);
      });
    } else {
      setTimeout(() => {
        setupImageDebugging();
        testImageHandlers();
        analyzeCurrentImages();
      }, 1000);
    }
  }

  // Only run in development/debugging mode
  if (
    window.location.search.includes("debug=images") ||
    localStorage.getItem("debugImages") === "true"
  ) {
    init();

    // Add global debug functions
    window.debugImages = {
      analyze: analyzeCurrentImages,
      test: testImageHandlers,
      enableDebug: () => localStorage.setItem("debugImages", "true"),
      disableDebug: () => localStorage.removeItem("debugImages"),
    };

    console.log(
      "🐛 Image debugging enabled. Use window.debugImages for manual testing."
    );
  }
})();
