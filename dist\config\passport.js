"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const passport_1 = __importDefault(require("passport"));
const passport_google_oauth20_1 = require("passport-google-oauth20");
const User_1 = __importDefault(require("../models/User"));
exports.default = () => {
    passport_1.default.serializeUser((user, done) => {
        done(null, user.id);
    });
    passport_1.default.deserializeUser((id, done) => __awaiter(void 0, void 0, void 0, function* () {
        try {
            const user = yield User_1.default.findById(id);
            done(null, user);
        }
        catch (error) {
            done(error, null);
        }
    }));
    passport_1.default.use(new passport_google_oauth20_1.Strategy({
        clientID: process.env.GOOGLE_CLIENT_ID,
        clientSecret: process.env.GOOGLE_CLIENT_SECRET,
        callbackURL: process.env.CALLBACK_URL,
        scope: ["profile", "email"],
    }, (accessToken, refreshToken, profile, done) => __awaiter(void 0, void 0, void 0, function* () {
        var _a, _b, _c, _d;
        try {
            const email = profile.emails ? profile.emails[0].value : "";
            // Đầu tiên kiểm tra xem người dùng đã tồn tại với googleId
            let user = yield User_1.default.findOne({ googleId: profile.id });
            if (!user && email) {
                // Nếu chưa có googleId, kiểm tra xem có user nào với email này không
                user = yield User_1.default.findOne({ email: email });
                if (user) {
                    // Nếu đã có user với email này (có thể đăng ký bằng email/password trước),
                    // cập nhật thông tin Google vào user hiện tại
                    user.googleId = profile.id;
                    user.displayName = user.displayName || profile.displayName;
                    user.firstName = user.firstName || ((_a = profile.name) === null || _a === void 0 ? void 0 : _a.givenName);
                    user.lastName = user.lastName || ((_b = profile.name) === null || _b === void 0 ? void 0 : _b.familyName);
                    user.profilePhoto =
                        user.profilePhoto ||
                            (profile.photos ? profile.photos[0].value : undefined);
                    yield user.save();
                }
            }
            if (!user) {
                // Nếu hoàn toàn chưa tồn tại user nào, tạo mới
                user = yield User_1.default.create({
                    googleId: profile.id,
                    email: email,
                    displayName: profile.displayName,
                    firstName: (_c = profile.name) === null || _c === void 0 ? void 0 : _c.givenName,
                    lastName: (_d = profile.name) === null || _d === void 0 ? void 0 : _d.familyName,
                    profilePhoto: profile.photos
                        ? profile.photos[0].value
                        : undefined,
                });
            }
            return done(null, user);
        }
        catch (error) {
            return done(error, null);
        }
    })));
};
//# sourceMappingURL=passport.js.map