/**
 * Centralized Authentication Configuration
 * Single source of truth for all authentication-related timeouts and settings
 */

// Type definitions for configuration
export interface AuthTimeDurations {
  /** JWT token expiration time in milliseconds */
  jwtExpirationMs: number;
  /** JWT token expiration time as string (for jwt.sign) */
  jwtExpirationString: string;
  /** Session expiration time in milliseconds */
  sessionExpirationMs: number;
  /** Cookie expiration time in milliseconds */
  cookieMaxAge: number;
  /** Time before token expiry to trigger refresh (in seconds) */
  tokenRefreshThreshold: number;
  /** Session cleanup interval in milliseconds */
  sessionCleanupInterval: number;
}

export interface AuthSecurityConfig {
  /** JWT secret key */
  jwtSecret: string;
  /** Cookie security settings */
  cookieSecure: boolean;
  /** <PERSON>ie httpOnly setting */
  cookieHttpOnly: boolean;
  /** Cookie sameSite setting */
  cookieSameSite: "strict" | "lax" | "none";
}

export interface AuthConfig {
  durations: AuthTimeDurations;
  security: AuthSecurityConfig;
}

// Default configuration values
const DEFAULT_SESSION_DAYS = 90; // 90 days as requested
const MS_PER_DAY = 24 * 60 * 60 * 1000;
const SECONDS_PER_DAY = 24 * 60 * 60;

/**
 * Create time durations based on the number of days
 * @param days Number of days for the session duration
 */
function createTimeDurations(days: number): AuthTimeDurations {
  const durationMs = days * MS_PER_DAY;

  return {
    jwtExpirationMs: durationMs,
    jwtExpirationString: `${days}d`,
    sessionExpirationMs: durationMs,
    cookieMaxAge: durationMs,
    tokenRefreshThreshold: 60 * 60, // 1 hour in seconds
    sessionCleanupInterval: 240 * 60 * 60 * 1000, // 240 hours in milliseconds
  };
}

/**
 * Validate configuration values
 */
function validateConfig(config: AuthConfig): void {
  const { durations, security } = config;

  // Validate durations
  if (durations.jwtExpirationMs <= 0) {
    throw new Error("JWT expiration must be greater than 0");
  }

  if (durations.sessionExpirationMs <= 0) {
    throw new Error("Session expiration must be greater than 0");
  }

  if (durations.cookieMaxAge <= 0) {
    throw new Error("Cookie max age must be greater than 0");
  }

  if (durations.tokenRefreshThreshold <= 0) {
    throw new Error("Token refresh threshold must be greater than 0");
  }

  // Validate security
  if (!security.jwtSecret || security.jwtSecret.length < 8) {
    throw new Error("JWT secret must be at least 8 characters long");
  }

  // Log warnings for potential issues
  if (durations.jwtExpirationMs !== durations.sessionExpirationMs) {
    console.warn(
      "⚠️ JWT and session expiration times are different. This may cause authentication issues."
    );
  }

  if (durations.cookieMaxAge < durations.sessionExpirationMs) {
    console.warn(
      "⚠️ Cookie expires before session. This may cause authentication issues."
    );
  }
}

/**
 * Get environment-specific session duration
 * Allows override via environment variable
 */
function getSessionDuration(): number {
  const envDays = process.env.AUTH_SESSION_DAYS;

  if (envDays) {
    const parsed = parseInt(envDays, 10);
    if (isNaN(parsed) || parsed <= 0) {
      console.warn(
        `⚠️ Invalid AUTH_SESSION_DAYS value: ${envDays}. Using default: ${DEFAULT_SESSION_DAYS} days`
      );
      return DEFAULT_SESSION_DAYS;
    }

    if (parsed > 365) {
      console.warn(
        `⚠️ AUTH_SESSION_DAYS (${parsed}) is very long. Consider security implications.`
      );
    }

    return parsed;
  }

  return DEFAULT_SESSION_DAYS;
}

/**
 * Create the authentication configuration
 */
function createAuthConfig(): AuthConfig {
  const sessionDays = getSessionDuration();
  const durations = createTimeDurations(sessionDays);

  const config: AuthConfig = {
    durations,
    security: {
      jwtSecret:
        process.env.JWT_SECRET ||
        "your-default-jwt-secret-change-in-production",
      cookieSecure: process.env.NODE_ENV === "production",
      cookieHttpOnly: true,
      cookieSameSite: process.env.NODE_ENV === "production" ? "strict" : "lax",
    },
  };

  // Validate configuration
  try {
    validateConfig(config);
  } catch (error) {
    console.error(
      "❌ Authentication configuration validation failed:",
      error.message
    );
    throw error;
  }

  // Log configuration info in development
  if (process.env.NODE_ENV !== "production") {
    console.log("🔐 Authentication Configuration:");
    console.log(`   Session Duration: ${sessionDays} days`);
    console.log(`   JWT Expiration: ${durations.jwtExpirationString}`);
    console.log(
      `   Cookie Max Age: ${Math.round(
        durations.cookieMaxAge / MS_PER_DAY
      )} days`
    );
    console.log(
      `   Token Refresh Threshold: ${
        durations.tokenRefreshThreshold / 3600
      } hours`
    );
    console.log(`   Environment: ${process.env.NODE_ENV || "development"}`);
  }

  return config;
}

// Export the configuration
export const authConfig = createAuthConfig();

// Export commonly used values for convenience
export const {
  jwtExpirationMs,
  jwtExpirationString,
  sessionExpirationMs,
  cookieMaxAge,
  tokenRefreshThreshold,
  sessionCleanupInterval,
} = authConfig.durations;

export const { jwtSecret, cookieSecure, cookieHttpOnly, cookieSameSite } =
  authConfig.security;

// Export helper functions
export const helpers = {
  /**
   * Convert milliseconds to days
   */
  msToDays: (ms: number) => Math.round(ms / MS_PER_DAY),

  /**
   * Convert days to milliseconds
   */
  daysToMs: (days: number) => days * MS_PER_DAY,

  /**
   * Check if a timestamp is expired
   */
  isExpired: (expiresAt: Date | number) => {
    const expiry =
      typeof expiresAt === "number" ? expiresAt : expiresAt.getTime();
    return Date.now() > expiry;
  },

  /**
   * Create expiration date from now
   */
  createExpirationDate: (durationMs: number = sessionExpirationMs) => {
    return new Date(Date.now() + durationMs);
  },

  /**
   * Get time until expiration in seconds
   */
  getTimeUntilExpiry: (expiresAt: Date | number) => {
    const expiry =
      typeof expiresAt === "number" ? expiresAt : expiresAt.getTime();
    return Math.max(0, Math.floor((expiry - Date.now()) / 1000));
  },
};

// Default export
export default authConfig;
