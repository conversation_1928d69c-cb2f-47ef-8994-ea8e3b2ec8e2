{"version": 3, "file": "updateProductQuestionCount.js", "sourceRoot": "", "sources": ["../../src/scripts/updateProductQuestionCount.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA,wDAAgC;AAChC,oDAA4B;AAC5B,kEAAyC;AACzC,0DAAkC;AAElC,6BAA6B;AAC7B,gBAAM,CAAC,MAAM,EAAE,CAAC;AAEhB;;GAEG;AACH,SAAe,iBAAiB;;QAC9B,IAAI,CAAC;YACH,MAAM,QAAQ,GACZ,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,uCAAuC,CAAC;YACrE,MAAM,kBAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YACjC,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;QACnD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;YACjD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC;IACH,CAAC;CAAA;AAED;;GAEG;AACH,SAAe,2BAA2B;;QACxC,IAAI,CAAC;YACH,yBAAyB;YACzB,MAAM,QAAQ,GAAG,MAAM,kBAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACxC,OAAO,CAAC,GAAG,CAAC,YAAY,QAAQ,CAAC,MAAM,uBAAuB,CAAC,CAAC;YAEhE,qBAAqB;YACrB,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;gBAC/B,0CAA0C;gBAC1C,MAAM,KAAK,GAAG,MAAM,cAAI,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;gBAE1D,4CAA4C;gBAC5C,MAAM,kBAAkB,GAAG,KAAK,CAAC,MAAM,CACrC,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC,EAC1C,CAAC,CACF,CAAC;gBAEF,4CAA4C;gBAC5C,MAAM,kBAAO,CAAC,SAAS,CACrB,EAAE,GAAG,EAAE,OAAO,CAAC,GAAG,EAAE,EACpB,EAAE,IAAI,EAAE,EAAE,aAAa,EAAE,kBAAkB,EAAE,EAAE,CAChD,CAAC;gBAEF,OAAO,CAAC,GAAG,CACT,YAAY,OAAO,CAAC,IAAI,UAAU,OAAO,CAAC,GAAG,wBAAwB,kBAAkB,QAAQ,KAAK,CAAC,MAAM,UAAU,CACtH,CAAC;YACJ,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,4DAA4D,CAAC,CAAC;QAC5E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,gDAAgD,EAAE,KAAK,CAAC,CAAC;QACzE,CAAC;gBAAS,CAAC;YACT,gDAAgD;YAChD,MAAM,kBAAQ,CAAC,UAAU,EAAE,CAAC;YAC5B,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;QAC9C,CAAC;IACH,CAAC;CAAA;AAED;;GAEG;AACH,SAAe,IAAI;;QACjB,MAAM,iBAAiB,EAAE,CAAC;QAC1B,MAAM,2BAA2B,EAAE,CAAC;IACtC,CAAC;CAAA;AAED,cAAc;AACd,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;IACrB,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;IAC7C,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC,CAAC"}