<!DOCTYPE html>
<html lang="vi">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>
      Test HMC - Nền tảng học tập cho sinh viên Cao đẳng Y tế Hà Nội
    </title>

    <meta
      name="description"
      content="Cung cấp tài liệu ôn thi, đ<PERSON> cươ<PERSON>, gi<PERSON><PERSON> tr<PERSON>, đ<PERSON> thi thử và quizizz ôn thi cho sinh viên Cao đẳng Y tế Hà Nội. Học tập hiệu quả với đáp án chi tiết và hỗ trợ 24/7."
    />
    <meta
      name="keywords"
      content="test hmc, cao đẳng y tế hà nội, tài liệu ôn thi hmc, đ<PERSON> cương hmc, gi<PERSON>o trình hmc, đ<PERSON> thi thử hmc, quizizz ôn thi, google form đáp án, ôn thi cao đẳng y tế hà nội, đi<PERSON><PERSON> dưỡng, d<PERSON><PERSON><PERSON>, y sỹ đa khoa, kỹ thuật xét nghiệm"
    />

    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>

    <style>
      /* Custom animations for bubbles */
      .bubble {
        position: absolute;
        background: linear-gradient(
          135deg,
          rgba(59, 130, 246, 0.3),
          rgba(20, 184, 166, 0.2)
        );
        border-radius: 50%;
        backdrop-filter: blur(1px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        z-index: 0;
        pointer-events: none;
      }

      .bubble-1 {
        width: 80px;
        height: 80px;
        top: 100vh;
        left: 5%;
        animation: bounce1 8s ease-in-out infinite;
      }

      .bubble-2 {
        width: 60px;
        height: 60px;
        top: 100vh;
        left: 15%;
        animation: bounce2 10s ease-in-out infinite 1s;
      }

      .bubble-3 {
        width: 100px;
        height: 100px;
        top: 100vh;
        left: 30%;
        animation: bounce3 12s ease-in-out infinite 2s;
      }

      .bubble-4 {
        width: 70px;
        height: 70px;
        top: 100vh;
        left: 60%;
        animation: bounce4 9s ease-in-out infinite 3s;
      }

      .bubble-5 {
        width: 90px;
        height: 90px;
        top: 100vh;
        left: 75%;
        animation: bounce5 11s ease-in-out infinite 4s;
      }

      .bubble-6 {
        width: 50px;
        height: 50px;
        top: 100vh;
        left: 85%;
        animation: bounce6 7s ease-in-out infinite 5s;
      }

      @keyframes bounce1 {
        0% {
          transform: translate(0, 0) rotate(0deg);
          opacity: 0.8;
        }
        15% {
          transform: translate(20vw, -40vh) rotate(45deg);
          opacity: 1;
        }
        30% {
          transform: translate(-10vw, -60vh) rotate(90deg);
          opacity: 0.9;
        }
        45% {
          transform: translate(30vw, -80vh) rotate(135deg);
          opacity: 1;
        }
        60% {
          transform: translate(10vw, -50vh) rotate(180deg);
          opacity: 0.8;
        }
        75% {
          transform: translate(-20vw, -70vh) rotate(225deg);
          opacity: 1;
        }
        90% {
          transform: translate(25vw, -30vh) rotate(270deg);
          opacity: 0.9;
        }
        100% {
          transform: translate(0, -100vh) rotate(360deg);
          opacity: 0;
        }
      }

      @keyframes bounce2 {
        0% {
          transform: translate(0, 0) rotate(0deg);
          opacity: 0.7;
        }
        20% {
          transform: translate(-15vw, -30vh) rotate(60deg);
          opacity: 1;
        }
        40% {
          transform: translate(25vw, -70vh) rotate(120deg);
          opacity: 0.8;
        }
        60% {
          transform: translate(-5vw, -40vh) rotate(180deg);
          opacity: 1;
        }
        80% {
          transform: translate(35vw, -60vh) rotate(240deg);
          opacity: 0.9;
        }
        100% {
          transform: translate(0, -100vh) rotate(360deg);
          opacity: 0;
        }
      }

      @keyframes bounce3 {
        0% {
          transform: translate(0, 0) rotate(0deg);
          opacity: 0.6;
        }
        25% {
          transform: translate(40vw, -45vh) rotate(90deg);
          opacity: 1;
        }
        50% {
          transform: translate(-20vw, -75vh) rotate(180deg);
          opacity: 0.8;
        }
        75% {
          transform: translate(30vw, -35vh) rotate(270deg);
          opacity: 1;
        }
        100% {
          transform: translate(0, -100vh) rotate(360deg);
          opacity: 0;
        }
      }

      @keyframes bounce4 {
        0% {
          transform: translate(0, 0) rotate(0deg);
          opacity: 0.9;
        }
        18% {
          transform: translate(-25vw, -35vh) rotate(50deg);
          opacity: 1;
        }
        36% {
          transform: translate(15vw, -55vh) rotate(100deg);
          opacity: 0.8;
        }
        54% {
          transform: translate(-35vw, -75vh) rotate(150deg);
          opacity: 1;
        }
        72% {
          transform: translate(20vw, -45vh) rotate(200deg);
          opacity: 0.9;
        }
        90% {
          transform: translate(-10vw, -25vh) rotate(300deg);
          opacity: 1;
        }
        100% {
          transform: translate(0, -100vh) rotate(360deg);
          opacity: 0;
        }
      }

      @keyframes bounce5 {
        0% {
          transform: translate(0, 0) rotate(0deg);
          opacity: 0.8;
        }
        22% {
          transform: translate(30vw, -30vh) rotate(80deg);
          opacity: 1;
        }
        44% {
          transform: translate(-15vw, -50vh) rotate(160deg);
          opacity: 0.7;
        }
        66% {
          transform: translate(45vw, -70vh) rotate(240deg);
          opacity: 1;
        }
        88% {
          transform: translate(-5vw, -40vh) rotate(320deg);
          opacity: 0.9;
        }
        100% {
          transform: translate(0, -100vh) rotate(360deg);
          opacity: 0;
        }
      }

      @keyframes bounce6 {
        0% {
          transform: translate(0, 0) rotate(0deg);
          opacity: 1;
        }
        16% {
          transform: translate(-30vw, -25vh) rotate(40deg);
          opacity: 0.8;
        }
        32% {
          transform: translate(20vw, -45vh) rotate(80deg);
          opacity: 1;
        }
        48% {
          transform: translate(-40vw, -65vh) rotate(120deg);
          opacity: 0.9;
        }
        64% {
          transform: translate(35vw, -35vh) rotate(200deg);
          opacity: 1;
        }
        80% {
          transform: translate(-10vw, -55vh) rotate(280deg);
          opacity: 0.8;
        }
        100% {
          transform: translate(0, -100vh) rotate(360deg);
          opacity: 0;
        }
      }

      .particle {
        position: absolute;
        background: radial-gradient(
          circle,
          rgba(59, 130, 246, 0.5),
          rgba(20, 184, 166, 0.3)
        );
        border-radius: 50%;
        pointer-events: none;
        z-index: 0;
      }

      .particle-1 {
        width: 4px;
        height: 4px;
        top: 100vh;
        left: 15%;
        animation: float-particle 15s linear infinite;
      }

      .particle-2 {
        width: 6px;
        height: 6px;
        top: 100vh;
        left: 45%;
        animation: float-particle 18s linear infinite 3s;
      }

      .particle-3 {
        width: 3px;
        height: 3px;
        top: 100vh;
        left: 75%;
        animation: float-particle 20s linear infinite 6s;
      }

      @keyframes float-particle {
        0% {
          transform: translateY(0) translateX(0) rotate(0deg);
          opacity: 0;
        }
        10% {
          opacity: 1;
        }
        90% {
          opacity: 1;
        }
        100% {
          transform: translateY(-110vh) translateX(20px) rotate(360deg);
          opacity: 0;
        }
      }

      .hidden {
        display: none !important;
      }

      .animate-pulse {
        animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
      }

      @keyframes pulse {
        0%,
        100% {
          opacity: 1;
        }
        50% {
          opacity: 0.5;
        }
      }
    </style>
  </head>
  <body class="min-h-screen bg-white">
    <!-- Hero Section -->
    <section
      class="relative bg-gradient-to-br from-blue-50 via-white to-teal-50 pt-16 pb-24"
    >
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
          <div class="relative z-10">
            <h1 class="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              Chào mừng đến với
              <span
                class="bg-gradient-to-r from-blue-600 to-teal-600 bg-clip-text text-transparent"
              >
                Test HMC
              </span>
              <span class="block text-2xl md:text-3xl mt-4 text-gray-700">
                Cao đẳng Y tế Hà Nội
              </span>
            </h1>

            <p
              class="text-xl md:text-2xl text-gray-600 mb-8 max-w-4xl mx-auto leading-relaxed"
            >
              Nền tảng học tập trực tuyến hàng đầu cho sinh viên
              <strong>Cao đẳng Y tế Hà Nội</strong>. Cung cấp
              <strong>tài liệu ôn thi</strong>, <strong>đề cương</strong>,
              <strong>giáo trình</strong>
              và đề thi thử chất lượng cao theo chuẩn của trường.
            </p>

            <div class="mb-8 text-lg text-gray-600 max-w-3xl mx-auto">
              <p class="mb-4">
                🏥 <strong>Chuyên ngành Y tế:</strong> Điều dưỡng, Dược, Kỹ
                thuật Y học, Y tế công cộng
              </p>
              <p class="mb-4">
                📚 <strong>Tài liệu đầy đủ:</strong> Giáo trình, đề cương, bài
                giảng từ giảng viên HMC
              </p>
              <p>
                🎯 <strong>Đề thi chuẩn:</strong> Theo format và nội dung của
                Cao đẳng Y tế Hà Nội
              </p>
            </div>

            <div
              class="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12"
            >
              <button
                class="bg-blue-600 text-white px-8 py-4 rounded-xl font-semibold text-lg hover:bg-blue-700 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl"
              >
                📚 Tài liệu ôn thi HMC
              </button>
              <button
                class="bg-white text-gray-800 px-8 py-4 rounded-xl font-semibold text-lg border-2 border-gray-200 hover:border-blue-300 hover:bg-blue-50 transition-all duration-200 shadow-lg hover:shadow-xl"
              >
                🚀 Đăng nhập với Google
              </button>
            </div>

            <!-- Stats -->
            <div
              class="grid grid-cols-2 md:grid-cols-4 gap-6 md:gap-8 max-w-3xl mx-auto"
            >
              <div class="text-center">
                <div class="text-3xl font-bold text-blue-600 mb-2">50+</div>
                <div class="text-gray-600">Môn học Y tế</div>
              </div>
              <div class="text-center">
                <div class="text-3xl font-bold text-teal-600 mb-2">2000+</div>
                <div class="text-gray-600">Sinh viên HMC</div>
              </div>
              <div class="text-center">
                <div class="text-3xl font-bold text-purple-600 mb-2">95%</div>
                <div class="text-gray-600">Tỷ lệ đậu</div>
              </div>
              <div class="text-center">
                <div class="text-3xl font-bold text-orange-600 mb-2">24/7</div>
                <div class="text-gray-600">Hỗ trợ học tập</div>
              </div>
            </div>
          </div>

          <!-- SEO Content -->
          <div
            class="mt-16 max-w-4xl mx-auto text-left bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg relative z-10"
          >
            <h2 class="text-2xl font-bold text-gray-900 mb-4">
              Về Cao đẳng Y tế Hà Nội (HMC)
            </h2>
            <p class="text-gray-700 mb-4 leading-relaxed">
              <strong>Cao đẳng Y tế Hà Nội</strong> là một trong những trường
              đào tạo y tế hàng đầu tại Việt Nam. Test HMC được phát triển để hỗ
              trợ sinh viên HMC trong việc học tập và ôn thi hiệu quả.
            </p>
            <div class="grid md:grid-cols-2 gap-6">
              <div>
                <h3 class="font-semibold text-gray-900 mb-2">
                  📖 Tài liệu học tập:
                </h3>
                <ul class="text-gray-700 space-y-1">
                  <li>• Giáo trình chính thức từ HMC</li>
                  <li>• Đề cương chi tiết từng môn học</li>
                  <li>• Bài giảng của giảng viên</li>
                  <li>• Tài liệu tham khảo bổ sung</li>
                </ul>
              </div>
              <div>
                <h3 class="font-semibold text-gray-900 mb-2">
                  🎯 Đề thi & Luyện tập:
                </h3>
                <ul class="text-gray-700 space-y-1">
                  <li>• Đề thi thử theo format HMC</li>
                  <li>• Ngân hàng câu hỏi phong phú</li>
                  <li>• Giải thích chi tiết đáp án</li>
                  <li>• Theo dõi tiến độ học tập</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Decorative Elements -->
      <div
        class="absolute top-32 left-4 md:left-10 text-blue-200/30 animate-bounce"
      >
        <i data-lucide="book-open" class="w-8 h-8"></i>
      </div>
      <div
        class="absolute bottom-32 right-4 md:right-10 text-teal-200/30 animate-pulse"
      >
        <i data-lucide="star" class="w-7 h-7"></i>
      </div>
      <div
        class="absolute top-1/2 left-2 md:left-1/4 text-purple-200/30 animate-ping"
      >
        <i data-lucide="users" class="w-6 h-6"></i>
      </div>
      <div
        class="absolute bottom-1/3 right-2 md:right-1/4 text-orange-200/30 animate-bounce"
      >
        <i data-lucide="award" class="w-7 h-7"></i>
      </div>

      <!-- Floating Bubbles Animation -->
      <div
        class="fixed inset-0 overflow-hidden pointer-events-none"
        style="z-index: 0"
      >
        <div class="bubble bubble-1"></div>
        <div class="bubble bubble-2"></div>
        <div class="bubble bubble-3"></div>
        <div class="bubble bubble-4"></div>
        <div class="bubble bubble-5"></div>
        <div class="bubble bubble-6"></div>

        <!-- Small floating particles -->
        <div class="particle particle-1"></div>
        <div class="particle particle-2"></div>
        <div class="particle particle-3"></div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="py-20 bg-gray-50 relative">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="relative z-10">
          <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Tại sao chọn Test HMC?
            </h2>
            <p class="text-xl text-gray-600 max-w-2xl mx-auto">
              Nền tảng học tập chuyên biệt cho sinh viên
              <strong>Cao đẳng Y tế Hà Nội</strong>, cung cấp đầy đủ tài liệu,
              đề cương và giáo trình chất lượng cao.
            </p>
          </div>

          <div
            class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20"
          >
            <!-- Feature 1 -->
            <div
              class="bg-white p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 group hover:scale-105 text-center"
            >
              <div class="flex flex-col items-center">
                <div
                  class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 group-hover:rotate-12 transition-all duration-300"
                >
                  <i data-lucide="book-open" class="text-white w-7 h-7"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">
                  Tài liệu ôn thi HMC
                </h3>
                <p class="text-gray-600 leading-relaxed">
                  Tài liệu ôn thi cao đẳng y tế Hà Nội đầy đủ, cập nhật theo
                  chương trình mới nhất của trường.
                </p>
              </div>
            </div>

            <!-- Feature 2 -->
            <div
              class="bg-white p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 group hover:scale-105 text-center"
            >
              <div class="flex flex-col items-center">
                <div
                  class="w-16 h-16 bg-gradient-to-br from-teal-500 to-teal-600 rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 group-hover:rotate-12 transition-all duration-300"
                >
                  <i data-lucide="file-text" class="text-white w-7 h-7"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">
                  Đề cương HMC
                </h3>
                <p class="text-gray-600 leading-relaxed">
                  Đề cương cao đẳng y tế Hà Nội chi tiết, giúp sinh viên nắm
                  vững kiến thức cốt lõi từng môn học.
                </p>
              </div>
            </div>

            <!-- Feature 3 -->
            <div
              class="bg-white p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 group hover:scale-105 text-center"
            >
              <div class="flex flex-col items-center">
                <div
                  class="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 group-hover:rotate-12 transition-all duration-300"
                >
                  <i data-lucide="trending-up" class="text-white w-7 h-7"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">
                  Giáo trình HMC
                </h3>
                <p class="text-gray-600 leading-relaxed">
                  Giáo trình cao đẳng y tế Hà Nội chính thức, được biên soạn bởi
                  đội ngũ giảng viên giàu kinh nghiệm.
                </p>
              </div>
            </div>

            <!-- Feature 4 -->
            <div
              class="bg-white p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 group hover:scale-105 text-center"
            >
              <div class="flex flex-col items-center">
                <div
                  class="w-16 h-16 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 group-hover:rotate-12 transition-all duration-300"
                >
                  <i data-lucide="users" class="text-white w-7 h-7"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">
                  Cộng đồng sinh viên HMC
                </h3>
                <p class="text-gray-600 leading-relaxed">
                  Kết nối với sinh viên cao đẳng y tế Hà Nội, thảo luận và chia
                  sẻ kinh nghiệm học tập.
                </p>
              </div>
            </div>

            <!-- Feature 5 -->
            <div
              class="bg-white p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 group hover:scale-105 text-center"
            >
              <div class="flex flex-col items-center">
                <div
                  class="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 group-hover:rotate-12 transition-all duration-300"
                >
                  <i data-lucide="clock" class="text-white w-7 h-7"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">
                  Học tập linh hoạt
                </h3>
                <p class="text-gray-600 leading-relaxed">
                  Truy cập tài liệu HMC 24/7 trên mọi thiết bị, học tập linh
                  hoạt theo lịch trình cá nhân.
                </p>
              </div>
            </div>

            <!-- Feature 6 -->
            <div
              class="bg-white p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 group hover:scale-105 text-center"
            >
              <div class="flex flex-col items-center">
                <div
                  class="w-16 h-16 bg-gradient-to-br from-pink-500 to-pink-600 rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 group-hover:rotate-12 transition-all duration-300"
                >
                  <i data-lucide="award" class="text-white w-7 h-7"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">
                  Đánh giá kết quả
                </h3>
                <p class="text-gray-600 leading-relaxed">
                  Hệ thống đánh giá tiến độ học tập, giúp sinh viên HMC chuẩn bị
                  tốt nhất cho kỳ thi.
                </p>
              </div>
            </div>
          </div>

          <!-- Chương trình đào tạo -->
          <div class="bg-white rounded-2xl p-8 shadow-lg mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-8 text-center">
              Chương trình đào tạo Cao đẳng Y tế Hà Nội
            </h2>
            <p class="text-center text-gray-600 mb-12 max-w-3xl mx-auto">
              Đào tạo đa dạng các chuyên ngành y tế với chương trình chuẩn quốc
              gia, đội ngũ giảng viên giàu kinh nghiệm và cơ sở vật chất hiện
              đại.
            </p>

            <div
              class="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
            >
              <!-- Program items -->
              <div
                class="bg-blue-50 p-4 rounded-xl hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 hover:scale-105 group cursor-pointer"
              >
                <div class="flex items-center space-x-3 mb-2">
                  <div
                    class="text-blue-600 group-hover:scale-110 transition-transform duration-300 flex-shrink-0"
                  >
                    <i data-lucide="stethoscope" class="w-6 h-6"></i>
                  </div>
                  <h3 class="font-bold text-blue-900 text-base flex-1">
                    Điều dưỡng
                  </h3>
                </div>
                <p class="text-xs text-blue-900 opacity-80 leading-relaxed">
                  Chăm sóc bệnh nhân chuyên nghiệp, kỹ thuật điều dưỡng hiện đại
                </p>
              </div>

              <div
                class="bg-teal-50 p-4 rounded-xl hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 hover:scale-105 group cursor-pointer"
              >
                <div class="flex items-center space-x-3 mb-2">
                  <div
                    class="text-teal-600 group-hover:scale-110 transition-transform duration-300 flex-shrink-0"
                  >
                    <i data-lucide="pill" class="w-6 h-6"></i>
                  </div>
                  <h3 class="font-bold text-teal-900 text-base flex-1">Dược</h3>
                </div>
                <p class="text-xs text-teal-900 opacity-80 leading-relaxed">
                  Kiến thức về thuốc, tương tác dược và tư vấn dược lâm sàng
                </p>
              </div>

              <div
                class="bg-red-50 p-4 rounded-xl hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 hover:scale-105 group cursor-pointer"
              >
                <div class="flex items-center space-x-3 mb-2">
                  <div
                    class="text-red-600 group-hover:scale-110 transition-transform duration-300 flex-shrink-0"
                  >
                    <i data-lucide="heart" class="w-6 h-6"></i>
                  </div>
                  <h3 class="font-bold text-red-900 text-base flex-1">
                    Y sỹ đa khoa
                  </h3>
                </div>
                <p class="text-xs text-red-900 opacity-80 leading-relaxed">
                  Chẩn đoán, điều trị và chăm sóc sức khỏe toàn diện
                </p>
              </div>

              <div
                class="bg-purple-50 p-4 rounded-xl hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 hover:scale-105 group cursor-pointer"
              >
                <div class="flex items-center space-x-3 mb-2">
                  <div
                    class="text-purple-600 group-hover:scale-110 transition-transform duration-300 flex-shrink-0"
                  >
                    <i data-lucide="test-tube" class="w-6 h-6"></i>
                  </div>
                  <h3 class="font-bold text-purple-900 text-base flex-1">
                    Kỹ thuật xét nghiệm y học
                  </h3>
                </div>
                <p class="text-xs text-purple-900 opacity-80 leading-relaxed">
                  Xét nghiệm sinh hóa, vi sinh, huyết học và miễn dịch
                </p>
              </div>
            </div>

            <!-- Thông tin bổ sung -->
            <div class="mt-12 grid md:grid-cols-3 gap-8">
              <div
                class="text-center p-6 bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl"
              >
                <div class="text-3xl font-bold text-blue-600 mb-2">3 năm</div>
                <div class="text-blue-800 font-semibold mb-2">
                  Thời gian đào tạo
                </div>
                <div class="text-sm text-blue-700">
                  Chương trình đào tạo chuẩn theo quy định Bộ Y tế
                </div>
              </div>
              <div
                class="text-center p-6 bg-gradient-to-br from-teal-50 to-teal-100 rounded-xl"
              >
                <div class="text-3xl font-bold text-teal-600 mb-2">100%</div>
                <div class="text-teal-800 font-semibold mb-2">
                  Thực hành lâm sàng
                </div>
                <div class="text-sm text-teal-700">
                  Học tập tại các bệnh viện hàng đầu Hà Nội
                </div>
              </div>
              <div
                class="text-center p-6 bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl"
              >
                <div class="text-3xl font-bold text-purple-600 mb-2">95%</div>
                <div class="text-purple-800 font-semibold mb-2">
                  Tỷ lệ có việc làm
                </div>
                <div class="text-sm text-purple-700">
                  Sinh viên có việc làm ngay sau tốt nghiệp
                </div>
              </div>
            </div>
          </div>

          <!-- Liên kết hỗ trợ -->
          <div
            class="bg-gradient-to-r from-blue-600 to-teal-600 rounded-2xl p-8 text-white"
          >
            <h3 class="text-2xl font-bold mb-6 text-center">
              Cần hỗ trợ thêm?
            </h3>
            <div class="grid md:grid-cols-3 gap-6">
              <a
                href="#"
                class="bg-white/20 backdrop-blur-sm p-4 rounded-xl hover:bg-white/30 transition-all duration-300 transform hover:scale-105 group"
              >
                <div class="flex items-start space-x-3">
                  <i
                    data-lucide="users"
                    class="w-6 h-6 group-hover:scale-110 transition-transform duration-300 flex-shrink-0 mt-1"
                  ></i>
                  <div class="flex-1">
                    <h4 class="font-semibold mb-1 text-sm">
                      Trung tâm trợ giúp
                    </h4>
                    <p class="text-xs opacity-90">Hỗ trợ sinh viên 24/7</p>
                  </div>
                </div>
              </a>
              <a
                href="#"
                class="bg-white/20 backdrop-blur-sm p-4 rounded-xl hover:bg-white/30 transition-all duration-300 transform hover:scale-105 group"
              >
                <div class="flex items-start space-x-3">
                  <i
                    data-lucide="file-text"
                    class="w-6 h-6 group-hover:scale-110 transition-transform duration-300 flex-shrink-0 mt-1"
                  ></i>
                  <div class="flex-1">
                    <h4 class="font-semibold mb-1 text-sm">
                      Câu hỏi thường gặp
                    </h4>
                    <p class="text-xs opacity-90">
                      Giải đáp thắc mắc nhanh chóng
                    </p>
                  </div>
                </div>
              </a>
              <a
                href="#"
                class="bg-white/20 backdrop-blur-sm p-4 rounded-xl hover:bg-white/30 transition-all duration-300 transform hover:scale-105 group"
              >
                <div class="flex items-start space-x-3">
                  <i
                    data-lucide="award"
                    class="w-6 h-6 group-hover:scale-110 transition-transform duration-300 flex-shrink-0 mt-1"
                  ></i>
                  <div class="flex-1">
                    <h4 class="font-semibold mb-1 text-sm">
                      Chính sách bảo mật
                    </h4>
                    <p class="text-xs opacity-90">Bảo vệ thông tin cá nhân</p>
                  </div>
                </div>
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="py-20 bg-gradient-to-r from-blue-600 to-teal-600">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
          <h2 class="text-3xl md:text-4xl font-bold text-white mb-6">
            Sẵn sàng bắt đầu hành trình học tập?
          </h2>

          <p class="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            Tham gia cùng hàng nghìn
            <strong>sinh viên Cao đẳng Y tế Hà Nội</strong> và trải nghiệm
            phương pháp học tập hiện đại với
            <strong>tài liệu ôn thi HMC</strong> chất lượng cao.
          </p>

          <div class="mb-8 text-blue-100">
            <p class="mb-2">✅ Tài liệu ôn thi cao đẳng y tế Hà Nội đầy đủ</p>
            <p class="mb-2">✅ Đề cương cao đẳng y tế Hà Nội chi tiết</p>
            <p class="mb-2">✅ Giáo trình cao đẳng y tế Hà Nội chính thức</p>
            <p>✅ Hỗ trợ học tập 24/7 cho sinh viên HMC</p>
          </div>

          <div class="mb-12">
            <p class="text-blue-200 mb-2">
              🏆 Được tin tưởng bởi sinh viên HMC
            </p>
            <p class="text-blue-200">
              📚 Liên kết chính thức với
              <a
                href="https://yhn.edu.vn/"
                target="_blank"
                rel="noopener noreferrer"
                class="underline hover:text-white"
                >Cao đẳng Y tế Hà Nội</a
              >
            </p>
          </div>

          <div
            class="flex flex-col sm:flex-row gap-6 justify-center items-center"
          >
            <button
              class="bg-white text-blue-600 px-8 py-4 rounded-xl font-semibold text-lg hover:bg-blue-50 transform hover:scale-105 hover:rotate-1 transition-all duration-200 shadow-lg hover:shadow-xl flex items-center space-x-2 animate-pulse hover:animate-none"
            >
              <i data-lucide="book-open" class="w-5 h-5"></i>
              <span>Tài liệu ôn thi HMC</span>
              <i data-lucide="arrow-right" class="w-5 h-5"></i>
            </button>

            <button
              class="bg-transparent border-2 border-white text-white px-8 py-4 rounded-xl font-semibold text-lg hover:bg-white hover:text-blue-600 hover:scale-105 hover:-rotate-1 transition-all duration-200 shadow-lg hover:shadow-xl flex items-center space-x-2"
            >
              <i data-lucide="users" class="w-5 h-5"></i>
              <span>Đăng ký miễn phí</span>
            </button>
          </div>

          <div class="mt-12 text-center">
            <p class="text-blue-100 mb-4">Đã là sinh viên HMC?</p>
            <button
              class="text-white font-semibold hover:text-blue-200 transition-colors duration-200 underline"
            >
              Đăng nhập tại đây
            </button>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <!-- Logo & Description -->
          <div class="col-span-1 md:col-span-2 lg:col-span-1">
            <div class="flex items-center space-x-2 mb-4">
              <div class="bg-blue-600 text-white p-2 rounded-lg">
                <i data-lucide="book-open" class="w-6 h-6"></i>
              </div>
              <span class="text-2xl font-bold">Test HMC</span>
            </div>
            <p class="text-gray-400 mb-6">
              Nền tảng học tập trực tuyến chuyên biệt cho
              <strong>sinh viên Cao đẳng Y tế Hà Nội</strong>, cung cấp tài liệu
              ôn thi, đề cương và giáo trình chất lượng cao.
            </p>
            <div class="mb-4">
              <p class="text-gray-400 text-sm">
                🏥 Liên kết với:
                <a
                  href="https://yhn.edu.vn/"
                  target="_blank"
                  rel="noopener noreferrer"
                  class="text-blue-400 hover:text-blue-300"
                  >Cao đẳng Y tế Hà Nội</a
                >
              </p>
            </div>
            <div class="flex space-x-4">
              <i
                data-lucide="facebook"
                class="w-5 h-5 text-gray-400 hover:text-blue-400 cursor-pointer transition-colors duration-200"
              ></i>
              <i
                data-lucide="twitter"
                class="w-5 h-5 text-gray-400 hover:text-blue-400 cursor-pointer transition-colors duration-200"
              ></i>
              <i
                data-lucide="instagram"
                class="w-5 h-5 text-gray-400 hover:text-pink-400 cursor-pointer transition-colors duration-200"
              ></i>
            </div>
          </div>

          <!-- Tài liệu HMC -->
          <div>
            <h3 class="text-lg font-semibold mb-4">Tài liệu HMC</h3>
            <ul class="space-y-2">
              <li>
                <a
                  href="#"
                  class="text-gray-400 hover:text-white transition-colors duration-200"
                  >Tài liệu ôn thi HMC</a
                >
              </li>
              <li>
                <a
                  href="#"
                  class="text-gray-400 hover:text-white transition-colors duration-200"
                  >Đề cương HMC</a
                >
              </li>
              <li>
                <a
                  href="#"
                  class="text-gray-400 hover:text-white transition-colors duration-200"
                  >Giáo trình HMC</a
                >
              </li>
              <li>
                <a
                  href="#"
                  class="text-gray-400 hover:text-white transition-colors duration-200"
                  >Đề thi thử</a
                >
              </li>
            </ul>
          </div>

          <!-- Chuyên ngành -->
          <div>
            <h3 class="text-lg font-semibold mb-4">Chuyên ngành</h3>
            <ul class="space-y-2">
              <li>
                <a
                  href="#"
                  class="text-gray-400 hover:text-white transition-colors duration-200"
                  >Điều dưỡng</a
                >
              </li>
              <li>
                <a
                  href="#"
                  class="text-gray-400 hover:text-white transition-colors duration-200"
                  >Dược</a
                >
              </li>
              <li>
                <a
                  href="#"
                  class="text-gray-400 hover:text-white transition-colors duration-200"
                  >Y sỹ đa khoa</a
                >
              </li>
              <li>
                <a
                  href="#"
                  class="text-gray-400 hover:text-white transition-colors duration-200"
                  >Kỹ thuật xét nghiệm</a
                >
              </li>
              <li>
                <a
                  href="#"
                  class="text-gray-400 hover:text-white transition-colors duration-200"
                  >Kỹ thuật hình ảnh</a
                >
              </li>
              <li>
                <a
                  href="#"
                  class="text-gray-400 hover:text-white transition-colors duration-200"
                  >Phục hồi chức năng</a
                >
              </li>
              <li>
                <a
                  href="#"
                  class="text-gray-400 hover:text-white transition-colors duration-200"
                  >Chăm sóc sắc đẹp</a
                >
              </li>
            </ul>
          </div>

          <!-- Liên hệ -->
          <div>
            <h3 class="text-lg font-semibold mb-4">Hỗ trợ</h3>
            <div class="space-y-3 mb-4">
              <div class="flex items-center space-x-3">
                <i data-lucide="mail" class="w-4 h-4 text-gray-400"></i>
                <span class="text-gray-400"><EMAIL></span>
              </div>
              <div class="flex items-center space-x-3">
                <i data-lucide="phone" class="w-4 h-4 text-gray-400"></i>
                <span class="text-gray-400">************</span>
              </div>
              <div class="flex items-center space-x-3">
                <i data-lucide="map-pin" class="w-4 h-4 text-gray-400"></i>
                <span class="text-gray-400">Hà Nội, Việt Nam</span>
              </div>
            </div>
            <ul class="space-y-2">
              <li>
                <a
                  href="#"
                  class="text-gray-400 hover:text-white transition-colors duration-200"
                  >Trung tâm trợ giúp</a
                >
              </li>
              <li>
                <a
                  href="#"
                  class="text-gray-400 hover:text-white transition-colors duration-200"
                  >Câu hỏi thường gặp</a
                >
              </li>
              <li>
                <a
                  href="#"
                  class="text-gray-400 hover:text-white transition-colors duration-200"
                  >Chính sách bảo mật</a
                >
              </li>
            </ul>
          </div>
        </div>

        <div class="border-t border-gray-800 mt-12 pt-8 text-center">
          <p class="text-gray-400 mb-2">
            © 2024 Test HMC - Nền tảng học tập cho sinh viên Cao đẳng Y tế Hà
            Nội. Tất cả quyền được bảo lưu.
          </p>
          <p class="text-gray-500 text-sm">
            Keywords: test hmc, cao đẳng y tế hà nội, điều dưỡng, dược, y sỹ đa
            khoa, kỹ thuật xét nghiệm, kỹ thuật hình ảnh, phục hồi chức năng,
            chăm sóc sắc đẹp
          </p>
        </div>
      </div>
    </footer>

    <script>
      // Initialize Lucide icons
      lucide.createIcons();

      // Mobile menu functionality
      const mobileMenuBtn = document.getElementById("mobile-menu-btn");
      const mobileMenu = document.getElementById("mobile-menu");
      let isMenuOpen = false;

      mobileMenuBtn.addEventListener("click", () => {
        isMenuOpen = !isMenuOpen;

        if (isMenuOpen) {
          mobileMenu.classList.remove("hidden");
          mobileMenuBtn.innerHTML = '<i data-lucide="x" class="w-6 h-6"></i>';
        } else {
          mobileMenu.classList.add("hidden");
          mobileMenuBtn.innerHTML =
            '<i data-lucide="menu" class="w-6 h-6"></i>';
        }

        // Re-initialize icons after changing innerHTML
        lucide.createIcons();
      });

      // Smooth scrolling for anchor links
      document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
        anchor.addEventListener("click", function (e) {
          e.preventDefault();
          const target = document.querySelector(this.getAttribute("href"));
          if (target) {
            target.scrollIntoView({
              behavior: "smooth",
            });
          }
        });
      });

      // Add scroll effect to header
      window.addEventListener("scroll", () => {
        const header = document.querySelector("header");
        if (window.scrollY > 100) {
          header.classList.add("bg-white/98");
          header.classList.remove("bg-white/95");
        } else {
          header.classList.add("bg-white/95");
          header.classList.remove("bg-white/98");
        }
      });
    </script>
  </body>
</html>
