<!DOCTYPE html>
<html lang="vi">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Danh sách môn học HMC - Tài liệu ôn thi Cao đẳng Y tế Hà Nội</title>
    <meta
      name="description"
      content="Danh sách đầy đủ các môn học Cao đẳng Y tế Hà Nội với tài liệu ôn thi, đ<PERSON> cư<PERSON>, gi<PERSON><PERSON> tr<PERSON><PERSON>, quizizz và google form đáp án chi tiết. <PERSON><PERSON><PERSON><PERSON> phẫu học, <PERSON><PERSON> lý họ<PERSON>, <PERSON><PERSON><PERSON><PERSON> lý, <PERSON><PERSON><PERSON><PERSON> dưỡng, <PERSON><PERSON> sinh y học."
    />
    <meta
      name="keywords"
      content="môn học hmc, danh sách môn học cao đẳng y tế hà nội, gi<PERSON><PERSON> phẫu học hmc, sinh lý học hmc, d<PERSON><PERSON><PERSON> lý học hmc, đi<PERSON><PERSON> dưỡng hmc, vi sinh y học hmc, ch<PERSON><PERSON> đ<PERSON><PERSON> hình ảnh hmc, tài liệu ôn thi hmc, quizizz ôn thi hmc, google form đáp án hmc, đề thi thử hmc, đề cương hmc"
    />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://testhmc.edu.vn/subjects" />
    <meta
      property="og:title"
      content="Danh sách môn học HMC - Tài liệu ôn thi Cao đẳng Y tế Hà Nội"
    />
    <meta
      property="og:description"
      content="Khám phá các môn học chuyên ngành y tế với tài liệu ôn thi đầy đủ, quizizz, google form đáp án và hỗ trợ 24/7 cho sinh viên HMC."
    />
    <meta
      property="og:image"
      content="https://testhmc.edu.vn/images/subjects-preview.jpg"
    />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://testhmc.edu.vn/subjects" />
    <meta
      property="twitter:title"
      content="Danh sách môn học HMC - Tài liệu ôn thi Cao đẳng Y tế Hà Nội"
    />
    <meta
      property="twitter:description"
      content="Khám phá các môn học chuyên ngành y tế với tài liệu ôn thi đầy đủ, quizizz, google form đáp án và hỗ trợ 24/7 cho sinh viên HMC."
    />
    <meta
      property="twitter:image"
      content="https://testhmc.edu.vn/images/subjects-preview.jpg"
    />

    <!-- Canonical URL -->
    <link rel="canonical" href="https://testhmc.edu.vn/subjects" />

    <!-- Structured Data -->
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "Course",
        "name": "Danh sách môn học Cao đẳng Y tế Hà Nội",
        "description": "Các môn học chuyên ngành y tế với tài liệu ôn thi, quizizz và google form đáp án",
        "provider": {
          "@type": "EducationalOrganization",
          "name": "Test HMC",
          "url": "https://testhmc.edu.vn"
        },
        "educationalLevel": "College",
        "teaches": "Medical Education",
        "courseMode": "online",
        "availableLanguage": "vi"
      }
    </script>

    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>

    <style>
      /* Custom animations */
      .fade-in {
        animation: fadeIn 0.6s ease-in-out;
      }

      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(20px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .card-hover {
        transition: all 0.3s ease;
      }

      .card-hover:hover {
        transform: translateY(-8px);
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
          0 10px 10px -5px rgba(0, 0, 0, 0.04);
      }

      .pulse-animation {
        animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
      }

      .shimmer {
        background: linear-gradient(
          90deg,
          #f3f4f6 25%,
          #e5e7eb 50%,
          #f3f4f6 75%
        );
        background-size: 200% 100%;
        animation: shimmer 1.5s infinite;
      }

      @keyframes shimmer {
        0% {
          background-position: -200% 0;
        }
        100% {
          background-position: 200% 0;
        }
      }

      .btn-primary {
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
      }

      .btn-primary:hover {
        background: linear-gradient(135deg, #1d4ed8, #1e40af);
      }

      .hidden {
        display: none !important;
      }
    </style>
  </head>
  <body class="min-h-screen bg-gray-50">
    <!-- Main Content -->
    <main class="py-8">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="text-center mb-12 fade-in">
          <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Danh sách môn học
            <span
              class="bg-gradient-to-r from-blue-600 to-teal-600 bg-clip-text text-transparent"
              >HMC</span
            >
          </h1>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Khám phá các môn học chất lượng cao được thiết kế riêng cho sinh
            viên
            <strong>Cao đẳng Y tế Hà Nội</strong> với tài liệu ôn thi, quizizz
            và google form đáp án chi tiết
          </p>

          <!-- Quick stats -->
          <div
            class="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-2xl mx-auto mt-8"
          >
            <div class="text-center">
              <div class="text-2xl font-bold text-blue-600">50+</div>
              <div class="text-sm text-gray-600">Môn học</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-teal-600">2000+</div>
              <div class="text-sm text-gray-600">Sinh viên</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-purple-600">1000+</div>
              <div class="text-sm text-gray-600">Quizizz</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-orange-600">24/7</div>
              <div class="text-sm text-gray-600">Hỗ trợ</div>
            </div>
          </div>
        </div>

        <!-- Search and Filters -->
        <div class="bg-white rounded-2xl shadow-lg p-6 mb-8 fade-in">
          <div class="flex flex-col lg:flex-row gap-6">
            <!-- Search -->
            <div class="flex-1">
              <div class="relative">
                <i
                  data-lucide="search"
                  class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"
                ></i>
                <input
                  type="text"
                  placeholder="Tìm kiếm môn học: Giải phẫu, Sinh lý, Dược lý, Điều dưỡng..."
                  id="search-input"
                  class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- Results count -->
        <div class="mb-6">
          <p class="text-gray-600">
            Hiển thị <span id="results-count" class="font-semibold">6</span> môn
            học
          </p>
        </div>

        <!-- Subjects Grid -->
        <div
          id="subjects-grid"
          class="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          <!-- Subject Card 1 - Giải phẫu học -->
          <div
            class="subject-card bg-white rounded-2xl shadow-lg overflow-hidden card-hover group fade-in"
            data-category="medical"
            data-rating="4.8"
            data-price="30000"
            data-students="1250"
          >
            <!-- Image & Badge -->
            <div class="relative">
              <img
                src="https://images.pexels.com/photos/4386466/pexels-photo-4386466.jpeg?auto=compress&cs=tinysrgb&w=400"
                alt="Giải phẫu học HMC - Tài liệu ôn thi và quizizz"
                class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
              />
              <div
                class="absolute top-4 left-4 bg-orange-500 text-white px-3 py-1 rounded-full text-sm font-semibold"
              >
                🔥 Phổ biến
              </div>
              <div
                class="absolute top-4 right-4 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-semibold"
              >
                -23%
              </div>
            </div>

            <!-- Content -->
            <div class="p-6">
              <!-- Title & Level -->
              <div class="mb-3">
                <h3
                  class="text-xl font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors duration-200"
                >
                  Giải phẫu học
                </h3>
                <span
                  class="inline-block px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800"
                >
                  Cơ bản
                </span>
              </div>

              <!-- Stats -->
              <div
                class="flex items-center justify-between mb-4 text-sm text-gray-500"
              >
                <div class="flex items-center space-x-1">
                  <i
                    data-lucide="star"
                    class="text-yellow-400 fill-current w-4 h-4"
                  ></i>
                  <span class="font-medium">4.8</span>
                </div>
                <div class="flex items-center space-x-1">
                  <i data-lucide="users" class="w-4 h-4"></i>
                  <span>1,250</span>
                </div>
                <div class="flex items-center space-x-1">
                  <i data-lucide="clock" class="w-4 h-4"></i>
                  <span>45 giờ</span>
                </div>
              </div>

              <!-- Description -->
              <p class="text-gray-600 text-sm mb-4">
                Khóa học giải phẫu học cơ bản với hình ảnh 3D chi tiết, phù hợp
                cho sinh viên năm nhất.
              </p>

              <!-- Highlights -->
              <div class="mb-4">
                <div class="flex flex-wrap gap-1">
                  <span
                    class="bg-blue-50 text-blue-700 px-2 py-1 rounded text-xs"
                    >✓ Quizizz</span
                  >
                  <span
                    class="bg-blue-50 text-blue-700 px-2 py-1 rounded text-xs"
                    >✓ Google form</span
                  >
                  <span
                    class="bg-blue-50 text-blue-700 px-2 py-1 rounded text-xs"
                    >✓ Đề thi mẫu</span
                  >
                  <span
                    class="bg-blue-50 text-blue-700 px-2 py-1 rounded text-xs"
                    >✓ Đáp án</span
                  >
                  <span
                    class="bg-blue-50 text-blue-700 px-2 py-1 rounded text-xs"
                    >✓ Hỏi đáp AI</span
                  >
                </div>
              </div>

              <!-- Price -->
              <div class="mb-6">
                <div class="flex items-center space-x-2">
                  <span class="text-2xl font-bold text-blue-600">30.000₫</span>
                  <span class="text-lg text-gray-400 line-through"
                    >39.000₫</span
                  >
                </div>
              </div>

              <!-- CTA Buttons -->
              <div class="space-y-3">
                <button
                  class="w-full btn-primary text-white font-bold py-4 px-6 rounded-xl transition-all duration-200 transform hover:scale-105 hover:shadow-lg flex items-center justify-center space-x-2"
                >
                  <i data-lucide="shopping-cart" class="w-5 h-5"></i>
                  <span>Mua ngay - Tiết kiệm 23%</span>
                </button>

                <div class="grid grid-cols-3 gap-2">
                  <button
                    class="bg-orange-50 hover:bg-orange-100 text-orange-700 font-semibold py-3 px-3 rounded-lg transition-all duration-200 flex items-center justify-center space-x-1 border border-orange-200 hover:border-orange-300"
                  >
                    <i data-lucide="plus" class="w-4 h-4"></i>
                    <span class="text-xs">Giỏ hàng</span>
                  </button>

                  <button
                    class="bg-green-50 hover:bg-green-100 text-green-700 font-semibold py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center space-x-2 border border-green-200 hover:border-green-300"
                  >
                    <i data-lucide="play" class="w-4 h-4"></i>
                    <span class="text-xs">Học thử</span>
                  </button>

                  <button
                    class="bg-gray-50 hover:bg-gray-100 text-gray-700 font-semibold py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center space-x-2 border border-gray-200 hover:border-gray-300"
                  >
                    <i data-lucide="message-circle" class="w-4 h-4"></i>
                    <span class="text-xs">Tư vấn</span>
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Subject Card 2 - Sinh lý học -->
          <div
            class="subject-card bg-white rounded-2xl shadow-lg overflow-hidden card-hover group fade-in"
            data-category="medical"
            data-rating="4.9"
            data-price="30000"
            data-students="980"
          >
            <div class="relative">
              <img
                src="https://images.pexels.com/photos/4386467/pexels-photo-4386467.jpeg?auto=compress&cs=tinysrgb&w=400"
                alt="Sinh lý học HMC - Tài liệu ôn thi và quizizz"
                class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
              />
              <div
                class="absolute top-4 right-4 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-semibold"
              >
                -23%
              </div>
            </div>

            <div class="p-6">
              <div class="mb-3">
                <h3
                  class="text-xl font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors duration-200"
                >
                  Sinh lý học
                </h3>
                <span
                  class="inline-block px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800"
                >
                  Cơ bản
                </span>
              </div>

              <div
                class="flex items-center justify-between mb-4 text-sm text-gray-500"
              >
                <div class="flex items-center space-x-1">
                  <i
                    data-lucide="star"
                    class="text-yellow-400 fill-current w-4 h-4"
                  ></i>
                  <span class="font-medium">4.9</span>
                </div>
                <div class="flex items-center space-x-1">
                  <i data-lucide="users" class="w-4 h-4"></i>
                  <span>980</span>
                </div>
                <div class="flex items-center space-x-1">
                  <i data-lucide="clock" class="w-4 h-4"></i>
                  <span>38 giờ</span>
                </div>
              </div>

              <p class="text-gray-600 text-sm mb-4">
                Tìm hiểu các chức năng của cơ thể người qua các bài giảng sinh
                động và dễ hiểu.
              </p>

              <div class="mb-4">
                <div class="flex flex-wrap gap-1">
                  <span
                    class="bg-blue-50 text-blue-700 px-2 py-1 rounded text-xs"
                    >✓ Quizizz</span
                  >
                  <span
                    class="bg-blue-50 text-blue-700 px-2 py-1 rounded text-xs"
                    >✓ Google form</span
                  >
                  <span
                    class="bg-blue-50 text-blue-700 px-2 py-1 rounded text-xs"
                    >✓ Đề thi mẫu</span
                  >
                  <span
                    class="bg-blue-50 text-blue-700 px-2 py-1 rounded text-xs"
                    >✓ Đáp án</span
                  >
                  <span
                    class="bg-blue-50 text-blue-700 px-2 py-1 rounded text-xs"
                    >✓ Hỏi đáp AI</span
                  >
                </div>
              </div>

              <div class="mb-6">
                <div class="flex items-center space-x-2">
                  <span class="text-2xl font-bold text-blue-600">30.000₫</span>
                  <span class="text-lg text-gray-400 line-through"
                    >39.000₫</span
                  >
                </div>
              </div>

              <div class="space-y-3">
                <button
                  class="w-full btn-primary text-white font-bold py-4 px-6 rounded-xl transition-all duration-200 transform hover:scale-105 hover:shadow-lg flex items-center justify-center space-x-2"
                >
                  <i data-lucide="shopping-cart" class="w-5 h-5"></i>
                  <span>Mua ngay - Tiết kiệm 23%</span>
                </button>

                <div class="grid grid-cols-3 gap-2">
                  <button
                    class="bg-orange-50 hover:bg-orange-100 text-orange-700 font-semibold py-3 px-3 rounded-lg transition-all duration-200 flex items-center justify-center space-x-1 border border-orange-200 hover:border-orange-300"
                  >
                    <i data-lucide="plus" class="w-4 h-4"></i>
                    <span class="text-xs">Giỏ hàng</span>
                  </button>

                  <button
                    class="bg-green-50 hover:bg-green-100 text-green-700 font-semibold py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center space-x-2 border border-green-200 hover:border-green-300"
                  >
                    <i data-lucide="play" class="w-4 h-4"></i>
                    <span class="text-xs">Học thử</span>
                  </button>

                  <button
                    class="bg-gray-50 hover:bg-gray-100 text-gray-700 font-semibold py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center space-x-2 border border-gray-200 hover:border-gray-300"
                  >
                    <i data-lucide="message-circle" class="w-4 h-4"></i>
                    <span class="text-xs">Tư vấn</span>
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Subject Card 3 - Dược lý học -->
          <div
            class="subject-card bg-white rounded-2xl shadow-lg overflow-hidden card-hover group fade-in"
            data-category="pharmacy"
            data-rating="4.7"
            data-price="30000"
            data-students="756"
          >
            <div class="relative">
              <img
                src="https://images.pexels.com/photos/4386468/pexels-photo-4386468.jpeg?auto=compress&cs=tinysrgb&w=400"
                alt="Dược lý học HMC - Tài liệu ôn thi và quizizz"
                class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
              />
              <div
                class="absolute top-4 left-4 bg-orange-500 text-white px-3 py-1 rounded-full text-sm font-semibold"
              >
                🔥 Phổ biến
              </div>
              <div
                class="absolute top-4 right-4 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-semibold"
              >
                -23%
              </div>
            </div>

            <div class="p-6">
              <div class="mb-3">
                <h3
                  class="text-xl font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors duration-200"
                >
                  Dược lý học cơ bản
                </h3>
                <span
                  class="inline-block px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800"
                >
                  Trung bình
                </span>
              </div>

              <div
                class="flex items-center justify-between mb-4 text-sm text-gray-500"
              >
                <div class="flex items-center space-x-1">
                  <i
                    data-lucide="star"
                    class="text-yellow-400 fill-current w-4 h-4"
                  ></i>
                  <span class="font-medium">4.7</span>
                </div>
                <div class="flex items-center space-x-1">
                  <i data-lucide="users" class="w-4 h-4"></i>
                  <span>756</span>
                </div>
                <div class="flex items-center space-x-1">
                  <i data-lucide="clock" class="w-4 h-4"></i>
                  <span>42 giờ</span>
                </div>
              </div>

              <p class="text-gray-600 text-sm mb-4">
                Khóa học về tác dụng của thuốc, cơ chế hoạt động và tương tác
                dược.
              </p>

              <div class="mb-4">
                <div class="flex flex-wrap gap-1">
                  <span
                    class="bg-blue-50 text-blue-700 px-2 py-1 rounded text-xs"
                    >✓ Quizizz</span
                  >
                  <span
                    class="bg-blue-50 text-blue-700 px-2 py-1 rounded text-xs"
                    >✓ Google form</span
                  >
                  <span
                    class="bg-blue-50 text-blue-700 px-2 py-1 rounded text-xs"
                    >✓ Đề thi mẫu</span
                  >
                  <span
                    class="bg-blue-50 text-blue-700 px-2 py-1 rounded text-xs"
                    >✓ Đáp án</span
                  >
                  <span
                    class="bg-blue-50 text-blue-700 px-2 py-1 rounded text-xs"
                    >✓ Hỏi đáp AI</span
                  >
                </div>
              </div>

              <div class="mb-6">
                <div class="flex items-center space-x-2">
                  <span class="text-2xl font-bold text-blue-600">30.000₫</span>
                  <span class="text-lg text-gray-400 line-through"
                    >39.000₫</span
                  >
                </div>
              </div>

              <div class="space-y-3">
                <button
                  class="w-full btn-primary text-white font-bold py-4 px-6 rounded-xl transition-all duration-200 transform hover:scale-105 hover:shadow-lg flex items-center justify-center space-x-2"
                >
                  <i data-lucide="shopping-cart" class="w-5 h-5"></i>
                  <span>Mua ngay - Tiết kiệm 23%</span>
                </button>

                <div class="grid grid-cols-3 gap-2">
                  <button
                    class="bg-orange-50 hover:bg-orange-100 text-orange-700 font-semibold py-3 px-3 rounded-lg transition-all duration-200 flex items-center justify-center space-x-1 border border-orange-200 hover:border-orange-300"
                  >
                    <i data-lucide="plus" class="w-4 h-4"></i>
                    <span class="text-xs">Giỏ hàng</span>
                  </button>

                  <button
                    class="bg-green-50 hover:bg-green-100 text-green-700 font-semibold py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center space-x-2 border border-green-200 hover:border-green-300"
                  >
                    <i data-lucide="play" class="w-4 h-4"></i>
                    <span class="text-xs">Học thử</span>
                  </button>

                  <button
                    class="bg-gray-50 hover:bg-gray-100 text-gray-700 font-semibold py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center space-x-2 border border-gray-200 hover:border-gray-300"
                  >
                    <i data-lucide="message-circle" class="w-4 h-4"></i>
                    <span class="text-xs">Tư vấn</span>
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Subject Card 4 - Kỹ thuật điều dưỡng -->
          <div
            class="subject-card bg-white rounded-2xl shadow-lg overflow-hidden card-hover group fade-in"
            data-category="nursing"
            data-rating="4.6"
            data-price="30000"
            data-students="1100"
          >
            <div class="relative">
              <img
                src="https://images.pexels.com/photos/4386469/pexels-photo-4386469.jpeg?auto=compress&cs=tinysrgb&w=400"
                alt="Kỹ thuật điều dưỡng HMC - Tài liệu ôn thi và quizizz"
                class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
              />
              <div
                class="absolute top-4 right-4 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-semibold"
              >
                -23%
              </div>
            </div>

            <div class="p-6">
              <div class="mb-3">
                <h3
                  class="text-xl font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors duration-200"
                >
                  Kỹ thuật điều dưỡng
                </h3>
                <span
                  class="inline-block px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800"
                >
                  Nâng cao
                </span>
              </div>

              <div
                class="flex items-center justify-between mb-4 text-sm text-gray-500"
              >
                <div class="flex items-center space-x-1">
                  <i
                    data-lucide="star"
                    class="text-yellow-400 fill-current w-4 h-4"
                  ></i>
                  <span class="font-medium">4.6</span>
                </div>
                <div class="flex items-center space-x-1">
                  <i data-lucide="users" class="w-4 h-4"></i>
                  <span>1,100</span>
                </div>
                <div class="flex items-center space-x-1">
                  <i data-lucide="clock" class="w-4 h-4"></i>
                  <span>50 giờ</span>
                </div>
              </div>

              <p class="text-gray-600 text-sm mb-4">
                Các kỹ thuật điều dưỡng cơ bản và nâng cao với video thực hành
                chi tiết.
              </p>

              <div class="mb-4">
                <div class="flex flex-wrap gap-1">
                  <span
                    class="bg-blue-50 text-blue-700 px-2 py-1 rounded text-xs"
                    >✓ Quizizz</span
                  >
                  <span
                    class="bg-blue-50 text-blue-700 px-2 py-1 rounded text-xs"
                    >✓ Google form</span
                  >
                  <span
                    class="bg-blue-50 text-blue-700 px-2 py-1 rounded text-xs"
                    >✓ Đề thi mẫu</span
                  >
                  <span
                    class="bg-blue-50 text-blue-700 px-2 py-1 rounded text-xs"
                    >✓ Đáp án</span
                  >
                  <span
                    class="bg-blue-50 text-blue-700 px-2 py-1 rounded text-xs"
                    >✓ Hỏi đáp AI</span
                  >
                </div>
              </div>

              <div class="mb-6">
                <div class="flex items-center space-x-2">
                  <span class="text-2xl font-bold text-blue-600">30.000₫</span>
                  <span class="text-lg text-gray-400 line-through"
                    >39.000₫</span
                  >
                </div>
              </div>

              <div class="space-y-3">
                <button
                  class="w-full btn-primary text-white font-bold py-4 px-6 rounded-xl transition-all duration-200 transform hover:scale-105 hover:shadow-lg flex items-center justify-center space-x-2"
                >
                  <i data-lucide="shopping-cart" class="w-5 h-5"></i>
                  <span>Mua ngay - Tiết kiệm 23%</span>
                </button>

                <div class="grid grid-cols-3 gap-2">
                  <button
                    class="bg-orange-50 hover:bg-orange-100 text-orange-700 font-semibold py-3 px-3 rounded-lg transition-all duration-200 flex items-center justify-center space-x-1 border border-orange-200 hover:border-orange-300"
                  >
                    <i data-lucide="plus" class="w-4 h-4"></i>
                    <span class="text-xs">Giỏ hàng</span>
                  </button>

                  <button
                    class="bg-green-50 hover:bg-green-100 text-green-700 font-semibold py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center space-x-2 border border-green-200 hover:border-green-300"
                  >
                    <i data-lucide="play" class="w-4 h-4"></i>
                    <span class="text-xs">Học thử</span>
                  </button>

                  <button
                    class="bg-gray-50 hover:bg-gray-100 text-gray-700 font-semibold py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center space-x-2 border border-gray-200 hover:border-gray-300"
                  >
                    <i data-lucide="message-circle" class="w-4 h-4"></i>
                    <span class="text-xs">Tư vấn</span>
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Subject Card 5 - Vi sinh y học -->
          <div
            class="subject-card bg-white rounded-2xl shadow-lg overflow-hidden card-hover group fade-in"
            data-category="lab"
            data-rating="4.5"
            data-price="30000"
            data-students="623"
          >
            <div class="relative">
              <img
                src="https://images.pexels.com/photos/4386470/pexels-photo-4386470.jpeg?auto=compress&cs=tinysrgb&w=400"
                alt="Vi sinh y học HMC - Tài liệu ôn thi và quizizz"
                class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
              />
              <div
                class="absolute top-4 right-4 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-semibold"
              >
                -23%
              </div>
            </div>

            <div class="p-6">
              <div class="mb-3">
                <h3
                  class="text-xl font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors duration-200"
                >
                  Vi sinh y học
                </h3>
                <span
                  class="inline-block px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800"
                >
                  Trung bình
                </span>
              </div>

              <div
                class="flex items-center justify-between mb-4 text-sm text-gray-500"
              >
                <div class="flex items-center space-x-1">
                  <i
                    data-lucide="star"
                    class="text-yellow-400 fill-current w-4 h-4"
                  ></i>
                  <span class="font-medium">4.5</span>
                </div>
                <div class="flex items-center space-x-1">
                  <i data-lucide="users" class="w-4 h-4"></i>
                  <span>623</span>
                </div>
                <div class="flex items-center space-x-1">
                  <i data-lucide="clock" class="w-4 h-4"></i>
                  <span>35 giờ</span>
                </div>
              </div>

              <p class="text-gray-600 text-sm mb-4">
                Nghiên cứu về vi khuẩn, virus và các tác nhân gây bệnh trong y
                học.
              </p>

              <div class="mb-4">
                <div class="flex flex-wrap gap-1">
                  <span
                    class="bg-blue-50 text-blue-700 px-2 py-1 rounded text-xs"
                    >✓ Quizizz</span
                  >
                  <span
                    class="bg-blue-50 text-blue-700 px-2 py-1 rounded text-xs"
                    >✓ Google form</span
                  >
                  <span
                    class="bg-blue-50 text-blue-700 px-2 py-1 rounded text-xs"
                    >✓ Đề thi mẫu</span
                  >
                  <span
                    class="bg-blue-50 text-blue-700 px-2 py-1 rounded text-xs"
                    >✓ Đáp án</span
                  >
                  <span
                    class="bg-blue-50 text-blue-700 px-2 py-1 rounded text-xs"
                    >✓ Hỏi đáp AI</span
                  >
                </div>
              </div>

              <div class="mb-6">
                <div class="flex items-center space-x-2">
                  <span class="text-2xl font-bold text-blue-600">30.000₫</span>
                  <span class="text-lg text-gray-400 line-through"
                    >39.000₫</span
                  >
                </div>
              </div>

              <div class="space-y-3">
                <button
                  class="w-full btn-primary text-white font-bold py-4 px-6 rounded-xl transition-all duration-200 transform hover:scale-105 hover:shadow-lg flex items-center justify-center space-x-2"
                >
                  <i data-lucide="shopping-cart" class="w-5 h-5"></i>
                  <span>Mua ngay - Tiết kiệm 23%</span>
                </button>

                <div class="grid grid-cols-3 gap-2">
                  <button
                    class="bg-orange-50 hover:bg-orange-100 text-orange-700 font-semibold py-3 px-3 rounded-lg transition-all duration-200 flex items-center justify-center space-x-1 border border-orange-200 hover:border-orange-300"
                  >
                    <i data-lucide="plus" class="w-4 h-4"></i>
                    <span class="text-xs">Giỏ hàng</span>
                  </button>

                  <button
                    class="bg-green-50 hover:bg-green-100 text-green-700 font-semibold py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center space-x-2 border border-green-200 hover:border-green-300"
                  >
                    <i data-lucide="play" class="w-4 h-4"></i>
                    <span class="text-xs">Học thử</span>
                  </button>

                  <button
                    class="bg-gray-50 hover:bg-gray-100 text-gray-700 font-semibold py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center space-x-2 border border-gray-200 hover:border-gray-300"
                  >
                    <i data-lucide="message-circle" class="w-4 h-4"></i>
                    <span class="text-xs">Tư vấn</span>
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Subject Card 6 - Chẩn đoán hình ảnh -->
          <div
            class="subject-card bg-white rounded-2xl shadow-lg overflow-hidden card-hover group fade-in"
            data-category="imaging"
            data-rating="4.8"
            data-price="30000"
            data-students="445"
          >
            <div class="relative">
              <img
                src="https://images.pexels.com/photos/4386471/pexels-photo-4386471.jpeg?auto=compress&cs=tinysrgb&w=400"
                alt="Chẩn đoán hình ảnh HMC - Tài liệu ôn thi và quizizz"
                class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
              />
              <div
                class="absolute top-4 left-4 bg-orange-500 text-white px-3 py-1 rounded-full text-sm font-semibold"
              >
                🔥 Phổ biến
              </div>
              <div
                class="absolute top-4 right-4 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-semibold"
              >
                -23%
              </div>
            </div>

            <div class="p-6">
              <div class="mb-3">
                <h3
                  class="text-xl font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors duration-200"
                >
                  Chẩn đoán hình ảnh
                </h3>
                <span
                  class="inline-block px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800"
                >
                  Nâng cao
                </span>
              </div>

              <div
                class="flex items-center justify-between mb-4 text-sm text-gray-500"
              >
                <div class="flex items-center space-x-1">
                  <i
                    data-lucide="star"
                    class="text-yellow-400 fill-current w-4 h-4"
                  ></i>
                  <span class="font-medium">4.8</span>
                </div>
                <div class="flex items-center space-x-1">
                  <i data-lucide="users" class="w-4 h-4"></i>
                  <span>445</span>
                </div>
                <div class="flex items-center space-x-1">
                  <i data-lucide="clock" class="w-4 h-4"></i>
                  <span>40 giờ</span>
                </div>
              </div>

              <p class="text-gray-600 text-sm mb-4">
                Học cách đọc và phân tích X-quang, CT, MRI với hàng trăm hình
                ảnh thực tế.
              </p>

              <div class="mb-4">
                <div class="flex flex-wrap gap-1">
                  <span
                    class="bg-blue-50 text-blue-700 px-2 py-1 rounded text-xs"
                    >✓ Quizizz</span
                  >
                  <span
                    class="bg-blue-50 text-blue-700 px-2 py-1 rounded text-xs"
                    >✓ Google form</span
                  >
                  <span
                    class="bg-blue-50 text-blue-700 px-2 py-1 rounded text-xs"
                    >✓ Đề thi mẫu</span
                  >
                  <span
                    class="bg-blue-50 text-blue-700 px-2 py-1 rounded text-xs"
                    >✓ Đáp án</span
                  >
                  <span
                    class="bg-blue-50 text-blue-700 px-2 py-1 rounded text-xs"
                    >✓ Hỏi đáp AI</span
                  >
                </div>
              </div>

              <div class="mb-6">
                <div class="flex items-center space-x-2">
                  <span class="text-2xl font-bold text-blue-600">30.000₫</span>
                  <span class="text-lg text-gray-400 line-through"
                    >39.000₫</span
                  >
                </div>
              </div>

              <div class="space-y-3">
                <button
                  class="w-full btn-primary text-white font-bold py-4 px-6 rounded-xl transition-all duration-200 transform hover:scale-105 hover:shadow-lg flex items-center justify-center space-x-2"
                >
                  <i data-lucide="shopping-cart" class="w-5 h-5"></i>
                  <span>Mua ngay - Tiết kiệm 23%</span>
                </button>

                <div class="grid grid-cols-3 gap-2">
                  <button
                    class="bg-orange-50 hover:bg-orange-100 text-orange-700 font-semibold py-3 px-3 rounded-lg transition-all duration-200 flex items-center justify-center space-x-1 border border-orange-200 hover:border-orange-300"
                  >
                    <i data-lucide="plus" class="w-4 h-4"></i>
                    <span class="text-xs">Giỏ hàng</span>
                  </button>

                  <button
                    class="bg-green-50 hover:bg-green-100 text-green-700 font-semibold py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center space-x-2 border border-green-200 hover:border-green-300"
                  >
                    <i data-lucide="play" class="w-4 h-4"></i>
                    <span class="text-xs">Học thử</span>
                  </button>

                  <button
                    class="bg-gray-50 hover:bg-gray-100 text-gray-700 font-semibold py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center space-x-2 border border-gray-200 hover:border-gray-300"
                  >
                    <i data-lucide="message-circle" class="w-4 h-4"></i>
                    <span class="text-xs">Tư vấn</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Load More -->
        <div class="text-center mt-12">
          <button
            id="load-more-btn"
            class="bg-white text-gray-700 px-8 py-3 rounded-lg border border-gray-300 hover:bg-gray-50 transition-colors duration-200 font-semibold"
          >
            Xem thêm môn học
          </button>
        </div>

        <!-- Contact CTA -->
        <div
          class="mt-16 bg-gradient-to-r from-blue-600 to-teal-600 rounded-2xl p-8 text-white text-center fade-in"
        >
          <h3 class="text-2xl font-bold mb-4">
            Cần tư vấn chọn môn học phù hợp?
          </h3>
          <p class="text-blue-100 mb-6 max-w-2xl mx-auto">
            Đội ngũ tư vấn giáo dục của chúng tôi sẽ giúp bạn lựa chọn các môn
            học phù hợp với mục tiêu học tập và ngành nghề mong muốn
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <button
              class="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors duration-200 flex items-center justify-center space-x-2"
            >
              <i data-lucide="phone" class="w-5 h-5"></i>
              <span>Gọi tư vấn: 1900 123 456</span>
            </button>
            <button
              class="bg-transparent border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-all duration-200 flex items-center justify-center space-x-2"
            >
              <i data-lucide="message-circle" class="w-5 h-5"></i>
              <span>Chat với tư vấn viên</span>
            </button>
          </div>
        </div>
      </div>
    </main>

    <script>
      // Initialize Lucide icons
      lucide.createIcons();

      // Mobile menu functionality
      const mobileMenuBtn = document.getElementById("mobile-menu-btn");
      const mobileMenu = document.getElementById("mobile-menu");
      let isMenuOpen = false;

      mobileMenuBtn.addEventListener("click", () => {
        isMenuOpen = !isMenuOpen;

        if (isMenuOpen) {
          mobileMenu.classList.remove("hidden");
          mobileMenuBtn.innerHTML = '<i data-lucide="x" class="w-6 h-6"></i>';
        } else {
          mobileMenu.classList.add("hidden");
          mobileMenuBtn.innerHTML =
            '<i data-lucide="menu" class="w-6 h-6"></i>';
        }

        lucide.createIcons();
      });

      // Search and filter functionality
      const searchInput = document.getElementById("search-input");
      const categoryFilter = document.getElementById("category-filter");
      const sortFilter = document.getElementById("sort-filter");
      const subjectCards = document.querySelectorAll(".subject-card");
      const resultsCount = document.getElementById("results-count");

      function filterAndSort() {
        const searchTerm = searchInput.value.toLowerCase();
        const selectedCategory = categoryFilter.value;
        const sortBy = sortFilter.value;

        let visibleCards = [];

        // Filter cards
        subjectCards.forEach((card) => {
          const title = card.querySelector("h3").textContent.toLowerCase();
          const category = card.dataset.category;

          const matchesSearch = title.includes(searchTerm);
          const matchesCategory =
            selectedCategory === "all" || category === selectedCategory;

          if (matchesSearch && matchesCategory) {
            card.style.display = "block";
            visibleCards.push(card);
          } else {
            card.style.display = "none";
          }
        });

        // Sort visible cards
        if (sortBy !== "popular") {
          visibleCards.sort((a, b) => {
            switch (sortBy) {
              case "rating":
                return (
                  parseFloat(b.dataset.rating) - parseFloat(a.dataset.rating)
                );
              case "price-low":
                return parseInt(a.dataset.price) - parseInt(b.dataset.price);
              case "price-high":
                return parseInt(b.dataset.price) - parseInt(a.dataset.price);
              case "students":
                return (
                  parseInt(b.dataset.students) - parseInt(a.dataset.students)
                );
              default:
                return 0;
            }
          });

          // Reorder in DOM
          const grid = document.getElementById("subjects-grid");
          visibleCards.forEach((card) => {
            grid.appendChild(card);
          });
        }

        // Update results count
        resultsCount.textContent = visibleCards.length;
      }

      // Event listeners for filters
      searchInput.addEventListener("input", filterAndSort);
      categoryFilter.addEventListener("change", filterAndSort);
      sortFilter.addEventListener("change", filterAndSort);

      // Add to cart functionality (demo)
      document.querySelectorAll("button").forEach((button) => {
        if (button.textContent.includes("Mua ngay")) {
          button.addEventListener("click", () => {
            alert(
              "Chức năng mua hàng sẽ được phát triển trong phiên bản tiếp theo!"
            );
          });
        } else if (button.textContent.includes("Giỏ hàng")) {
          button.addEventListener("click", () => {
            alert("Đã thêm vào giỏ hàng!");
          });
        } else if (button.textContent.includes("Học thử")) {
          button.addEventListener("click", () => {
            alert("Chức năng học thử sẽ được ra mắt sớm!");
          });
        } else if (button.textContent.includes("Tư vấn")) {
          button.addEventListener("click", () => {
            alert("Liên hệ: 1900 123 456 để được tư vấn chi tiết!");
          });
        }
      });

      // Smooth scrolling for anchor links
      document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
        anchor.addEventListener("click", function (e) {
          e.preventDefault();
          const target = document.querySelector(this.getAttribute("href"));
          if (target) {
            target.scrollIntoView({
              behavior: "smooth",
            });
          }
        });
      });

      // Add scroll effect to header
      window.addEventListener("scroll", () => {
        const header = document.querySelector("header");
        if (window.scrollY > 100) {
          header.classList.add("bg-white/98");
          header.classList.remove("bg-white/95");
        } else {
          header.classList.add("bg-white/95");
          header.classList.remove("bg-white/98");
        }
      });

      // Load more functionality (demo)
      document.getElementById("load-more-btn").addEventListener("click", () => {
        alert("Tính năng tải thêm môn học đang được phát triển!");
      });
    </script>
  </body>
</html>
