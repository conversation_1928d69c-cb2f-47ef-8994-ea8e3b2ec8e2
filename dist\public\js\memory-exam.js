/**
 * memory-exam.js
 * <PERSON><PERSON><PERSON> ch<PERSON>h quản lý tính năng "Ghi nhớ"
 *
 * Pattern: IIFE với namespace isolation
 */
(function (window) {
    "use strict";
    // Private variables và state
    let _config = {
        productId: null,
        apiBase: "/exam/memory",
        domSelectors: {
            container: "#memoryQuestionsContainer",
            loadingIndicator: "#memoryLoadingIndicator",
            totalCount: "#totalMemoryQuestionsCount",
            searchInput: "#memorySearchInput",
            statsTotal: "#memoryStatsTotalQuestions",
            statsAccuracy: "#memoryStatsAccuracy",
            statsPracticed: "#memoryStatsPracticed",
            bulkDeleteBtn: "#bulkDeleteBtn",
            selectedCountSpan: "#selectedCount",
            practiceModal: "#memoryPracticeModal",
        },
    };
    let _state = {
        questions: [],
        filteredQuestions: [],
        selectedQuestions: [],
        isLoading: false,
        searchTerm: "",
        examFilter: "all",
        sourceFilter: "all",
        currentPage: 1,
        itemsPerPage: 20,
        stats: {
            totalQuestions: 0,
            averageAccuracy: 0,
            practiceCount: 0,
        },
    };
    // DOM cache để lưu trữ tham chiếu đến DOM elements
    let _dom = {};
    // Khởi tạo DOM cache
    function _initDomCache() {
        Object.keys(_config.domSelectors).forEach((key) => {
            const selector = _config.domSelectors[key];
            _dom[key] = document.querySelector(selector);
        });
    }
    // Khởi tạo module
    function _init() {
        var _a;
        // Lấy productId từ URL
        const urlParams = new URLSearchParams(window.location.search);
        _config.productId =
            ((_a = document
                .querySelector('meta[name="product-id"]')) === null || _a === void 0 ? void 0 : _a.getAttribute("content")) || window.location.pathname.split("/")[2];
        console.log("Memory Exam module initialized with productId:", _config.productId);
        // Khởi tạo DOM cache
        _initDomCache();
        // Gán các event listeners
        _setupEventListeners();
        // Tải dữ liệu
        _loadMemoryQuestions();
    }
    // Thiết lập các event listeners
    function _setupEventListeners() {
        // Search input với debounce
        if (_dom.searchInput) {
            _dom.searchInput.addEventListener("input", _debounce(function () {
                _state.searchTerm = _dom.searchInput.value.toLowerCase().trim();
                _filterQuestions();
            }, 300));
        }
        // Event delegation cho container chứa danh sách câu hỏi
        if (_dom.container) {
            _dom.container.addEventListener("click", function (e) {
                // Handle checkbox selection
                if (e.target.matches(".memory-question-checkbox")) {
                    _handleCheckboxSelection(e.target);
                }
                // Handle delete button
                if (e.target.closest(".delete-memory-btn")) {
                    const questionElement = e.target.closest(".memory-question-item");
                    const questionId = questionElement.dataset.id;
                    _deleteMemoryQuestion(questionId);
                }
            });
        }
    }
    // Hàm tải danh sách câu hỏi ghi nhớ
    function _loadMemoryQuestions() {
        _state.isLoading = true;
        _showLoadingIndicator();
        // Gọi API để lấy danh sách câu hỏi ghi nhớ
        fetch(`${_config.apiBase}/${_config.productId}?page=${_state.currentPage}&limit=${_state.itemsPerPage}`)
            .then((response) => response.json())
            .then((data) => {
            if (data.success) {
                _state.questions = data.data.questions || [];
                _state.stats = data.data.stats || {
                    totalQuestions: _state.questions.length,
                    averageAccuracy: 0,
                    practiceCount: 0,
                };
                _updateUI();
            }
            else {
                console.error("Failed to load memory questions:", data.message);
                _showErrorMessage("Không thể tải danh sách câu hỏi ghi nhớ");
            }
        })
            .catch((error) => {
            console.error("Error loading memory questions:", error);
            _showErrorMessage("Đã có lỗi xảy ra khi tải dữ liệu");
        })
            .finally(() => {
            _state.isLoading = false;
            _hideLoadingIndicator();
        });
    }
    // Cập nhật giao diện người dùng
    function _updateUI() {
        _updateStats();
        _filterQuestions();
        _renderQuestions();
    }
    // Cập nhật thống kê
    function _updateStats() {
        if (_dom.statsTotal) {
            _dom.statsTotal.textContent = _state.stats.totalQuestions || 0;
        }
        if (_dom.totalCount) {
            _dom.totalCount.textContent = _state.stats.totalQuestions || 0;
        }
        if (_dom.statsAccuracy) {
            const accuracy = _state.stats.averageAccuracy || 0;
            _dom.statsAccuracy.textContent = `${Math.round(accuracy)}%`;
        }
        if (_dom.statsPracticed) {
            _dom.statsPracticed.textContent = _state.stats.practiceCount || 0;
        }
    }
    // Lọc câu hỏi dựa trên filter và search
    function _filterQuestions() {
        _state.filteredQuestions = _state.questions.filter((question) => {
            var _a, _b, _c, _d;
            // Filter by search term
            const matchesSearch = _state.searchTerm === "" ||
                ((_b = (_a = question.questionData) === null || _a === void 0 ? void 0 : _a.text) === null || _b === void 0 ? void 0 : _b.toLowerCase().includes(_state.searchTerm)) ||
                ((_d = (_c = question.questionData) === null || _c === void 0 ? void 0 : _c.options) === null || _d === void 0 ? void 0 : _d.some((option) => { var _a; return (_a = option.text) === null || _a === void 0 ? void 0 : _a.toLowerCase().includes(_state.searchTerm); }));
            // Filter by exam
            const matchesExam = _state.examFilter === "all" || question.examId === _state.examFilter;
            // Filter by source
            const matchesSource = _state.sourceFilter === "all" ||
                question.source === _state.sourceFilter;
            return matchesSearch && matchesExam && matchesSource;
        });
        // Cập nhật số lượng hiển thị
        if (_dom.searchInput && _dom.searchInput.value.trim() !== "") {
            document.getElementById("memorySearchInfo").style.display = "block";
            document.getElementById("memorySearchResultCount").textContent =
                _state.filteredQuestions.length;
            document.getElementById("clearMemorySearchBtn").style.display = "block";
        }
        else {
            document.getElementById("memorySearchInfo").style.display = "none";
            document.getElementById("clearMemorySearchBtn").style.display = "none";
        }
        // Render lại câu hỏi
        _renderQuestions();
    }
    // Render danh sách câu hỏi
    function _renderQuestions() {
        if (!_dom.container)
            return;
        if (_state.filteredQuestions.length === 0) {
            _dom.container.innerHTML = `
        <div class="text-center py-8 bg-white rounded-lg">
          <div class="text-gray-400 mb-3">
            <i class="fas fa-search text-3xl"></i>
          </div>
          <h3 class="text-lg font-medium text-gray-700 mb-2">Không tìm thấy câu hỏi</h3>
          <p class="text-sm text-gray-500">Thử thay đổi bộ lọc hoặc từ khóa tìm kiếm</p>
        </div>
      `;
            return;
        }
        let html = "";
        _state.filteredQuestions.forEach((question, index) => {
            var _a;
            const sourceLabel = question.source === "manual"
                ? '<span class="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-0.5 rounded-full">Thủ công</span>'
                : '<span class="bg-orange-100 text-orange-800 text-xs font-medium px-2 py-0.5 rounded-full">Thi thử</span>';
            const accuracy = question.practiceCount > 0
                ? Math.round((question.correctCount / question.practiceCount) * 100)
                : 0;
            const accuracyClass = accuracy >= 70
                ? "text-green-600"
                : accuracy >= 50
                    ? "text-yellow-600"
                    : "text-red-600";
            const questionText = ((_a = question.questionData) === null || _a === void 0 ? void 0 : _a.text) || "Không có nội dung câu hỏi";
            html += `
        <div class="memory-question-item bg-white rounded-lg border border-gray-200 p-4 mb-4 hover:shadow-sm transition-all"
             data-id="${question._id}" data-index="${index}">
          <div class="flex items-start gap-3">
            <div class="flex-shrink-0 pt-1">
              <input type="checkbox" class="memory-question-checkbox h-5 w-5 text-indigo-600 rounded" />
            </div>
            <div class="flex-1">
              <div class="flex items-center justify-between mb-3">
                <div class="flex items-center gap-2">
                  ${sourceLabel}
                  <span class="text-xs text-gray-500">${new Date(question.createdAt).toLocaleDateString("vi-VN")}</span>
                </div>
                <button class="delete-memory-btn text-red-500 hover:text-red-700 p-1" title="Xóa khỏi ghi nhớ">
                  <i class="fas fa-trash-alt"></i>
                </button>
              </div>
              <div class="text-gray-700 mb-3 text-sm leading-relaxed break-words">
                ${questionText}
              </div>
              <div class="flex items-center justify-between text-xs">
                <div>
                  <span class="text-gray-500">Đã luyện: <span class="font-semibold">${question.practiceCount || 0}</span></span>
                  <span class="mx-1">|</span>
                  <span class="text-gray-500">Độ chính xác: <span class="font-semibold ${accuracyClass}">${accuracy}%</span></span>
                </div>
                <div class="text-gray-500">
                  ${question.lastPracticed
                ? `Luyện gần nhất: ${new Date(question.lastPracticed).toLocaleDateString("vi-VN")}`
                : "Chưa luyện tập"}
                </div>
              </div>
            </div>
          </div>
        </div>
      `;
        });
        _dom.container.innerHTML = html;
    }
    // Xử lý checkbox selection
    function _handleCheckboxSelection(checkbox) {
        const questionItem = checkbox.closest(".memory-question-item");
        const questionId = questionItem.dataset.id;
        if (checkbox.checked) {
            _state.selectedQuestions.push(questionId);
            questionItem.classList.add("bg-indigo-50");
        }
        else {
            _state.selectedQuestions = _state.selectedQuestions.filter((id) => id !== questionId);
            questionItem.classList.remove("bg-indigo-50");
        }
        // Update bulk delete button
        _updateBulkDeleteButton();
    }
    // Cập nhật trạng thái nút xóa hàng loạt
    function _updateBulkDeleteButton() {
        if (_dom.bulkDeleteBtn && _dom.selectedCountSpan) {
            _dom.bulkDeleteBtn.disabled = _state.selectedQuestions.length === 0;
            _dom.selectedCountSpan.textContent = _state.selectedQuestions.length;
        }
    }
    // Xóa một câu hỏi ghi nhớ
    function _deleteMemoryQuestion(questionId) {
        if (!confirm("Bạn có chắc muốn xóa câu hỏi này khỏi danh sách ghi nhớ?")) {
            return;
        }
        fetch(`${_config.apiBase}/${_config.productId}/${questionId}`, {
            method: "DELETE",
            headers: {
                "Content-Type": "application/json",
            },
        })
            .then((response) => response.json())
            .then((data) => {
            if (data.success) {
                // Xóa câu hỏi khỏi state
                _state.questions = _state.questions.filter((q) => q._id !== questionId);
                _state.filteredQuestions = _state.filteredQuestions.filter((q) => q._id !== questionId);
                _state.selectedQuestions = _state.selectedQuestions.filter((id) => id !== questionId);
                // Cập nhật stats
                _state.stats.totalQuestions = _state.questions.length;
                // Cập nhật UI
                _updateUI();
                // Hiển thị thông báo
                _showSuccessToast("Đã xóa câu hỏi khỏi danh sách ghi nhớ");
            }
            else {
                console.error("Failed to delete memory question:", data.message);
                _showErrorToast("Không thể xóa câu hỏi");
            }
        })
            .catch((error) => {
            console.error("Error deleting memory question:", error);
            _showErrorToast("Đã có lỗi xảy ra");
        });
    }
    // Xóa nhiều câu hỏi đã chọn
    function deleteBulkMemoryQuestions() {
        if (_state.selectedQuestions.length === 0)
            return;
        if (!confirm(`Bạn có chắc muốn xóa ${_state.selectedQuestions.length} câu hỏi đã chọn?`)) {
            return;
        }
        fetch(`${_config.apiBase}/${_config.productId}/bulk-delete`, {
            method: "DELETE",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify({
                questionIds: _state.selectedQuestions,
            }),
        })
            .then((response) => response.json())
            .then((data) => {
            if (data.success) {
                // Xóa các câu hỏi khỏi state
                _state.questions = _state.questions.filter((q) => !_state.selectedQuestions.includes(q._id));
                _state.filteredQuestions = _state.filteredQuestions.filter((q) => !_state.selectedQuestions.includes(q._id));
                // Reset selected questions
                _state.selectedQuestions = [];
                // Cập nhật stats
                _state.stats.totalQuestions = _state.questions.length;
                // Cập nhật UI
                _updateUI();
                _updateBulkDeleteButton();
                // Hiển thị thông báo
                _showSuccessToast(`Đã xóa ${data.deletedCount} câu hỏi khỏi danh sách ghi nhớ`);
            }
            else {
                console.error("Failed to delete memory questions:", data.message);
                _showErrorToast("Không thể xóa câu hỏi");
            }
        })
            .catch((error) => {
            console.error("Error deleting memory questions:", error);
            _showErrorToast("Đã có lỗi xảy ra");
        });
    }
    // Hiển thị modal bắt đầu luyện tập
    function startMemoryPractice() {
        if (_state.questions.length === 0) {
            alert("Bạn chưa có câu hỏi nào trong danh sách ghi nhớ để luyện tập");
            return;
        }
        if (_dom.practiceModal) {
            _dom.practiceModal.classList.remove("hidden");
        }
    }
    // Đóng modal luyện tập
    function closeMemoryPracticeModal() {
        if (_dom.practiceModal) {
            _dom.practiceModal.classList.add("hidden");
        }
    }
    // Bắt đầu bài luyện tập ghi nhớ
    function startMemoryExam() {
        const questionCount = parseInt(document.getElementById("questionCount").value) || 10;
        const timeLimit = parseInt(document.getElementById("timeLimit").value) || 15;
        const shuffleQuestions = document.getElementById("shuffleQuestions").checked;
        const shuffleAnswers = document.getElementById("shuffleAnswers").checked;
        // Tạo query params
        const queryParams = new URLSearchParams();
        queryParams.set("count", questionCount.toString());
        queryParams.set("time", timeLimit.toString());
        if (shuffleQuestions)
            queryParams.set("shuffleQuestions", "true");
        if (shuffleAnswers)
            queryParams.set("shuffleAnswers", "true");
        // Gọi API để bắt đầu bài luyện tập
        fetch(`${_config.apiBase}/${_config.productId}/practice/start`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify({
                count: questionCount,
                timeLimit: timeLimit,
                shuffleQuestions: shuffleQuestions,
                shuffleAnswers: shuffleAnswers,
            }),
        })
            .then((response) => response.json())
            .then((data) => {
            if (data.success && data.practiceId) {
                // Chuyển đến trang luyện tập
                window.location.href = `/course/${_config.productId}/${data.practiceId}/memory-practice?${queryParams.toString()}`;
            }
            else {
                console.error("Failed to start memory practice:", data.message);
                alert("Không thể bắt đầu bài luyện tập: " +
                    (data.message || "Lỗi không xác định"));
            }
        })
            .catch((error) => {
            console.error("Error starting memory practice:", error);
            alert("Đã có lỗi xảy ra khi bắt đầu bài luyện tập");
        });
    }
    // Hiển thị thông báo lỗi
    function _showErrorMessage(message) {
        if (_dom.container) {
            _dom.container.innerHTML = `
        <div class="text-center py-8 bg-white rounded-lg">
          <div class="text-red-500 mb-3">
            <i class="fas fa-exclamation-circle text-3xl"></i>
          </div>
          <h3 class="text-lg font-medium text-gray-700 mb-2">Đã xảy ra lỗi</h3>
          <p class="text-sm text-gray-500 mb-4">${message}</p>
          <button onclick="window.MemoryExam.loadMemoryQuestions()" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
            <i class="fas fa-redo mr-2"></i>Thử lại
          </button>
        </div>
      `;
        }
    }
    // Hiển thị toast thành công
    function _showSuccessToast(message) {
        // Triển khai sau hoặc sử dụng thư viện toast
        console.log("Success:", message);
    }
    // Hiển thị toast lỗi
    function _showErrorToast(message) {
        // Triển khai sau hoặc sử dụng thư viện toast
        console.error("Error:", message);
    }
    // Hiển thị loading indicator
    function _showLoadingIndicator() {
        if (_dom.loadingIndicator) {
            _dom.loadingIndicator.style.display = "block";
        }
    }
    // Ẩn loading indicator
    function _hideLoadingIndicator() {
        if (_dom.loadingIndicator) {
            _dom.loadingIndicator.style.display = "none";
        }
    }
    // Utility: Debounce function
    function _debounce(func, wait) {
        let timeout;
        return function () {
            const context = this;
            const args = arguments;
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(context, args), wait);
        };
    }
    // Utility: Throttle function
    function _throttle(func, wait) {
        let timeout = null;
        let previous = 0;
        return function () {
            const context = this;
            const args = arguments;
            const now = Date.now();
            if (!previous)
                previous = now;
            const remaining = wait - (now - previous);
            if (remaining <= 0 || remaining > wait) {
                if (timeout) {
                    clearTimeout(timeout);
                    timeout = null;
                }
                previous = now;
                func.apply(context, args);
            }
            else if (!timeout) {
                timeout = setTimeout(() => {
                    previous = Date.now();
                    timeout = null;
                    func.apply(context, args);
                }, remaining);
            }
        };
    }
    // Public API
    const publicAPI = {
        init: _init,
        loadMemoryQuestions: _loadMemoryQuestions,
        startMemoryPractice: startMemoryPractice,
        closeMemoryPracticeModal: closeMemoryPracticeModal,
        startMemoryExam: startMemoryExam,
        deleteBulkMemoryQuestions: deleteBulkMemoryQuestions,
        // Helper cho tab Đề cương
        addQuestion: function (questionId, examId) {
            // Thêm câu hỏi vào danh sách ghi nhớ
            fetch(`${_config.apiBase}/${_config.productId}/add`, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({
                    questionId: questionId,
                    examId: examId || null,
                    source: "manual",
                }),
            })
                .then((response) => response.json())
                .then((data) => {
                if (data.success) {
                    _showSuccessToast("Đã thêm câu hỏi vào danh sách ghi nhớ");
                    // Cập nhật icon nếu đang ở tab đề cương
                    const addBtn = document.querySelector(`[data-question-id="${questionId}"] .add-to-memory-btn`);
                    if (addBtn) {
                        addBtn.innerHTML = '<i class="fas fa-bookmark"></i>';
                        addBtn.classList.add("added");
                    }
                }
                else {
                    _showErrorToast(data.message || "Không thể thêm câu hỏi");
                }
            })
                .catch((error) => {
                console.error("Error adding question to memory:", error);
                _showErrorToast("Đã có lỗi xảy ra");
            });
        },
        // Filter helpers
        setSourceFilter: function (source) {
            _state.sourceFilter = source;
            _filterQuestions();
        },
        setExamFilter: function (examId) {
            _state.examFilter = examId;
            _filterQuestions();
        },
        clearSearch: function () {
            if (_dom.searchInput) {
                _dom.searchInput.value = "";
                _state.searchTerm = "";
                _filterQuestions();
            }
        },
    };
    // Expose public API to window
    window.MemoryExam = publicAPI;
    // Expose addToMemory helper globally for use from other modules
    window.addToMemory = function (questionId, examId) {
        publicAPI.addQuestion(questionId, examId);
    };
    // Auto-initialize on DOMContentLoaded if we're on memory tab
    document.addEventListener("DOMContentLoaded", function () {
        var _a;
        const currentTab = (_a = document
            .querySelector('meta[name="current-tab"]')) === null || _a === void 0 ? void 0 : _a.getAttribute("content");
        if (currentTab === "memory") {
            publicAPI.init();
        }
    });
})(window);
//# sourceMappingURL=memory-exam.js.map