import { Schema, model, Types } from "mongoose";

interface IUserProgress {
  user_id: Types.ObjectId;
  product_id: Types.ObjectId;
  best_exam_attempts: {
    exam_id: Types.ObjectId;
    score: number;
    correct_answers: number;
    total_questions: number;
    completed_at: Date;
  }[];
  total_correct_answers: number;
  total_questions: number;
  progress_percentage: number;
  last_updated: Date;
}

const userProgressSchema = new Schema<IUserProgress>({
  user_id: {
    type: Schema.Types.ObjectId,
    ref: "User",
    required: true,
  },
  product_id: {
    type: Schema.Types.ObjectId,
    ref: "Product",
    required: true,
  },
  best_exam_attempts: [
    {
      exam_id: {
        type: Schema.Types.ObjectId,
        ref: "Exam",
      },
      score: {
        type: Number,
        default: 0,
      },
      correct_answers: {
        type: Number,
        default: 0,
      },
      total_questions: {
        type: Number,
        default: 0,
      },
      completed_at: {
        type: Date,
        default: Date.now,
      },
    },
  ],
  total_correct_answers: {
    type: Number,
    default: 0,
  },
  total_questions: {
    type: Number,
    default: 0,
  },
  progress_percentage: {
    type: Number,
    default: 0,
  },
  last_updated: {
    type: Date,
    default: Date.now,
  },
});

// Index cho truy vấn nhanh
userProgressSchema.index({ user_id: 1, product_id: 1 }, { unique: true });

const UserProgress = model<IUserProgress>("UserProgress", userProgressSchema);

export default UserProgress;
export { IUserProgress };
