{"version": 3, "file": "authMiddleware.js", "sourceRoot": "", "sources": ["../../src/middlewares/authMiddleware.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,gEAA+B;AAC/B,0DAA6C;AAC7C,gEAAwC;AACxC,2EAA6D;AAC7D,uDAA8D;AAiB9D,0BAA0B;AACnB,MAAM,iBAAiB,GAAG,CAC/B,GAAY,EACZ,GAAa,EACb,IAAkB,EAClB,EAAE;;IACF,IAAI,CAAC;QACH,mCAAmC;QACnC,MAAM,KAAK,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,KAAI,MAAA,GAAG,CAAC,OAAO,CAAC,aAAa,0CAAE,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAA,CAAC;QAE1E,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,+CAA+C;YAC/C,IAAI,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC;gBACtB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,0BAA0B,EAAE,CAAC,CAAC;YACvE,CAAC;iBAAM,CAAC;gBACN,OAAO,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAChC,CAAC;QACH,CAAC;QAED,kCAAkC;QAClC,MAAM,OAAO,GAAG,MAAM,cAAc,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QAE7D,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,eAAe;YACf,0EAA0E;YAC1E,KAAK;YAEL,uCAAuC;YACvC,GAAG,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YAEvB,2DAA2D;YAC3D,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC;gBACvB,MAAM,aAAa,GAAG;;;;;;;;;;;SAWrB,CAAC;gBAEF,OAAO,GAAG,CAAC,IAAI,CAAC;;;;;;;;;;;gBAWR,aAAa;;;SAGpB,CAAC,CAAC;YACL,CAAC;YAED,IAAI,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC;gBACtB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBAC1B,OAAO,EAAE,+CAA+C;oBACxD,KAAK,EAAE,iBAAiB;iBACzB,CAAC,CAAC;YACL,CAAC;iBAAM,CAAC;gBACN,OAAO,GAAG,CAAC,QAAQ,CAAC,2CAA2C,CAAC,CAAC;YACnE,CAAC;QACH,CAAC;QAED,iBAAiB;QACjB,IAAI,OAAmB,CAAC;QACxB,IAAI,CAAC;YACH,OAAO,GAAG,sBAAG,CAAC,MAAM,CAClB,KAAK,EACL,OAAO,CAAC,GAAG,CAAC,UAAoB,CACnB,CAAC;QAClB,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,yEAAyE;YACzE,qCAAqC;YACrC,IAAI,KAAK,CAAC,IAAI,KAAK,mBAAmB,IAAI,OAAO,EAAE,CAAC;gBAClD,gBAAgB;gBAChB,MAAM,IAAI,GAAG,MAAM,cAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;gBACjD,IAAI,CAAC,IAAI,EAAE,CAAC;oBACV,MAAM,KAAK,CAAC,CAAC,8CAA8C;gBAC7D,CAAC;gBAED,oBAAoB;gBACpB,MAAM,QAAQ,GAAG,cAAc,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;gBAEpD,iCAAiC;gBACjC,MAAM,iBAAO,CAAC,SAAS,CAAC,EAAE,GAAG,EAAE,OAAO,CAAC,GAAG,EAAE,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAC;gBAEnE,gCAAgC;gBAChC,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,QAAQ,EAAE,uBAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC;gBAE1D,+BAA+B;gBAC/B,OAAO,GAAG,sBAAG,CAAC,MAAM,CAClB,QAAQ,EACR,OAAO,CAAC,GAAG,CAAC,UAAoB,CACnB,CAAC;gBAEhB,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;YAC/C,CAAC;iBAAM,CAAC;gBACN,MAAM,KAAK,CAAC,CAAC,qCAAqC;YACpD,CAAC;QACH,CAAC;QAED,uCAAuC;QACvC,MAAM,IAAI,GAAG,MAAM,cAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAC7C,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,IAAI,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC;gBACtB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,4BAA4B,EAAE,CAAC,CAAC;YACzE,CAAC;iBAAM,CAAC;gBACN,OAAO,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAChC,CAAC;QACH,CAAC;QAED,4CAA4C;QAC5C,IAAI,OAAO,CAAC,MAAM,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,CAAC;YACtD,OAAO,CAAC,GAAG,CACT,qCAAqC,IAAI,CAAC,GAAG,SAAS,OAAO,CAAC,MAAM,EAAE,CACvE,CAAC;YAEF,IAAI,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC;gBACtB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBAC1B,OAAO,EAAE,+BAA+B;oBACxC,KAAK,EAAE,iBAAiB;iBACzB,CAAC,CAAC;YACL,CAAC;iBAAM,CAAC;gBACN,OAAO,GAAG,CAAC,QAAQ,CAAC,2CAA2C,CAAC,CAAC;YACnE,CAAC;QACH,CAAC;QAED,gDAAgD;QAChD,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;YAChB,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;YAClD,MAAM,eAAe,GAAG,OAAO,CAAC,GAAG,GAAG,WAAW,CAAC;YAClD,MAAM,yBAAyB,GAAG,IAAI,CAAC,KAAK,CAC1C,wBAAU,CAAC,GAAG,CAAC,iBAAiB,GAAG,IAAI,CACxC,CAAC;YAEF,IAAI,eAAe,GAAG,yBAAyB,EAAE,CAAC;gBAChD,gBAAgB;gBAChB,MAAM,QAAQ,GAAG,cAAc,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;gBAEpD,iCAAiC;gBACjC,MAAM,iBAAO,CAAC,SAAS,CAAC,EAAE,GAAG,EAAE,OAAO,CAAC,GAAG,EAAE,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAC;gBAEnE,gCAAgC;gBAChC,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,QAAQ,EAAE,uBAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC;gBAE1D,OAAO,CAAC,GAAG,CAAC,iDAAiD,CAAC,CAAC;YACjE,CAAC;QACH,CAAC;QAED,yCAAyC;QACzC,cAAc,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE;YACvD,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,GAAG,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,4CAA4C;QAC5C,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;QAChB,GAAG,CAAC,OAAO,GAAG,OAAO,CAAC;QACtB,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACpB,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;QAElD,IAAI,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC;YACtB,IAAI,KAAK,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;gBACvC,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,qBAAqB,EAAE,CAAC,CAAC;YAClE,CAAC;YACD,IAAI,KAAK,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;gBACvC,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,mBAAmB,EAAE,CAAC,CAAC;YAChE,CAAC;YACD,OAAO,GAAG;iBACP,MAAM,CAAC,GAAG,CAAC;iBACX,IAAI,CAAC,EAAE,OAAO,EAAE,eAAe,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC9D,CAAC;aAAM,CAAC;YACN,qDAAqD;YACrD,OAAO,GAAG,CAAC,QAAQ,CAAC,2CAA2C,CAAC,CAAC;QACnE,CAAC;IACH,CAAC;AACH,CAAC,CAAA,CAAC;AAxLW,QAAA,iBAAiB,qBAwL5B;AAEF,0CAA0C;AACnC,MAAM,iBAAiB,GAAG,CAC/B,GAAY,EACZ,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,IAAI,CAAC;QACH,MAAM,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC;QACtB,MAAM,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC,CAAC,2CAA2C;QAE1E,yDAAyD;QACzD,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,6BAA6B;gBACtC,KAAK,EAAE,cAAc;aACtB,CAAC,CAAC;QACL,CAAC;QAED,MAAM,cAAc,GAAG,MAAM,iBAAO,CAAC,IAAI,CAAC;YACxC,MAAM,EAAE,IAAI,CAAC,GAAG;YAChB,QAAQ,EAAE,IAAI;SACf,CAAC,CAAC;QAEH,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9B,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EACL,gGAAgG;gBAClG,iBAAiB,EAAE,IAAI;gBACvB,cAAc,EAAE,cAAc,CAAC,MAAM;aACtC,CAAC,CAAC;QACL,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACpB,OAAO,GAAG;aACP,MAAM,CAAC,GAAG,CAAC;aACX,IAAI,CAAC,EAAE,OAAO,EAAE,wBAAwB,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;IACvE,CAAC;AACH,CAAC,CAAA,CAAC;AArCW,QAAA,iBAAiB,qBAqC5B;AAEF,gEAAgE;AACzD,MAAM,kBAAkB,GAAG,CAChC,GAAY,EACZ,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,IAAI,CAAC;QACH,sBAAsB;QACtB,MAAM,KAAK,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC;QAC9B,MAAM,QAAQ,GAAI,GAAG,CAAC,KAAK,CAAC,QAAmB,IAAI,SAAS,CAAC;QAE7D,+DAA+D;QAE/D,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,eAAe;YACf,2EAA2E;YAC3E,KAAK;YACL,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;QAC/B,CAAC;QAED,mBAAmB;QACnB,MAAM,OAAO,GAAG,MAAM,cAAc,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QAE7D,wFAAwF;QACxF,IAAI,IAAI,CAAC;QAET,IAAI,CAAC;YACH,iBAAiB;YACjB,MAAM,OAAO,GAAG,sBAAG,CAAC,MAAM,CACxB,KAAK,EACL,OAAO,CAAC,GAAG,CAAC,UAAoB,CACnB,CAAC;YAEhB,8BAA8B;YAC9B,IAAI,GAAG,MAAM,cAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YAEvC,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,eAAe;gBACf,4EAA4E;gBAC5E,KAAK;YACP,CAAC;QACH,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,eAAe;YACf,qFAAqF;YACrF,KAAK;QACP,CAAC;QAED,wCAAwC;QACxC,IAAI,IAAI,EAAE,CAAC;YACT,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;YAChB,eAAe;YACf,+FAA+F;YAC/F,KAAK;QACP,CAAC;aAAM,CAAC;YACN,eAAe;YACf,sFAAsF;YACtF,KAAK;QACP,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACpB,MAAM,QAAQ,GAAI,GAAG,CAAC,KAAK,CAAC,QAAmB,IAAI,SAAS,CAAC;QAC7D,8EAA8E;QAE9E,yDAAyD;QACzD,IAAI,EAAE,CAAC;IACT,CAAC;AACH,CAAC,CAAA,CAAC;AAlEW,QAAA,kBAAkB,sBAkE7B;AAEF,2DAA2D;AAC3D,SAAS,YAAY,CAAC,GAAY;IAChC,mEAAmE;IACnE,MAAM,YAAY,GAAG,GAAG,CAAC,OAAO,CAAC,MAAM,IAAI,EAAE,CAAC;IAC9C,MAAM,IAAI,GAAG,GAAG,CAAC,IAAI,IAAI,EAAE,CAAC;IAE5B,OAAO,CACL,YAAY,CAAC,QAAQ,CAAC,kBAAkB,CAAC;QACzC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;QACxB,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CACjC,CAAC;AACJ,CAAC"}