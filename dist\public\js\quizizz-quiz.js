// Quizizz - Hiển thị câu hỏi và kết quả
// Hiển thị câu hỏi hiện tại
function showQuestion(index) {
    // Dừng âm thanh nếu đang phát khi chuyển câu hỏi
    if (window.stopAllSounds) {
        window.stopAllSounds();
    }
    // Hủy bất kỳ timeout chuyển câu hỏi nào đang chạy
    if (nextQuestionTimeout) {
        clearTimeout(nextQuestionTimeout);
    }
    // Hủy interval đếm ngược nếu có
    if (window.currentCountdownInterval) {
        clearInterval(window.currentCountdownInterval);
        window.currentCountdownInterval = null;
    }
    // Save to localStorage khi hiển thị câu hỏi mới (trừ khi đang restore)
    if (!window.isRestoringFromLocalStorage &&
        !retryMode &&
        typeof saveQuizizzDataToStorage === "function") {
        saveQuizizzDataToStorage();
    }
    // Bỏ vô hiệu hóa nút nộp bài khi hiển thị câu hỏi mới
    const submitButton = document.getElementById("submitExamButton");
    if (submitButton) {
        submitButton.disabled = false;
        submitButton.classList.remove("opacity-50", "cursor-not-allowed");
        submitButton.innerHTML = "Nộp bài"; // Đặt lại nội dung ban đầu
    }
    // Xác định câu hỏi cần hiển thị (câu hiện tại hoặc câu đang làm lại)
    let question;
    if (retryMode && questionToRetry) {
        question = questionToRetry;
    }
    else {
        question = window.examData.questions[index];
    }
    const questionTemplate = document.getElementById("questionTemplate");
    const questionContainer = document.getElementById("questionContainer");
    // Tạo bản sao từ template
    const questionEl = questionTemplate.content.cloneNode(true);
    // Cập nhật nội dung
    let questionTitle;
    if (retryMode) {
        questionTitle = `Làm lại câu hỏi: ${question.text}`;
    }
    else {
        questionTitle = `Câu ${index + 1}: ${question.text}`;
    }
    questionEl.querySelector("#questionText").textContent = questionTitle;
    // Kiểm tra và hiển thị hình ảnh nếu có
    const questionImage = questionEl.querySelector("#questionImage");
    if (question.image) {
        questionImage.classList.remove("hidden");
        questionImage.querySelector("img").src = question.image;
    }
    // Thêm các lựa chọn
    const optionsContainer = questionEl.querySelector("#optionsContainer");
    optionsContainer.innerHTML = "";
    // Xác định mảng options hoặc answers để hiển thị
    let optionsArray = [];
    if (question.options &&
        Array.isArray(question.options) &&
        question.options.length > 0) {
        optionsArray = question.options;
    }
    else if (question.answers &&
        Array.isArray(question.answers) &&
        question.answers.length > 0) {
        optionsArray = question.answers;
    }
    // Xác định xem có đang dùng giao diện tối không
    const isDarkMode = document
        .getElementById("quizizzContainer")
        .classList.contains("theme-dark");
    // Kiểm tra xem có lựa chọn để hiển thị không
    if (optionsArray.length > 0) {
        // Xử lý cho mọi loại câu hỏi có options/answers
        optionsArray.forEach((option, optIndex) => {
            const optionLetter = String.fromCharCode(65 + optIndex); // A, B, C, D
            const optionNumber = optIndex + 1; // 1, 2, 3, 4
            // Tạo button cho lựa chọn
            const optionButton = document.createElement("button");
            optionButton.className = "option-card w-full font-medium";
            // Xác định text để hiển thị
            const optionText = typeof option === "object" ? option.text || "" : option;
            // Loại bỏ tiền tố "A.", "B.", "C.", "D." nếu có
            let cleanOptionText = optionText;
            // Sử dụng regex để tìm và loại bỏ tiền tố theo nhiều định dạng:
            // 1. Chữ cái + dấu chấm: A. B. C. D.
            // 2. Chữ cái + dấu ngoặc đóng: A) B) C) D)
            // 3. Số + dấu chấm: 1. 2. 3. 4.
            // 4. Số + dấu ngoặc đóng: 1) 2) 3) 4)
            // Bắt đầu bằng whitespace (nếu có), theo sau là chữ cái hoặc số, rồi đến dấu chấm hoặc dấu đóng ngoặc
            const prefixRegex = /^\s*(?:[A-D]|[1-9])[\.\)][\s\u00A0]*/i;
            if (prefixRegex.test(cleanOptionText)) {
                cleanOptionText = cleanOptionText.replace(prefixRegex, "");
            }
            // Thêm các lớp màu sắc dựa trên vị trí
            const colorClasses = optIndex === 0
                ? ["option-red"]
                : optIndex === 1
                    ? ["option-blue"]
                    : optIndex === 2
                        ? ["option-green"]
                        : ["option-yellow"];
            colorClasses.forEach((className) => {
                optionButton.classList.add(className);
            });
            // Tạo nội dung cho từng lựa chọn
            optionButton.innerHTML = `
        <div class="flex items-center">
          <div class="option-letter">
            ${optionLetter}
          </div>
          <div>${cleanOptionText}</div>
        </div>
      `;
            // Thêm số thứ tự ở góc trên phải nếu đang dùng giao diện tối
            // if (isDarkMode) {
            //   const numberElement = document.createElement("div");
            //   numberElement.className = "option-number";
            //   numberElement.textContent = optionNumber;
            //   optionButton.appendChild(numberElement);
            // }
            // Xử lý sự kiện khi người dùng chọn
            optionButton.addEventListener("click", () => {
                // Kiểm tra xem option có thuộc tính isCorrect hay không
                const isCorrect = typeof option === "object" ? !!option.isCorrect : false;
                selectAnswer(optIndex, isCorrect);
            });
            // Thêm vào container
            optionsContainer.appendChild(optionButton);
        });
    }
    else {
        console.error("Không có options/answers hoặc cấu trúc không hợp lệ:", question);
        // Hiển thị thông báo nếu không có options
        const noOptionsMessage = document.createElement("div");
        noOptionsMessage.className = "text-center p-4 text-red-500";
        noOptionsMessage.textContent = "Không có các lựa chọn cho câu hỏi này!";
        optionsContainer.appendChild(noOptionsMessage);
    }
    // Cập nhật counter và progress với animation
    if (!retryMode) {
        document.getElementById("questionCounter").textContent = `Câu hỏi ${index + 1}/${window.examData.questions.length}`;
        // *** ANIMATE PROGRESS BAR ***
        const progressBar = document.getElementById("progressBar");
        const newWidth = ((index + 1) / window.examData.questions.length) * 100;
        // Thêm animation class nếu chưa có
        if (!progressBar.classList.contains("quiz-transition")) {
            progressBar.classList.add("quiz-transition");
        }
        progressBar.style.width = `${newWidth}%`;
        // Thêm pulse effect khi progress thay đổi
        progressBar.classList.add("quiz-pulse");
        setTimeout(() => {
            progressBar.classList.remove("quiz-pulse");
        }, 600);
    }
    else {
        document.getElementById("questionCounter").textContent = `Làm lại câu sai`;
        console.log(`🔄 RETRY MODE: Hiển thị đủ 4 đáp án cho người dùng tự chọn`);
    }
    // *** THÊM ANIMATION CHO CHUYỂN CÂU HỎI ***
    // Xóa nội dung cũ và thêm câu hỏi mới
    questionContainer.innerHTML = "";
    questionContainer.appendChild(questionEl);
    // Áp dụng hiệu ứng zoom-in cho câu hỏi và các lựa chọn
    questionContainer.classList.remove("quiz-transition"); // Xóa lớp transition cũ
    questionContainer.classList.add("quiz-zoom-in"); // Thêm lớp zoom-in
    setTimeout(() => {
        questionContainer.classList.remove("quiz-zoom-in"); // Xóa lớp sau khi animation hoàn tất
    }, 500); // Thời gian animation
}
// *** THÊM HELPER FUNCTIONS CHO ANSWERED QUESTIONS ***
// Function to check if a question has been answered
function isQuestionAnswered(questionIndex) {
    return (window.answeredQuestionsMap &&
        window.answeredQuestionsMap.has(questionIndex));
}
// Function to get answer for a question if it exists
function getQuestionAnswer(questionIndex) {
    if (isQuestionAnswered(questionIndex)) {
        return window.answeredQuestionsMap.get(questionIndex);
    }
    return null;
}
// Function to navigate to any question (answered or not)
function navigateToQuestion(targetIndex) {
    if (targetIndex >= 0 && targetIndex < window.examData.questions.length) {
        window.currentQuestionIndex = targetIndex;
        console.log(`🎯 Navigating to question ${targetIndex + 1}`);
        showQuestion(targetIndex);
        // Save state when navigating
        if (typeof saveQuizizzDataToStorage === "function") {
            saveQuizizzDataToStorage();
        }
    }
}
// Xử lý khi người dùng chọn đáp án
function selectAnswer(optionIndex, isCorrect) {
    // *** KIỂM TRA NẾU QUESTION ĐÃ ĐƯỢC ANSWER ***
    // Nếu câu hỏi đã được trả lời (đang restore), không cho phép click lại
    // NHƯNG CHO PHÉP CLICK LẠI KHI ĐANG Ở RETRY MODE
    if (!retryMode && isQuestionAnswered(currentQuestionIndex)) {
        console.log(`⚠️ Question ${currentQuestionIndex + 1} đã được trả lời, bỏ qua click`);
        return;
    }
    if (retryMode) {
        console.log(`🔄 RETRY MODE: Cho phép người dùng chọn đáp án option ${optionIndex + 1}`);
    }
    // Vô hiệu hóa tất cả các nút lựa chọn
    const allOptions = document.querySelectorAll("#optionsContainer .option-card");
    // Tìm đáp án đúng
    let correctOptionIndex = -1;
    try {
        // Lấy câu hỏi hiện tại
        let currentQuestion;
        if (retryMode && questionToRetry) {
            currentQuestion = questionToRetry;
        }
        else {
            currentQuestion = window.examData.questions[currentQuestionIndex];
        }
        // Xác định mảng options hoặc answers để tìm đáp án đúng
        let optionsArray = [];
        if (currentQuestion.options && Array.isArray(currentQuestion.options)) {
            optionsArray = currentQuestion.options;
        }
        else if (currentQuestion.answers &&
            Array.isArray(currentQuestion.answers)) {
            optionsArray = currentQuestion.answers;
        }
        if (optionsArray.length > 0) {
            // Tìm đáp án đúng
            correctOptionIndex = optionsArray.findIndex((opt) => typeof opt === "object" && opt.isCorrect);
        }
    }
    catch (error) {
        console.error("Lỗi khi tìm đáp án đúng:", error);
    }
    // Kiểm tra xem có đang dùng giao diện tối không
    const isDarkMode = document
        .getElementById("quizizzContainer")
        .classList.contains("theme-dark");
    if (isCorrect) {
        // *** ANIMATION CHO ĐÁP ÁN ĐÚNG ***
        // Đầu tiên là hiệu ứng gentle scale cho button được chọn
        const selectedButton = allOptions[optionIndex];
        selectedButton.classList.add("quiz-gentle-scale");
        // Nếu đúng: Chỉ hiển thị đáp án đúng, ẩn hết các đáp án khác
        allOptions.forEach((button, index) => {
            if (index !== optionIndex) {
                // Animation fade out cho các options khác
                button.classList.add("quiz-fade-out");
                setTimeout(() => {
                    button.style.visibility = "hidden";
                    button.style.opacity = "0";
                    button.style.display = "block";
                }, 360);
            }
            else {
                // Highlight đáp án người dùng chọn (đúng)
                button.disabled = true;
                button.classList.remove("opacity-50");
                // Thêm animation cho đáp án đúng
                setTimeout(() => {
                    button.classList.add("selected", "answer-correct", "quiz-correct-answer");
                    // Thêm icon dấu tích xanh với animation
                    const checkIcon = document.createElement("span");
                    checkIcon.className =
                        "absolute top-2 right-2 text-green-600 quiz-success-icon";
                    checkIcon.innerHTML = '<i class="fas fa-check-circle"></i>';
                    button.style.position = "relative";
                    button.appendChild(checkIcon);
                    // Play success sound if available
                    if (typeof playSuccessSound === "function") {
                        playSuccessSound();
                    }
                }, 240);
            }
        });
        // Nếu đang ở chế độ làm lại, cần cập nhật đáp án mới
        if (retryMode) {
            // Tìm vị trí câu hỏi trong mảng userAnswers
            const answerIndex = userAnswers.findIndex((answer) => answer.questionIndex === originalQuestionIndex);
            if (answerIndex !== -1) {
                // Cập nhật kết quả mới
                userAnswers[answerIndex].isCorrect = true;
                userAnswers[answerIndex].selectedIndex = optionIndex;
                // Tăng điểm
                score++;
                // Xóa khỏi danh sách câu sai
                const incorrectIndex = incorrectQuestions.findIndex((q) => q.questionIndex === originalQuestionIndex);
                if (incorrectIndex !== -1) {
                    incorrectQuestions.splice(incorrectIndex, 1);
                }
                // *** SYNC WITH WINDOW OBJECT FOR RETRY MODE ***
                window.score = score;
                window.userAnswers = userAnswers;
                window.incorrectQuestions = incorrectQuestions;
                if (typeof window.exposeVariablesToWindow === "function") {
                    window.exposeVariablesToWindow();
                }
                console.log(`✅ Updated retry answer: new score=${score}, remaining incorrect=${incorrectQuestions.length}`);
            }
        }
        else {
            score++;
        }
    }
    else {
        // *** ANIMATION CHO ĐÁP ÁN SAI ***
        // Đầu tiên là hiệu ứng gentle scale cho button được chọn (sai)
        const selectedButton = allOptions[optionIndex];
        // selectedButton.classList.add("quiz-gentle-shake-scale"); // Loại bỏ hiệu ứng rung
        // Nếu sai: Chỉ hiển thị đáp án đã chọn (sai) và đáp án đúng
        allOptions.forEach((button, index) => {
            if (index !== optionIndex && index !== correctOptionIndex) {
                // Animation fade out cho các options khác
                button.classList.add("quiz-fade-out");
                setTimeout(() => {
                    button.style.visibility = "hidden";
                    button.style.opacity = "0";
                    button.style.display = "block";
                    // }, 360);
                }, 50);
            }
            else {
                // Bỏ opacity chung
                button.classList.remove("opacity-50");
                if (index === optionIndex) {
                    // Highlight đáp án người dùng chọn (sai)
                    button.disabled = true;
                    // Thêm animation cho đáp án sai
                    setTimeout(() => {
                        button.classList.add("border-red-500", "bg-red-50", "answer-wrong", "quiz-wrong-answer");
                        // Thêm icon dấu X đỏ với animation
                        const xIcon = document.createElement("span");
                        xIcon.className =
                            "absolute top-2 right-2 text-red-600 quiz-error-icon";
                        xIcon.innerHTML = '<i class="fas fa-times-circle"></i>';
                        button.style.position = "relative";
                        button.appendChild(xIcon);
                        // Play error sound if available
                        if (typeof playErrorSound === "function") {
                            playErrorSound();
                        }
                    }, 50); // Delay giảm xuống để hiển thị ngay lập tức
                }
                else if (index === correctOptionIndex) {
                    // Highlight đáp án đúng với delay animation
                    button.disabled = true;
                    setTimeout(() => {
                        button.classList.add("selected", "answer-correct", "quiz-correct-answer");
                        // Thêm icon dấu tích xanh với animation
                        const correctIcon = document.createElement("span");
                        correctIcon.className =
                            "absolute top-2 right-2 text-green-600 quiz-success-icon";
                        correctIcon.innerHTML = '<i class="fas fa-check-circle"></i>';
                        button.style.position = "relative";
                        button.appendChild(correctIcon);
                    }, 50); // Delay giảm xuống để hiển thị cùng lúc với đáp án sai
                }
            }
        });
        // Lưu câu hỏi sai để có thể làm lại sau
        if (!retryMode) {
            incorrectQuestions.push({
                questionIndex: currentQuestionIndex,
                selectedIndex: optionIndex,
                question: window.examData.questions[currentQuestionIndex],
            });
        }
    }
    // Lưu câu trả lời của người dùng (nếu không phải đang làm lại)
    if (!retryMode) {
        userAnswers.push({
            questionIndex: currentQuestionIndex,
            selectedIndex: optionIndex,
            isCorrect: isCorrect,
        });
        // *** UPDATE ANSWERED QUESTIONS MAP ***
        if (!window.answeredQuestionsMap) {
            window.answeredQuestionsMap = new Map();
        }
        window.answeredQuestionsMap.set(currentQuestionIndex, {
            selectedIndex: optionIndex,
            isCorrect: isCorrect,
        });
        // Tăng biến đếm số câu hỏi đã làm
        questionsProcessed++;
        // *** SYNC WITH WINDOW OBJECT ***
        window.score = score;
        window.userAnswers = userAnswers;
        window.questionsProcessed = questionsProcessed;
        if (typeof window.exposeVariablesToWindow === "function") {
            window.exposeVariablesToWindow();
        }
        // Lưu vào localStorage ngay khi có answer change
        if (typeof saveQuizizzDataToStorage === "function") {
            saveQuizizzDataToStorage();
        }
    }
    // Vô hiệu hóa nút nộp bài trong khi đợi chuyển câu tiếp theo
    const submitButton = document.getElementById("submitExamButton");
    if (submitButton) {
        submitButton.disabled = true;
        submitButton.classList.add("opacity-50", "cursor-not-allowed");
        // Hiển thị đếm ngược thời gian chuyển câu trong nút nộp bài
        const originalText = submitButton.innerHTML;
        const countdownDuration = transitionTime / 1000;
        let remainingTime = countdownDuration;
        // Cập nhật nội dung nút nộp bài để hiển thị thời gian đếm ngược
        const updateCountdown = () => {
            if (remainingTime <= 0) {
                // Hết thời gian, khôi phục nội dung nút
                clearInterval(countdownInterval);
                submitButton.innerHTML = originalText;
                return;
            }
            // submitButton.innerHTML = `Đang chuyển câu... (${remainingTime}s)`;
            remainingTime -= 1;
        };
        // Gọi ngay lập tức và sau đó mỗi 1 giây
        updateCountdown();
        const countdownInterval = setInterval(updateCountdown, 1000);
        // Lưu interval để xóa khi cần thiết
        window.currentCountdownInterval = countdownInterval;
    }
    // Tự động chuyển câu hỏi tiếp theo sau khoảng thời gian được chọn
    const transitionDelay = retryMode ? 1500 : transitionTime; // Retry mode nhanh hơn
    nextQuestionTimeout = setTimeout(handleNextQuestion, transitionDelay);
    // Phát âm thanh phù hợp
    if (window.playSound) {
        window.playSound(isCorrect);
    }
}
// Xử lý chuyển câu hỏi tiếp theo
function handleNextQuestion() {
    // Dừng âm thanh trước khi chuyển câu
    if (window.stopAllSounds) {
        window.stopAllSounds();
    }
    // Nếu đang ở chế độ làm lại, luôn chuyển về bài thi chính
    if (retryMode) {
        retryMode = false;
        questionToRetry = null;
        justRetried = true; // Đánh dấu vừa làm lại để không hiện thông báo làm lại ngay
        if (currentQuestionIndex >= window.examData.questions.length - 1) {
            showResult();
        }
        else {
            currentQuestionIndex++;
            // *** SYNC WITH WINDOW OBJECT ***
            window.currentQuestionIndex = currentQuestionIndex;
            if (typeof window.exposeVariablesToWindow === "function") {
                window.exposeVariablesToWindow();
            }
            showQuestion(currentQuestionIndex);
        }
        return;
    }
    // Xử lý chế độ làm lại câu sai sau mỗi 5 câu
    if (!retryMode &&
        !justRetried && // Không hiện thông báo nếu vừa làm lại xong
        // Kiểm tra sau câu 5, 10, 15, 20...
        (currentQuestionIndex + 1) % 5 === 0 &&
        currentQuestionIndex + 1 > 0 &&
        incorrectQuestions.length > 0) {
        // Chọn câu sai đầu tiên để làm lại
        const retryQuestion = incorrectQuestions[0]; // Không xóa ngay, chỉ tham chiếu
        if (retryQuestion) {
            // Hiển thị thông báo hỏi người dùng
            showRetryConfirmation(() => {
                // Người dùng chọn làm lại
                incorrectQuestions.shift(); // Chỉ xóa khi người dùng chọn làm lại
                // Chuyển sang chế độ làm lại
                retryMode = true;
                questionToRetry = retryQuestion.question;
                originalQuestionIndex = retryQuestion.questionIndex;
                // Hiển thị câu hỏi làm lại
                showQuestion(currentQuestionIndex);
            }, () => {
                // Người dùng chọn bỏ qua
                justRetried = true; // Đánh dấu đã xử lý cơ hội làm lại
                if (currentQuestionIndex >= window.examData.questions.length - 1) {
                    // Đã hết câu hỏi, hiển thị kết quả
                    showResult();
                }
                else {
                    // Chuyển đến câu hỏi tiếp theo
                    currentQuestionIndex++;
                    // *** SYNC WITH WINDOW OBJECT ***
                    window.currentQuestionIndex = currentQuestionIndex;
                    if (typeof window.exposeVariablesToWindow === "function") {
                        window.exposeVariablesToWindow();
                    }
                    showQuestion(currentQuestionIndex);
                }
            });
            return;
        }
    }
    // Reset cờ justRetried sau khi đã xử lý
    justRetried = false;
    if (currentQuestionIndex >= window.examData.questions.length - 1 &&
        !retryMode) {
        // Đã hết câu hỏi, hiển thị kết quả
        showResult();
    }
    else if (!retryMode) {
        // Chuyển đến câu hỏi tiếp theo
        currentQuestionIndex++;
        // *** SYNC WITH WINDOW OBJECT ***
        window.currentQuestionIndex = currentQuestionIndex;
        if (typeof window.exposeVariablesToWindow === "function") {
            window.exposeVariablesToWindow();
        }
        showQuestion(currentQuestionIndex);
        // Lưu localStorage khi chuyển câu hỏi
        if (typeof saveQuizizzDataToStorage === "function") {
            saveQuizizzDataToStorage();
        }
    }
}
// Hiển thị thông báo làm lại câu sai với tùy chọn đồng ý hoặc từ chối
function showRetryConfirmation(onAccept, onDecline) {
    // Tạo phần tử thông báo
    const notification = document.createElement("div");
    notification.className =
        "fixed inset-0 flex items-center justify-center z-50";
    notification.innerHTML = `
    <div class="fixed inset-0 bg-black opacity-50"></div>
    <div class="bg-white rounded-lg shadow-xl p-4 sm:p-6 max-w-md w-full mx-2 sm:mx-auto z-10 relative">
      <div class="mb-4 text-center">
        <div class="mx-auto flex items-center justify-center h-12 sm:h-16 w-12 sm:w-16 rounded-full bg-blue-100 mb-4">
          <i class="fas fa-redo text-blue-600 text-xl sm:text-2xl"></i>
        </div>
        <h3 class="text-base sm:text-lg font-medium text-gray-900">
          Cơ hội làm lại câu sai!
        </h3>
        <p class="text-sm sm:text-base text-gray-500 mt-2">
          Bạn có muốn làm lại 1 câu hỏi đã trả lời sai không?
        </p>
      </div>
      <div class="flex justify-center space-x-4">
        <button id="declineRetry" class="bg-gray-200 hover:bg-gray-300 text-gray-800 py-2 px-4 rounded-lg transition-colors text-sm sm:text-base">
          Bỏ qua
        </button>
        <button id="acceptRetry" class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors text-sm sm:text-base">
          Làm lại
        </button>
      </div>
    </div>
  `;
    document.body.appendChild(notification);
    // Xử lý sự kiện nút xác nhận
    document.getElementById("acceptRetry").addEventListener("click", () => {
        document.body.removeChild(notification);
        if (typeof onAccept === "function") {
            onAccept();
        }
    });
    // Xử lý sự kiện nút từ chối
    document.getElementById("declineRetry").addEventListener("click", () => {
        document.body.removeChild(notification);
        if (typeof onDecline === "function") {
            onDecline();
        }
    });
}
// Hiển thị kết quả cuối cùng
function showResult() {
    // Dừng âm thanh khi hiển thị kết quả
    if (window.stopAllSounds) {
        window.stopAllSounds();
    }
    // Đánh dấu là đang nộp bài để không hiển thị cảnh báo
    isSubmitting = true;
    // Dừng các quá trình kiểm tra bảo mật
    if (window.securityInterval) {
        clearInterval(window.securityInterval);
    }
    // Dừng giám sát DOM nếu có
    if (window.securityObserver) {
        window.securityObserver.disconnect();
    }
    // Dừng bộ đếm thời gian
    if (timerInterval) {
        clearInterval(timerInterval);
    }
    // Ẩn nút nộp bài
    const submitButton = document.getElementById("submitExamButton");
    if (submitButton) {
        submitButton.style.display = "none";
    }
    // Nếu kết quả đã được hiển thị, không cần hiển thị lại
    if (document.querySelector(".result-display") !== null) {
        // console.log("Kết quả đã được hiển thị, không cần hiển thị lại");
        return;
    }
    const template = document.getElementById("resultTemplate");
    const resultEl = template.content.cloneNode(true);
    const questionContainer = document.getElementById("questionContainer");
    // Tính toán thời gian làm bài
    const totalTime = Math.floor((Date.now() - startTime) / 1000);
    const minutes = Math.floor(totalTime / 60);
    const seconds = totalTime % 60;
    // Cập nhật điểm và thống kê
    const totalQuestions = window.examData.questions.length;
    const scoreValue = Math.round((score / totalQuestions) * 10 * 100) / 100;
    resultEl.querySelector("#finalScore").textContent = `${scoreValue}/10`;
    resultEl.querySelector("#totalCorrect").textContent = `${score}/${totalQuestions}`;
    resultEl.querySelector("#totalTime").textContent = `${minutes}:${seconds
        .toString()
        .padStart(2, "0")}`;
    // Lưu kết quả bài thi vào lịch sử
    saveExamHistory(score, totalQuestions, totalTime);
    // Tạo tóm tắt các câu hỏi
    const summaryContainer = resultEl.querySelector("#summaryContainer");
    userAnswers.forEach((answer, index) => {
        const questionDot = document.createElement("div");
        questionDot.className = `h-8 w-8 rounded-full flex items-center justify-center text-xs font-medium ${answer.isCorrect
            ? "bg-green-100 text-green-800 border border-green-300"
            : "bg-red-100 text-red-800 border border-red-300"}`;
        questionDot.textContent = index + 1;
        // Tooltip với chi tiết
        questionDot.title = `Câu ${index + 1}: ${answer.isCorrect ? "Đúng" : "Sai"}`;
        // Click vào số câu hỏi để cuộn đến câu đó trong phần xem lại
        questionDot.style.cursor = "pointer";
        questionDot.addEventListener("click", () => {
            const reviewContainer = resultEl.querySelector("#reviewContainer");
            const questionReview = document.getElementById(`question-review-${index}`);
            if (reviewContainer && questionReview) {
                // Cuộn đến vị trí câu hỏi trong container scroll
                questionReview.scrollIntoView({
                    behavior: "smooth",
                    block: "center",
                    inline: "nearest",
                });
                questionReview.classList.add("bg-purple-50");
                setTimeout(() => {
                    questionReview.classList.remove("bg-purple-50");
                }, 1800);
            }
        });
        summaryContainer.appendChild(questionDot);
    });
    // Phần xem lại luôn hiển thị với khả năng scroll
    const reviewContainer = resultEl.querySelector("#reviewContainer");
    // Tạo nội dung xem lại chi tiết từng câu
    createReviewDetails(reviewContainer);
    // *** ANIMATION CHO HIỂN THỊ KẾT QUẢ ***
    // Fade out câu hỏi hiện tại trước
    if (questionContainer.children.length > 0) {
        const currentContent = questionContainer.children[0];
        currentContent.classList.add("quiz-slide-out-right");
        setTimeout(() => {
            // Hiển thị kết quả với animation
            questionContainer.innerHTML = "";
            // Thêm animation classes cho các elements
            const resultDisplay = resultEl.querySelector(".result-display");
            if (resultDisplay) {
                resultDisplay.classList.add("quiz-fade-in");
            }
            // Animation cho điểm số
            const finalScore = resultEl.querySelector("#finalScore");
            if (finalScore) {
                finalScore.classList.add("quiz-celebration");
            }
            // Stagger animation cho summary dots
            const summaryDots = resultEl.querySelectorAll("#summaryContainer > div");
            summaryDots.forEach((dot, index) => {
                dot.classList.add("quiz-pop-in", `quiz-stagger-${(index % 5) + 1}`);
            });
            questionContainer.appendChild(resultEl);
            // Animate progress bar để hiển thị score
            setTimeout(() => {
                const progressBars = resultEl.querySelectorAll('[style*="width"]');
                progressBars.forEach((bar) => {
                    bar.classList.add("quiz-transition-slow");
                });
            }, 600);
        }, 480); // Wait for slide-out animation
    }
    else {
        // Nếu không có content cũ, hiển thị trực tiếp với animation
        questionContainer.innerHTML = "";
        const resultDisplay = resultEl.querySelector(".result-display");
        if (resultDisplay) {
            resultDisplay.classList.add("quiz-bounce-in");
        }
        questionContainer.appendChild(resultEl);
    }
    // Cập nhật danh sách câu hỏi sai
    updateWrongQuestions();
}
// Tạo nội dung xem lại chi tiết từng câu
function createReviewDetails(reviewContainer) {
    userAnswers.forEach((answer, index) => {
        const question = window.examData.questions[answer.questionIndex];
        const reviewItem = document.createElement("div");
        reviewItem.id = `question-review-${index}`;
        reviewItem.className =
            "border-b border-gray-200 pb-4 mb-4 transition-colors duration-300";
        // Lấy mảng options
        let optionsArray = [];
        if (question.options && Array.isArray(question.options)) {
            optionsArray = question.options;
        }
        else if (question.answers && Array.isArray(question.answers)) {
            optionsArray = question.answers;
        }
        // Tìm đáp án đúng
        const correctOption = optionsArray.find((opt) => typeof opt === "object" && opt.isCorrect);
        const correctText = correctOption
            ? correctOption.text
            : "Không tìm thấy đáp án đúng";
        // Tìm đáp án người dùng đã chọn
        const selectedOption = optionsArray[answer.selectedIndex];
        const selectedText = selectedOption
            ? typeof selectedOption === "object"
                ? selectedOption.text
                : selectedOption
            : "Không xác định";
        reviewItem.innerHTML = `
      <div class="text-left mb-2">
        <h4 class="font-medium text-gray-800">Câu ${index + 1}: ${question.text}</h4>
        ${question.image
            ? `<img src="${question.image}" alt="Hình ảnh câu hỏi" class="max-w-full h-auto rounded my-2">`
            : ""}
      </div>
      <div class="grid grid-cols-1 gap-2 text-left">
        ${optionsArray
            .map((opt, i) => {
            const optText = typeof opt === "object" ? opt.text : opt;
            let className = "p-2 rounded border";
            // Đánh dấu đáp án người dùng đã chọn
            if (i === answer.selectedIndex) {
                className += answer.isCorrect
                    ? " bg-green-100 border-green-500 text-green-800"
                    : " bg-red-100 border-red-500 text-red-800";
            }
            // Đánh dấu đáp án đúng nếu người dùng chọn sai
            else if (!answer.isCorrect &&
                typeof opt === "object" &&
                opt.isCorrect) {
                className += " bg-green-100 border-green-500 text-green-800";
            }
            else {
                className += " border-gray-200 text-gray-600";
            }
            return `<div class="${className}">
            <div class="flex items-center">
              <div class="flex-shrink-0 h-6 w-6 rounded-full bg-purple-100 text-purple-600 flex items-center justify-center mr-3">
                ${String.fromCharCode(65 + i)}
              </div>
              <div>${optText}</div>
            </div>
          </div>`;
        })
            .join("")}
        <div class="mt-2 text-sm ${answer.isCorrect ? "text-green-600" : "text-red-600"}">
          ${answer.isCorrect
            ? '<i class="fas fa-check mr-1"></i>Đáp án đúng'
            : `<i class="fas fa-times mr-1"></i>Đáp án sai. Đáp án đúng: ${correctText}`}
        </div>
      </div>
    `;
        reviewContainer.appendChild(reviewItem);
    });
}
// Cập nhật danh sách câu hỏi sai
function updateWrongQuestions() {
    wrongQuestions = [];
    // Lọc ra các câu hỏi sai từ userAnswers
    userAnswers.forEach((answer) => {
        if (!answer.isCorrect) {
            // Thêm câu hỏi vào danh sách sai
            wrongQuestions.push(Object.assign(Object.assign({}, window.examData.questions[answer.questionIndex]), { userSelectedIndex: answer.selectedIndex }));
        }
    });
    // Ẩn nút làm lại nếu không có câu hỏi sai nào
    if (wrongQuestions.length === 0) {
        document.getElementById("retryWrongAnswers").style.display = "none";
    }
}
// Thiết lập âm thanh cho ứng dụng
function setupAudio() {
    // Khởi tạo âm thanh
    window.correctSound = new Audio("/media/correct_answer.mp3");
    window.wrongSound = new Audio("/media/wrong_answer.mp3");
    // Đảm bảo âm thanh được nạp trước
    window.correctSound.preload = "auto";
    window.wrongSound.preload = "auto";
    // Đồng bộ trạng thái âm thanh
    window.soundEnabled = soundEnabled;
    // Thêm hàm dừng tất cả âm thanh
    window.stopAllSounds = function () {
        try {
            if (window.correctSound) {
                window.correctSound.pause();
                window.correctSound.currentTime = 0;
            }
            if (window.wrongSound) {
                window.wrongSound.pause();
                window.wrongSound.currentTime = 0;
            }
        }
        catch (error) {
            console.error("Lỗi khi dừng âm thanh:", error);
        }
    };
    // Thêm hàm phát âm thanh vào window object để có thể gọi từ bất kỳ đâu
    window.playSound = function (isCorrect) {
        if (!soundEnabled)
            return;
        // Dừng tất cả âm thanh trước khi phát
        window.stopAllSounds();
        try {
            if (isCorrect) {
                window.correctSound.currentTime = 0;
                window.correctSound.play().catch((error) => {
                    console.error("Lỗi phát âm thanh đúng:", error);
                });
            }
            else {
                window.wrongSound.currentTime = 0;
                window.wrongSound.play().catch((error) => {
                    console.error("Lỗi phát âm thanh sai:", error);
                });
            }
        }
        catch (error) {
            console.error("Lỗi khi phát âm thanh:", error);
        }
    };
    // Thêm event listener để kích hoạt audio khi có tương tác người dùng
    document.addEventListener("click", initAudio, { once: true });
    // Kích hoạt audio
    function initAudio() {
        const silentAudio = new Audio("data:audio/mp3;base64,SUQzBAAAAAAAI1RTU0UAAAAPAAADTGF2ZjU4LjM1LjEwNAAAAAAAAAAAAAAA//tAwAAAAAAAAAAAAAAAAAAAAAAASW5mbwAAAA8AAAACAAABIADf/////////////////////////////////////////wAAADlMQU1FMy4xMDABzQAAAAAAAAAAFIAkCLhCAABAAAABIDVjN9YAAAAAAAAAAAAAAAAAAAAAAP/7QMQAAAf4XkAAAgAAA0gAAABBAAADSAAAAETEFNRTMuMTAwVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVf/7QMRVA8AAAaQAAAABg2gAAABFVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVQ==");
        silentAudio
            .play()
            .then(() => {
            console.log("Audio được kích hoạt thành công");
            // Nạp file âm thanh
            window.correctSound.load();
            window.wrongSound.load();
        })
            .catch((e) => {
            console.error("Không thể kích hoạt audio:", e);
        });
    }
}
//# sourceMappingURL=quizizz-quiz.js.map