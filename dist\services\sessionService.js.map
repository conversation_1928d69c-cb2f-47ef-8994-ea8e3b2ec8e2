{"version": 3, "file": "sessionService.js", "sourceRoot": "", "sources": ["../../src/services/sessionService.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,gEAAsD;AACtD,0DAA6C;AAC7C,gEAA+B;AAC/B,yDAA2C;AAE3C,gEAAoC;AAEpC,yCAAyC;AAClC,MAAM,eAAe,GAAG,CAAC,GAAY,EAAE,EAAE;IAC9C,MAAM,MAAM,GAAG,IAAI,sBAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC;IACvD,MAAM,OAAO,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC;IACpC,MAAM,EAAE,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;IAC1B,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;IAElC,OAAO;QACL,SAAS,EAAE,GAAG,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,SAAS;QACjD,EAAE,EAAE,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,MAAM,CAAC,aAAa,IAAI,SAAS;QACnD,OAAO,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,SAAS;QACxE,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,SAAS;QACpD,UAAU,EAAE,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,MAAM,IAAI,gBAAgB;KAC9D,CAAC;AACJ,CAAC,CAAC;AAbW,QAAA,eAAe,mBAa1B;AAEF,gBAAgB;AACT,MAAM,aAAa,GAAG,CAAC,IAAW,EAAU,EAAE;IACnD,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,gBAAgB,CAAC;IAC1D,MAAM,UAAU,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IAE5C,OAAO,sBAAG,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE,MAAM,EAAE;QAC3D,SAAS,EAAE,UAAU;KACtB,CAAC,CAAC;AACL,CAAC,CAAC;AAPW,QAAA,aAAa,iBAOxB;AAEF,0BAA0B;AACnB,MAAM,aAAa,GAAG,CAC3B,IAAW,EACX,QAAgB,EAChB,GAAY,EACmC,EAAE;IACjD,gBAAgB;IAChB,MAAM,KAAK,GAAG,IAAA,qBAAa,EAAC,IAAI,CAAC,CAAC;IAElC,2BAA2B;IAC3B,MAAM,UAAU,GAAG,IAAA,uBAAe,EAAC,GAAG,CAAC,CAAC;IAExC,sBAAsB;IACtB,MAAM,OAAO,GAAG,MAAM,iBAAO,CAAC,aAAa,CACzC,IAAI,CAAC,GAAa,EAClB,KAAK,EACL,QAAQ,EACR,UAAU,CACX,CAAC;IAEF,eAAe;IACf,iFAAiF;IACjF,KAAK;IAEL,sDAAsD;IACtD,MAAM,cAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,GAAG,EAAE;QACrC,WAAW,EAAE,KAAK;QAClB,gBAAgB,EAAE,QAAQ;KAC3B,CAAC,CAAC;IAEH,mDAAmD;IACnD,MAAM,UAAU,CAAC,wBAAwB,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,QAAQ,CAAC,CAAC;IAEzE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC;AAC5B,CAAC,CAAA,CAAC;AAjCW,QAAA,aAAa,iBAiCxB;AAEF,8BAA8B;AACvB,MAAM,kBAAkB,GAAG,CAChC,MAAc,EACd,eAAuB,EACR,EAAE;IACjB,4BAA4B;IAC5B,MAAM,aAAa,GAAG,MAAM,iBAAO,CAAC,IAAI,CAAC;QACvC,MAAM;QACN,QAAQ,EAAE,EAAE,GAAG,EAAE,eAAe,EAAE;KACnC,CAAC,CAAC;IAEH,iBAAiB;IACjB,KAAK,MAAM,OAAO,IAAI,aAAa,EAAE,CAAC;QACpC,MAAM,iBAAO,CAAC,SAAS,CAAC,EAAE,GAAG,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;IAChD,CAAC;IAED,kCAAkC;IAClC,MAAM,UAAU,CAAC,kBAAkB,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;IAE7D,qEAAqE;AACvE,CAAC,CAAA,CAAC;AAnBW,QAAA,kBAAkB,sBAmB7B;AAEF,8BAA8B;AACvB,MAAM,gBAAgB,GAAG,CAC9B,KAAa,EACa,EAAE;IAC5B,OAAO,iBAAO,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC;AAChD,CAAC,CAAA,CAAC;AAJW,QAAA,gBAAgB,oBAI3B;AAEF,gCAAgC;AACzB,MAAM,aAAa,GAAG,CAAO,KAAa,EAAiB,EAAE;IAClE,YAAY;IACZ,MAAM,OAAO,GAAG,MAAM,iBAAO,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;IAEjD,IAAI,OAAO,EAAE,CAAC;QACZ,2DAA2D;QAC3D,MAAM,iBAAO,CAAC,SAAS,CAAC,EAAE,GAAG,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;QAE9C,gDAAgD;QAChD,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;YAC5B,MAAM,cAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,MAAM,EAAE;gBAC3C,WAAW,EAAE,IAAI;gBACjB,gBAAgB,EAAE,IAAI;aACvB,CAAC,CAAC;QACL,CAAC;QAED,eAAe;QACf,+EAA+E;QAC/E,iDAAiD;QACjD,OAAO;QACP,KAAK;IACP,CAAC;AACH,CAAC,CAAA,CAAC;AAtBW,QAAA,aAAa,iBAsBxB;AAEF,wCAAwC;AACjC,MAAM,iBAAiB,GAAG,CAC/B,SAAiB,EACC,EAAE;IACpB,IAAI,CAAC;QACH,MAAM,OAAO,GAAG,MAAM,iBAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QAElD,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,KAAK,CAAC;QACf,CAAC;QAED,sBAAsB;QACtB,MAAM,iBAAO,CAAC,SAAS,CAAC,EAAE,GAAG,EAAE,SAAS,EAAE,CAAC,CAAC;QAE5C,eAAe;QACf,kFAAkF;QAClF,KAAK;QAEL,OAAO,IAAI,CAAC;IACd,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,0DAA0D;QAC1D,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC,CAAA,CAAC;AAtBW,QAAA,iBAAiB,qBAsB5B;AAEF,8CAA8C;AACvC,MAAM,eAAe,GAAG,CAAO,MAAc,EAAuB,EAAE;IAC3E,OAAO,iBAAO,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AAC1E,CAAC,CAAA,CAAC;AAFW,QAAA,eAAe,mBAE1B;AAEF,yCAAyC;AAClC,MAAM,oBAAoB,GAAG,CAAO,KAAa,EAAiB,EAAE;IACzE,MAAM,iBAAO,CAAC,SAAS,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,UAAU,EAAE,IAAI,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC;AAC3E,CAAC,CAAA,CAAC;AAFW,QAAA,oBAAoB,wBAE/B;AAEF,qCAAqC;AAC9B,MAAM,sBAAsB,GAAG,GAAmB,EAAE;IACzD,sCAAsC;IACtC,MAAM,QAAQ,GAAG,WAAW,CAAC,GAAS,EAAE;QACtC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,iBAAO,CAAC,qBAAqB,EAAE,CAAC;YACrD,qEAAqE;QACvE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,+DAA+D;QACjE,CAAC;IACH,CAAC,CAAA,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,aAAa;IAEtC,OAAO,QAAQ,CAAC;AAClB,CAAC,CAAC;AAZW,QAAA,sBAAsB,0BAYjC;AAEF,kBAAe;IACb,aAAa,EAAb,qBAAa;IACb,kBAAkB,EAAlB,0BAAkB;IAClB,gBAAgB,EAAhB,wBAAgB;IAChB,aAAa,EAAb,qBAAa;IACb,eAAe,EAAf,uBAAe;IACf,oBAAoB,EAApB,4BAAoB;IACpB,sBAAsB,EAAtB,8BAAsB;IACtB,eAAe,EAAf,uBAAe;IACf,aAAa,EAAb,qBAAa;IACb,iBAAiB,EAAjB,yBAAiB;CAClB,CAAC"}