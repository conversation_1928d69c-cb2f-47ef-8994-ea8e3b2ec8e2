"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.redirectLegacyExamRoute = void 0;
// Cập nhật kiểu hàm để không trả về gì
const redirectLegacyExamRoute = (req, res, next) => {
    // Lấy tham số từ query string
    const { examId, examType } = req.query;
    // Kiểm tra tham số bắt buộc
    if (!examId || !examType) {
        res.status(400).json({
            success: false,
            message: "Thiếu thông tin cần thiết: examId và examType là bắt buộc",
        });
        return;
    }
    // Chuyển hướng đến URL chính xác với cùng các tham số
    res.redirect(`/exam/retry-wrong-questions?examId=${examId}&examType=${examType}`);
};
exports.redirectLegacyExamRoute = redirectLegacyExamRoute;
//# sourceMappingURL=redirectController.js.map