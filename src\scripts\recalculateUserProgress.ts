import mongoose from "mongoose";
import dotenv from "dotenv";
import { recalculateUserProgress } from "../services/userProgress.service";

// Load environment variables
dotenv.config();

// Lấy userId từ tham số dòng lệnh
const userId = process.argv[2];

if (!userId) {
  console.error(
    "❌ Vui lòng cung cấp userId. Ví dụ: npm run recalculate-user-progress 60f1234567890abcdef12345"
  );
  process.exit(1);
}

// Connect to MongoDB
mongoose
  .connect(process.env.MONGODB_URI || "mongodb://localhost:27017/google-auth")
  .then(async () => {
    console.log("📚 Kết nối MongoDB thành công");

    console.log(
      `🔄 Bắt đầu cập nhật lại tiến độ học tập cho người dùng ${userId}...`
    );

    try {
      const result = await recalculateUserProgress(userId);

      if (result) {
        console.log("✅ Cập nhật tiến độ học tập thành công!");
      } else {
        console.error("❌ Cập nhật tiến độ học tập thất bại!");
      }
    } catch (error) {
      console.error("❌ Lỗi khi cập nhật tiến độ học tập:", error);
    }

    // Đóng kết nối
    await mongoose.connection.close();
    console.log("📚 Đã đóng kết nối MongoDB");
    process.exit(0);
  })
  .catch((err) => {
    console.error("❌ Lỗi kết nối MongoDB:", err);
    process.exit(1);
  });
