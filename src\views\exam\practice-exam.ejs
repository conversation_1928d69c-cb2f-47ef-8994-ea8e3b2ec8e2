<style>
  /* Custom responsive styles for practice exam */
  @media (max-width: 1024px) {
    .sidebar-mobile {
      transform: translateX(-100%);
      position: fixed;
      z-index: 40;
      height: 100vh;
      transition: transform 0.3s ease-in-out;
    }

    .sidebar-mobile.active {
      transform: translateX(0);
    }
  }

  @media (max-width: 640px) {
    .question-text {
      font-size: 0.875rem !important;
      line-height: 1.5 !important;
    }

    .option-text {
      font-size: 0.875rem !important;
    }

    .nav-button {
      padding: 0.5rem 1rem !important;
      font-size: 0.875rem !important;
    }

    .practice-header {
      padding: 0.75rem 1rem !important;
    }

    .practice-header h1 {
      font-size: 1rem !important;
    }

    .timer-badge {
      padding: 0.25rem 0.5rem !important;
      font-size: 0.75rem !important;
    }
  }

  /* Prevent text overflow */
  .break-words {
    overflow-wrap: break-word !important;
    word-wrap: break-word !important;
    word-break: break-word !important;
    hyphens: auto;
  }

  /* Question container responsive */
  .question-container {
    min-height: 0;
    display: flex;
    flex-direction: column;
  }

  .options-container {
    flex: 1;
    overflow-y: auto;
  }
</style>

<div class="flex flex-col h-screen bg-gray-100">
  <!-- Header -->
  <header class="bg-orange-600 shadow">
    <div
      class="max-w-7xl mx-auto px-2 sm:px-4 lg:px-8 flex justify-between items-center py-2 sm:py-4 practice-header"
    >
      <div class="flex items-center min-w-0 flex-1">
        <button
          id="mobile-menu-btn"
          class="lg:hidden mr-2 sm:mr-3 text-white hover:text-orange-200 transition-colors"
        >
          <i class="fas fa-bars text-lg"></i>
        </button>
        <h1 class="text-lg sm:text-xl font-bold text-white truncate">
          <i class="fas fa-dumbbell mr-1 sm:mr-2"></i>Thi thử:
          <span class="hidden sm:inline"><%= product.name %></span
          ><span class="sm:hidden">TH</span>
        </h1>
      </div>
      <div
        class="flex items-center text-white space-x-2 sm:space-x-4 flex-shrink-0"
      >
        <div
          class="bg-orange-700 px-2 sm:px-3 py-1 rounded-full flex items-center timer-badge"
        >
          <i class="fas fa-clock mr-1 text-xs sm:text-sm"></i>
          <span id="timer" class="text-xs sm:text-sm font-mono">01:00:00</span>
        </div>
        <div
          class="bg-orange-700 px-2 sm:px-3 py-1 rounded-full flex items-center timer-badge"
        >
          <i class="fas fa-question-circle mr-1 text-xs sm:text-sm"></i>
          <span id="current-question" class="text-xs sm:text-sm">1</span>/<span
            id="total-questions-header"
            class="text-xs sm:text-sm"
            ><%= practiceInfo.totalQuestions %></span
          >
        </div>
      </div>
    </div>
  </header>

  <!-- Mobile sidebar overlay -->
  <div
    id="mobile-overlay"
    class="fixed inset-0 bg-black bg-opacity-50 z-30 lg:hidden hidden"
  ></div>

  <!-- Main content -->
  <div class="flex-grow flex min-h-0">
    <!-- Sidebar -->
    <div
      id="mobile-sidebar"
      class="bg-white w-60 xl:w-64 border-r border-gray-200 shadow-sm flex-shrink-0 sidebar-mobile lg:flex lg:flex-col lg:relative lg:transform-none"
    >
      <div class="p-4 border-b border-gray-200">
        <h2 class="text-lg font-medium text-gray-800">Thông tin thi thử</h2>
        <div class="mt-2 text-sm text-gray-600 space-y-1">
          <p><span class="font-medium">Học viên:</span> <%= student.email %></p>
          <p><span class="font-medium">Khóa học:</span> <%= product.name %></p>
          <p>
            <span class="font-medium">Tổng câu hỏi:</span>
            <span id="total-questions"><%= practiceInfo.totalQuestions %></span>
          </p>
          <p>
            <span class="font-medium">Thời gian:</span>
            <span class="text-orange-600 font-medium">60 phút</span>
          </p>
        </div>
      </div>
      <div id="progress-section" class="p-4 border-b border-gray-200">
        <div class="flex justify-between items-center mb-2">
          <h3 class="font-medium text-gray-700">Tiến độ làm bài</h3>
          <span class="text-sm text-gray-600">
            <span id="answered-questions">0</span>/<span id="total-questions-2"
              ><%= practiceInfo.totalQuestions %></span
            >
          </span>
        </div>
        <div class="h-2 bg-gray-200 rounded-full overflow-hidden">
          <div
            id="progressBar"
            class="h-full bg-orange-600"
            style="width: 0%"
          ></div>
        </div>
      </div>
      <div id="question-nav-section" class="p-4 flex-1 overflow-y-auto">
        <h3 class="font-medium text-gray-700 mb-3">Danh sách câu hỏi</h3>
        <div
          class="grid grid-cols-5 gap-2 max-h-80 overflow-y-auto pr-2"
          id="question-list"
        >
          <!-- Các câu hỏi sẽ được thêm bằng JavaScript -->
        </div>
      </div>
    </div>

    <!-- Main question area -->
    <div class="flex-grow flex flex-col p-4 md:p-6 overflow-y-auto min-w-0">
      <!-- Loading screen -->
      <div id="loading-screen" class="flex items-center justify-center h-full">
        <div class="text-center">
          <div
            class="animate-spin rounded-full h-16 w-16 border-b-2 border-orange-600 mx-auto mb-4"
          ></div>
          <h3 class="text-lg font-medium text-gray-700 mb-2">
            Đang chuẩn bị bài thi thử
          </h3>
          <p class="text-gray-500">Vui lòng đợi trong giây lát...</p>
        </div>
      </div>

      <!-- Exam container - Sẽ ẩn khi hiển thị kết quả -->
      <div id="exam-container" class="hidden flex flex-col h-full">
        <div
          class="bg-white rounded-lg shadow-sm p-4 md:p-6 mb-4 flex items-center justify-between"
        >
          <div>
            <h2 class="text-lg font-medium text-gray-800">
              Câu hỏi <span id="current-question-main">1</span>
            </h2>
          </div>
          <div class="hidden md:block">
            <span
              class="bg-orange-100 text-orange-800 text-xs font-medium px-2 py-1 rounded"
            >
              Thi thử
            </span>
          </div>
        </div>

        <form id="exam-form" onsubmit="return false;">
          <div
            id="questions-container"
            class="question-card mb-4 flex-grow flex flex-col"
            style="
              transition: opacity 0.3s ease, transform 0.3s ease;
              opacity: 1;
              transform: translateX(0);
            "
          >
            <!-- Câu hỏi sẽ được thêm bằng JavaScript -->
          </div>
        </form>

        <!-- Navigation buttons -->
        <div
          class="flex flex-col sm:flex-row justify-between items-center bg-white rounded-lg p-4 shadow-sm space-y-2 sm:space-y-0"
        >
          <button
            id="prev-btn"
            onclick="previousQuestion()"
            class="w-full sm:w-auto px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors disabled:opacity-50 disabled:cursor-not-allowed text-sm sm:text-base"
            disabled
          >
            <i class="fas fa-chevron-left mr-2"></i>Câu trước
          </button>

          <div class="flex space-x-2 order-last sm:order-none">
            <button
              id="submit-btn"
              onclick="submitExam()"
              class="px-4 sm:px-6 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-all duration-300 hover:scale-105 hover:shadow-lg font-medium text-sm sm:text-base"
              style="
                transition: all 0.3s ease, background-color 0.3s ease,
                  transform 0.2s ease;
              "
              disabled
            >
              <i class="fas fa-paper-plane mr-2"></i>Nộp bài
            </button>
          </div>

          <button
            id="next-btn"
            onclick="nextQuestion()"
            class="w-full sm:w-auto px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors text-sm sm:text-base"
          >
            Câu tiếp <i class="fas fa-chevron-right ml-2"></i>
          </button>
        </div>
      </div>

      <!-- Result container - Sẽ hiển thị sau khi nộp bài -->
      <div id="result-container" class="hidden">
        <div class="bg-white sm:rounded-lg sm:shadow-sm overflow-hidden">
          <!-- Header kết quả -->
          <div class="bg-orange-600 px-2 sm:px-6 py-3 sm:py-4 text-white">
            <h2 class="text-lg sm:text-xl font-bold">
              <i class="fas fa-trophy mr-2"></i>Kết quả thi thử
            </h2>
          </div>

          <!-- Thông tin kết quả -->
          <div class="p-1 sm:p-6">
            <div class="text-center mb-3 sm:mb-8 px-2">
              <div
                class="inline-flex items-center justify-center w-16 h-16 sm:w-24 sm:h-24 rounded-full bg-orange-100 text-orange-600 mb-2 sm:mb-4"
              >
                <i class="fas fa-chart-pie text-3xl sm:text-5xl"></i>
              </div>
              <h3
                class="text-lg sm:text-2xl font-bold text-gray-800 mb-1 sm:mb-2"
              >
                Hoàn thành!
              </h3>
              <div
                class="text-3xl sm:text-5xl font-bold text-orange-600 mt-1 sm:mt-2"
              >
                <span id="score-percentage">0</span>%
              </div>
              <p class="text-gray-600 mt-1 sm:mt-2 text-sm sm:text-base">
                <span id="correct-count">0</span>/<span id="total-count"
                  >0</span
                >
                câu đúng
              </p>
            </div>

            <div
              class="bg-white sm:bg-gray-50 sm:rounded-lg p-2 sm:p-4 mb-2 sm:mb-6 border-t border-b sm:border sm:border-gray-200"
            >
              <div class="flex justify-between items-center mb-2">
                <span class="text-gray-700 text-sm sm:text-base"
                  >Thời gian làm bài:</span
                >
                <span
                  class="font-medium text-orange-600 text-sm sm:text-base"
                  id="examTime"
                  >0:00</span
                >
              </div>
              <div class="flex justify-between items-center">
                <span class="text-gray-700 text-sm sm:text-base"
                  >Loại thi:</span
                >
                <span class="font-medium text-gray-800 text-sm sm:text-base"
                  >Thi thử môn học</span
                >
              </div>
            </div>

            <div class="mb-2 sm:mb-6">
              <h4
                class="font-medium text-gray-800 mb-1 sm:mb-3 px-2 sm:px-0 text-sm sm:text-base"
              >
                Tóm tắt câu hỏi
              </h4>
              <div
                class="max-h-48 overflow-y-auto border-0 sm:border sm:border-gray-200 sm:rounded-lg p-2 sm:p-3 bg-gray-50 sm:bg-transparent"
              >
                <div
                  id="summaryContainer"
                  class="grid grid-cols-8 sm:grid-cols-8 md:grid-cols-10 lg:grid-cols-12 gap-1 sm:gap-2"
                >
                  <!-- Summary items will be added here -->
                </div>
              </div>
            </div>

            <div
              class="bg-white sm:bg-gray-50 sm:rounded-lg p-0 sm:p-4 mb-2 sm:mb-6"
            >
              <h4
                class="font-medium text-gray-800 mb-2 sm:mb-4 px-2 sm:px-0 text-sm sm:text-base border-b pb-2 sm:border-0 sm:pb-0"
              >
                Xem lại câu trả lời
              </h4>
              <div
                id="answerResults"
                class="h-96 sm:max-h-96 overflow-y-auto pr-0 sm:pr-2 space-y-2 sm:space-y-4 px-2 sm:px-0"
              >
                <!-- Review items will be added here -->
              </div>
            </div>

            <div
              class="flex flex-col sm:flex-row justify-center space-y-2 sm:space-y-0 sm:space-x-4 px-2 sm:px-0 mt-4 sm:mt-0"
            >
              <a
                href="/home"
                class="bg-gray-600 text-white px-6 sm:px-6 py-3 sm:py-2 rounded-full font-medium hover:bg-gray-700 transition-colors text-center text-sm sm:text-base"
              >
                <i class="fas fa-home mr-2"></i>Về trang chủ
              </a>
              <button
                onclick="startNewPracticeExam()"
                class="bg-orange-600 text-white px-6 sm:px-6 py-3 sm:py-2 rounded-full font-medium hover:bg-orange-700 transition-colors text-sm sm:text-base"
              >
                <i class="fas fa-redo mr-2"></i>Thi lại
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Warning popup: Chưa trả lời hết câu hỏi -->
  <div
    id="warning-popup"
    class="fixed inset-0 flex items-center justify-center z-50 hidden"
  >
    <div class="fixed inset-0 bg-black opacity-50"></div>
    <div
      class="bg-white rounded-lg shadow-xl p-6 max-w-md w-full z-10 relative"
    >
      <div class="mb-4 text-center">
        <div
          class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-yellow-100 mb-4"
        >
          <i class="fas fa-exclamation-triangle text-yellow-600 text-2xl"></i>
        </div>
        <h3 class="text-lg font-medium text-gray-900">
          Bạn chưa trả lời hết câu hỏi
        </h3>
        <p class="text-gray-500 mt-2">
          Bạn vẫn còn một số câu hỏi chưa trả lời:
        </p>
        <div class="mt-3" id="unanswered-questions">
          <!-- Danh sách câu hỏi chưa trả lời -->
        </div>
        <p class="text-gray-500 mt-2">
          Bạn có thể nhấn vào số thứ tự để quay lại câu hỏi đó.
        </p>
      </div>
      <div class="flex justify-center space-x-3">
        <button
          id="continue-button"
          class="bg-gray-200 hover:bg-gray-300 text-gray-800 py-2 px-4 rounded-lg transition-colors"
        >
          Tiếp tục làm bài
        </button>
        <button
          id="force-submit-button"
          class="bg-orange-600 hover:bg-orange-700 text-white py-2 px-4 rounded-lg transition-colors"
        >
          Nộp bài ngay
        </button>
      </div>
    </div>
  </div>

  <!-- Continue/New Exam Modal -->
  <div
    id="continue-exam-modal"
    class="fixed inset-0 flex items-center justify-center z-50 hidden"
  >
    <div class="fixed inset-0 bg-black opacity-50"></div>
    <div
      class="bg-white rounded-lg shadow-xl p-6 max-w-lg w-full z-10 relative mx-4"
    >
      <div class="mb-4 text-center">
        <div
          class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-blue-100 mb-4"
        >
          <i class="fas fa-question-circle text-blue-600 text-2xl"></i>
        </div>
        <h3 class="text-lg font-medium text-gray-900">
          Bạn có bài thi thử đang dở dang
        </h3>
        <p class="text-gray-500 mt-2">
          Bạn có một bài thi thử chưa hoàn thành với
          <span
            id="time-remaining-text"
            class="text-orange-600 font-medium"
          ></span>
          còn lại.
        </p>
        <p class="text-gray-500 mt-1">
          Bạn muốn làm tiếp bài cũ hay bắt đầu bài mới?
        </p>
      </div>
      <div
        class="flex flex-col sm:flex-row justify-center space-y-2 sm:space-y-0 sm:space-x-3"
      >
        <button
          id="continue-exam-btn"
          class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-6 rounded-lg transition-colors font-medium"
        >
          <i class="fas fa-play mr-2"></i>Làm tiếp bài cũ
        </button>
        <button
          id="new-exam-btn"
          class="bg-orange-600 hover:bg-orange-700 text-white py-2 px-6 rounded-lg transition-colors font-medium"
        >
          <i class="fas fa-refresh mr-2"></i>Bắt đầu bài mới
        </button>
      </div>
    </div>
  </div>

  <!-- Time Up Modal -->
  <div
    id="time-up-modal"
    class="fixed inset-0 flex items-center justify-center z-50 hidden"
  >
    <div class="fixed inset-0 bg-black opacity-50"></div>
    <div
      class="bg-white rounded-lg shadow-xl p-6 max-w-md w-full z-10 relative"
    >
      <div class="mb-4 text-center">
        <div
          class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-4"
        >
          <i class="fas fa-clock text-red-600 text-2xl"></i>
        </div>
        <h3 class="text-lg font-medium text-gray-900">Hết giờ làm bài</h3>
        <p class="text-gray-500 mt-2">
          Thời gian làm bài đã kết thúc. Bài thi sẽ được tự động nộp.
        </p>
      </div>
      <div class="flex justify-center">
        <button
          id="auto-submit-button"
          class="bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-lg transition-colors"
        >
          Đồng ý
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Encrypted Practice Data -->
<% if (typeof encryptedPracticeData !== 'undefined' && encryptedPracticeData) {
%>
<script type="application/json" id="encryptedPracticeData">
  {
    "appConfigData": "<%- encryptedPracticeData %>",
    "token": "<%- encryptionToken %>",
    "salt": "<%- encryptionSalt %>"
  }
</script>
<% } %>

<!-- Practice Info -->
<script type="application/json" id="practiceInfo">
  <%- JSON.stringify(practiceInfo) %>
</script>

<!-- Client-side encryption library -->
<script src="/js/client-encryption.js"></script>

<!-- Practice Exam JavaScript -->
<script>
  // Practice exam variables
  let practiceQuestions = [];
  let currentQuestionIndex = 0;
  let userAnswers = {};
  let startTime = Date.now();
  let timerInterval;
  let timeLimit = 60 * 60 * 1000; // 60 minutes in milliseconds
  let practiceInfo = {};
  let existingExamData = null;

  // LocalStorage key for saving practice data (with practiceId)
  let PRACTICE_STORAGE_KEY = "practice_exam_data";

  // Update storage key to include practiceId when available
  function updateStorageKey() {
    if (practiceInfo.practiceId) {
      const oldKey = PRACTICE_STORAGE_KEY;
      PRACTICE_STORAGE_KEY = `practice_exam_data_${practiceInfo.practiceId}`;

      // *** CLEAR ALL OLD EXAM DATA FIRST ***
      if (typeof clearAllExamDataFromStorage === "function") {
        const currentExamInfo = {
          type: "practice",
          practiceId: practiceInfo.practiceId,
        };
        const cleanupResult = clearAllExamDataFromStorage(currentExamInfo);
      } else {
        console.warn("⚠️ Function clearAllExamDataFromStorage không tìm thấy");

        // Fallback: Clean up old localStorage without practiceId if exists
        if (oldKey !== PRACTICE_STORAGE_KEY) {
          try {
            localStorage.removeItem(oldKey);
            localStorage.removeItem("practice_exam_data"); // Remove old generic key
            // Also clear any localStorage from before the options-order fix
            Object.keys(localStorage).forEach((key) => {
              if (
                key.startsWith("practice_exam_data") &&
                key !== PRACTICE_STORAGE_KEY
              ) {
                localStorage.removeItem(key);
              }
            });
            console.log("🧹 Cleaned up old localStorage keys (fallback)");
          } catch (e) {
            console.warn("⚠️ Could not clean old localStorage:", e);
          }
        }
      }
    }
  }
  let periodicSaveInterval;

  // LocalStorage functions for encrypted data
  function savePracticeDataToStorage() {
    try {
      const practiceData = {
        courseId: practiceInfo.courseId,
        courseName: practiceInfo.courseName,
        practiceId: practiceInfo.practiceId, // Thêm practiceId
        userAnswers: userAnswers,
        currentQuestionIndex: currentQuestionIndex,
        startTime: startTime,
        timeLimit: timeLimit,
        timestamp: Date.now(),
        totalQuestions: practiceQuestions.length,
        // Save question IDs to ensure consistent order when restoring
        questionIds: practiceQuestions.map((q) => q._id),
        // Note: Question options order is preserved from server to maintain consistency
      };

      // Encrypt the data before storing
      if (typeof encryptStringOptimized === "function") {
        const encryptedData = encryptStringOptimized(
          JSON.stringify(practiceData)
        );
        localStorage.setItem(
          PRACTICE_STORAGE_KEY,
          JSON.stringify(encryptedData)
        );
      } else {
        console.warn("⚠️ Không tìm thấy function mã hóa, lưu dạng plain text");
        localStorage.setItem(
          PRACTICE_STORAGE_KEY,
          JSON.stringify(practiceData)
        );
      }
    } catch (error) {
      console.error("❌ Lỗi lưu dữ liệu localStorage:", error);
    }
  }

  function loadPracticeDataFromStorage() {
    try {
      const storedData = localStorage.getItem(PRACTICE_STORAGE_KEY);
      if (!storedData) {
        return null;
      }

      let practiceData;

      // Try to decrypt if it's encrypted
      try {
        const encryptedObj = JSON.parse(storedData);
        if (
          encryptedObj.encryptedData &&
          typeof decryptStringOptimized === "function"
        ) {
          const decryptedText = decryptStringOptimized(
            encryptedObj.encryptedData,
            encryptedObj.token,
            encryptedObj.salt
          );
          practiceData = JSON.parse(decryptedText);
        } else {
          // Fallback to plain text
          practiceData = JSON.parse(storedData);
        }
      } catch (e) {
        // If decryption fails, try plain text
        practiceData = JSON.parse(storedData);
      }

      // Validate data and check if it's still valid (not expired)
      if (practiceData && practiceData.timestamp) {
        const elapsed = Date.now() - practiceData.timestamp;
        const maxAge = 60 * 60 * 1000; // 60 minutes

        if (elapsed < maxAge) {
          return practiceData;
        } else {
          clearPracticeDataFromStorage();
          return null;
        }
      }

      return practiceData;
    } catch (error) {
      clearPracticeDataFromStorage();
      return null;
    }
  }

  function clearPracticeDataFromStorage() {
    try {
      localStorage.removeItem(PRACTICE_STORAGE_KEY);
    } catch (error) {
      console.error("❌ Lỗi xóa dữ liệu localStorage:");
    }
  }

  function restorePracticeDataFromStorage() {
    const savedData = loadPracticeDataFromStorage();

    // Early return if no saved data
    if (!savedData) {
      console.log("📁 Không có dữ liệu");
      return false;
    }

    // Validate both courseId and practiceId
    const isValidData =
      savedData && savedData.courseId === practiceInfo.courseId;
    const hasPracticeId =
      practiceInfo.practiceId && savedData && savedData.practiceId;
    const practiceIdMatches =
      !hasPracticeId ||
      (savedData && savedData.practiceId === practiceInfo.practiceId);

    if (isValidData && practiceIdMatches) {
      console.log(
        `🔄 Khôi phục dữ liệu làm bài từ localStorage (practiceId: ${
          practiceInfo.practiceId || "none"
        })`
      );

      // Validate question order consistency
      if (savedData.questionIds && savedData.questionIds.length > 0) {
        const currentQuestionIds = practiceQuestions.map((q) => q._id);
        const savedQuestionIds = savedData.questionIds;

        // Check if question order is the same
        if (
          currentQuestionIds.length !== savedQuestionIds.length ||
          !currentQuestionIds.every(
            (id, index) => id === savedQuestionIds[index]
          )
        ) {
          console.warn(
            "⚠️ Thứ tự câu hỏi đã thay đổi, không thể khôi phục localStorage"
          );
          console.log("Current IDs:", currentQuestionIds.slice(0, 3));
          console.log("Saved IDs:", savedQuestionIds.slice(0, 3));
          clearPracticeDataFromStorage();
          return false;
        }
        console.log(
          "✅ Thứ tự câu hỏi và options nhất quán, tiếp tục khôi phục"
        );
      }

      // Restore user answers
      userAnswers = savedData.userAnswers || {};

      // Restore current question index
      if (savedData.currentQuestionIndex !== undefined) {
        currentQuestionIndex = savedData.currentQuestionIndex;
      }

      // Update UI and show current question with selected answers
      updateProgress();
      updateQuestionNavigation();
      updateSubmitButton();

      // CRITICAL: Re-render current question to show selected answers
      showQuestion(currentQuestionIndex);

      // Force update submit button after restore to ensure correct state
      // Use multiple checks to ensure it's properly enabled
      const forceUpdateSubmitButton = () => {
        updateSubmitButton();
        const submitBtn = document.getElementById("submit-btn");
        const answeredCount = Object.keys(userAnswers).length;

        console.log(
          `🔄 Force update submit button: ${answeredCount} answers, disabled: ${submitBtn?.disabled}`
        );

        if (submitBtn && answeredCount > 0) {
          submitBtn.disabled = false;
          console.log("✅ Submit button force enabled after restore");
        }
      };

      // Multiple attempts to ensure button is enabled
      setTimeout(forceUpdateSubmitButton, 50);
      setTimeout(forceUpdateSubmitButton, 200);
      setTimeout(forceUpdateSubmitButton, 500);

      console.log(
        `✅ Khôi phục thành công: ${
          Object.keys(userAnswers).length
        } câu đã trả lời, câu hiện tại: ${currentQuestionIndex + 1}`
      );
      return true;
    } else {
      if (savedData) {
        console.log(`❌ Dữ liệu localStorage không khớp:`, {
          savedCourseId: savedData.courseId,
          currentCourseId: practiceInfo.courseId,
          savedPracticeId: savedData.practiceId || "undefined",
          currentPracticeId: practiceInfo.practiceId || "undefined",
        });
      }
      return false;
    }
  }

  function startPeriodicSave() {
    // Save data every 30 seconds
    periodicSaveInterval = setInterval(() => {
      savePracticeDataToStorage();
    }, 30000); // 30 seconds
  }

  function stopPeriodicSave() {
    if (periodicSaveInterval) {
      clearInterval(periodicSaveInterval);
      periodicSaveInterval = null;
    }
  }

  function syncAnswersToServer() {
    if (!practiceInfo.practiceId || Object.keys(userAnswers).length === 0) {
      return;
    }

    fetch("/exam/practice/sync-answers", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        practiceId: practiceInfo.practiceId,
        userAnswers: userAnswers,
        currentQuestionIndex: currentQuestionIndex,
      }),
    })
      .then((response) => response.json())
      .then((data) => {
        if (data.success) {
        } else {
          console.warn("⚠️ Lỗi sync answers:");
        }
      })
      .catch((error) => {
        console.error("❌ Lỗi network khi sync answers:");
      });
  }

  // Function removed - logic moved to server-side

  function showContinueModal(existingExam) {
    existingExamData = existingExam;
    const modal = document.getElementById("continue-exam-modal");
    const timeRemainingText = document.getElementById("time-remaining-text");

    // Format time remaining
    const timeRemaining = existingExam.timeRemaining;
    const hours = Math.floor(timeRemaining / 3600);
    const minutes = Math.floor((timeRemaining % 3600) / 60);
    const seconds = timeRemaining % 60;

    if (hours > 0) {
      timeRemainingText.textContent = `${hours}:${minutes
        .toString()
        .padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
    } else {
      timeRemainingText.textContent = `${minutes}:${seconds
        .toString()
        .padStart(2, "0")}`;
    }

    modal.classList.remove("hidden");

    // Setup button listeners
    document.getElementById("continue-exam-btn").onclick = () =>
      continueExistingExam();
    document.getElementById("new-exam-btn").onclick = () => startNewExam();
  }

  function continueExistingExam() {
    const modal = document.getElementById("continue-exam-modal");
    modal.classList.add("hidden");

    console.log("🔄 Continuing existing exam...");
    // Legacy function - logic moved to exams.ejs
    // This should not be called in new design
    console.warn("⚠️ Legacy function called - should use new modal flow");
  }

  function startNewExam() {
    const modal = document.getElementById("continue-exam-modal");
    modal.classList.add("hidden");

    console.log("🆕 Starting new exam...");

    // Clear localStorage when starting new exam
    clearPracticeDataFromStorage();

    // Legacy function - logic moved to exams.ejs
    // This should not be called in new design
    console.warn("⚠️ Legacy function called - should use new modal flow");
  }

  // Initialize practice exam
  document.addEventListener("DOMContentLoaded", function () {
    console.log("🌟 DOMContentLoaded fired, DOM state:", document.readyState);

    // Double check DOM is ready
    if (document.readyState === "loading") {
      console.log("⏳ DOM still loading, waiting...");
      setTimeout(() => initializeAfterDOMReady(), 200);
      return;
    }

    initializeAfterDOMReady();
  });

  function initializeAfterDOMReady() {
    console.log("🎬 Starting initialization, DOM ready:", document.readyState);
    try {
      // Get practice info
      const practiceInfoScript = document.getElementById("practiceInfo");
      if (practiceInfoScript) {
        practiceInfo = JSON.parse(practiceInfoScript.textContent);

        // Update storage key with practiceId if available
        updateStorageKey();
      }

      // Load and decrypt practice data
      const encryptedDataScript = document.getElementById(
        "encryptedPracticeData"
      );
      if (encryptedDataScript) {
        const encryptedData = JSON.parse(encryptedDataScript.textContent);

        // Try different decryption methods in order of preference
        let decryptedData;

        if (typeof decryptDataOptimized === "function") {
          const decryptedText = decryptDataOptimized(
            encryptedData.appConfigData,
            encryptedData.token,
            encryptedData.salt
          );

          decryptedData = JSON.parse(decryptedText);
        } else if (typeof decryptData === "function") {
          decryptedData = decryptData(
            encryptedData.appConfigData,
            encryptedData.token,
            encryptedData.salt
          );
        } else {
          throw new Error(
            "Không tìm thấy function giải mã nào. Hãy kiểm tra client-encryption.js"
          );
        }

        practiceQuestions = decryptedData.questions || [];

        // DEBUG: Log first question details
        if (practiceQuestions.length > 0) {
        }
      } else {
      }

      if (practiceQuestions.length === 0) {
        // If no questions loaded, check if this is existing practice scenario
        if (practiceInfo.hasExisting) {
          showContinueModal(practiceInfo.existingExam);
        } else {
          showError("Không có câu hỏi nào được tải. Vui lòng thử lại.");
        }
        return;
      }

      // Check if server detected existing practice
      if (practiceInfo.hasExisting) {
        showContinueModal(practiceInfo.existingExam);
      } else {
        // No existing practice, proceed with normal initialization

        // Add slight delay to ensure DOM is ready
        setTimeout(() => {
          initializePracticeExam();
        }, 100);
      }
    } catch (error) {
      console.error("Lỗi khởi tạo thi thử:", error);
      showError("Có lỗi xảy ra khi chuẩn bị bài thi. Vui lòng thử lại.");
    }
  }

  function initializePracticeExam() {
    // Hide loading screen and show exam container
    const loadingScreen = document.getElementById("loading-screen");
    const examContainer = document.getElementById("exam-container");

    // Update localStorage key with practiceId
    updateStorageKey();

    // Hide loading screen
    if (loadingScreen) {
      loadingScreen.classList.add("hidden");
    }
    if (examContainer) {
      examContainer.classList.remove("hidden");
    }

    // Validate practiceQuestions
    // Validate practiceQuestions

    if (!practiceQuestions || practiceQuestions.length === 0) {
      console.error("❌ No practice questions loaded!");
      showError("Không có câu hỏi nào được tải. Vui lòng thử lại.");
      return;
    }

    // Try to restore from localStorage for new exams (not continue mode)
    if (!practiceInfo.isContinue) {
      const restoredFromStorage = restorePracticeDataFromStorage();
      if (!restoredFromStorage) {
        // Initial save to localStorage for new exam
        savePracticeDataToStorage();
      }
    }

    // Check if this is a continue exam
    if (practiceInfo.isContinue && practiceInfo.existingAnswers) {
      // Convert userAnswers format if needed
      const serverAnswers = practiceInfo.existingAnswers || {};

      // Check if it's object-based (questionId -> answer) or index-based (index -> answer)
      const firstKey = Object.keys(serverAnswers)[0];
      let serverUserAnswers = {};

      if (firstKey && !isNaN(parseInt(firstKey))) {
        // Already index-based, use directly
        serverUserAnswers = serverAnswers;
      } else {
        // Convert from questionId-based to index-based
        for (let i = 0; i < practiceQuestions.length; i++) {
          const question = practiceQuestions[i];
          if (
            question &&
            question._id &&
            serverAnswers[question._id] !== undefined
          ) {
            serverUserAnswers[i] = serverAnswers[question._id];
          }
        }
      }

      // Try to get localStorage data for same practiceId
      const localStorageData = loadPracticeDataFromStorage();
      let localUserAnswers = {};

      if (
        localStorageData &&
        localStorageData.practiceId === practiceInfo.practiceId &&
        localStorageData.userAnswers
      ) {
        localUserAnswers = localStorageData.userAnswers;
      }

      // Merge localStorage and server data (localStorage wins if newer)
      userAnswers = { ...serverUserAnswers };

      // Merge local answers if they exist and are potentially newer
      if (Object.keys(localUserAnswers).length > 0) {
        const localCount = Object.keys(localUserAnswers).length;
        const serverCount = Object.keys(serverUserAnswers).length;

        if (localCount >= serverCount) {
          // Local storage has more or equal answers, use it
          userAnswers = { ...userAnswers, ...localUserAnswers };
          console.log(
            `🔀 Merged answers: Local (${localCount}) + Server (${serverCount}) = Final (${
              Object.keys(userAnswers).length
            })`
          );
        } else {
          console.log(
            `📡 Using server answers as they have more data (${serverCount} vs ${localCount})`
          );
        }
      }

      // Update time limit if continuing
      if (practiceInfo.timeRemaining) {
        timeLimit = practiceInfo.timeRemaining * 1000; // Convert to milliseconds
      }

      // Update start time for continue exam
      if (practiceInfo.startTime) {
        startTime = new Date(practiceInfo.startTime).getTime();
        console.log(`🕐 Original start time: ${new Date(startTime)}`);
      }

      // Force update submit button for continue mode
      const continueForceUpdateSubmitButton = () => {
        updateSubmitButton();
        const submitBtn = document.getElementById("submit-btn");
        const answeredCount = Object.keys(userAnswers).length;

        if (submitBtn && answeredCount > 0) {
          submitBtn.disabled = false;
        }
      };

      // Multiple attempts for continue mode
      setTimeout(continueForceUpdateSubmitButton, 50);
      setTimeout(continueForceUpdateSubmitButton, 200);
      setTimeout(continueForceUpdateSubmitButton, 500);
    }

    // Initialize question navigation
    createQuestionNavigation();

    // Show first question or last answered question
    const lastAnsweredIndex =
      Object.keys(userAnswers).length > 0
        ? Math.max(...Object.keys(userAnswers).map(Number))
        : 0;

    // Show first question or last answered question

    showQuestion(lastAnsweredIndex);

    // Start timer
    startTimer();

    // Initialize event listeners
    initializeEventListeners();

    // Start periodic save
    startPeriodicSave();

    // Final force update submit button after all initialization
    const finalForceUpdateSubmitButton = () => {
      updateSubmitButton();
      const submitBtn = document.getElementById("submit-btn");
      const answeredCount = Object.keys(userAnswers).length;

      if (submitBtn && answeredCount > 0) {
        submitBtn.disabled = false;
        console.log("✅ Submit button final force enabled");
      }
    };

    // Final multiple attempts after full initialization
    setTimeout(finalForceUpdateSubmitButton, 100);
    setTimeout(finalForceUpdateSubmitButton, 300);
    setTimeout(finalForceUpdateSubmitButton, 800);

    console.log("✅ Thi thử đã được khởi tạo thành công");
  }

  function createQuestionNavigation() {
    const questionList = document.getElementById("question-list");
    questionList.innerHTML = "";

    practiceQuestions.forEach((_, index) => {
      const questionItem = document.createElement("button");
      questionItem.className =
        "w-8 h-8 rounded border-2 border-gray-300 bg-white text-sm font-medium hover:bg-gray-50";
      questionItem.textContent = index + 1;
      questionItem.id = `question-nav-${index}`;
      questionItem.onclick = () => goToQuestion(index);
      questionList.appendChild(questionItem);
    });
  }

  function showQuestion(index) {
    if (index < 0 || index >= practiceQuestions.length) {
      console.error(
        `❌ Invalid question index: ${index} (max: ${
          practiceQuestions.length - 1
        })`
      );
      return;
    }

    const container = document.getElementById("questions-container");
    if (!container) return;

    // Add fade out animation
    container.style.opacity = "0";
    container.style.transform = "translateX(10px)";

    // Use timeout to allow fade out animation to complete
    setTimeout(() => {
      currentQuestionIndex = index;
      const question = practiceQuestions[index];

      // Update question counter
      const currentQuestionEl = document.getElementById("current-question");
      const currentQuestionMainEl = document.getElementById(
        "current-question-main"
      );

      if (currentQuestionEl) {
        currentQuestionEl.textContent = index + 1;
      }
      if (currentQuestionMainEl) {
        currentQuestionMainEl.textContent = index + 1;
      }

      // Update navigation buttons
      document.getElementById("prev-btn").disabled = index === 0;
      document.getElementById("next-btn").style.display =
        index === practiceQuestions.length - 1 ? "none" : "block";

      // Update navigation highlight
      updateQuestionNavigation();

      // Render question
      renderQuestion(question, index);

      // Update progress
      updateProgress();

      // Update submit button
      updateSubmitButton();

      // Save current progress to localStorage
      savePracticeDataToStorage();

      // Add fade in animation
      setTimeout(() => {
        container.style.opacity = "1";
        container.style.transform = "translateX(0)";
      }, 50);
    }, 200); // Wait for fade out to complete
  }

  function renderQuestion(question, index) {
    const container = document.getElementById("questions-container");
    if (!container) {
      console.error("❌ questions-container element not found!");
      return;
    }

    const questionId = `question_${index}`;

    let optionsHtml = "";
    if (question.options && question.options.length > 0) {
      optionsHtml = question.options
        .map((option, optionIndex) => {
          const optionId = `${questionId}_${optionIndex}`;
          const isChecked = userAnswers[index] === optionIndex;

          // Loại bỏ tiền tố "A.", "B.", "C.", "D." nếu có
          let cleanOptionText = option.text;
          // Sử dụng regex để tìm và loại bỏ tiền tố theo nhiều định dạng:
          // 1. Chữ cái + dấu chấm: A. B. C. D.
          // 2. Chữ cái + dấu ngoặc đóng: A) B) C) D)
          // 3. Số + dấu chấm: 1. 2. 3. 4.
          // 4. Số + dấu ngoặc đóng: 1) 2) 3) 4)
          // Bắt đầu bằng whitespace (nếu có), theo sau là chữ cái hoặc số, rồi đến dấu chấm hoặc dấu đóng ngoặc
          const prefixRegex = /^\s*(?:[A-D]|[1-9])[\.\)][\s\u00A0]*/i;
          if (prefixRegex.test(cleanOptionText)) {
            cleanOptionText = cleanOptionText.replace(prefixRegex, "");
          }

          return `
        <div class="flex items-start p-3 rounded-lg border border-gray-200 hover:border-gray-300 hover:bg-gray-50 transition-colors ${
          isChecked ? "border-orange-500 bg-orange-50" : ""
        }">
          <input
            type="radio"
            id="${optionId}"
            name="${questionId}"
            value="${optionIndex}"
            class="mt-1 mr-3 h-4 w-4 text-orange-600 focus:ring-orange-500 flex-shrink-0"
            ${isChecked ? "checked" : ""}
            onchange="selectAnswer(${index}, ${optionIndex})"
          />
          <label for="${optionId}" class="text-sm md:text-base text-gray-700 leading-relaxed cursor-pointer flex-grow break-words">
            ${cleanOptionText}
          </label>
        </div>
      `;
        })
        .join("");
    }

    container.innerHTML = `
    <div class="bg-white rounded-lg shadow-sm p-4 md:p-6 flex-grow min-h-0">
      <div class="mb-4">
        <h3 class="text-base md:text-lg font-medium text-gray-800 mb-3 leading-relaxed break-words">
          Câu ${index + 1}: ${question.text || "Không có nội dung câu hỏi"}
        </h3>
        ${
          question.image
            ? `<img src="${question.image}" alt="Hình ảnh câu hỏi" class="max-w-full h-auto mb-4 rounded-lg">`
            : ""
        }
      </div>
      <div class="space-y-2 overflow-y-auto max-h-96">
        ${optionsHtml}
      </div>
    </div>
  `;
  }

  function selectAnswer(questionIndex, optionIndex) {
    userAnswers[questionIndex] = optionIndex;

    // Update navigation immediately
    updateSingleNavigationItem(questionIndex);

    updateProgress();
    updateSubmitButton();

    // Save to localStorage after user selects an answer
    savePracticeDataToStorage();

    // Also sync to server periodically for cross-device support
    if (!window.nextSyncTimeout) {
      window.nextSyncTimeout = setTimeout(() => {
        syncAnswersToServer();
        window.nextSyncTimeout = null;
      }, 5000); // Sync after 5 seconds of no activity
    }
  }

  function updateSingleNavigationItem(index) {
    const navItem = document.getElementById(`question-nav-${index}`);
    if (navItem) {
      // Reset classes - no transition to prevent flicker
      navItem.className = "w-8 h-8 rounded border-2 text-sm font-medium";

      if (index === currentQuestionIndex) {
        // Current question
        navItem.className += " border-orange-500 bg-orange-500 text-white";
      } else if (userAnswers.hasOwnProperty(index)) {
        // Answered question
        navItem.className += " border-green-500 bg-green-500 text-white";
      } else {
        // Unanswered question
        navItem.className +=
          " border-gray-300 bg-white text-gray-700 hover:bg-gray-50";
      }
    }
  }

  function updateQuestionNavigation() {
    practiceQuestions.forEach((_, index) => {
      updateSingleNavigationItem(index);
    });
  }

  function updateProgress() {
    const answeredCount = Object.keys(userAnswers).length;
    const totalQuestions = practiceQuestions.length;
    const progressPercent = (answeredCount / totalQuestions) * 100;

    document.getElementById("answered-questions").textContent = answeredCount;

    // Add smooth animation to progress bar
    const progressBar = document.getElementById("progressBar");
    if (progressBar) {
      progressBar.style.transition = "width 0.4s ease";
      progressBar.style.width = `${progressPercent}%`;
    }
  }

  function updateSubmitButton() {
    const answeredCount = Object.keys(userAnswers).length;
    const totalQuestions = practiceQuestions.length;
    const submitBtn = document.getElementById("submit-btn");

    if (!submitBtn) {
      console.warn("⚠️ Submit button not found in updateSubmitButton");
      return;
    }

    // Always enable submit button if there are any answers
    submitBtn.disabled = false;

    if (answeredCount === totalQuestions) {
      submitBtn.innerHTML = '<i class="fas fa-paper-plane mr-2"></i>Nộp bài';
    } else {
      submitBtn.innerHTML = `<i class="fas fa-paper-plane mr-2"></i>Nộp bài (${answeredCount}/${totalQuestions})`;
    }
  }

  function nextQuestion() {
    if (currentQuestionIndex < practiceQuestions.length - 1) {
      showQuestion(currentQuestionIndex + 1);
    }
  }

  function previousQuestion() {
    if (currentQuestionIndex > 0) {
      showQuestion(currentQuestionIndex - 1);
    }
  }

  function goToQuestion(index) {
    showQuestion(index);
  }

  function submitExam() {
    const answeredCount = Object.keys(userAnswers).length;
    const totalQuestions = practiceQuestions.length;

    // Add loading animation to submit button
    const submitBtn = document.getElementById("submit-btn");
    if (submitBtn) {
      submitBtn.disabled = true;
      submitBtn.innerHTML =
        '<i class="fas fa-spinner fa-spin mr-2"></i>Đang xử lý...';
      submitBtn.style.transform = "scale(0.98)";
      submitBtn.style.opacity = "0.8";
    }

    if (answeredCount < totalQuestions) {
      showUnansweredWarning();
      return;
    }

    finishExam();
  }

  function showUnansweredWarning() {
    const unansweredQuestions = [];
    for (let i = 0; i < practiceQuestions.length; i++) {
      if (!userAnswers.hasOwnProperty(i)) {
        unansweredQuestions.push(i + 1);
      }
    }

    const unansweredContainer = document.getElementById("unanswered-questions");
    unansweredContainer.innerHTML = unansweredQuestions
      .map(
        (num) =>
          `<button class="inline-block bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-sm mr-1 mb-1 hover:bg-yellow-200" onclick="goToQuestion(${
            num - 1
          }); closeWarningPopup();">${num}</button>`
      )
      .join("");

    document.getElementById("warning-popup").classList.remove("hidden");
  }

  function closeWarningPopup() {
    document.getElementById("warning-popup").classList.add("hidden");
  }

  function forceSubmitExam() {
    closeWarningPopup();
    finishExam();
  }

  function finishExam() {
    // Mark exam as completed to prevent beforeunload warning
    window.examCompleted = true;

    // Stop timer
    if (timerInterval) {
      clearInterval(timerInterval);
    }

    // Stop periodic save
    stopPeriodicSave();

    // Clear localStorage when exam is finished
    clearPracticeDataFromStorage();

    // Calculate results
    const results = calculateResults();

    // Save results
    savePracticeResults(results);

    // Show results
    showResults(results);
  }

  function calculateResults() {
    let correctCount = 0;
    const totalQuestions = practiceQuestions.length;
    const endTime = Date.now();
    const duration = Math.floor((endTime - startTime) / 1000); // in seconds

    const detailedAnswers = [];

    for (let i = 0; i < practiceQuestions.length; i++) {
      const question = practiceQuestions[i];

      // Validate question exists
      if (!question) {
        console.warn(`⚠️ Question ${i} is undefined, skipping...`);
        continue;
      }

      if (!question.text) {
        console.warn(`⚠️ Question ${i} missing text:`, question);
      }

      const userAnswerIndex = userAnswers[i];
      let isCorrect = false;

      if (
        userAnswerIndex !== undefined &&
        question.options &&
        question.options[userAnswerIndex]
      ) {
        isCorrect = question.options[userAnswerIndex].isCorrect;
        if (isCorrect) correctCount++;
      }

      detailedAnswers.push({
        questionIndex: i,
        question: question.text || `Câu hỏi ${i + 1}`,
        userAnswerIndex: userAnswerIndex,
        userAnswerText:
          userAnswerIndex !== undefined &&
          question.options &&
          question.options[userAnswerIndex]
            ? question.options[userAnswerIndex].text
            : "Không trả lời",
        correctAnswerIndex: question.options
          ? question.options.findIndex((opt) => opt.isCorrect)
          : -1,
        correctAnswerText: question.options
          ? question.options.find((opt) => opt.isCorrect)?.text || "N/A"
          : "N/A",
        isCorrect: isCorrect,
      });
    }

    const score = Math.round((correctCount / totalQuestions) * 100);

    return {
      score,
      correctCount,
      totalQuestions,
      duration,
      detailedAnswers,
    };
  }

  async function savePracticeResults(results) {
    try {
      const saveData = {
        courseId: practiceInfo.courseId,
        courseName: practiceInfo.courseName,
        score: results.score,
        totalQuestions: results.totalQuestions,
        correctAnswers: results.correctCount,
        duration: results.duration,
        selectedQuestions: practiceQuestions
          .map((q) => q._id)
          .filter((id) => id),
        userAnswers: results.detailedAnswers
          .map((answer) => {
            // Lấy câu hỏi và các options
            const question = practiceQuestions[answer.questionIndex];
            const options = question?.options || [];

            // Lấy ID của đáp án được chọn (nếu có)
            let selectedAnswerId = null;
            if (
              answer.userAnswerIndex !== undefined &&
              options[answer.userAnswerIndex]
            ) {
              selectedAnswerId = options[answer.userAnswerIndex]._id;
            }

            return {
              questionId: question._id,
              selectedAnswerId: selectedAnswerId,
              isCorrect: answer.isCorrect,
            };
          })
          .filter(
            (answer) => answer.questionId && answer.selectedAnswerId !== null
          ),
      };

      const response = await fetch("/exam/practice/save-result", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(saveData),
      });

      const result = await response.json();
      if (result.success) {
        console.log("✅ Đã lưu kết quả thi thử thành công");
      } else {
        console.error("❌ Lỗi lưu kết quả:");
      }
    } catch (error) {
      console.error("❌ Lỗi khi lưu kết quả thi thử:", error);
    }
  }

  function showResults(results) {
    const examContainer = document.getElementById("exam-container");
    const resultContainer = document.getElementById("result-container");
    const progressSection = document.getElementById("progress-section");
    const questionNavSection = document.getElementById("question-nav-section");

    // Add fade out animation to exam container
    examContainer.style.transition = "opacity 0.5s ease, transform 0.5s ease";
    examContainer.style.opacity = "0";
    examContainer.style.transform = "scale(0.95)";

    // Add fade out animation to sidebar sections
    if (progressSection) {
      progressSection.style.transition = "opacity 0.3s ease";
      progressSection.style.opacity = "0";
    }

    if (questionNavSection) {
      questionNavSection.style.transition = "opacity 0.3s ease";
      questionNavSection.style.opacity = "0";
    }

    setTimeout(() => {
      // Hide exam container
      examContainer.classList.add("hidden");

      // Hide progress and question navigation sections
      if (progressSection) {
        progressSection.classList.add("hidden");
      }

      if (questionNavSection) {
        questionNavSection.classList.add("hidden");
      }

      // Show results container with initial hidden state
      resultContainer.style.transition =
        "opacity 0.6s ease, transform 0.6s ease";
      resultContainer.style.opacity = "0";
      resultContainer.style.transform = "translateY(20px) scale(0.98)";
      resultContainer.classList.remove("hidden");

      // Update result displays
      document.getElementById("score-percentage").textContent = results.score;
      document.getElementById("correct-count").textContent =
        results.correctCount;
      document.getElementById("total-count").textContent =
        results.totalQuestions;
      document.getElementById("examTime").textContent = formatTime(
        results.duration
      );

      // Create summary
      createResultSummary(results.detailedAnswers);

      // Create detailed review
      createDetailedReview(results.detailedAnswers);

      // Add fade in animation for results
      setTimeout(() => {
        resultContainer.style.opacity = "1";
        resultContainer.style.transform = "translateY(0) scale(1)";
      }, 100);
    }, 500); // Wait for fade out animation to complete
  }

  function createResultSummary(answers) {
    const container = document.getElementById("summaryContainer");
    container.innerHTML = "";

    answers.forEach((answer, index) => {
      const summaryItem = document.createElement("div");
      summaryItem.className = `w-8 h-8 rounded flex items-center justify-center text-xs font-medium cursor-pointer hover:opacity-80 transition-opacity ${
        answer.isCorrect ? "bg-green-500 text-white" : "bg-red-500 text-white"
      }`;
      summaryItem.textContent = index + 1;
      summaryItem.title = answer.isCorrect ? "Đúng" : "Sai";

      // Add click event to scroll to question
      summaryItem.onclick = () => scrollToQuestion(index);

      container.appendChild(summaryItem);
    });
  }

  function createDetailedReview(answers) {
    const container = document.getElementById("answerResults");
    container.innerHTML = "";

    answers.forEach((answer, index) => {
      const reviewItem = document.createElement("div");
      reviewItem.className = "border rounded-lg p-4 scroll-target";
      reviewItem.id = `question-review-${index}`;

      // Get the question options from practiceQuestions
      const question = practiceQuestions[index];
      const options = question?.options || [];

      // Create options HTML with highlighting
      let optionsHtml = "";
      if (options.length > 0) {
        const optionLabels = ["A", "B", "C", "D", "E", "F"];

        optionsHtml = options
          .map((option, optionIndex) => {
            const label = optionLabels[optionIndex] || optionIndex + 1;
            const isUserAnswer = answer.userAnswerIndex === optionIndex;
            const isCorrectAnswer = option.isCorrect;

            // Loại bỏ tiền tố "A.", "B.", "C.", "D." nếu có
            let cleanOptionText = option.text;
            const prefixRegex = /^\s*(?:[A-D]|[1-9])[\.\)][\s\u00A0]*/i;
            if (prefixRegex.test(cleanOptionText)) {
              cleanOptionText = cleanOptionText.replace(prefixRegex, "");
            }

            let optionClass = "p-2 rounded border ";
            let textClass = "";
            let icon = "";

            if (isCorrectAnswer) {
              optionClass += "bg-green-50 border-green-200 ";
              textClass = "text-green-700 font-medium";
              icon = '<i class="fas fa-check text-green-600 ml-2"></i>';
            } else if (isUserAnswer && !isCorrectAnswer) {
              optionClass += "bg-red-50 border-red-200 ";
              textClass = "text-red-700 font-medium";
              icon = '<i class="fas fa-times text-red-600 ml-2"></i>';
            } else if (isUserAnswer) {
              optionClass += "bg-green-50 border-green-200 ";
              textClass = "text-green-700 font-medium";
              icon = '<i class="fas fa-check text-green-600 ml-2"></i>';
            } else {
              optionClass += "bg-gray-50 border-gray-200 ";
              textClass = "text-gray-600";
            }

            return `
            <div class="${optionClass}">
              <span class="${textClass}">
                <strong>${label}.</strong> ${cleanOptionText}${icon}
              </span>
            </div>
          `;
          })
          .join("");
      }

      reviewItem.innerHTML = `
      <div class="flex items-start justify-between mb-2">
        <h5 class="font-medium text-gray-800">Câu ${index + 1}</h5>
        <span class="px-2 py-1 rounded text-xs font-medium ${
          answer.isCorrect
            ? "bg-green-100 text-green-800"
            : "bg-red-100 text-red-800"
        }">
          ${answer.isCorrect ? "Đúng" : "Sai"}
        </span>
      </div>
      <p class="text-sm text-gray-600 mb-3">${answer.question}</p>

      ${
        optionsHtml
          ? `
        <div class="mb-3">
          <h6 class="font-medium text-gray-700 mb-2 text-sm">Các lựa chọn:</h6>
          <div class="space-y-2">
            ${optionsHtml}
          </div>
        </div>
      `
          : ""
      }

      ${
        answer.userAnswerIndex === undefined
          ? `
        <div class="text-sm bg-yellow-50 border border-yellow-200 p-3 rounded">
          <span class="text-yellow-700 font-medium">
            <i class="fas fa-exclamation-triangle mr-2"></i>Không trả lời
          </span>
        </div>
      `
          : ""
      }
    `;

      container.appendChild(reviewItem);
    });
  }

  function scrollToQuestion(questionIndex) {
    const targetElement = document.getElementById(
      `question-review-${questionIndex}`
    );
    const container = document.getElementById("answerResults");

    if (targetElement && container) {
      // Calculate position to scroll to
      const containerRect = container.getBoundingClientRect();
      const targetRect = targetElement.getBoundingClientRect();
      const scrollTop =
        container.scrollTop + targetRect.top - containerRect.top - 20; // 20px offset

      // Smooth scroll to the target
      container.scrollTo({
        top: scrollTop,
        behavior: "smooth",
      });

      // Highlight the target briefly
      targetElement.style.transition = "all 0.3s ease";
      targetElement.style.backgroundColor = "#FEF3C7"; // yellow-100
      targetElement.style.borderColor = "#F59E0B"; // yellow-500

      setTimeout(() => {
        targetElement.style.backgroundColor = "";
        targetElement.style.borderColor = "";
      }, 1500);

      console.log(`📍 Scrolled to question ${questionIndex + 1}`);
    }
  }

  function startTimer() {
    const timerElement = document.getElementById("timer");
    let timeRemaining = timeLimit;

    timerInterval = setInterval(() => {
      timeRemaining -= 1000;

      if (timeRemaining <= 0) {
        // Time's up
        clearInterval(timerInterval);
        showTimeUpModal();
        return;
      }

      // Update timer display
      const hours = Math.floor(timeRemaining / (1000 * 60 * 60));
      const minutes = Math.floor(
        (timeRemaining % (1000 * 60 * 60)) / (1000 * 60)
      );
      const seconds = Math.floor((timeRemaining % (1000 * 60)) / 1000);

      timerElement.textContent = `${hours.toString().padStart(2, "0")}:${minutes
        .toString()
        .padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;

      // Change color when time is running low (last 5 minutes)
      if (timeRemaining <= 5 * 60 * 1000) {
        timerElement.parentElement.className =
          "bg-red-700 px-3 py-1 rounded-full flex items-center animate-pulse";
      }
    }, 1000);
  }

  function showTimeUpModal() {
    document.getElementById("time-up-modal").classList.remove("hidden");
  }

  function autoSubmitExam() {
    // Mark exam as completed to prevent beforeunload warning
    window.examCompleted = true;

    document.getElementById("time-up-modal").classList.add("hidden");

    // Clear localStorage when time is up
    clearPracticeDataFromStorage();

    finishExam();
  }

  function formatTime(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, "0")}:${secs
        .toString()
        .padStart(2, "0")}`;
    } else {
      return `${minutes}:${secs.toString().padStart(2, "0")}`;
    }
  }

  function initializeEventListeners() {
    // Warning popup buttons
    document.getElementById("continue-button").onclick = closeWarningPopup;
    document.getElementById("force-submit-button").onclick = forceSubmitExam;

    // Time up modal button
    document.getElementById("auto-submit-button").onclick = autoSubmitExam;

    // Keyboard shortcuts
    document.addEventListener("keydown", function (e) {
      if (e.target.type === "radio") return; // Don't interfere with radio button navigation

      switch (e.key) {
        case "ArrowLeft":
          e.preventDefault();
          previousQuestion();
          break;
        case "ArrowRight":
          e.preventDefault();
          nextQuestion();
          break;
        case "Enter":
          e.preventDefault();
          if (e.ctrlKey) {
            submitExam();
          }
          break;
      }
    });

    // Save data when user leaves the page
    window.addEventListener("beforeunload", function (e) {
      savePracticeDataToStorage();
      console.log("💾 Saved data before page unload");

      // Show warning like Google Forms if exam is in progress
      if (practiceQuestions.length > 0 && !window.examCompleted) {
        const message =
          "Bạn có chắc muốn rời khỏi trang? Tiến độ làm bài đã được lưu và bạn có thể tiếp tục sau.";
        e.preventDefault();
        e.returnValue = message;
        return message;
      }
    });
  }

  function showError(message) {
    document.getElementById("loading-screen").innerHTML = `
    <div class="text-center">
      <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
        <i class="fas fa-exclamation-triangle text-red-600 text-2xl"></i>
      </div>
      <h3 class="text-lg font-medium text-gray-700 mb-2">Có lỗi xảy ra</h3>
      <p class="text-gray-500 mb-4">${message}</p>
      <button onclick="window.location.href='/home'" class="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700">
        Về trang chủ
      </button>
    </div>
  `;
  }

  // Function để thi lại - tạo bài thi mới
  async function startNewPracticeExam() {
    const productId = window.location.pathname.split("/")[2]; // Lấy productId từ URL

    console.log("🔄 Bắt đầu thi lại - tạo bài thi mới...");

    try {
      // Clear localStorage cũ
      clearPracticeDataFromStorage();

      // Tạo bài thi mới
      const response = await fetch(`/exam/practice/${productId}/start`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify({
          action: "new",
        }),
      });

      const result = await response.json();

      if (result.success && result.practiceId) {
        console.log("✅ Đã tạo bài thi mới:", result.practiceId);

        // Chuyển hướng đến bài thi mới
        const newPracticeUrl = `/course/${productId}/${result.practiceId}/practice-exam`;
        window.location.href = newPracticeUrl;
      } else {
        throw new Error(result.message || "Không thể tạo bài thi mới");
      }
    } catch (error) {
      console.error("❌ Lỗi thi lại:", error);
      alert("Có lỗi khi tạo bài thi mới. Vui lòng thử lại.");
    }
  }
</script>

<!-- Security measures -->
<script src="/js/security-measures.js"></script>
<script>
  // Mobile menu functionality
  function initMobileMenu() {
    const mobileMenuBtn = document.getElementById("mobile-menu-btn");
    const mobileSidebar = document.getElementById("mobile-sidebar");
    const mobileOverlay = document.getElementById("mobile-overlay");

    if (mobileMenuBtn && mobileSidebar && mobileOverlay) {
      mobileMenuBtn.addEventListener("click", () => {
        mobileSidebar.classList.toggle("active");
        mobileOverlay.classList.toggle("hidden");
        document.body.style.overflow = mobileSidebar.classList.contains(
          "active"
        )
          ? "hidden"
          : "";
      });

      mobileOverlay.addEventListener("click", () => {
        mobileSidebar.classList.remove("active");
        mobileOverlay.classList.add("hidden");
        document.body.style.overflow = "";
      });

      // Close on escape key
      document.addEventListener("keydown", (e) => {
        if (e.key === "Escape" && !mobileOverlay.classList.contains("hidden")) {
          mobileSidebar.classList.remove("active");
          mobileOverlay.classList.add("hidden");
          document.body.style.overflow = "";
        }
      });
    }
  }

  document.addEventListener("DOMContentLoaded", function () {
    // Initialize mobile menu
    initMobileMenu();
    GlobalSecurityMeasures({
      contextMessage: "làm bài thi thử",
      enableDevToolsDetection: true,
      enableScreenshotBlocking: true,
      enableRightClickBlocking: true,
      enableCopyBlocking: true,
      enablePrintBlocking: true,
      enableViewSourceBlocking: true,
      enableSavePageBlocking: true,
      enableDragDropBlocking: true,
      devToolsThreshold: 160,
      redirectOnDevTools: false,
      redirectUrl: "/home",
    });
  });
</script>
