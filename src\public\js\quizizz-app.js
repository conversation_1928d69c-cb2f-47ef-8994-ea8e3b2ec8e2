// Quizizz - File JavaScript chính

// Khởi tạo biến và khởi tạo
let currentQuestionIndex = 0;
let score = 0;
let userAnswers = [];
let examDuration = 30 * 60; // Mặc định 30 phút (sẽ được cập nhật từ dữ liệu bài thi)
let remainingTime = examDuration;
let startTime = Date.now();
let timerInterval;
let nextQuestionTimeout;
let wrongQuestions = [];
let isSubmitting = false;
let isExitPopupVisible = false;
let transitionTime = 3000; // Thời gian mặc định cho việc chuyển câu hỏi
let soundEnabled = true; // Biến kiểm soát bật/tắt âm thanh (mặc định là bật)
// Biến cho tính năng làm lại câu sai
let questionsProcessed = 0; // Số câu đã làm
let incorrectQuestions = []; // <PERSON><PERSON><PERSON> câu trả lời sai
let retryMode = false; // Đang ở chế độ làm lại hay không
let questionToRetry = null; // Câu hỏi cần làm lại
let originalQuestionIndex = null; // Lưu vị trí ban đầu của câu hỏi đang làm lại
let justRetried = false; // Đánh dấu người dùng vừa làm lại một câu để tránh hiện thông báo làm lại liên tục

// Khởi tạo dữ liệu từ JSON script
let examData;

// Khởi tạo ứng dụng khi DOM đã sẵn sàng
document.addEventListener("DOMContentLoaded", initQuizizz);

// Khởi tạo ứng dụng
function initQuizizz() {
  // Lấy dữ liệu từ JSON script
  const examDataElement = document.getElementById("exam-data");
  examData = JSON.parse(examDataElement.textContent);

  // Lấy thời gian làm bài từ dữ liệu
  const durationElement = document.getElementById("exam-duration");
  if (durationElement) {
    examDuration = parseInt(durationElement.textContent) * 60;
    remainingTime = examDuration;
  }

  // Xử lý sự kiện khi người dùng thay đổi thời gian chuyển câu
  setupTransitionTimeControl();

  // Xử lý sự kiện bật/tắt âm thanh
  setupSoundToggle();

  // Hiển thị câu hỏi đầu tiên
  showQuestion(currentQuestionIndex);

  // Khởi động bộ đếm thời gian
  timerInterval = setInterval(updateTimer, 1000);
  updateTimer(); // Cập nhật lần đầu ngay lập tức

  // Thêm sự kiện cho nút nộp bài
  document
    .getElementById("submitExamButton")
    .addEventListener("click", function () {
      // Hiển thị popup xác nhận nộp bài
      showExitConfirmPopup(
        "Xác nhận nộp bài",
        "Bạn có chắc chắn muốn nộp bài? Thời gian làm bài còn lại sẽ bị hủy.",
        function () {
          isSubmitting = true;
          showResult();
        }
      );
    });

  // Xử lý sự kiện nút làm lại câu hỏi sai
  document.addEventListener("click", function (e) {
    if (e.target && e.target.id === "retryWrongAnswers") {
      handleRetryWrongQuestions();
    }
  });

  // Cấu hình sự kiện ngăn chặn rời khỏi trang
  setupPageLeaveHandlers();

  // Khởi tạo âm thanh
  setupAudio();

  console.log("Khởi tạo Quizizz thành công");
}

// Thiết lập điều khiển thời gian chuyển câu
function setupTransitionTimeControl() {
  const transitionSelect = document.getElementById("transitionTime");
  if (transitionSelect) {
    // Đặt giá trị mặc định từ localStorage hoặc giá trị mặc định
    const savedTransitionTime = localStorage.getItem("quizizzTransitionTime");
    if (savedTransitionTime) {
      transitionSelect.value = savedTransitionTime;
      transitionTime = parseInt(savedTransitionTime, 10);
    }

    // Xử lý sự kiện thay đổi
    transitionSelect.addEventListener("change", function () {
      transitionTime = parseInt(this.value, 10);
      // Lưu vào localStorage để duy trì giữa các phiên
      localStorage.setItem("quizizzTransitionTime", transitionTime);
      console.log("Đã thay đổi thời gian chuyển câu hỏi:", transitionTime);
    });
  }
}

// Thiết lập điều khiển âm thanh
function setupSoundToggle() {
  const soundToggleBtn = document.getElementById("soundToggleBtn");
  const soundIcon = document.getElementById("soundIcon");

  if (soundToggleBtn && soundIcon) {
    // Đặt giá trị từ localStorage hoặc mặc định là bật
    const savedSoundSetting = localStorage.getItem("quizizzSoundEnabled");
    if (savedSoundSetting !== null) {
      soundEnabled = savedSoundSetting === "true";
      // Cập nhật icon theo trạng thái
      updateSoundIcon();
    }

    // Xử lý sự kiện thay đổi
    soundToggleBtn.addEventListener("click", function () {
      soundEnabled = !soundEnabled;
      // Lưu vào localStorage để duy trì giữa các phiên
      localStorage.setItem("quizizzSoundEnabled", soundEnabled);
      console.log("Âm thanh đã được " + (soundEnabled ? "bật" : "tắt"));

      // Cập nhật icon âm thanh
      updateSoundIcon();

      // Đồng bộ trạng thái âm thanh
      window.soundEnabled = soundEnabled;
    });
  }

  // Hàm cập nhật icon âm thanh
  function updateSoundIcon() {
    if (soundEnabled) {
      soundIcon.className = "fas fa-volume-up text-lg";
      soundToggleBtn.setAttribute("title", "Tắt âm thanh");
    } else {
      soundIcon.className = "fas fa-volume-mute text-lg text-gray-400";
      soundToggleBtn.setAttribute("title", "Bật âm thanh");
    }
  }
}

// Thiết lập xử lý rời khỏi trang
function setupPageLeaveHandlers() {
  // Xử lý sự kiện khi người dùng sử dụng nút Back/Forward của trình duyệt
  window.addEventListener("popstate", function (event) {
    console.log("Sự kiện popstate đã kích hoạt!", event);

    if (!isSubmitting && !isExitPopupVisible) {
      // Ngăn chặn hành vi mặc định của trình duyệt
      event.preventDefault();

      // Hiển thị popup xác nhận
      showExitConfirmPopup(
        "Bạn đang cố gắng rời khỏi trang",
        "Sử dụng nút Quay lại của trình duyệt sẽ kết thúc bài thi của bạn. Bạn có muốn tiếp tục?"
      );

      // Thêm một mục vào lịch sử để ngăn chặn việc quay lại
      history.pushState(null, null, window.location.pathname);
    }
  });

  // Xử lý sự kiện khi người dùng nhấn các phím tắt để tải lại trang
  document.addEventListener("keydown", function (event) {
    // Phát hiện Ctrl+R, F5, hoặc Ctrl+F5
    if (
      !isSubmitting &&
      !isExitPopupVisible &&
      ((event.ctrlKey && event.key === "r") ||
        event.key === "F5" ||
        (event.ctrlKey && event.key === "F5"))
    ) {
      console.log("Phát hiện phím tắt tải lại trang:", event.key);

      // Ngăn chặn hành vi mặc định
      event.preventDefault();

      // Hiển thị popup xác nhận
      showExitConfirmPopup(
        "Bạn đang cố gắng tải lại trang",
        "Tải lại trang sẽ kết thúc bài thi của bạn. Bạn có muốn tiếp tục?"
      );

      return false;
    }
  });

  // Ngăn chặn menu chuột phải để tránh người dùng chọn "Tải lại" từ menu
  document.addEventListener("contextmenu", function (event) {
    // Chỉ ngăn chặn trong khi làm bài thi
    if (!isSubmitting && !isExitPopupVisible) {
      event.preventDefault();
      return false;
    }
  });

  // Thêm một mục vào lịch sử ngay khi trang tải để ngăn chặn nút Back
  history.pushState(null, null, window.location.pathname);

  // Xử lý sự kiện khi người dùng tải lại trang hoặc đóng tab
  window.addEventListener("beforeunload", function (event) {
    console.log("Sự kiện beforeunload được kích hoạt!");

    if (!isSubmitting) {
      // Ghi chú: Popup tùy chỉnh không hoạt động với beforeunload
      // Thay vào đó, chúng tôi sẽ hiển thị hộp thoại xác nhận tiêu chuẩn của trình duyệt
      event.preventDefault();
      event.returnValue =
        "Bạn có chắc chắn muốn rời khỏi trang? Việc này sẽ kết thúc bài thi của bạn.";
      return event.returnValue;
    }
  });
}

// Thiết lập âm thanh cho ứng dụng
function setupAudio() {
  // Khởi tạo âm thanh
  window.correctSound = new Audio("/media/correct_answer.mp3");
  window.wrongSound = new Audio("/media/wrong_answer.mp3");

  // Đảm bảo âm thanh được nạp trước
  window.correctSound.preload = "auto";
  window.wrongSound.preload = "auto";

  // Đồng bộ trạng thái âm thanh
  window.soundEnabled = soundEnabled;

  // Thêm hàm dừng tất cả âm thanh
  window.stopAllSounds = function () {
    try {
      if (window.correctSound) {
        window.correctSound.pause();
        window.correctSound.currentTime = 0;
      }
      if (window.wrongSound) {
        window.wrongSound.pause();
        window.wrongSound.currentTime = 0;
      }
    } catch (error) {
      console.error("Lỗi khi dừng âm thanh:", error);
    }
  };

  // Thêm hàm phát âm thanh vào window object để có thể gọi từ bất kỳ đâu
  window.playSound = function (isCorrect) {
    if (!soundEnabled) return;

    // Dừng tất cả âm thanh trước khi phát
    window.stopAllSounds();

    try {
      if (isCorrect) {
        window.correctSound.currentTime = 0;
        window.correctSound.play().catch((error) => {
          console.error("Lỗi phát âm thanh đúng:", error);
        });
      } else {
        window.wrongSound.currentTime = 0;
        window.wrongSound.play().catch((error) => {
          console.error("Lỗi phát âm thanh sai:", error);
        });
      }
    } catch (error) {
      console.error("Lỗi khi phát âm thanh:", error);
    }
  };

  // Thêm event listener để kích hoạt audio khi có tương tác người dùng
  document.addEventListener("click", initAudio, { once: true });

  // Kích hoạt audio
  function initAudio() {
    const silentAudio = new Audio(
      "data:audio/mp3;base64,SUQzBAAAAAAAI1RTU0UAAAAPAAADTGF2ZjU4LjM1LjEwNAAAAAAAAAAAAAAA//tAwAAAAAAAAAAAAAAAAAAAAAAASW5mbwAAAA8AAAACAAABIADf/////////////////////////////////////////wAAADlMQU1FMy4xMDABzQAAAAAAAAAAFIAkCLhCAABAAAABIDVjN9YAAAAAAAAAAAAAAAAAAAAAAP/7QMQAAAf4XkAAAgAAA0gAAABBAAADSAAAAETEFNRTMuMTAwVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVf/7QMRVA8AAAaQAAAABg2gAAABFVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVQ=="
    );
    silentAudio
      .play()
      .then(() => {
        console.log("Audio được kích hoạt thành công");
        // Nạp file âm thanh
        window.correctSound.load();
        window.wrongSound.load();
      })
      .catch((e) => {
        console.error("Không thể kích hoạt audio:", e);
      });
  }
}
