"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importStar(require("mongoose"));
const auth_config_1 = require("../config/auth-config");
const sessionSchema = new mongoose_1.Schema({
    userId: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: "User",
        required: true,
        index: true,
    },
    token: {
        type: String,
        required: true,
        unique: true,
    },
    clientId: {
        type: String,
        required: true,
        index: true,
    },
    deviceInfo: {
        userAgent: String,
        ip: String,
        deviceName: String,
        browser: String,
        os: String,
    },
    isActive: {
        type: Boolean,
        default: true,
        index: true,
    },
    isCurrentDevice: {
        type: Boolean,
        default: false,
    },
    lastActive: {
        type: Date,
        default: Date.now,
    },
    expiresAt: {
        type: Date,
        required: true,
        index: true,
    },
}, {
    timestamps: true,
});
// Đánh chỉ mục phức hợp để truy vấn nhanh hơn
sessionSchema.index({ userId: 1, clientId: 1 });
sessionSchema.index({ userId: 1, isActive: 1 });
// Phương thức tĩnh để tạo phiên mới
sessionSchema.statics.createSession = function (userId_1, token_1, clientId_1, deviceInfo_1) {
    return __awaiter(this, arguments, void 0, function* (userId, token, clientId, deviceInfo, expiresIn = auth_config_1.AuthConfig.SESSION.EXPIRATION // Sử dụng cấu hình mới (90 ngày)
    ) {
        // Tạo thời gian hết hạn
        const expiresAt = new Date(Date.now() + expiresIn);
        // Thiết lập thiết bị hiện tại là thiết bị mới nhất
        yield this.updateMany({ userId, isCurrentDevice: true }, { $set: { isCurrentDevice: false } });
        // Tạo phiên mới
        return this.create({
            userId,
            token,
            clientId,
            deviceInfo,
            isActive: true,
            isCurrentDevice: true,
            expiresAt,
        });
    });
};
// Phương thức tĩnh để tìm phiên hợp lệ theo token
sessionSchema.statics.findValidSessionByToken = function (token) {
    return this.findOne({
        token,
        isActive: true,
        expiresAt: { $gt: new Date() },
    });
};
// Phương thức tĩnh để đánh dấu tất cả các phiên khác là không hoạt động
sessionSchema.statics.deactivateOtherSessions = function (userId, currentClientId) {
    return this.updateMany({
        userId,
        clientId: { $ne: currentClientId },
        isActive: true,
    }, {
        $set: { isActive: false },
    });
};
// Phương thức tĩnh để đánh dấu phiên hiện tại là phiên mới nhất
sessionSchema.statics.setCurrentDevice = function (userId, clientId) {
    return this.updateMany({ userId }, { $set: { isCurrentDevice: false } }).then(() => {
        return this.updateOne({ userId, clientId }, { $set: { isCurrentDevice: true } });
    });
};
// Phương thức tĩnh để xóa tất cả các phiên hết hạn
sessionSchema.statics.removeExpiredSessions = function () {
    return this.deleteMany({
        $or: [{ expiresAt: { $lte: new Date() } }, { isActive: false }],
    });
};
exports.default = mongoose_1.default.model("Session", sessionSchema);
//# sourceMappingURL=Session.js.map