"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getStudentCourses = exports.getStudentByEmail = void 0;
const mongoose_1 = __importDefault(require("mongoose"));
// Giả định có model Student
const Student = mongoose_1.default.model("Student");
const getStudentByEmail = (email) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const student = yield Student.findOne({ email }).lean();
        return student;
    }
    catch (error) {
        console.error("Error getting student by email:", error);
        throw error;
    }
});
exports.getStudentByEmail = getStudentByEmail;
const getStudentCourses = (studentId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const student = yield Student.findById(studentId)
            .populate("courses.productId")
            .lean();
        if (!student) {
            return [];
        }
        return student.courses || [];
    }
    catch (error) {
        console.error("Error getting student courses:", error);
        throw error;
    }
});
exports.getStudentCourses = getStudentCourses;
exports.default = {
    getStudentByEmail: exports.getStudentByEmail,
    getStudentCourses: exports.getStudentCourses,
};
//# sourceMappingURL=student.service.js.map