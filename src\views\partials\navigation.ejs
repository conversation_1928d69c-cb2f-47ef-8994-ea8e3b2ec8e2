<% if (user) { %>
<!-- User đ<PERSON> đăng nhập -->
<div class="flex items-center space-x-4">
  <div class="relative">
    <button
      id="profile-button"
      class="flex items-center space-x-3 focus:outline-none"
    >
      <span class="hidden md:inline text-gray-700 font-semibold text-lg"
        ><%= user.displayName %></span
      >
      <img
        src="<%= user.profilePhoto %>"
        alt="Avatar"
        class="w-12 h-12 rounded-full object-cover border-2 border-indigo-300 shadow-md"
      />
    </button>

    <!-- Dropdown Menu -->
    <div
      id="profile-dropdown"
      class="absolute right-0 mt-3 w-56 bg-white rounded-md shadow-lg py-1 z-50 hidden"
    >
      <a
        href="/profile"
        class="block px-4 py-2 text-sm text-gray-700 hover:bg-indigo-50"
      >
        <i class="fas fa-user mr-2"></i> Thông tin tài khoản
      </a>
      <a
        href="/exam/history"
        class="block px-4 py-2 text-sm text-gray-700 hover:bg-indigo-50"
      >
        <i class="fas fa-history mr-2"></i> Lịch sử làm bài thi
      </a>
      <a
        href="/courses"
        class="block px-4 py-2 text-sm text-gray-700 hover:bg-indigo-50"
      >
        <i class="fas fa-book mr-2"></i> Danh sách môn học
      </a>
      <a
        href="/auth/logout"
        class="block px-4 py-2 text-sm text-gray-700 hover:bg-indigo-50"
      >
        <i class="fas fa-sign-out-alt mr-2"></i> Đăng xuất
      </a>
    </div>
  </div>
</div>
<% } else { %>
<!-- User chưa đăng nhập -->
<div class="flex items-center space-x-4">
  <a
    href="/login"
    class="inline-flex items-center px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors"
  >
    <i class="fas fa-sign-in-alt mr-2"></i> Đăng nhập
  </a>
</div>
<% } %>
