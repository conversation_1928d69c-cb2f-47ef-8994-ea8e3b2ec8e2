"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importDefault(require("mongoose"));
const dotenv_1 = __importDefault(require("dotenv"));
const products_1 = __importDefault(require("../models/products"));
const exam_1 = __importDefault(require("../models/exam"));
// Load environment variables
dotenv_1.default.config();
/**
 * Kết nối đến cơ sở dữ liệu MongoDB
 */
function connectToDatabase() {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const mongoUri = process.env.MONGODB_URI || "mongodb://localhost:27017/google-auth";
            yield mongoose_1.default.connect(mongoUri);
            console.log("Đã kết nối thành công đến MongoDB");
        }
        catch (error) {
            console.error("Lỗi kết nối đến MongoDB:", error);
            process.exit(1);
        }
    });
}
/**
 * Cập nhật số lượng câu hỏi cho mỗi môn học
 */
function updateProductQuestionCounts() {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            // Lấy tất cả các môn học
            const products = yield products_1.default.find({});
            console.log(`Tìm thấy ${products.length} môn học cần cập nhật`);
            // Xử lý từng môn học
            for (const product of products) {
                // Lấy tất cả các đề thi thuộc môn học này
                const exams = yield exam_1.default.find({ productId: product._id });
                // Tính tổng số câu hỏi từ tất cả các đề thi
                const totalQuestionCount = exams.reduce((total, exam) => total + (exam.count || 0), 0);
                // Cập nhật trường countQuestion của môn học
                yield products_1.default.updateOne({ _id: product._id }, { $set: { countQuestion: totalQuestionCount } });
                console.log(`Môn học "${product.name}" (ID: ${product._id}) - Tổng số câu hỏi: ${totalQuestionCount} (từ ${exams.length} đề thi)`);
            }
            console.log("Đã cập nhật tổng số câu hỏi cho tất cả môn học thành công!");
        }
        catch (error) {
            console.error("Lỗi khi cập nhật số lượng câu hỏi cho môn học:", error);
        }
        finally {
            // Ngắt kết nối cơ sở dữ liệu sau khi hoàn thành
            yield mongoose_1.default.disconnect();
            console.log("Đã ngắt kết nối khỏi MongoDB");
        }
    });
}
/**
 * Hàm chính để chạy script
 */
function main() {
    return __awaiter(this, void 0, void 0, function* () {
        yield connectToDatabase();
        yield updateProductQuestionCounts();
    });
}
// Chạy script
main().catch((error) => {
    console.error("Lỗi không mong muốn:", error);
    process.exit(1);
});
//# sourceMappingURL=updateProductQuestionCount.js.map