<!DOCTYPE html>
<html lang="vi">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><PERSON>h<PERSON><PERSON> học của tôi</title>
    <!-- TailwindCSS CDN: C<PERSON> cấp các lớp tiện ích cho việc tạo kiểu -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Lucide Icons CDN: Thư viện icon đ<PERSON><PERSON><PERSON> sử dụng trong thiết kế -->
    <script src="https://unpkg.com/lucide@latest"></script>
  </head>
  <body class="bg-gradient-to-br from-blue-50 via-white to-teal-50">
    <div class="min-h-screen py-8">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- <PERSON><PERSON><PERSON><PERSON> thông tin người dùng -->
        <div
          class="bg-white rounded-3xl shadow-xl p-8 mb-8 relative overflow-hidden"
        >
          <!-- <PERSON><PERSON><PERSON> yếu tố trang trí nền -->
          <div
            class="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-blue-100 to-teal-100 rounded-full -translate-y-32 translate-x-32 opacity-50"
          ></div>
          <div
            class="absolute bottom-0 left-0 w-48 h-48 bg-gradient-to-tr from-purple-100 to-pink-100 rounded-full translate-y-24 -translate-x-24 opacity-50"
          ></div>

          <div class="relative z-10">
            <div
              class="flex flex-col md:flex-row items-center md:items-start space-y-6 md:space-y-0 md:space-x-8"
            >
              <!-- Avatar -->
              <div class="relative">
                <img
                  src="https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=150"
                  alt="Tài Nguyễn Văn"
                  class="w-24 h-24 rounded-full object-cover border-4 border-white shadow-lg"
                />
                <div
                  class="absolute -bottom-2 -right-2 bg-green-500 text-white rounded-full p-2"
                >
                  <i data-lucide="check-circle" class="w-4 h-4"></i>
                </div>
              </div>

              <!-- Thông tin người dùng -->
              <div class="flex-1 text-center md:text-left">
                <h1 class="text-3xl font-bold text-gray-900 mb-2">
                  Xin chào, Tài Nguyễn Văn!
                </h1>
                <!-- UPDATED: Container for user details -->
                <div class="space-y-2">
                  <div
                    class="flex items-center justify-center md:justify-start space-x-2 text-gray-600"
                  >
                    <i data-lucide="mail" class="w-4 h-4 text-green-600"></i>
                    <span><EMAIL></span>
                  </div>
                  <div
                    class="flex items-center justify-center md:justify-start space-x-2 text-gray-600"
                  >
                    <i
                      data-lucide="book-open"
                      class="w-4 h-4 text-blue-600"
                    ></i>
                    <span
                      >Tổng số môn học:
                      <span class="font-semibold text-gray-800">5</span></span
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Tiêu đề cho các khóa học đang học -->
        <h2 class="text-2xl font-bold text-gray-800 mb-6 px-4 sm:px-0">
          Môn học đang học
        </h2>

        <!-- Lưới hiển thị các khóa học đang học (Active) -->
        <div
          id="content-active"
          class="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          <!-- Thẻ khóa học 1: Tin học -->
          <div
            class="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 group"
          >
            <div class="relative">
              <img
                src="https://images.pexels.com/photos/4386466/pexels-photo-4386466.jpeg?auto=compress&cs=tinysrgb&w=300"
                alt="Tin học"
                class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
              />
              <div class="absolute top-4 left-4">
                <span
                  class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                >
                  <i data-lucide="play" class="w-3 h-3 mr-1"></i>
                  Đang học
                </span>
              </div>
            </div>
            <div class="p-6">
              <h3
                class="text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors duration-200"
              >
                Tin học
              </h3>
              <div class="mb-4">
                <div class="flex justify-between items-center mb-2">
                  <span class="text-sm font-medium text-gray-700">Tiến độ</span>
                  <span class="text-sm font-bold text-gray-900">75%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div
                    class="h-2 rounded-full transition-all duration-300 bg-blue-500"
                    style="width: 75%"
                  ></div>
                </div>
                <div class="flex justify-between text-xs text-gray-500 mt-1">
                  <span>18/24 bài học</span>
                  <span>32 giờ</span>
                </div>
              </div>

              <div class="grid grid-cols-2 gap-3">
                <button
                  class="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-bold py-3 px-4 rounded-xl transition-all duration-200 transform hover:scale-105 hover:shadow-lg flex items-center justify-center space-x-2"
                >
                  <i data-lucide="play" class="w-4 h-4"></i>
                  <span>Học ngay</span>
                </button>
                <button
                  class="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 font-semibold py-3 px-4 rounded-xl transition-colors duration-200 flex items-center justify-center space-x-2"
                >
                  <i data-lucide="file-text" class="w-4 h-4"></i>
                  <span>Tài liệu</span>
                </button>
              </div>
            </div>
          </div>

          <!-- Thẻ khóa học 2: Hóa Dược -->
          <div
            class="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 group"
          >
            <div class="relative">
              <img
                src="https://images.pexels.com/photos/4386467/pexels-photo-4386467.jpeg?auto=compress&cs=tinysrgb&w=300"
                alt="Hóa Dược"
                class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
              />
              <div class="absolute top-4 left-4">
                <span
                  class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                >
                  <i data-lucide="play" class="w-3 h-3 mr-1"></i>
                  Đang học
                </span>
              </div>
            </div>
            <div class="p-6">
              <h3
                class="text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors duration-200"
              >
                Hóa Dược
              </h3>
              <div class="mb-4">
                <div class="flex justify-between items-center mb-2">
                  <span class="text-sm font-medium text-gray-700">Tiến độ</span>
                  <span class="text-sm font-bold text-gray-900">60%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div
                    class="h-2 rounded-full transition-all duration-300 bg-blue-500"
                    style="width: 60%"
                  ></div>
                </div>
                <div class="flex justify-between text-xs text-gray-500 mt-1">
                  <span>18/30 bài học</span>
                  <span>45 giờ</span>
                </div>
              </div>

              <div class="grid grid-cols-2 gap-3">
                <button
                  class="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-bold py-3 px-4 rounded-xl transition-all duration-200 transform hover:scale-105 hover:shadow-lg flex items-center justify-center space-x-2"
                >
                  <i data-lucide="play" class="w-4 h-4"></i>
                  <span>Học ngay</span>
                </button>
                <button
                  class="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 font-semibold py-3 px-4 rounded-xl transition-colors duration-200 flex items-center justify-center space-x-2"
                >
                  <i data-lucide="file-text" class="w-4 h-4"></i>
                  <span>Tài liệu</span>
                </button>
              </div>
            </div>
          </div>

          <!-- Thẻ khóa học 3: Anh Văn 2 - Dược -->
          <div
            class="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 group"
          >
            <div class="relative">
              <img
                src="https://images.pexels.com/photos/4386468/pexels-photo-4386468.jpeg?auto=compress&cs=tinysrgb&w=300"
                alt="Anh Văn 2 - Dược"
                class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
              />
              <div class="absolute top-4 left-4">
                <span
                  class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                >
                  <i data-lucide="play" class="w-3 h-3 mr-1"></i>
                  Đang học
                </span>
              </div>
            </div>
            <div class="p-6">
              <h3
                class="text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors duration-200"
              >
                Anh Văn 2 - Dược
              </h3>
              <div class="mb-4">
                <div class="flex justify-between items-center mb-2">
                  <span class="text-sm font-medium text-gray-700">Tiến độ</span>
                  <span class="text-sm font-bold text-gray-900">90%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div
                    class="h-2 rounded-full transition-all duration-300 bg-green-500"
                    style="width: 90%"
                  ></div>
                </div>
                <div class="flex justify-between text-xs text-gray-500 mt-1">
                  <span>18/20 bài học</span>
                  <span>28 giờ</span>
                </div>
              </div>

              <div class="grid grid-cols-2 gap-3">
                <button
                  class="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-bold py-3 px-4 rounded-xl transition-all duration-200 transform hover:scale-105 hover:shadow-lg flex items-center justify-center space-x-2"
                >
                  <i data-lucide="play" class="w-4 h-4"></i>
                  <span>Học ngay</span>
                </button>
                <button
                  class="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 font-semibold py-3 px-4 rounded-xl transition-colors duration-200 flex items-center justify-center space-x-2"
                >
                  <i data-lucide="file-text" class="w-4 h-4"></i>
                  <span>Tài liệu</span>
                </button>
              </div>
            </div>
          </div>

          <!-- Thẻ khóa học 4: Chăm sóc sức khỏe người lớn 1 -->
          <div
            class="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 group"
          >
            <div class="relative">
              <img
                src="https://images.pexels.com/photos/4386469/pexels-photo-4386469.jpeg?auto=compress&cs=tinysrgb&w=300"
                alt="Chăm sóc sức khỏe người lớn 1"
                class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
              />
              <div class="absolute top-4 left-4">
                <span
                  class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                >
                  <i data-lucide="play" class="w-3 h-3 mr-1"></i>
                  Đang học
                </span>
              </div>
            </div>
            <div class="p-6">
              <h3
                class="text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors duration-200"
              >
                Chăm sóc sức khỏe người lớn 1
              </h3>
              <div class="mb-4">
                <div class="flex justify-between items-center mb-2">
                  <span class="text-sm font-medium text-gray-700">Tiến độ</span>
                  <span class="text-sm font-bold text-gray-900">45%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div
                    class="h-2 rounded-full transition-all duration-300 bg-orange-500"
                    style="width: 45%"
                  ></div>
                </div>
                <div class="flex justify-between text-xs text-gray-500 mt-1">
                  <span>16/35 bài học</span>
                  <span>50 giờ</span>
                </div>
              </div>

              <div class="grid grid-cols-2 gap-3">
                <button
                  class="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-bold py-3 px-4 rounded-xl transition-all duration-200 transform hover:scale-105 hover:shadow-lg flex items-center justify-center space-x-2"
                >
                  <i data-lucide="play" class="w-4 h-4"></i>
                  <span>Học ngay</span>
                </button>
                <button
                  class="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 font-semibold py-3 px-4 rounded-xl transition-colors duration-200 flex items-center justify-center space-x-2"
                >
                  <i data-lucide="file-text" class="w-4 h-4"></i>
                  <span>Tài liệu</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Phần Hành động nhanh/Hỗ trợ -->
        <div
          class="mt-12 bg-gradient-to-r from-blue-600 to-teal-600 rounded-2xl p-8 text-white"
        >
          <div class="text-center mb-8">
            <h3 class="text-2xl font-bold mb-4">Cần hỗ trợ học tập?</h3>
            <p class="text-blue-100 max-w-2xl mx-auto">
              Đội ngũ hỗ trợ học tập của chúng tôi luôn sẵn sàng giúp bạn đạt
              được mục tiêu học tập
            </p>
          </div>
          <div class="grid md:grid-cols-3 gap-6">
            <div
              class="bg-white/20 backdrop-blur-sm p-6 rounded-xl text-center hover:bg-white/30 transition-all duration-300 transform hover:scale-105"
            >
              <i data-lucide="message-circle" class="mx-auto mb-4 w-8 h-8"></i>
              <h4 class="font-semibold mb-2">Hỏi đáp trực tuyến</h4>
              <p class="text-sm text-blue-100">
                Chat với giảng viên và bạn học
              </p>
            </div>
            <div
              class="bg-white/20 backdrop-blur-sm p-6 rounded-xl text-center hover:bg-white/30 transition-all duration-300 transform hover:scale-105"
            >
              <i data-lucide="calendar" class="mx-auto mb-4 w-8 h-8"></i>
              <h4 class="font-semibold mb-2">Lịch học cá nhân</h4>
              <p class="text-sm text-blue-100">
                Quản lý thời gian học tập hiệu quả
              </p>
            </div>
            <div
              class="bg-white/20 backdrop-blur-sm p-6 rounded-xl text-center hover:bg-white/30 transition-all duration-300 transform hover:scale-105"
            >
              <i data-lucide="award" class="mx-auto mb-4 w-8 h-8"></i>
              <h4 class="font-semibold mb-2">Theo dõi tiến độ</h4>
              <p class="text-sm text-blue-100">
                Báo cáo chi tiết kết quả học tập
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script>
      // Khởi tạo các icon Lucide sau khi trang đã tải
      lucide.createIcons();
    </script>
  </body>
</html>
