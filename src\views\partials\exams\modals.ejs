<!-- Modal chọn hình thức làm bài thi -->
<div
  id="examModal"
  class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden flex items-center justify-center z-50"
>
  <div
    class="bg-white rounded-lg shadow-xl w-full max-w-md mx-4 overflow-hidden"
  >
    <div class="px-6 py-4 border-b border-gray-200">
      <div class="flex items-center justify-between">
        <h3 class="text-xl font-semibold text-gray-800" id="modalTitle">
          Chọn hình thức làm bài
        </h3>
        <button
          onclick="closeExamModal()"
          class="text-gray-500 hover:text-gray-700 focus:outline-none"
        >
          <i class="fas fa-times"></i>
        </button>
      </div>
    </div>
    <div class="p-6">
      <input type="hidden" id="examId" value="" />

      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- Google Form Option -->
        <div
          onclick="selectExamType('google-form')"
          class="cursor-pointer border rounded-lg p-4 hover:border-indigo-500 hover:bg-indigo-50 transition-colors flex flex-col items-center text-center option-card"
        >
          <div
            class="w-16 h-16 mb-2 rounded-full bg-red-100 flex items-center justify-center"
          >
            <i class="fab fa-google text-red-500 text-2xl"></i>
          </div>
          <h4 class="font-medium text-gray-800 mb-1">Google Form</h4>
          <p class="text-sm text-gray-500">Xem kết quả sau khi hoàn thành</p>
        </div>

        <!-- Quizizz Option -->
        <div
          onclick="selectExamType('quizizz')"
          class="cursor-pointer border rounded-lg p-4 hover:border-indigo-500 hover:bg-indigo-50 transition-colors flex flex-col items-center text-center option-card"
        >
          <div
            class="w-16 h-16 mb-2 rounded-full bg-purple-100 flex items-center justify-center"
          >
            <i class="fas fa-gamepad text-purple-500 text-2xl"></i>
          </div>
          <h4 class="font-medium text-gray-800 mb-1">Quizizz</h4>
          <p class="text-sm text-gray-500">Xem kết quả ngay sau mỗi câu hỏi</p>
        </div>
      </div>

      <!-- Tùy chọn đảo câu hỏi và đáp án -->
      <div class="mt-6 pt-6 border-t border-gray-200">
        <h5 class="font-medium text-gray-800 mb-4">
          <i class="fas fa-shuffle mr-2 text-indigo-500"></i>
          Tùy chọn đảo thứ tự
        </h5>

        <div class="space-y-3">
          <!-- Đảo câu hỏi -->
          <div class="flex items-center">
            <input
              type="checkbox"
              id="shuffleQuestions"
              class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
              checked
            />
            <label
              for="shuffleQuestions"
              class="ml-3 text-sm text-gray-700 cursor-pointer"
            >
              <i class="fas fa-question-circle mr-1 text-blue-500"></i>
              Đảo thứ tự câu hỏi
            </label>
          </div>

          <!-- Đảo đáp án -->
          <div class="flex items-center">
            <input
              type="checkbox"
              id="shuffleAnswers"
              class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
              checked
            />
            <label
              for="shuffleAnswers"
              class="ml-3 text-sm text-gray-700 cursor-pointer"
            >
              <i class="fas fa-list mr-1 text-green-500"></i>
              Đảo thứ tự đáp án
            </label>
          </div>
        </div>
      </div>
    </div>
    <div class="px-6 py-4 bg-gray-50 flex justify-end">
      <button
        onclick="closeExamModal()"
        class="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none mr-2"
      >
        Hủy
      </button>
      <button
        onclick="startExam()"
        id="startExamBtn"
        disabled
        class="px-4 py-2 text-white bg-indigo-600 rounded-md hover:bg-indigo-700 focus:outline-none"
      >
        Bắt đầu làm bài
      </button>
    </div>
  </div>
</div>

<!-- Modal tiếp tục/làm mới bài thi thử -->
<div
  id="continueModal"
  class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden flex items-center justify-center"
  style="z-index: 9999"
>
  <div
    class="bg-white rounded-lg shadow-xl w-full max-w-md mx-4 overflow-hidden"
  >
    <div class="px-6 py-4 border-b border-gray-200 text-center">
      <div
        class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4"
      >
        <i class="fas fa-question-circle text-blue-500 text-2xl"></i>
      </div>
      <h3 class="text-xl font-semibold text-gray-800">
        Bạn có bài thi thử đang dở dang
      </h3>
    </div>
    <div class="p-6 text-center">
      <p class="text-gray-600 mb-4">
        Bạn có một bài thi thử chưa hoàn thành với
        <span class="font-bold text-orange-600" id="timeRemaining">--:--</span>
        còn lại.
      </p>
      <p class="text-sm text-gray-500 mb-6">
        Bạn muốn làm tiếp bài cũ hay bắt đầu bài mới?
      </p>

      <div class="flex flex-col sm:flex-row gap-3">
        <button
          onclick="continueExistingPractice()"
          class="flex-1 inline-flex items-center justify-center px-4 py-3 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
        >
          <i class="fas fa-play mr-2"></i>
          Làm tiếp bài cũ
        </button>
        <button
          onclick="startNewPractice()"
          class="flex-1 inline-flex items-center justify-center px-4 py-3 bg-orange-600 text-white text-sm font-medium rounded-lg hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 transition-colors"
        >
          <i class="fas fa-redo mr-2"></i>
          Bắt đầu bài mới
        </button>
      </div>

      <button
        onclick="closeContinueModal()"
        class="mt-3 w-full text-sm text-gray-500 hover:text-gray-700 focus:outline-none"
      >
        Đóng chuẩn bị thi thử
      </button>
    </div>
  </div>
</div>

<!-- Modal detail practice history -->
<div
  id="practiceDetailModal"
  class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden flex items-center justify-center z-50"
>
  <div
    class="bg-white rounded-lg shadow-xl w-full max-w-6xl mx-4 overflow-hidden max-h-[90vh] flex flex-col"
  >
    <!-- Header -->
    <div class="px-6 py-4 border-b border-gray-200 flex-shrink-0">
      <div class="flex items-center justify-between">
        <h3 class="text-xl font-semibold text-gray-800">
          <i class="fas fa-chart-bar mr-2 text-indigo-500"></i>
          Chi tiết lịch sử thi thử
        </h3>
        <button
          onclick="closePracticeDetailModal()"
          class="text-gray-500 hover:text-gray-700 focus:outline-none"
        >
          <i class="fas fa-times text-xl"></i>
        </button>
      </div>
    </div>

    <!-- Content -->
    <div class="flex-1 overflow-hidden flex flex-col">
      <div id="practiceDetailContent" class="flex-1 overflow-y-auto p-6">
        <!-- Loading state -->
        <div class="text-center py-8" id="detailLoading">
          <i class="fas fa-spinner fa-spin text-gray-400 mb-2 text-2xl"></i>
          <p class="text-gray-500">Đang tải chi tiết...</p>
        </div>
      </div>
    </div>
  </div>
</div>
