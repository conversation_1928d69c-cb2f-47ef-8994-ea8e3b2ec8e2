#!/bin/sh

# Script khởi động ứng dụng auth-app
echo "Đang khởi động auth-app..."

# Kiểm tra và sao chép file môi trường nếu cần
if [ ! -f ".env" ] && [ -f "env.example" ]; then
  echo "Không tìm thấy file .env, sao chép từ env.example..."
  cp env.example .env
  echo "Đã sao chép env.example thành .env"
fi

# Kiểm tra các biến môi trường cần thiết
echo "Kiểm tra cấu hình môi trường..."

# Thiết lập giá trị mặc định nếu không có trong môi trường
export PORT=${PORT:-5000}
export NODE_ENV=${NODE_ENV:-production}
export JWT_EXPIRATION=${JWT_EXPIRATION:-1d}

# Kiểm tra MongoDB
if [ -z "$MONGODB_URI" ]; then
  echo "CẢNH BÁO: Không tìm thấy MONGODB_URI trong biến môi trường. Sử dụng giá trị mặc định, nhưng có thể không hoạt động."
  export MONGODB_URI="mongodb://localhost:27017/auth-app"
fi

# Kiểm tra JWT Secret
if [ -z "$JWT_SECRET" ]; then
  echo "CẢNH BÁO: Không tìm thấy JWT_SECRET trong biến môi trường. Tạo giá trị ngẫu nhiên tạm thời."
  export JWT_SECRET=$(date +%s | sha256sum | base64 | head -c 32)
fi

# Kiểm tra cấu hình Google OAuth
if [ -z "$GOOGLE_CLIENT_ID" ] || [ -z "$GOOGLE_CLIENT_SECRET" ] || [ -z "$CALLBACK_URL" ]; then
  echo "CẢNH BÁO: Thiếu cấu hình Google OAuth. Tính năng đăng nhập với Google sẽ không hoạt động."
fi

# Khởi động ứng dụng
echo "Khởi động server Node.js..."
exec node dist/server.js 