import { Request, Response } from "express";
import examService from "../services/exam.service";
import { responseError } from "../util/errorhandler";
import questionService from "../services/question.service";
import Question from "../models/question";
import Exam from "../models/exam";
import mongoose from "mongoose";
import { encryptObjectOptimized } from "../util/encryption";
import PracticeExamHistory from "../models/PracticeExamHistory";
import { Product } from "../services/modelService";
import { getStudentByEmail } from "../services/student.service";

// Interface cho câu hỏi đã được xử lý
interface ProcessedQuestion {
  _id: mongoose.Types.ObjectId;
  text: string;
  options: Array<{
    text: string;
    isCorrect: boolean;
    order?: number;
  }>;
  image?: string;
  questionNumber: number;
  [key: string]: any; // Cho phép các thuộc t<PERSON>h khác
}

// Helper đ<PERSON> đảo thứ tự ngẫu nhiên một mảng
const shuffleArray = (array: any[]) => {
  let currentIndex = array.length;
  let randomIndex;

  // Trong khi còn phần tử chưa xử lý
  while (currentIndex !== 0) {
    // Chọn một phần tử còn lại ngẫu nhiên
    randomIndex = Math.floor(Math.random() * currentIndex);
    currentIndex--;

    // Hoán đổi với phần tử hiện tại
    [array[currentIndex], array[randomIndex]] = [
      array[randomIndex],
      array[currentIndex],
    ];
  }

  return array;
};

// Thuật toán tối ưu để chọn N câu hỏi ngẫu nhiên từ mảng lớn
// Sử dụng Fisher-Yates shuffle được tối ưu - O(N) thay vì O(total)
const selectRandomQuestions = (questions: any[], count: number) => {
  const totalQuestions = questions.length;

  if (totalQuestions <= count) {
    // Nếu tổng số câu hỏi <= số cần lấy, trả về toàn bộ
    return shuffleArray([...questions]);
  }

  // Thuật toán reservoir sampling được tối ưu
  // Đây là thuật toán nhanh nhất cho việc chọn ngẫu nhiên
  const selected = [];

  // Lấy count câu hỏi đầu tiên
  for (let i = 0; i < count; i++) {
    selected[i] = questions[i];
  }

  // Với mỗi câu hỏi còn lại, có xác suất count/i để thay thế
  for (let i = count; i < totalQuestions; i++) {
    const randomIndex = Math.floor(Math.random() * (i + 1));
    if (randomIndex < count) {
      selected[randomIndex] = questions[i];
    }
  }

  // Shuffle kết quả để đảm bảo thứ tự ngẫu nhiên
  return shuffleArray(selected);
};

// Helper function to optimize questions for storage/client (remove unnecessary fields)
const optimizeQuestions = (questions: any[]) => {
  return questions.map((question) => {
    // Tạo mảng answerOrder để lưu thứ tự các câu trả lời
    const answerOrder = question.options
      ? question.options.map((_: any, index: number) => index + 1)
      : [1, 2, 3, 4];

    return {
      questionId: question._id, // Chỉ lưu ID của câu hỏi
      answerOrder: answerOrder, // Lưu thứ tự các câu trả lời [1,2,3,4] hoặc [4,2,1,3]
    };
  });
};

// Helper function để gửi dữ liệu câu hỏi đầy đủ đến client nhưng vẫn tối ưu dữ liệu
const prepareQuestionsForClient = (questions: any[]) => {
  return questions.map((question) => {
    // Đảm bảo chỉ lấy các trường cần thiết để giảm kích thước dữ liệu
    return {
      _id: question._id,
      text: question.text,
      options: question.options || [],
      image: question.image || null,
      questionNumber: question.questionNumber || 0,
    };
  });
};

// Lấy đề thi và câu hỏi với các tùy chọn đã bị xáo trộn
const getExamWithShuffledQuestions = async (
  examId: string,
  shuffleQuestions: boolean = false,
  shuffleAnswers: boolean = false
) => {
  try {
    const exam = await Exam.findById(examId);
    if (!exam) {
      throw new Error("Không tìm thấy đề thi");
    }

    // Lấy tất cả câu hỏi của đề thi
    let questions = await Question.find({
      examId: new mongoose.Types.ObjectId(examId),
    });

    // Đảo thứ tự câu hỏi nếu được yêu cầu
    if (shuffleQuestions) {
      questions = shuffleArray([...questions]);
    }

    // Xử lý các lựa chọn trong mỗi câu hỏi
    const processedQuestions = questions.map((question) => {
      const questionObj = question.toObject() as any;

      // Chuyển đổi trường answers thành options để phù hợp với frontend
      if (questionObj.answers && Array.isArray(questionObj.answers)) {
        // Đảo thứ tự các lựa chọn nếu được yêu cầu
        questionObj.options = shuffleAnswers
          ? shuffleArray([...questionObj.answers])
          : [...questionObj.answers];
        // Xóa trường answers để tránh trùng lặp dữ liệu
        delete questionObj.answers;
      } else {
        // Tạo options mặc định nếu không có answers
        const defaultOptions = [
          { text: "Lựa chọn A", isCorrect: true },
          { text: "Lựa chọn B", isCorrect: false },
          { text: "Lựa chọn C", isCorrect: false },
          { text: "Lựa chọn D", isCorrect: false },
        ];
        questionObj.options = shuffleAnswers
          ? shuffleArray([...defaultOptions])
          : defaultOptions;
      }

      return questionObj;
    });

    return {
      exam,
      questions: processedQuestions,
    };
  } catch (error) {
    throw error;
  }
};

// Helper function để khôi phục dữ liệu câu hỏi đầy đủ từ database
const fetchFullQuestionsFromOptimized = async (
  optimizedQuestions: any[]
): Promise<ProcessedQuestion[]> => {
  try {
    // Lấy tất cả questionIds từ mảng optimizedQuestions
    const questionIds = optimizedQuestions.map((q) => q.questionId || q);

    const fullQuestions = await Question.find({ _id: { $in: questionIds } });

    if (!fullQuestions || fullQuestions.length === 0) {
      console.error("❌ Không tìm thấy câu hỏi nào từ database!");
      return [];
    }

    // Map câu hỏi đã tối ưu với dữ liệu đầy đủ và sắp xếp theo answerOrder
    const processedQuestions: ProcessedQuestion[] = [];

    for (let index = 0; index < optimizedQuestions.length; index++) {
      const optimized = optimizedQuestions[index];
      const fullQuestion = fullQuestions.find(
        (q) =>
          q._id.toString() === (optimized.questionId || optimized).toString()
      );

      if (!fullQuestion) {
        console.warn(
          `⚠️ Không tìm thấy câu hỏi với ID: ${
            optimized.questionId || optimized
          }`
        );
        continue;
      }

      // Chuyển từ document sang plain object
      const rawQuestionObj: any = fullQuestion.toObject
        ? fullQuestion.toObject()
        : { ...fullQuestion };

      // Tạo đối tượng mới với các trường theo interface
      const processedQuestion: ProcessedQuestion = {
        _id: rawQuestionObj._id,
        text: rawQuestionObj.text,
        options: [],
        image: rawQuestionObj.image || null,
        questionNumber: index + 1,
      };

      // Xử lý options theo answerOrder đã lưu
      if (rawQuestionObj.answers && Array.isArray(rawQuestionObj.answers)) {
        // Lấy các options từ answers
        const originalOptions = [...rawQuestionObj.answers];

        // Nếu có answerOrder, sắp xếp lại options theo thứ tự đã lưu
        if (optimized.answerOrder && Array.isArray(optimized.answerOrder)) {
          const orderedOptions = [];

          for (let i = 0; i < optimized.answerOrder.length; i++) {
            const originalIndex = optimized.answerOrder[i] - 1;
            if (originalIndex >= 0 && originalIndex < originalOptions.length) {
              orderedOptions.push(originalOptions[originalIndex]);
            }
          }

          processedQuestion.options =
            orderedOptions.length > 0 ? orderedOptions : originalOptions;
        } else {
          // Nếu không có answerOrder, sử dụng thứ tự gốc
          processedQuestion.options = originalOptions;
        }
      }

      processedQuestions.push(processedQuestion);
    }

    return processedQuestions;
  } catch (error) {
    console.error("❌ Lỗi khi khôi phục dữ liệu câu hỏi:", error);
    return [];
  }
};

class ExamController {
  /**
   * Lấy danh sách bài kiểm tra theo sản phẩm
   */
  async getExamsByProduct(req: Request, res: Response) {
    try {
      const { productId } = req.params;
      const { page = 1, limit = 10 } = req.query;

      const result = await examService.getExamsByProduct(
        productId,
        Number(page),
        Number(limit)
      );

      return res.status(200).json({
        message: "Success",
        data: result,
      });
    } catch (error: any) {
      return responseError(res, error.message, 400);
    }
  }

  /**
   * Lấy thông tin bài kiểm tra theo ID
   */
  async getExamById(req: Request, res: Response) {
    try {
      const { examId } = req.params;
      const exam = await examService.getExamById(examId);

      return res.status(200).json({
        message: "Success",
        data: exam,
      });
    } catch (error: any) {
      return responseError(res, error.message, 400);
    }
  }

  /**
   * Tạo bài kiểm tra mới
   */
  async createExam(req: Request, res: Response) {
    try {
      const { productId } = req.params;
      const examData = req.body;

      const newExam = await examService.createExam(productId, examData);

      return res.status(201).json({
        message: "Success",
        data: newExam,
      });
    } catch (error: any) {
      return responseError(res, error.message, 400);
    }
  }

  /**
   * Cập nhật bài kiểm tra
   */
  async updateExam(req: Request, res: Response) {
    try {
      const { examId } = req.params;
      const examData = req.body;

      const updatedExam = await examService.updateExam(examId, examData);

      return res.status(200).json({
        message: "Success",
        data: updatedExam,
      });
    } catch (error: any) {
      return responseError(res, error.message, 400);
    }
  }

  /**
   * Xóa bài kiểm tra
   */
  async deleteExam(req: Request, res: Response) {
    try {
      const { examId } = req.params;

      const result = await examService.deleteExam(examId);

      return res.status(200).json({
        message: "Success",
        data: result,
      });
    } catch (error: any) {
      return responseError(res, error.message, 400);
    }
  }

  /**
   * Lấy bài kiểm tra kèm tất cả câu hỏi
   */
  async getExamWithQuestions(req: Request, res: Response) {
    try {
      const { examId } = req.params;

      const result = await examService.getExamWithQuestions(examId);

      return res.status(200).json({
        message: "Success",
        data: result,
      });
    } catch (error: any) {
      return responseError(res, error.message, 400);
    }
  }

  /**
   * Hiển thị trang làm bài thi bằng Google Form
   */
  async takeExamGoogleForm(req: Request, res: Response) {
    try {
      const { examId } = req.params;
      // Lấy các tham số đảo thứ tự từ query string
      const shuffleQuestions = req.query.shuffleQuestions === "true";
      const shuffleAnswers = req.query.shuffleAnswers === "true";

      const { exam, questions } = await getExamWithShuffledQuestions(
        examId,
        shuffleQuestions,
        shuffleAnswers
      );

      // Mã hóa dữ liệu câu hỏi
      let encryptedQuestionsConfig = null;
      let renderData: any = {
        user: res.locals.user,
        student: req.student,
        exam,
        title: `Làm bài thi: ${exam.name} - Google Form`,
        questions: questions, // Always pass questions for template fallback
      };

      try {
        if (questions && questions.length > 0) {
          const encrypted = encryptObjectOptimized(questions);
          encryptedQuestionsConfig = {
            appConfigData: encrypted.appConfigData,
            token: encrypted.token,
            salt: encrypted.salt,
            algorithm: encrypted.algorithm,
          };

          // Pass encrypted data AND keep questions for template fallback
          renderData.encryptedQuestionsConfig = encryptedQuestionsConfig;
        }
      } catch (encryptError) {
        console.warn(
          "⚠️ Không thể mã hóa câu hỏi Google Form, sử dụng dữ liệu thường:",
          encryptError
        );
        // Questions are already in renderData
      }

      return res.render("exam/google-form", renderData);
    } catch (error: any) {
      console.error("Lỗi khi lấy bài thi Google Form:", error);
      return res.status(500).render("error", {
        message: "Đã xảy ra lỗi khi tải bài thi",
        error: { status: 500, stack: error.message },
        user: res.locals.user,
      });
    }
  }

  /**
   * Hiển thị trang làm bài thi bằng Quizizz
   */
  async takeExamQuizizz(req: Request, res: Response) {
    try {
      const { examId } = req.params;
      // Lấy các tham số đảo thứ tự từ query string
      const shuffleQuestions = req.query.shuffleQuestions === "true";
      const shuffleAnswers = req.query.shuffleAnswers === "true";

      const { exam, questions } = await getExamWithShuffledQuestions(
        examId,
        shuffleQuestions,
        shuffleAnswers
      );

      // Chuyển đổi dữ liệu câu hỏi thành chuỗi JSON an toàn
      const safeQuestions = questions.map((q) => {
        // Đảm bảo options được xử lý đúng
        let processedOptions = [];

        if (q.options && Array.isArray(q.options) && q.options.length > 0) {
          processedOptions = q.options.map((opt) => {
            if (typeof opt === "object") {
              return {
                text: opt.text || "",
                isCorrect: !!opt.isCorrect,
              };
            }
            return { text: String(opt), isCorrect: false };
          });
        } else {
          // Nếu không có options, tạo ra các options mặc định
          console.warn(
            `Câu hỏi không có options: ${q._id}. Tạo options mặc định.`
          );
          processedOptions = [
            { text: "Lựa chọn A", isCorrect: true },
            { text: "Lựa chọn B", isCorrect: false },
            { text: "Lựa chọn C", isCorrect: false },
            { text: "Lựa chọn D", isCorrect: false },
          ];
        }

        return {
          ...q,
          options: processedOptions,
        };
      });

      // Mã hóa dữ liệu câu hỏi Quizizz
      let encryptedQuestionsConfig = null;
      let renderData: any = {
        user: res.locals.user,
        student: req.student,
        exam,
        title: `Làm bài thi: ${exam.name} - Quizizz`,
        questions: safeQuestions, // Always pass questions for template fallback
      };

      try {
        if (safeQuestions && safeQuestions.length > 0) {
          // Mã hóa exam data với questions
          const examData = {
            examId: exam._id,
            questions: safeQuestions,
          };

          const encrypted = encryptObjectOptimized(examData);
          encryptedQuestionsConfig = {
            appConfigData: encrypted.appConfigData,
            token: encrypted.token,
            salt: encrypted.salt,
            algorithm: encrypted.algorithm,
          };

          // Pass encrypted data AND keep questions for template fallback
          renderData.encryptedExamConfig = encryptedQuestionsConfig;
        }
      } catch (encryptError) {
        console.warn(
          "⚠️ Không thể mã hóa câu hỏi Quizizz, sử dụng dữ liệu thường:",
          encryptError
        );
        // Questions are already in renderData
      }

      return res.render("exam/quizizz", renderData);
    } catch (error: any) {
      console.error("Lỗi khi lấy bài thi Quizizz:", error);
      return res.status(500).render("error", {
        message: "Đã xảy ra lỗi khi tải bài thi",
        error: { status: 500, stack: error.message },
        user: res.locals.user,
      });
    }
  }

  /**
   * Hiển thị trang làm lại câu hỏi sai
   */
  async retryWrongQuestions(req: Request, res: Response) {
    try {
      const { examId, examType } = req.query;

      if (!examId || !examType) {
        throw new Error("Thiếu thông tin cần thiết");
      }

      // Lấy thông tin bài thi
      const exam = await examService.getExamById(examId as string);

      if (!exam) {
        throw new Error("Không tìm thấy bài thi");
      }

      // Kiểm tra loại đề thi được yêu cầu
      const templateName =
        examType === "google-form"
          ? "exam/wrong-questions-google-form"
          : "exam/wrong-questions-quizizz";

      return res.render(templateName, {
        user: res.locals.user,
        student: req.student,
        exam,
        title: `Làm lại câu hỏi sai: ${exam.name}`,
      });
    } catch (error: any) {
      console.error("Lỗi khi xử lý làm lại câu hỏi sai:", error);
      return res.status(500).render("error", {
        message: "Đã xảy ra lỗi khi tải trang làm lại câu hỏi",
        error: { status: 500, stack: error.message },
        user: res.locals.user,
      });
    }
  }

  /**
   * API để lấy danh sách câu hỏi sai từ một bài thi
   */
  async getWrongQuestions(req: Request, res: Response) {
    try {
      const { examId } = req.params;
      const studentId = req.student?._id;

      if (!examId) {
        return res.status(400).json({
          success: false,
          message: "Thiếu ID bài thi",
        });
      }

      // Lấy thông tin bài thi
      const exam = await examService.getExamById(examId);
      if (!exam) {
        return res.status(404).json({
          success: false,
          message: "Không tìm thấy bài thi",
        });
      }

      // Lấy tất cả câu hỏi của bài thi
      const questions = await Question.find({
        examId: new mongoose.Types.ObjectId(examId),
      });

      // Lấy kết quả bài thi gần nhất của học sinh (giả định đã có API lấy kết quả)
      // Đây là mẫu, bạn cần điều chỉnh theo cấu trúc dữ liệu thực tế
      const examResult = await import("../models/examResult").then(
        (module) => module.default
      );

      const latestResult = await examResult
        .findOne({
          examId,
          studentId,
        })
        .sort({ createdAt: -1 });

      // Nếu không có kết quả hoặc chưa làm bài, trả về mảng rỗng
      if (!latestResult || !latestResult.answers) {
        return res.status(404).json({
          success: false,
          message: "Không tìm thấy kết quả bài thi",
        });
      }

      // Lọc các câu hỏi sai
      const wrongQuestions = [];
      for (const answer of latestResult.answers) {
        if (!answer.isCorrect) {
          const question = questions.find(
            (q) => q._id.toString() === answer.questionId.toString()
          );
          if (question) {
            // Chuyển đổi từ document sang object và xáo trộn các lựa chọn
            const questionObj = question.toObject() as any;

            // Chuyển đổi trường answers thành options nếu cần
            if (questionObj.answers && Array.isArray(questionObj.answers)) {
              questionObj.options = [...questionObj.answers];
              // Xóa trường answers để tránh trùng lặp dữ liệu
              delete questionObj.answers;
            }

            wrongQuestions.push(questionObj);
          }
        }
      }

      // Trả về danh sách câu hỏi sai
      return res.status(200).json({
        success: true,
        examId,
        questions: wrongQuestions,
      });
    } catch (error: any) {
      console.error("Lỗi khi lấy danh sách câu hỏi sai:", error);
      return res.status(500).json({
        success: false,
        message: "Đã xảy ra lỗi khi xử lý yêu cầu",
        error: error.message,
      });
    }
  }
}

// Export các hàm controller riêng lẻ
export const getExamById = (req: Request, res: Response) =>
  new ExamController().getExamById(req, res);

export const getExamsByProductId = (req: Request, res: Response) =>
  new ExamController().getExamsByProduct(req, res);

export const createExam = (req: Request, res: Response) =>
  new ExamController().createExam(req, res);

export const takeExamGoogleForm = (req: Request, res: Response) =>
  new ExamController().takeExamGoogleForm(req, res);

export const takeExamQuizizz = (req: Request, res: Response) =>
  new ExamController().takeExamQuizizz(req, res);

export const retryWrongQuestions = (req: Request, res: Response) =>
  new ExamController().retryWrongQuestions(req, res);

export const getWrongQuestions = (req: Request, res: Response) =>
  new ExamController().getWrongQuestions(req, res);

// ====================
// PRACTICE EXAM FUNCTIONS
// ====================

/**
 * Kiểm tra bài thi thử đang làm dở
 */
export const checkExistingPractice = async (req: Request, res: Response) => {
  try {
    const { productId } = req.params;
    const userId = res.locals.user?._id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: "Không tìm thấy thông tin người dùng",
      });
    }

    // Tìm bài thi thử đang làm dở (status = in_progress và trong vòng 60 phút)
    const existingPractice = await PracticeExamHistory.findOne({
      userId,
      courseId: productId,
      status: "in_progress",
      startedAt: {
        $gte: new Date(Date.now() - 60 * 60 * 1000), // 60 phút trước
      },
    }).sort({ startedAt: -1 });

    if (existingPractice) {
      const timeElapsed = Math.floor(
        (Date.now() - existingPractice.startedAt.getTime()) / 1000
      );
      const timeRemaining = 60 * 60 - timeElapsed; // 60 phút - thời gian đã trôi qua

      if (timeRemaining > 0) {
        // Còn thời gian và status = in_progress, có thể làm tiếp
        res.json({
          success: true,
          hasExistingPractice: true,
          hasIncompletePractice: true,
          practiceId: existingPractice._id.toString(),
          timeRemaining,
          timeElapsed,
          startTime: existingPractice.startedAt,
          status: existingPractice.status,
          totalQuestions: existingPractice.totalQuestions || 100,
          // Backward compatibility
          hasExisting: true,
          existingExam: {
            id: existingPractice._id,
            practiceId: existingPractice._id.toString(),
            startTime: existingPractice.startedAt,
            selectedQuestions: existingPractice.selectedQuestions,
            userAnswers: existingPractice.userAnswers,
            timeElapsed,
            timeRemaining,
            status: existingPractice.status,
          },
        });
      } else {
        // Hết thời gian, bài thi cũ không còn hiệu lực
        res.json({
          success: true,
          hasExistingPractice: false,
          hasIncompletePractice: false,
          // Backward compatibility
          hasExisting: false,
        });
      }
    } else {
      // Không có bài thi nào đang làm dở
      res.json({
        success: true,
        hasExistingPractice: false,
        hasIncompletePractice: false,
        // Backward compatibility
        hasExisting: false,
      });
    }
  } catch (error) {
    console.error("Error checking existing practice:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi kiểm tra bài thi hiện tại",
    });
  }
};

/**
 * Bắt đầu thi thử - hiển thị trang chuẩn bị và tạo session thi thử
 */
export const startPracticeExam = async (req: Request, res: Response) => {
  const { productId } = req.params;
  const action = req.body?.action; // 'new' hoặc 'continue' - chỉ có trong POST request
  const userId = res.locals.user?._id;

  // Kiểm tra nếu là API call (từ frontend)
  const isApiCall =
    req.headers["content-type"]?.includes("application/json") &&
    req.method === "POST";

  try {
    if (!userId) {
      if (isApiCall) {
        return res.status(401).json({
          success: false,
          message: "Người dùng chưa đăng nhập",
        });
      }
      return res.redirect("/login");
    }

    // Lấy thông tin khóa học
    const product = await Product.findById(productId);
    if (!product) {
      if (isApiCall) {
        return res.status(404).json({
          success: false,
          message: "Không tìm thấy khóa học",
        });
      }
      return res.status(404).render("error", {
        message: "Không tìm thấy khóa học",
        error: { status: 404 },
        user: res.locals.user,
      });
    }

    // Lấy thông tin sinh viên
    const student = await getStudentByEmail(res.locals.user.email);
    if (!student) {
      if (isApiCall) {
        return res.status(403).json({
          success: false,
          message: "Bạn chưa được đăng ký vào khóa học này",
        });
      }
      return res.status(403).render("error", {
        message: "Bạn chưa được đăng ký vào khóa học này",
        error: { status: 403 },
        user: res.locals.user,
      });
    }

    // Nếu là GET request, check existing practice để quyết định hiển thị gì
    if (!action) {
      // GET request - check existing practice
      const existingPractice = await PracticeExamHistory.findOne({
        userId,
        courseId: productId,
        status: "in_progress",
        startedAt: {
          $gte: new Date(Date.now() - 60 * 60 * 1000), // 60 phút trước
        },
      }).sort({ startedAt: -1 });

      if (existingPractice) {
        const timeElapsed = Math.floor(
          (Date.now() - existingPractice.createdAt.getTime()) / 1000
        );
        const timeRemaining = 60 * 60 - timeElapsed;

        if (timeRemaining > 0) {
          // Có bài thi đang làm dở, render với thông tin existing
          return res.render("exam/practice-exam", {
            user: res.locals.user,
            student,
            product,
            practiceInfo: {
              courseId: productId,
              courseName: product.name,
              hasExisting: true,
              existingExam: {
                timeRemaining,
                timeElapsed,
                startTime: existingPractice.createdAt,
                practiceId: existingPractice._id.toString(), // Thêm practiceId
              },
            },
          });
        }
      }
      // Không có bài thi đang làm dở, proceed tạo bài mới
    }

    // Kiểm tra nếu user muốn làm tiếp bài cũ (POST request hoặc route với practiceId)
    if (action === "continue") {
      let existingPractice;

      // Nếu có practiceId trong body (từ route mới), tìm theo ID cụ thể
      if (req.body.practiceId) {
        existingPractice = await PracticeExamHistory.findOne({
          _id: req.body.practiceId,
          userId,
          courseId: productId,
          status: "in_progress",
        });
      } else {
        // Fallback - tìm practice gần nhất
        existingPractice = await PracticeExamHistory.findOne({
          userId,
          courseId: productId,
          status: "in_progress",
          startedAt: {
            $gte: new Date(Date.now() - 60 * 60 * 1000), // 60 phút trước
          },
        }).sort({ startedAt: -1 });
      }

      if (existingPractice) {
        const timeElapsed = Math.floor(
          (Date.now() - existingPractice.startedAt.getTime()) / 1000
        );
        const timeRemaining = 60 * 60 - timeElapsed;

        if (timeRemaining <= 0) {
          // Bài thi đã hết giờ, tự động cập nhật trạng thái thành "time_up"
          await PracticeExamHistory.findByIdAndUpdate(existingPractice._id, {
            status: "time_up",
            completedAt: new Date(),
          });
          // Tiếp tục tạo bài thi mới
        } else if (timeRemaining > 0) {
          // Use saved questions to maintain consistent order
          let questionsToUse = [];

          if (
            existingPractice.selectedQuestions &&
            existingPractice.selectedQuestions.length > 0
          ) {
            // Sử dụng helper function mới để khôi phục dữ liệu câu hỏi đầy đủ
            questionsToUse = await fetchFullQuestionsFromOptimized(
              existingPractice.selectedQuestions
            );
          }

          if (questionsToUse.length === 0) {
            // Nếu không có câu hỏi, fallback tạo bài mới
            // Continue to create new exam logic
          } else {
            // Mã hóa dữ liệu bài thi cũ
            const encryptionResult = encryptObjectOptimized({
              questions: prepareQuestionsForClient(questionsToUse),
              practiceInfo: {
                courseId: productId,
                courseName: product.name,
                totalQuestions: questionsToUse.length,
                duration: 60,
                type: "course-review",
                timeRemaining,
                existingAnswers: existingPractice.userAnswers,
                startTime: existingPractice.startedAt,
              },
            });

            return res.render("exam/practice-exam", {
              user: res.locals.user,
              student,
              product,
              encryptedPracticeData: encryptionResult.appConfigData,
              encryptionToken: encryptionResult.token,
              encryptionSalt: encryptionResult.salt,
              practiceInfo: {
                courseId: productId,
                courseName: product.name,
                totalQuestions: questionsToUse.length,
                duration: 60,
                timeRemaining,
                existingAnswers: existingPractice.userAnswers,
                isContinue: true,
                practiceId: existingPractice._id.toString(), // Thêm practiceId của bài cũ
              },
            });
          }
        }
      }
    }

    // Nếu user chọn "làm bài mới" và có bài thi cũ đang làm dở
    if (action === "new") {
      const existingPractice = await PracticeExamHistory.findOne({
        userId,
        courseId: productId,
        status: "in_progress",
        startedAt: {
          $gte: new Date(Date.now() - 60 * 60 * 1000), // 60 phút trước
        },
      }).sort({ startedAt: -1 });

      if (existingPractice) {
        // Update bài thi cũ sang trạng thái "abandoned" khi user chọn làm bài mới
        await PracticeExamHistory.findByIdAndUpdate(existingPractice._id, {
          status: "abandoned",
          completedAt: new Date(),
        });
      }
    }

    // Lấy tất cả đề thi của khóa học
    const exams = await Exam.find({
      productId: new mongoose.Types.ObjectId(productId),
      status: "active",
    });

    if (exams.length === 0) {
      return res.render("exam/practice-start", {
        user: res.locals.user,
        student,
        product,
        error: "Không có đề thi nào trong khóa học này để tạo bài thi thử",
      });
    }

    // Lấy tất cả câu hỏi từ các đề thi
    const examIds = exams.map((exam) => exam._id);
    const allQuestions = await Question.find({
      examId: { $in: examIds },
    });

    console.log(
      `📊 Tìm thấy ${allQuestions.length} câu hỏi từ ${exams.length} đề thi`
    );

    if (allQuestions.length < 10) {
      return res.render("exam/practice-start", {
        user: res.locals.user,
        student,
        product,
        error: `Cần ít nhất 10 câu hỏi để tạo bài thi thử. Hiện tại chỉ có ${allQuestions.length} câu hỏi.`,
      });
    }

    // Chọn 100 câu hỏi ngẫu nhiên (hoặc ít hơn nếu không đủ)
    const questionCount = Math.min(100, allQuestions.length);
    const selectedQuestions = selectRandomQuestions(
      allQuestions,
      questionCount
    );

    // Xáo trộn các lựa chọn trong mỗi câu hỏi (chỉ 1 lần khi tạo bài)
    const processedQuestions = selectedQuestions.map((question, index) => {
      const questionObj = question.toObject ? question.toObject() : question;

      if (questionObj.answers && Array.isArray(questionObj.answers)) {
        questionObj.options = shuffleArray([...questionObj.answers]);
        delete questionObj.answers;
      }

      // Thêm số thứ tự câu hỏi
      questionObj.questionNumber = index + 1;

      // Ensure required fields exist
      if (!questionObj.text || !questionObj.options) {
        console.warn(`⚠️ Question ${index + 1} missing required fields:`, {
          hasText: !!questionObj.text,
          hasOptions: !!questionObj.options,
        });
      }

      return questionObj;
    });

    // Create optimized questions for storage (only essential fields)
    const optimizedQuestions = optimizeQuestions(processedQuestions);

    // Tạo record practice exam với status "in_progress"
    let practiceId = null;
    try {
      const newPracticeExam = new PracticeExamHistory({
        courseId: productId,
        courseName: product.name,
        userId,
        totalQuestions: processedQuestions.length,
        practiceType: "course-review",
        status: "in_progress",
        selectedQuestions: optimizedQuestions, // Đã được tối ưu theo định dạng mới (questionId và answerOrder)
        userAnswers: [],
        startedAt: new Date(),
      });

      await newPracticeExam.save();
      practiceId = newPracticeExam._id.toString();
    } catch (error) {
      console.error("❌ Lỗi tạo record practice exam:", error);
      // Không return error ở đây, vẫn cho phép user làm bài
    }

    // Nếu là API call, trả về JSON response với practiceId
    if (isApiCall) {
      return res.status(201).json({
        success: true,
        message: "Đã tạo bài thi thử mới thành công",
        practiceId: practiceId,
        courseId: productId,
        courseName: product.name,
        totalQuestions: processedQuestions.length,
      });
    }

    // Mã hóa dữ liệu câu hỏi đầy đủ để hiển thị trên client
    const encryptionResult = encryptObjectOptimized({
      questions: prepareQuestionsForClient(processedQuestions), // Sử dụng hàm mới để chuẩn bị dữ liệu
      practiceInfo: {
        courseId: productId,
        courseName: product.name,
        totalQuestions: processedQuestions.length,
        duration: 60, // 60 phút
        type: "course-review",
      },
    });

    // Render trang làm bài thi thử
    res.render("exam/practice-exam", {
      user: res.locals.user,
      student,
      product,
      encryptedPracticeData: encryptionResult.appConfigData,
      encryptionToken: encryptionResult.token,
      encryptionSalt: encryptionResult.salt,
      practiceInfo: {
        courseId: productId,
        courseName: product.name,
        totalQuestions: processedQuestions.length,
        duration: 60,
        practiceId: practiceId, // Thêm practiceId
      },
    });
  } catch (error: any) {
    console.error("Lỗi khi bắt đầu thi thử:", error);

    if (isApiCall) {
      return res.status(500).json({
        success: false,
        message: "Đã xảy ra lỗi khi chuẩn bị bài thi thử",
        error: error.message,
      });
    }

    res.status(500).render("error", {
      message: "Đã xảy ra lỗi khi chuẩn bị bài thi thử",
      error: { status: 500 },
      user: res.locals.user,
    });
  }
};

/**
 * Hiển thị trang làm bài thi thử
 */
export const takePracticeExam = async (req: Request, res: Response) => {
  // Function này có thể để trống vì chúng ta redirect trực tiếp từ startPracticeExam
  // hoặc sử dụng để hiển thị trang làm bài cụ thể nếu cần
  res.redirect("/home");
};

/**
 * Lưu kết quả thi thử
 */
export const savePracticeExamResult = async (req: Request, res: Response) => {
  try {
    const {
      courseId,
      courseName,
      score,
      totalQuestions,
      correctAnswers,
      duration,
      selectedQuestions,
      userAnswers,
    } = req.body;

    const userId = res.locals.user?._id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: "Người dùng chưa đăng nhập",
      });
    }

    // Validate dữ liệu
    if (
      !courseId ||
      !courseName ||
      score === undefined ||
      !totalQuestions ||
      !duration
    ) {
      return res.status(400).json({
        success: false,
        message: "Thiếu thông tin cần thiết để lưu kết quả",
      });
    }

    // Kiểm tra xem có bài thi thử đang làm dở không (status = in_progress và trong vòng 60 phút)
    const existingPractice = await PracticeExamHistory.findOne({
      userId,
      courseId,
      status: "in_progress",
      startedAt: {
        $gte: new Date(Date.now() - 60 * 60 * 1000), // 60 phút trước
      },
    }).sort({ startedAt: -1 });

    let practiceHistory;

    if (existingPractice) {
      // Chuyển đổi định dạng userAnswers để phù hợp với schema mới
      const formattedUserAnswers = Array.isArray(userAnswers)
        ? userAnswers.map((answer) => {
            // Đảm bảo có selectedAnswerId
            const selectedAnswerId = answer.selectedAnswerId || null;

            if (!selectedAnswerId) {
              console.warn(
                "⚠️ Không có selectedAnswerId cho câu hỏi:",
                answer.questionId
              );
            }

            return {
              questionId: answer.questionId,
              selectedAnswerId: selectedAnswerId,
              isCorrect: answer.isCorrect,
            };
          })
        : [];

      // Update existing practice to completed
      practiceHistory = await PracticeExamHistory.findByIdAndUpdate(
        existingPractice._id,
        {
          score: Math.round(score),
          totalQuestions,
          correctAnswers:
            correctAnswers || Math.round((score * totalQuestions) / 100),
          duration,
          userAnswers: formattedUserAnswers,
          status: "completed",
          completedAt: new Date(),
        },
        { new: true }
      );
    } else {
      // Chuyển đổi định dạng userAnswers và selectedQuestions để phù hợp với schema mới
      const formattedUserAnswers = Array.isArray(userAnswers)
        ? userAnswers.map((answer) => {
            // Đảm bảo có selectedAnswerId
            const selectedAnswerId = answer.selectedAnswerId || null;

            if (!selectedAnswerId) {
              console.warn(
                "⚠️ Không có selectedAnswerId cho câu hỏi:",
                answer.questionId
              );
            }

            return {
              questionId: answer.questionId,
              selectedAnswerId: selectedAnswerId,
              isCorrect: answer.isCorrect,
            };
          })
        : [];

      const formattedSelectedQuestions = Array.isArray(selectedQuestions)
        ? selectedQuestions.map((question) => {
            if (typeof question === "object" && question.questionId) {
              return {
                questionId: question.questionId,
                answerOrder: question.answerOrder || [1, 2, 3, 4],
              };
            } else if (typeof question === "object" && question._id) {
              return {
                questionId: question._id,
                answerOrder: [1, 2, 3, 4], // Default order
              };
            } else {
              return {
                questionId: question,
                answerOrder: [1, 2, 3, 4], // Default order
              };
            }
          })
        : [];

      // Tạo bản ghi lịch sử thi thử mới (đã hoàn thành)
      practiceHistory = new PracticeExamHistory({
        courseId,
        courseName,
        userId,
        score: Math.round(score),
        totalQuestions,
        correctAnswers:
          correctAnswers || Math.round((score * totalQuestions) / 100),
        duration,
        practiceType: "course-review",
        status: "completed",
        selectedQuestions: formattedSelectedQuestions,
        userAnswers: formattedUserAnswers,
        startedAt: new Date(Date.now() - duration * 1000), // Tính ngược từ thời gian hoàn thành
        completedAt: new Date(),
      });

      await practiceHistory.save();
    }

    return res.status(201).json({
      success: true,
      message: "Đã lưu kết quả thi thử thành công",
      practiceHistory: {
        id: practiceHistory._id,
        score: practiceHistory.score,
        totalQuestions: practiceHistory.totalQuestions,
        correctAnswers: practiceHistory.correctAnswers,
        duration: practiceHistory.duration,
        completedAt: practiceHistory.completedAt,
      },
    });
  } catch (error: any) {
    console.error("Lỗi khi lưu kết quả thi thử:", error);
    return res.status(500).json({
      success: false,
      message: "Đã xảy ra lỗi khi lưu kết quả thi thử",
      error: error.message,
    });
  }
};

export default new ExamController();
