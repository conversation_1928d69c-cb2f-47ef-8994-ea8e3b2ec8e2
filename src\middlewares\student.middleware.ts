import { Request, Response, NextFunction } from "express";
import Student from "../models/student";
import { responseError } from "../util/errorhandler";
import { getStudentByEmail } from "../services/student.service";
import { Product } from "../services/modelService";

// Mở rộng interface Request để thêm các thuộc tính cần thiết
declare global {
  namespace Express {
    interface Request {
      student?: any;
      studentCourses?: any[];
    }
  }
}

/**
 * Middleware xác thực sinh viên có tồn tại trong hệ thống
 */
export const validateStudent = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { studentId } = req.params;

    const student = await Student.findById(studentId);

    if (!student) {
      return responseError(res, "Sinh viên không tồn tại", 404);
    }

    // Lưu thông tin sinh viên vào request để sử dụng ở các middleware tiếp theo
    req.student = student;
    next();
  } catch (error: any) {
    return responseError(res, error.message, 400);
  }
};

/**
 * Middleware lấy danh sách khóa học mà sinh viên đã tham gia
 */
export const getStudentCourses = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { studentId } = req.params;

    // Tìm tất cả các khóa học (products) mà sinh viên đã tham gia
    const coursesData = await Student.find({ _id: studentId })
      .populate("productId", "name status")
      .lean();

    if (!coursesData || coursesData.length === 0) {
      return responseError(res, "Sinh viên chưa tham gia khóa học nào", 404);
    }

    // Lấy danh sách productId từ kết quả
    const courses = coursesData.map((data) => data.productId);

    // Lưu danh sách khóa học vào request để sử dụng ở các middleware tiếp theo
    req.studentCourses = courses;

    // Nếu được gọi như một API độc lập, trả về kết quả
    if (req.url.endsWith("/courses")) {
      return res.status(200).json({
        message: "Success",
        data: courses,
      });
    }

    // Nếu không thì chuyển đến middleware tiếp theo
    next();
  } catch (error: any) {
    return responseError(res, error.message, 400);
  }
};

/**
 * Middleware xác thực sinh viên có quyền truy cập khóa học
 */
export const validateStudentCourse = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { studentId, productId } = req.params;

    // Kiểm tra xem sinh viên có thuộc khóa học này hay không
    const enrollment = await Student.findOne({
      _id: studentId,
      productId,
    });

    if (!enrollment) {
      return responseError(
        res,
        "Sinh viên không có quyền truy cập khóa học này",
        403
      );
    }

    next();
  } catch (error: any) {
    return responseError(res, error.message, 400);
  }
};

/**
 * Middleware kiểm tra xem người dùng đã đăng nhập và là sinh viên của một môn học cụ thể
 */
export const checkStudentCourseAccess = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    // Kiểm tra đã đăng nhập chưa
    if (!res.locals.user) {
      return res.redirect("/login?error=Vui lòng đăng nhập để truy cập");
    }

    // Xử lý đặc biệt cho trang làm lại câu hỏi sai
    if (req.path === "/retry-wrong-questions") {
      const { examId } = req.query;

      if (!examId) {
        return res.status(400).render("error", {
          message: "ID bài thi không hợp lệ",
          error: { status: 400 },
          user: res.locals.user,
        });
      }

      // Lấy thông tin bài thi từ examId
      const exam = await import("../models/exam").then(
        (module) => module.default
      );
      const examData = await exam.findById(examId);

      if (!examData) {
        return res.status(404).render("error", {
          message: "Không tìm thấy bài thi",
          error: { status: 404 },
          user: res.locals.user,
        });
      }

      // Lấy productId từ bài thi
      const productId = examData.productId.toString();

      // Tìm sinh viên theo email
      const email = res.locals.user.email;
      const student = await import("../models/student").then(
        (module) => module.default
      );

      // Tìm xem sinh viên có đăng ký môn học này không
      const studentData = await student.findOne({
        email: email,
        productId: productId,
      });

      if (!studentData) {
        return res.status(403).render("error", {
          message: "Bạn không có quyền truy cập trang này",
          error: {
            status: 403,
            description: "Bạn không được đăng ký vào môn học này",
          },
          user: res.locals.user,
        });
      }

      // Nếu qua tất cả kiểm tra, cho phép truy cập
      req.student = studentData;
      return next();
    }

    // Xử lý bình thường cho các trường hợp khác
    // Lấy productId từ params hoặc từ bài thi
    let productId = req.params.productId;
    const examId = req.params.examId;

    // Nếu không có productId nhưng có examId, lấy productId từ bài thi
    if (!productId && examId) {
      const exam = await import("../models/exam").then(
        (module) => module.default
      );
      const examData = await exam.findById(examId);

      if (!examData) {
        return res.status(404).render("error", {
          message: "Không tìm thấy bài thi",
          error: { status: 404 },
          user: res.locals.user,
        });
      }

      productId = examData.productId.toString();
    }

    if (!productId) {
      return res.status(400).render("error", {
        message: "ID môn học không hợp lệ",
        error: { status: 400 },
        user: res.locals.user,
      });
    }

    // Tìm sinh viên theo email
    const email = res.locals.user.email;
    const student = await import("../models/student").then(
      (module) => module.default
    );

    // Tìm xem sinh viên có đăng ký môn học này không
    const studentData = await student.findOne({
      email: email,
      productId: productId,
    });

    if (!studentData) {
      return res.status(403).render("error", {
        message: "Bạn không có quyền truy cập trang này",
        error: {
          status: 403,
          description: "Bạn không được đăng ký vào môn học này",
        },
        user: res.locals.user,
      });
    }

    // Nếu qua tất cả kiểm tra, cho phép truy cập
    req.student = studentData;
    next();
  } catch (error) {
    console.error("Lỗi khi kiểm tra quyền truy cập:", error);
    return res.status(500).render("error", {
      message: "Đã xảy ra lỗi khi xử lý yêu cầu",
      error: { status: 500 },
      user: res.locals.user,
    });
  }
};
