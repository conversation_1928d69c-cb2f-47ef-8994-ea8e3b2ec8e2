import { Request, Response, NextFunction } from "express";
import { <PERSON>rrorHandler } from "../util/errorhandler";

// Middleware xử lý lỗi 404 (Not Found)
export const notFoundMiddleware = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  // Log thông tin đường dẫn cho việc debug
  // console.log(
  //   `[404] Route không tìm thấy: ${req.originalUrl}, Method: ${req.method}`
  // );

  // Render trang 404 với URL gốc
  res.status(404).render("404", {
    user: res.locals?.user || null,
    originalUrl: req.originalUrl,
    layout: "layouts/layout", // Đảm bảo sử dụng layout chung
  });
};

// Middleware bắt các lỗi không lường trước (uncaught)
export const uncaughtErrorMiddleware = (
  err: Error,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  console.error(`UNCAUGHT ERROR on ${req.method} ${req.path}:`, err);

  // Nếu lỗi không phải là instance củ<PERSON>, chuyển đổi nó
  if (!(err instanceof ErrorHandler)) {
    const error = new ErrorHandler(
      process.env.NODE_ENV === "production"
        ? "Đã xảy ra lỗi hệ thống"
        : err.message || "Lỗi hệ thống không xác định",
      500
    );
    return next(error);
  }

  // Tiếp tục với middleware xử lý lỗi tiếp theo
  next(err);
};

// Middleware xử lý lỗi 500 và các lỗi khác
export const globalErrorMiddleware = (
  err: ErrorHandler,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const statusCode = err.statusCode || 500;
  const message = err.message || "Lỗi máy chủ nội bộ";

  // Log chi tiết lỗi
  console.error(`[${statusCode}] Error for ${req.method} ${req.path}:`, {
    message: message,
    stack: err.stack,
  });

  // Kiểm tra nếu yêu cầu là API (XHR hoặc chờ đợi JSON)
  const isApiRequest =
    req.xhr ||
    req.headers.accept?.includes("application/json") ||
    req.path.startsWith("/api/");

  if (isApiRequest) {
    // Trả về JSON cho các yêu cầu API
    res.status(statusCode).json({
      success: false,
      message: message,
      stack: process.env.NODE_ENV === "development" ? err.stack : undefined,
    });
  } else {
    try {
      // Render trang lỗi 500 cho các yêu cầu thông thường
      res.status(statusCode).render("500", {
        user: res.locals?.user || null,
        statusCode: statusCode,
        message: message,
        stack: process.env.NODE_ENV === "development" ? err.stack : null,
        layout: "layouts/layout", // Đảm bảo sử dụng layout chung
      });
    } catch (renderError) {
      // Nếu có lỗi khi render, trả về lỗi dạng plain text
      console.error("Error rendering error page:", renderError);
      res.status(statusCode).send(`Lỗi máy chủ: ${message}`);
    }
  }
};
