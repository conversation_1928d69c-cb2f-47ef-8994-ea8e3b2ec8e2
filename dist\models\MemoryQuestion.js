"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importStar(require("mongoose"));
const memoryQuestionSchema = new mongoose_1.Schema({
    userId: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: "User",
        required: true,
        index: true,
    },
    productId: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: "Product",
        required: true,
        index: true,
    },
    questionId: { type: mongoose_1.Schema.Types.ObjectId, ref: "Question", required: true },
    examId: { type: mongoose_1.Schema.Types.ObjectId, ref: "Exam" }, // Optional - để trace nguồn gốc câu hỏi
    source: {
        type: String,
        enum: ["manual", "auto"],
        required: true,
        default: "manual",
    },
    practiceCount: { type: Number, default: 0 },
    correctCount: { type: Number, default: 0 }, // Số lần trả lời đúng
    lastPracticed: { type: Date },
    createdAt: { type: Date, default: Date.now },
    isActive: { type: Boolean, default: true },
});
// Compound index để tối ưu queries
memoryQuestionSchema.index({ userId: 1, productId: 1, isActive: 1 });
memoryQuestionSchema.index({ userId: 1, questionId: 1 }, { unique: true });
exports.default = mongoose_1.default.model("MemoryQuestion", memoryQuestionSchema);
//# sourceMappingURL=MemoryQuestion.js.map