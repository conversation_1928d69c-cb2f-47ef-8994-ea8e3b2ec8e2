import express from "express";
import { authenticateToken } from "../middlewares/authMiddleware";
import asyncHandler from "../util/asynHandler";

const router = express.Router();

// L<PERSON>y thông tin người dùng hiện tại
router.get(
  "/me",
  async<PERSON>and<PERSON>(authenticateToken),
  asyncHandler(async (req, res) => {
    try {
      const user = req.user;

      if (!user) {
        return res.status(401).json({ message: "Không tìm thấy người dùng" });
      }

      // Tr<PERSON> về thông tin người dùng (loại bỏ các trường nh<PERSON>y cảm)
      res.status(200).json({
        id: user._id,
        email: user.email,
        displayName: user.displayName,
        firstName: user.firstName,
        lastName: user.lastName,
        profilePhoto: user.profilePhoto,
      });
    } catch (error: any) {
      res.status(500).json({ message: error.message });
    }
  })
);

export default router;
