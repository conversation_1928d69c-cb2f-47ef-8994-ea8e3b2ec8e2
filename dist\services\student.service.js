"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getStudentCourses = exports.getStudentByEmail = void 0;
const student_1 = __importDefault(require("../models/student"));
class StudentsService {
    createStudent(_a) {
        return __awaiter(this, arguments, void 0, function* ({ email, productId, }) {
            const normalizedEmail = email.toLowerCase();
            try {
                const student = yield student_1.default.findOneAndUpdate({ email: normalizedEmail, productId }, { email: normalizedEmail, productId }, { new: true, upsert: true }).lean();
                console.log("student", student);
                if (student &&
                    student.createdAt.getTime() !== student.updatedAt.getTime()) {
                    return {
                        message: "User already in the class",
                    };
                }
                return {
                    student,
                };
            }
            catch (error) {
                console.error("Error creating/finding student:", error);
                throw error;
            }
        });
    }
    getListStudentByProductId(productId_1) {
        return __awaiter(this, arguments, void 0, function* (productId, page = 1) {
            const limit = 15;
            const skip = (page - 1) * limit;
            const [students, total] = yield Promise.all([
                student_1.default.find({ productId })
                    .sort({ createdAt: -1 })
                    .select("-__v -createdAt -updatedAt")
                    .skip(skip)
                    .limit(limit)
                    .lean(),
                student_1.default.countDocuments(),
            ]);
            return {
                students,
                pagination: {
                    total,
                    page,
                    totalPages: Math.ceil(total / limit),
                },
            };
        });
    }
    deleteStudent(studentId) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield student_1.default.findByIdAndDelete(studentId);
        });
    }
    searchStudentByProductId(keyword_1, productId_1) {
        return __awaiter(this, arguments, void 0, function* (keyword, productId, page = 1) {
            const limit = 15;
            const skip = (page - 1) * limit;
            const [students, total] = yield Promise.all([
                student_1.default.find({
                    $or: [{ email: { $regex: keyword, $options: "i" } }],
                    productId,
                })
                    .select("-__v -createdAt -updatedAt")
                    .skip(skip)
                    .limit(limit)
                    .lean(),
                student_1.default.countDocuments({
                    $or: [{ email: { $regex: keyword, $options: "i" } }, { productId }],
                }),
            ]);
            return {
                students,
                pagination: {
                    total,
                    page,
                    totalPages: Math.ceil(total / limit),
                },
            };
        });
    }
    getCountStudentByProductId(productId) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield student_1.default.countDocuments({ productId });
        });
    }
    /**
     * Lấy danh sách đề thi của sinh viên theo khóa học
     * @param studentId ID của sinh viên
     * @param productId ID của khóa học
     * @returns Danh sách đề thi
     */
    getStudentExams(studentId, productId) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                // Xác nhận sinh viên tồn tại và có quyền truy cập khóa học
                const student = yield student_1.default.findOne({ _id: studentId, productId });
                if (!student) {
                    throw new Error("Sinh viên không có quyền truy cập khóa học này");
                }
                // Giả định rằng có một model Exam với cấu trúc phù hợp
                // Bạn cần thay thế đoạn này bằng truy vấn thực tế đến model Exam của bạn
                // Ví dụ:
                // const exams = await Exam.find({ productId }).lean();
                // Hiện tại trả về dữ liệu mẫu
                return {
                    exams: [],
                    message: "Cần triển khai kết nối với model Exam thực tế",
                };
            }
            catch (error) {
                console.error("Error fetching student exams:", error);
                throw error;
            }
        });
    }
    /**
     * Lấy danh sách bài kiểm tra của sinh viên theo khóa học
     * @param studentId ID của sinh viên
     * @param productId ID của khóa học
     * @returns Danh sách bài kiểm tra
     */
    getStudentTests(studentId, productId) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                // Xác nhận sinh viên tồn tại và có quyền truy cập khóa học
                const student = yield student_1.default.findOne({ _id: studentId, productId });
                if (!student) {
                    throw new Error("Sinh viên không có quyền truy cập khóa học này");
                }
                // Giả định rằng có một model Test với cấu trúc phù hợp
                // Bạn cần thay thế đoạn này bằng truy vấn thực tế đến model Test của bạn
                // Ví dụ:
                // const tests = await Test.find({ productId }).lean();
                // Hiện tại trả về dữ liệu mẫu
                return {
                    tests: [],
                    message: "Cần triển khai kết nối với model Test thực tế",
                };
            }
            catch (error) {
                console.error("Error fetching student tests:", error);
                throw error;
            }
        });
    }
}
// Hàm tìm sinh viên theo email
const getStudentByEmail = (email) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        return yield student_1.default.findOne({ email });
    }
    catch (error) {
        console.error("Lỗi khi tìm sinh viên theo email:", error);
        throw error;
    }
});
exports.getStudentByEmail = getStudentByEmail;
// Hàm lấy khóa học của sinh viên
const getStudentCourses = (studentId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // Lấy sinh viên đã chọn để lấy email
        const student = yield student_1.default.findById(studentId);
        if (!student) {
            throw new Error("Không tìm thấy sinh viên");
        }
        // Tìm tất cả bản ghi sinh viên có cùng email
        const studentRecords = yield student_1.default.find({
            email: student.email,
        }).populate("productId");
        if (!studentRecords || studentRecords.length === 0) {
            return [];
        }
        // Lọc các bản ghi có productId và loại bỏ các bản ghi bị trùng lặp
        const uniqueProductIds = new Map();
        studentRecords.forEach((record) => {
            if (record.productId) {
                const productId = record.productId._id.toString();
                if (!uniqueProductIds.has(productId)) {
                    uniqueProductIds.set(productId, record.productId);
                }
            }
        });
        // Chuyển Map thành mảng các khóa học
        return Array.from(uniqueProductIds.values());
    }
    catch (error) {
        console.error("Lỗi khi lấy khóa học của sinh viên:", error);
        throw error;
    }
});
exports.getStudentCourses = getStudentCourses;
exports.default = new StudentsService();
//# sourceMappingURL=student.service.js.map