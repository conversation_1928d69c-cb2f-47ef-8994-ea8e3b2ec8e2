import { Response } from "express";
import User from "../models/User";
import redisActiveDeviceService from "./redisService";

/**
 * Server-Sent Events Service
 * Quản lý các kết nối SSE và gửi thông báo đến client
 * Updated to use Redis for activeDevices persistence
 */

// Lưu trữ tất cả các kết nối theo userId
const connections: Map<string, Map<string, Response>> = new Map();

// Lưu trữ các interval ping theo connectionId (userId:clientId)
const pingIntervals: Map<string, NodeJS.Timeout> = new Map();

// Lưu trữ thông tin forced logout để xử lý khi thiết bị kết nối lại
const pendingForceLogouts: Map<
  string,
  { excludeClientId: string | null; timestamp: number }
> = new Map();

// Tạo connectionId
function getConnectionId(userId: string, clientId: string): string {
  return `${userId}:${clientId}`;
}

// Thêm kết nối SSE mới
export const addConnection = async (
  userId: string,
  clientId: string,
  res: Response
): Promise<void> => {
  try {
    // Kiểm tra xem thiết bị này có phải là thiết bị hoạt động mới nhất không
    const currentActiveDevice = await User.findById(userId).select(
      "lastActiveDevice"
    );
    const dbActiveDevice = currentActiveDevice?.lastActiveDevice;

    // Đồng bộ thông tin active device từ DB với Redis
    if (dbActiveDevice) {
      const currentActiveDevice =
        await redisActiveDeviceService.getActiveDevice(userId);
      if (!currentActiveDevice || currentActiveDevice !== dbActiveDevice) {
        // console.log(
        //   `Syncing active device for user ${userId} from DB: ${dbActiveDevice}`
        // );
        await redisActiveDeviceService.setActiveDevice(userId, dbActiveDevice);
      }
    }

    // Kiểm tra xem kết nối đã tồn tại chưa
    if (connections.has(userId) && connections.get(userId)?.has(clientId)) {
      const connectionId = getConnectionId(userId, clientId);
      // console.log(
      //   `Found existing connection for ${connectionId}, cleaning up...`
      // );

      // Xóa interval ping cũ nếu có
      if (pingIntervals.has(connectionId)) {
        clearInterval(pingIntervals.get(connectionId)!);
        pingIntervals.delete(connectionId);
      }
    }

    if (!connections.has(userId)) {
      connections.set(userId, new Map());
    }

    // Lưu kết nối vào Map với key là clientId
    connections.get(userId)?.set(clientId, res);
    // console.log(
    //   `New SSE connection added for user ${userId}, clientId: ${clientId}`
    // );

    // Cấu hình kết nối SSE
    setupSSEConnection(res, userId, clientId);

    // Kiểm tra xem thiết bị này có phải là thiết bị active mới nhất không
    // Nếu không phải, kiểm tra và xử lý forced logout
    if (
      await redisActiveDeviceService.hasActiveDeviceButNotThis(userId, clientId)
    ) {
      const currentActiveDevice =
        await redisActiveDeviceService.getActiveDevice(userId);
      // console.log(
      //   `Client ${clientId} is not the newest device (${currentActiveDevice}) for user ${userId}`
      // );

      const shouldForceLogout = await checkPendingForceLogout(
        userId,
        clientId,
        res
      );

      if (shouldForceLogout) {
        // console.log(
        //   `Forcing logout of old device ${clientId} for user ${userId}`
        // );
      }
    }

    // Xử lý khi client ngắt kết nối
    res.on("close", () => {
      // console.log(
      //   `Connection closed for user ${userId}, clientId: ${clientId}`
      // );
      removeConnection(userId, clientId);
    });
  } catch (error) {
    // console.error(
    //   `Error in addConnection for user ${userId}, clientId: ${clientId}:`,
    //   error
    // );
  }
};

// Thiết lập kết nối SSE
export const setupSSEConnection = (
  res: Response,
  userId?: string,
  clientId?: string
): void => {
  try {
    // Gửi event kết nối thành công
    res.write(
      `event: connected\ndata: ${JSON.stringify({
        connected: true,
        timestamp: new Date().toISOString(),
      })}\n\n`
    );

    // Nếu có userId và clientId, quản lý interval với Map
    let pingInterval: NodeJS.Timeout;
    if (userId && clientId) {
      const connectionId = getConnectionId(userId, clientId);

      // Xóa interval cũ nếu có
      if (pingIntervals.has(connectionId)) {
        clearInterval(pingIntervals.get(connectionId)!);
        pingIntervals.delete(connectionId);
      }

      // Tạo interval mới
      pingInterval = setInterval(() => {
        try {
          res.write(
            `event: ping\ndata: ${JSON.stringify({
              timestamp: new Date().toISOString(),
            })}\n\n`
          );
        } catch (error) {
          console.error(`Error sending ping to ${connectionId}:`, error);
          clearInterval(pingInterval);
          pingIntervals.delete(connectionId);
        }
      }, 30000);

      // Lưu interval vào Map
      pingIntervals.set(connectionId, pingInterval);

      // Xóa interval khi kết nối đóng
      res.on("close", () => {
        if (pingIntervals.has(connectionId)) {
          clearInterval(pingIntervals.get(connectionId)!);
          pingIntervals.delete(connectionId);
        }
      });
    } else {
      // Backwards compatibility - cách cũ
      pingInterval = setInterval(() => {
        try {
          res.write(
            `event: ping\ndata: ${JSON.stringify({
              timestamp: new Date().toISOString(),
            })}\n\n`
          );
        } catch (error) {
          console.error("Error sending ping:", error);
          clearInterval(pingInterval);
        }
      }, 30000);

      // Xóa interval khi kết nối đóng
      res.on("close", () => {
        clearInterval(pingInterval);
      });
    }
  } catch (error) {
    console.error("Error in setupSSEConnection:", error);
  }
};

// Xóa kết nối SSE
export const removeConnection = (userId: string, clientId: string): void => {
  try {
    const connectionId = getConnectionId(userId, clientId);

    // Xóa interval ping nếu có
    if (pingIntervals.has(connectionId)) {
      console.log(`Cleaning up ping interval for ${connectionId}`);
      clearInterval(pingIntervals.get(connectionId)!);
      pingIntervals.delete(connectionId);
    }

    if (connections.has(userId)) {
      connections.get(userId)?.delete(clientId);
      // console.log(
      //   `SSE connection removed for user ${userId}, clientId: ${clientId}`
      // );

      // Nếu không còn kết nối nào cho userId, xóa entry
      if (connections.get(userId)?.size === 0) {
        connections.delete(userId);
        // console.log(`Removed all connections for user ${userId}`);
      }
    }
  } catch (error) {
    // console.error(
    //   `Error in removeConnection for user ${userId}, clientId: ${clientId}:`,
    //   error
    // );
  }
};

// Thiết lập thiết bị hiện tại là thiết bị đăng nhập mới nhất
export const setCurrentDeviceAsActive = async (
  userId: string,
  clientId: string
): Promise<void> => {
  try {
    // Lưu vào bộ nhớ cache
    await redisActiveDeviceService.setActiveDevice(userId, clientId);

    // Lưu vào cơ sở dữ liệu để duy trì giữa các lần khởi động server
    await User.findByIdAndUpdate(userId, { lastActiveDevice: clientId });

    // console.log(
    //   `Set device ${clientId} as newest active device for user ${userId} (in memory and DB)`
    // );
  } catch (error) {
    // console.error(`Error updating lastActiveDevice in database:`, error);
    // Vẫn lưu vào bộ nhớ cache trong trường hợp lỗi DB
    await redisActiveDeviceService.setActiveDevice(userId, clientId);
    // console.log(
    //   `Set device ${clientId} as newest active device for user ${userId} (memory only)`
    // );
  }
};

// Khôi phục activeDevices từ cơ sở dữ liệu vào Redis khi khởi động server
export const restoreActiveDevices = async (): Promise<void> => {
  try {
    console.log("Restoring active devices from database to Redis...");

    // Tìm tất cả người dùng có lastActiveDevice
    const users = await User.find({ lastActiveDevice: { $ne: null } }).select(
      "_id lastActiveDevice"
    );

    // Sử dụng Redis service để khôi phục
    const count = await redisActiveDeviceService.restoreFromDatabase(
      users.map((user) => ({
        _id: user._id.toString(),
        lastActiveDevice: user.lastActiveDevice,
      }))
    );

    console.log(`Restored ${count} active devices to Redis from database`);
  } catch (error) {
    console.error("Error restoring active devices to Redis:", error);
  }
};

// Đăng xuất tất cả các thiết bị của người dùng, trừ thiết bị hiện tại
export const logoutOtherDevices = async (
  userId: string,
  currentClientId: string | null = null
): Promise<void> => {
  // Không cần gọi lại setCurrentDeviceAsActive ở đây vì nó thường được gọi trước khi gọi hàm này
  // Nếu cần, chỉ đặt thiết bị hiện tại làm thiết bị hoạt động mới nhất khi chưa được đặt trước đó
  if (
    currentClientId &&
    !(await redisActiveDeviceService.isActiveDevice(userId, currentClientId))
  ) {
    await setCurrentDeviceAsActive(userId, currentClientId);
  }

  // Kiểm tra kết nối tồn tại
  if (!connections.has(userId)) {
    // console.log(
    //   `User ${userId} has no active SSE connections - storing forced logout flag for future connections`
    // );

    // Lưu trạng thái vào bộ nhớ tạm để xử lý khi thiết bị kết nối lại
    storeForceLogoutFlag(userId, currentClientId);
    return;
  }

  const userConnections = connections.get(userId)!;
  const connectionsCount = userConnections.size;

  // Nếu chỉ có một kết nối và đó chính là thiết bị hiện tại, không cần làm gì cả
  if (
    connectionsCount === 1 &&
    currentClientId &&
    userConnections.has(currentClientId)
  ) {
    // console.log(
    //   `Only one connection for user ${userId} and it's the current device. No need to force logout.`
    // );
    return;
  }

  // console.log(
  //   `Attempting to logout ${connectionsCount} device(s) for user ${userId} (excluding clientId: ${currentClientId})`
  // );

  let successCount = 0;

  // Gửi trực tiếp đến từng thiết bị
  userConnections.forEach((res, clientId) => {
    if (currentClientId === null || clientId !== currentClientId) {
      try {
        // Tạo dữ liệu sự kiện với timestamp để tránh cache
        const eventData = JSON.stringify({
          message:
            "Tài khoản của bạn vừa được đăng nhập từ thiết bị khác. Bạn đã bị đăng xuất.",
          timestamp: new Date().toISOString(),
          forced: true,
        });

        // Tạo event string đầy đủ
        const event = `event: forced-logout\ndata: ${eventData}\n\n`;

        // console.log(`Sending forced-logout directly to clientId ${clientId}`);

        // Gửi sự kiện
        res.write(event);

        // Thêm ping ngay sau để đảm bảo event được gửi
        res.write(
          `event: ping\ndata: ${JSON.stringify({
            timestamp: new Date().toISOString(),
          })}\n\n`
        );

        successCount++;
      } catch (error) {
        console.error(
          `Error sending logout notification to client ${clientId}:`,
          error
        );
      }
    } else {
      console.log(`Skipping current device with clientId: ${clientId}`);
    }
  });

  // console.log(
  //   `Successfully sent logout notifications to ${successCount}/${
  //     currentClientId ? connectionsCount - 1 : connectionsCount
  //   } devices for user ${userId}`
  // );

  // Lưu trạng thái vào bộ nhớ tạm để xử lý các thiết bị kết nối sau này
  storeForceLogoutFlag(userId, currentClientId);
};

// Lưu flag để xử lý khi thiết bị kết nối lại
function storeForceLogoutFlag(userId: string, excludeClientId: string | null) {
  pendingForceLogouts.set(userId, {
    excludeClientId,
    timestamp: Date.now(),
  });
  // console.log(
  //   `Stored forced logout flag for user ${userId}, excluding clientId: ${excludeClientId}`
  // );
}

// Kiểm tra và xử lý forced logout bảo lưu khi thiết bị kết nối mới
export const checkPendingForceLogout = async (
  userId: string,
  clientId: string,
  res: Response
): Promise<boolean> => {
  // Xóa các flag quá hạn (lưu tối đa 1 giờ)
  const ONE_HOUR = 60 * 60 * 1000;
  const now = Date.now();

  // Dọn dẹp các flag cũ
  pendingForceLogouts.forEach((data, uid) => {
    if (now - data.timestamp > ONE_HOUR) {
      pendingForceLogouts.delete(uid);
    }
  });

  // Kiểm tra xem thiết bị này có phải là thiết bị hoạt động mới nhất không từ cả DB và bộ nhớ
  const isActiveInMemory = await redisActiveDeviceService.isActiveDevice(
    userId,
    clientId
  );

  // Kiểm tra từ DB nếu không tìm thấy trong bộ nhớ cache hoặc cần xác nhận
  if (!isActiveInMemory) {
    try {
      const user = await User.findById(userId).select("lastActiveDevice");
      if (user && user.lastActiveDevice === clientId) {
        console.log(
          `Device ${clientId} is the newest active device for user ${userId} (from DB), no need to logout`
        );

        // Cập nhật lại bộ nhớ cache nếu DB có thông tin mới hơn
        await redisActiveDeviceService.setActiveDevice(userId, clientId);

        return false;
      }
    } catch (error) {
      console.error(
        `Error checking active device in DB for user ${userId}:`,
        error
      );
      // Trong trường hợp lỗi, chỉ dựa vào bộ nhớ cache
    }
  } else {
    console.log(
      `Device ${clientId} is the newest active device for user ${userId} (from memory), no need to logout`
    );
    return false;
  }

  // Kiểm tra xem có forced logout nào chờ cho user này không
  if (pendingForceLogouts.has(userId)) {
    const data = pendingForceLogouts.get(userId)!;

    // Nếu clientId hiện tại không phải là thiết bị đăng nhập mới
    if (data.excludeClientId !== clientId) {
      // console.log(
      //   `Processing pending forced logout for user ${userId}, client ${clientId}`
      // );

      // Gửi thông báo forced logout
      try {
        const eventData = JSON.stringify({
          message:
            "Tài khoản của bạn đã được đăng nhập từ thiết bị khác. Bạn đã bị đăng xuất.",
          timestamp: new Date().toISOString(),
          forced: true,
          delayed: true,
        });

        res.write(`event: forced-logout\ndata: ${eventData}\n\n`);
        // console.log(`Sent delayed forced-logout to client ${clientId}`);
        return true;
      } catch (error) {
        console.error(
          `Error sending delayed forced-logout to client ${clientId}:`,
          error
        );
      }
    } else {
      console.log(
        `Client ${clientId} is the new login device, skipping forced logout`
      );
    }
  }

  return false;
};

// Lấy tất cả kết nối của một người dùng
export const getUserConnections = (
  userId: string
): Map<string, Response> | undefined => {
  return connections.get(userId);
};

// Trả về clientId của thiết bị mới nhất đang hoạt động
export const getActiveDevice = async (
  userId: string
): Promise<string | undefined> => {
  return await redisActiveDeviceService.getActiveDevice(userId);
};

export default {
  addConnection,
  setupSSEConnection,
  removeConnection,
  logoutOtherDevices,
  getUserConnections,
  setCurrentDeviceAsActive,
  getActiveDevice,
  restoreActiveDevices,
};
