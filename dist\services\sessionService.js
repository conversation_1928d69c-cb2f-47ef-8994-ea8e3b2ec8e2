"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.scheduleSessionCleanup = exports.updateLastActiveTime = exports.getUserSessions = exports.deleteSessionById = exports.logoutSession = exports.findValidSession = exports.logoutOtherDevices = exports.createSession = exports.generateToken = exports.parseDeviceInfo = void 0;
const Session_1 = __importDefault(require("../models/Session"));
const User_1 = __importDefault(require("../models/User"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const sseService = __importStar(require("./sseService"));
const ua_parser_js_1 = __importDefault(require("ua-parser-js"));
// Xử lý thông tin thiết bị từ User-Agent
const parseDeviceInfo = (req) => {
    const parser = new ua_parser_js_1.default(req.headers["user-agent"]);
    const browser = parser.getBrowser();
    const os = parser.getOS();
    const device = parser.getDevice();
    return {
        userAgent: req.headers["user-agent"] || "unknown",
        ip: req.ip || req.socket.remoteAddress || "unknown",
        browser: browser.name ? `${browser.name} ${browser.version}` : "unknown",
        os: os.name ? `${os.name} ${os.version}` : "unknown",
        deviceName: device.model || device.vendor || "unknown device",
    };
};
exports.parseDeviceInfo = parseDeviceInfo;
// Tạo JWT token
const generateToken = (user) => {
    const secret = process.env.JWT_SECRET || "default_secret";
    const expiration = 90 * 24 * 60 * 60 * 1000;
    return jsonwebtoken_1.default.sign({ id: user._id, email: user.email }, secret, {
        expiresIn: expiration,
    });
};
exports.generateToken = generateToken;
// Tạo phiên đăng nhập mới
const createSession = (user, clientId, req) => __awaiter(void 0, void 0, void 0, function* () {
    // Tạo JWT token
    const token = (0, exports.generateToken)(user);
    // Xử lý thông tin thiết bị
    const deviceInfo = (0, exports.parseDeviceInfo)(req);
    // Tạo phiên đăng nhập
    const session = yield Session_1.default.createSession(user._id, token, clientId, deviceInfo);
    // console.log(
    //   `Created new session for user ${user._id}, device: ${deviceInfo.deviceName}`
    // );
    // Cập nhật token vào User model cho tương thích ngược
    yield User_1.default.findByIdAndUpdate(user._id, {
        activeToken: token,
        lastActiveDevice: clientId,
    });
    // Đảm bảo cập nhật activeDevices trong SSE service
    yield sseService.setCurrentDeviceAsActive(user._id.toString(), clientId);
    return { token, session };
});
exports.createSession = createSession;
// Đăng xuất các thiết bị khác
const logoutOtherDevices = (userId, currentClientId) => __awaiter(void 0, void 0, void 0, function* () {
    // Tìm tất cả các phiên khác
    const otherSessions = yield Session_1.default.find({
        userId,
        clientId: { $ne: currentClientId },
    });
    // Xóa từng phiên
    for (const session of otherSessions) {
        yield Session_1.default.deleteOne({ _id: session._id });
    }
    // Thông báo cho các thiết bị khác
    yield sseService.logoutOtherDevices(userId, currentClientId);
    // console.log(`Đã xóa các phiên đăng nhập khác cho user ${userId}`);
});
exports.logoutOtherDevices = logoutOtherDevices;
// Tìm phiên hợp lệ theo token
const findValidSession = (token) => __awaiter(void 0, void 0, void 0, function* () {
    return Session_1.default.findValidSessionByToken(token);
});
exports.findValidSession = findValidSession;
// Đăng xuất từ một phiên cụ thể
const logoutSession = (token) => __awaiter(void 0, void 0, void 0, function* () {
    // Tìm phiên
    const session = yield Session_1.default.findOne({ token });
    if (session) {
        // Xóa hoàn toàn phiên thay vì chỉ đánh dấu không hoạt động
        yield Session_1.default.deleteOne({ _id: session._id });
        // Nếu là thiết bị hiện tại, cập nhật User model
        if (session.isCurrentDevice) {
            yield User_1.default.findByIdAndUpdate(session.userId, {
                activeToken: null,
                lastActiveDevice: null,
            });
        }
        // console.log(
        //   `Đã xóa hoàn toàn phiên đăng nhập cho user ${session.userId}, thiết bị: ${
        //     session.deviceInfo.deviceName || "unknown"
        //   }`
        // );
    }
});
exports.logoutSession = logoutSession;
// Đăng xuất hoàn toàn một phiên theo ID
const deleteSessionById = (sessionId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const session = yield Session_1.default.findById(sessionId);
        if (!session) {
            return false;
        }
        // Xóa hoàn toàn phiên
        yield Session_1.default.deleteOne({ _id: sessionId });
        // console.log(
        //   `Đã xóa hoàn toàn phiên đăng nhập ID ${sessionId} cho user ${session.userId}`
        // );
        return true;
    }
    catch (error) {
        // console.error(`Lỗi khi xóa phiên đăng nhập: ${error}`);
        return false;
    }
});
exports.deleteSessionById = deleteSessionById;
// Liệt kê tất cả các phiên của một người dùng
const getUserSessions = (userId) => __awaiter(void 0, void 0, void 0, function* () {
    return Session_1.default.find({ userId, isActive: true }).sort({ createdAt: -1 });
});
exports.getUserSessions = getUserSessions;
// Cập nhật thời gian hoạt động cuối cùng
const updateLastActiveTime = (token) => __awaiter(void 0, void 0, void 0, function* () {
    yield Session_1.default.updateOne({ token }, { $set: { lastActive: new Date() } });
});
exports.updateLastActiveTime = updateLastActiveTime;
// Lập lịch dọn dẹp các phiên hết hạn
const scheduleSessionCleanup = () => {
    // Dọn dẹp các phiên hết hạn hàng ngày
    const interval = setInterval(() => __awaiter(void 0, void 0, void 0, function* () {
        try {
            const result = yield Session_1.default.removeExpiredSessions();
            // console.log(`Cleaned up ${result.deletedCount} expired sessions`);
        }
        catch (error) {
            // console.error("Error cleaning up expired sessions:", error);
        }
    }), 24 * 60 * 60 * 1000); // Mỗi 24 giờ
    return interval;
};
exports.scheduleSessionCleanup = scheduleSessionCleanup;
exports.default = {
    createSession: exports.createSession,
    logoutOtherDevices: exports.logoutOtherDevices,
    findValidSession: exports.findValidSession,
    logoutSession: exports.logoutSession,
    getUserSessions: exports.getUserSessions,
    updateLastActiveTime: exports.updateLastActiveTime,
    scheduleSessionCleanup: exports.scheduleSessionCleanup,
    parseDeviceInfo: exports.parseDeviceInfo,
    generateToken: exports.generateToken,
    deleteSessionById: exports.deleteSessionById,
};
//# sourceMappingURL=sessionService.js.map