"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importDefault(require("mongoose"));
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config();
// Chỉ sử dụng một kết nối duy nhất cho cả ứng dụng
mongoose_1.default
    // .connect("******************************************************")
    .connect("mongodb://localhost:27017/google-auth")
    .then(() => console.log("Kết nối thành công đến cơ sở dữ liệu!"))
    .catch((err) => console.error("Lỗi kết nối đến cơ sở dữ liệu:", err));
// .connect(process.env.MONGODB_URI as string)
// Export connection để sử dụng nếu cần thiết
exports.default = mongoose_1.default.connection;
//# sourceMappingURL=dbConfig.js.map