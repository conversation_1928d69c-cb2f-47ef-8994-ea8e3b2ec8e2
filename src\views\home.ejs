<% if (typeof announcement !== "undefined" && announcement) { %> <%-
include('partials/announcement-modal') %> <% } %>
<div class="bg-gradient-to-br from-blue-50 via-white to-teal-50">
  <div class="min-h-screen py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Phần Header thông tin người dùng -->
      <div
        class="bg-white rounded-3xl shadow-xl p-8 mb-8 relative overflow-hidden"
      >
        <!-- C<PERSON><PERSON> yếu tố trang trí nền -->
        <div
          class="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-blue-100 to-teal-100 rounded-full -translate-y-32 translate-x-32 opacity-50"
        ></div>
        <div
          class="absolute bottom-0 left-0 w-48 h-48 bg-gradient-to-tr from-purple-100 to-pink-100 rounded-full translate-y-24 -translate-x-24 opacity-50"
        ></div>

        <div class="relative z-10">
          <div
            class="flex flex-col md:flex-row items-center md:items-start space-y-6 md:space-y-0 md:space-x-8"
          >
            <!-- Avatar -->
            <div class="relative">
              <img
                src="<%= user.profilePhoto %>"
                alt="<%= user.displayName %>"
                class="w-24 h-24 rounded-full object-cover border-4 border-white shadow-lg"
              />
              <div
                class="absolute -bottom-2 -right-2 bg-green-500 text-white rounded-full p-2"
              >
                <i data-lucide="check-circle" class="w-4 h-4"></i>
              </div>
            </div>

            <!-- Thông tin người dùng -->
            <div class="flex-1 text-center md:text-left">
              <h1 class="text-3xl font-bold text-gray-900 mb-2">
                Xin chào, <%= user.displayName %>!
              </h1>
              <!-- UPDATED: Container for user details -->
              <div class="space-y-2">
                <div
                  class="flex items-center justify-center md:justify-start space-x-2 text-gray-600"
                >
                  <i data-lucide="mail" class="w-4 h-4 text-green-600"></i>
                  <span><%= user.email %></span>
                </div>
                <div
                  class="flex items-center justify-center md:justify-start space-x-2 text-gray-600"
                >
                  <i data-lucide="book-open" class="w-4 h-4 text-blue-600"></i>
                  <span
                    >Tổng số môn học:
                    <span class="font-semibold text-gray-800"
                      ><%= courses ? courses.length : 0 %></span
                    ></span
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Tiêu đề cho các khóa học đang học -->
      <h2 class="text-2xl font-bold text-gray-800 mb-6 px-4 sm:px-0">
        Môn học đang học
      </h2>

      <!-- Thông báo lỗi nếu có -->
      <% if (typeof error !== 'undefined' && error) { %>
      <div
        class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4"
      >
        <%= error %>
      </div>
      <% } %>

      <!-- Thông báo nếu không tìm thấy thông tin sinh viên -->
      <% if (typeof student === 'undefined' || !student) { %>
      <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
        <div class="flex">
          <div class="flex-shrink-0">
            <i class="fas fa-exclamation-triangle text-yellow-400"></i>
          </div>
          <div class="ml-3">
            <p class="text-sm text-yellow-700">
              Không tìm thấy thông tin sinh viên. Vui lòng liên hệ quản trị viên
              để được đăng ký.
            </p>
          </div>
        </div>
      </div>
      <% } else if (!courses || courses.length === 0) { %>
      <!-- Thông báo nếu không có khóa học nào -->
      <div class="bg-blue-50 border-l-4 border-blue-400 p-4">
        <div class="flex">
          <div class="flex-shrink-0">
            <i class="fas fa-info-circle text-blue-400"></i>
          </div>
          <div class="ml-3">
            <p class="text-sm text-blue-700">
              Bạn chưa được đăng ký vào môn học nào.
            </p>
          </div>
        </div>
      </div>
      <% } else { %>
      <!-- Lưới hiển thị các khóa học đang học (Active) -->
      <div id="content-active" class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        <% courses.forEach(course => { %>
        <!-- Thẻ khóa học -->
        <div
          class="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 group"
        >
          <div class="relative">
            <img
              src="<%= course.image || 'https://images.pexels.com/photos/4386466/pexels-photo-4386466.jpeg?auto=compress&cs=tinysrgb&w=300' %>"
              alt="<%= course.name %>"
              class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
            />
            <div class="absolute top-4 left-4">
              <span
                class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium <%= course.status === 'active' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800' %>"
              >
                <i class="fa-solid fa-play w-3 h-3 mr-1"></i>
                <%= course.status === 'active' ? 'Đang học' : course.status %>
              </span>
            </div>
          </div>
          <div class="p-6">
            <h3
              class="text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors duration-200"
            >
              <%= course.name %>
            </h3>
            <div class="mb-4">
              <div class="flex justify-between items-center mb-2">
                <span class="text-sm font-medium text-gray-700">Tiến độ</span>
                <span class="text-sm font-bold text-gray-900"
                  ><%= course.progress_percentage || 0 %>%</span
                >
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div
                  class="h-2 rounded-full transition-all duration-300 bg-blue-500"
                  style="width: <%= course.progress_percentage || 0 %>%"
                ></div>
              </div>
              <div class="flex justify-between text-xs text-gray-500 mt-1">
                <span><%= course.total_questions || 0 %> câu hỏi</span>
                <span>Đang học</span>
              </div>
            </div>

            <div class="grid grid-cols-2 gap-3">
              <a
                href="/course/<%= course._id %>/exams"
                class="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-bold py-3 px-4 rounded-xl transition-all duration-200 transform hover:scale-105 hover:shadow-lg flex items-center justify-center space-x-2"
              >
                <i class="fa-solid fa-play w-4 h-4"></i>
                <span>Học ngay</span>
              </a>
              <a
                href="/course/<%= course._id %>/exams?tab=outline"
                class="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 font-semibold py-3 px-4 rounded-xl transition-colors duration-200 flex items-center justify-center space-x-2"
              >
                <i class="fa-solid fa-file-lines w-4 h-4"></i>
                <span>Tài liệu</span>
              </a>
            </div>
          </div>
        </div>
        <% }); %>
      </div>
      <% } %>

      <!-- Phần Hành động nhanh/Hỗ trợ -->
      <div
        class="mt-12 bg-gradient-to-r from-blue-600 to-teal-600 rounded-2xl p-8 text-white"
      >
        <div class="text-center mb-8">
          <h3 class="text-2xl font-bold mb-4">Cần hỗ trợ học tập?</h3>
          <p class="text-blue-100 max-w-2xl mx-auto">
            Đội ngũ hỗ trợ học tập của chúng tôi luôn sẵn sàng giúp bạn đạt được
            mục tiêu học tập
          </p>
        </div>
        <div class="grid md:grid-cols-3 gap-6">
          <div
            class="bg-white/20 backdrop-blur-sm p-6 rounded-xl text-center hover:bg-white/30 transition-all duration-300 transform hover:scale-105"
          >
            <i data-lucide="message-circle" class="mx-auto mb-4 w-8 h-8"></i>
            <h4 class="font-semibold mb-2">Hỏi đáp trực tuyến</h4>
            <p class="text-sm text-blue-100">Chat với giảng viên và bạn học</p>
          </div>
          <div
            class="bg-white/20 backdrop-blur-sm p-6 rounded-xl text-center hover:bg-white/30 transition-all duration-300 transform hover:scale-105"
          >
            <i data-lucide="calendar" class="mx-auto mb-4 w-8 h-8"></i>
            <h4 class="font-semibold mb-2">Lịch học cá nhân</h4>
            <p class="text-sm text-blue-100">
              Quản lý thời gian học tập hiệu quả
            </p>
          </div>
          <div
            class="bg-white/20 backdrop-blur-sm p-6 rounded-xl text-center hover:bg-white/30 transition-all duration-300 transform hover:scale-105"
          >
            <i data-lucide="award" class="mx-auto mb-4 w-8 h-8"></i>
            <h4 class="font-semibold mb-2">Theo dõi tiến độ</h4>
            <p class="text-sm text-blue-100">
              Báo cáo chi tiết kết quả học tập
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  // Khởi tạo các icon Lucide sau khi trang đã tải
  document.addEventListener("DOMContentLoaded", function () {
    if (typeof lucide !== "undefined") {
      lucide.createIcons();
    }
  });
</script>
