"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getCourseExams = void 0;
const modelService_1 = require("../services/modelService");
const question_1 = __importDefault(require("../models/question"));
const mongoose_1 = __importDefault(require("mongoose"));
const encryption_1 = require("../util/encryption");
const Announcement_1 = __importDefault(require("../models/Announcement"));
const MemoryQuestion_1 = __importDefault(require("../models/MemoryQuestion"));
/**
 * Hiển thị trang danh sách đề thi của một môn học
 */
const getCourseExams = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const productId = req.params.productId;
        const tab = req.query.tab || "exams"; // Mặc định tab là 'exams'
        // Validate tab parameter
        const validTabs = ["exams", "practice", "outline", "memory"];
        const currentTab = validTabs.includes(tab) ? tab : "exams";
        // Lấy thông tin khóa học
        const product = yield modelService_1.Product.findById(productId);
        if (!product) {
            return res.status(404).render("error", {
                message: "Không tìm thấy thông tin môn học",
                error: { status: 404 },
                user: res.locals.user,
            });
        }
        // Lấy danh sách đề thi của khóa học
        const exams = yield modelService_1.Exam.find({
            productId: new mongoose_1.default.Types.ObjectId(productId),
        });
        let questionsByExam = [];
        if (currentTab === "outline" || currentTab === "practice") {
            // Bước 1: Lấy ID các đề thi
            const examIds = exams.map((exam) => exam._id);
            console.log("🚀 examIds", examIds);
            if (examIds.length > 0) {
                // Bước 2: Lấy danh sách câu hỏi của các đề thi
                const questions = yield question_1.default.find({
                    examId: { $in: examIds },
                })
                    .populate("examId", "name")
                    .sort({ examId: 1, orderNumber: -1 });
                // Bước 3: Nhóm câu hỏi theo đề thi ở backend
                const questionGroupMap = {};
                questions.forEach((question) => {
                    const examId = question.examId._id.toString();
                    const examName = question.examId.name;
                    if (!questionGroupMap[examId]) {
                        questionGroupMap[examId] = {
                            examId: examId,
                            examName: examName,
                            questions: [],
                        };
                    }
                    questionGroupMap[examId].questions.push({
                        _id: question._id,
                        text: question.text,
                        answers: question.answers,
                        image: question.image,
                        difficulty: question.difficulty,
                    });
                });
                // Bước 4: Chuyển đổi object thành array
                questionsByExam = Object.values(questionGroupMap);
            }
        }
        // Nếu đang ở tab memory, lấy danh sách câu hỏi ghi nhớ của user
        let memoryQuestions = [];
        if (currentTab === "memory") {
            memoryQuestions = yield MemoryQuestion_1.default.find({
                userId: res.locals.user._id,
                productId: productId,
                isActive: true,
            })
                .populate("questionId")
                .sort({ createdAt: -1 })
                .limit(100);
        }
        // Nếu đang ở tab outline, lấy danh sách IDs câu hỏi đã ghi nhớ
        let memorizedQuestionIds = [];
        if (currentTab === "outline") {
            const memorizedQuestions = yield MemoryQuestion_1.default.find({
                userId: res.locals.user._id,
                productId: productId,
                isActive: true,
            }).select("questionId");
            memorizedQuestionIds = memorizedQuestions.map((mq) => mq.questionId.toString());
        }
        // Mã hóa dữ liệu câu hỏi nếu có
        let encryptedQuestions = null;
        let encryptionKey = null;
        let encryptionIV = null;
        if (questionsByExam && questionsByExam.length > 0) {
            try {
                const encryptionResult = (0, encryption_1.encryptObject)(questionsByExam);
                encryptedQuestions = encryptionResult.encryptedData;
                encryptionKey = encryptionResult.key;
                encryptionIV = encryptionResult.iv;
            }
            catch (error) {
                console.error("Lỗi khi mã hóa dữ liệu:", error);
                // Fallback: sử dụng dữ liệu không mã hóa
            }
        }
        // Render trang danh sách đề thi với thông tin tab
        const finalQuestionsByExam = currentTab === "outline"
            ? encryptedQuestions
                ? null
                : questionsByExam
            : questionsByExam;
        // Lấy thông báo
        const announcement = yield Announcement_1.default.findOne({
            location: `course/${productId}`,
            isActive: true,
        }).sort({ priority: -1 }); // Lấy thông báo có priority cao nhất
        res.render("courses/exams", {
            user: res.locals.user,
            student: req.student,
            product,
            exams,
            questionsByExam: finalQuestionsByExam,
            encryptedQuestions,
            encryptionKey,
            encryptionIV,
            currentTab,
            announcement,
            memoryQuestions,
            memorizedQuestionIds,
        });
    }
    catch (error) {
        console.error("Lỗi khi lấy thông tin đề thi:", error);
        res.status(500).render("error", {
            message: "Có lỗi xảy ra khi tải thông tin đề thi",
            error: { status: 500 },
            user: res.locals.user,
        });
    }
});
exports.getCourseExams = getCourseExams;
//# sourceMappingURL=course.controller.js.map