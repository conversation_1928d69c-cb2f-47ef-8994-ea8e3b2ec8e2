"use strict";
/**
 * Authentication Configuration
 * Centralized configuration for all authentication-related timeouts and settings
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthConfigInfo = exports.AuthUtils = exports.AuthConfig = void 0;
// Time constants in milliseconds
const MINUTE = 60 * 1000;
const HOUR = 60 * MINUTE;
const DAY = 24 * HOUR;
/**
 * Authentication timeout configurations
 */
exports.AuthConfig = {
    // Default login session duration (90 days)
    DEFAULT_LOGIN_DURATION: 90 * DAY,
    // JWT Token configurations
    JWT: {
        // JWT token expiration (90 days)
        EXPIRATION: 90 * DAY,
        // JWT refresh threshold (auto refresh when token has less than 1 hour remaining)
        REFRESH_THRESHOLD: 1 * HOUR,
    },
    // Session configurations
    SESSION: {
        // Session expiration in database (90 days - matching JWT)
        EXPIRATION: 90 * DAY,
        // Session cleanup interval (daily)
        CLEANUP_INTERVAL: 1 * DAY,
    },
    // Cookie configurations
    COOKIE: {
        // <PERSON>ie max age (90 days - matching JWT and session)
        MAX_AGE: 90 * DAY,
        // Cookie security options
        HTTP_ONLY: true,
        SECURE: process.env.NODE_ENV === "production",
        SAME_SITE: "lax",
    },
    // Password reset configurations
    RESET_TOKEN: {
        // Reset token expiration (1 hour)
        EXPIRATION: 1 * HOUR,
    },
    // Other authentication timeouts
    OTHER: {
        // Rate limiting window
        RATE_LIMIT_WINDOW: 15 * MINUTE,
        // Account lockout duration (if implemented)
        ACCOUNT_LOCKOUT: 30 * MINUTE,
    },
};
/**
 * Helper functions for authentication configuration
 */
exports.AuthUtils = {
    /**
     * Get JWT expiration in seconds (for jsonwebtoken library)
     */
    getJwtExpirationInSeconds() {
        return Math.floor(exports.AuthConfig.JWT.EXPIRATION / 1000);
    },
    /**
     * Get JWT expiration as string (for jsonwebtoken library)
     */
    getJwtExpirationString() {
        return `${exports.AuthConfig.JWT.EXPIRATION}ms`;
    },
    /**
     * Check if token should be refreshed
     */
    shouldRefreshToken(expirationTime) {
        const timeUntilExpiration = expirationTime - Date.now();
        return timeUntilExpiration < exports.AuthConfig.JWT.REFRESH_THRESHOLD;
    },
    /**
     * Calculate expiration date from now
     */
    getExpirationDate(duration = exports.AuthConfig.DEFAULT_LOGIN_DURATION) {
        return new Date(Date.now() + duration);
    },
    /**
     * Get cookie options with configured settings
     */
    getCookieOptions() {
        return {
            httpOnly: exports.AuthConfig.COOKIE.HTTP_ONLY,
            secure: exports.AuthConfig.COOKIE.SECURE,
            sameSite: exports.AuthConfig.COOKIE.SAME_SITE,
            maxAge: exports.AuthConfig.COOKIE.MAX_AGE,
        };
    },
};
/**
 * Development/debugging helper
 */
exports.AuthConfigInfo = {
    // Human-readable duration descriptions
    durations: {
        defaultLogin: `${exports.AuthConfig.DEFAULT_LOGIN_DURATION / DAY} days`,
        jwtExpiration: `${exports.AuthConfig.JWT.EXPIRATION / DAY} days`,
        sessionExpiration: `${exports.AuthConfig.SESSION.EXPIRATION / DAY} days`,
        cookieMaxAge: `${exports.AuthConfig.COOKIE.MAX_AGE / DAY} days`,
        resetTokenExpiration: `${exports.AuthConfig.RESET_TOKEN.EXPIRATION / HOUR} hour(s)`,
    },
    // Configuration summary for logging
    summary: {
        loginDuration: exports.AuthConfig.DEFAULT_LOGIN_DURATION,
        jwtExpiration: exports.AuthConfig.JWT.EXPIRATION,
        sessionExpiration: exports.AuthConfig.SESSION.EXPIRATION,
        cookieMaxAge: exports.AuthConfig.COOKIE.MAX_AGE,
        allConfiguredFor90Days: exports.AuthConfig.DEFAULT_LOGIN_DURATION === exports.AuthConfig.JWT.EXPIRATION &&
            exports.AuthConfig.JWT.EXPIRATION === exports.AuthConfig.SESSION.EXPIRATION &&
            exports.AuthConfig.SESSION.EXPIRATION === exports.AuthConfig.COOKIE.MAX_AGE,
    },
};
exports.default = exports.AuthConfig;
//# sourceMappingURL=auth-config.js.map